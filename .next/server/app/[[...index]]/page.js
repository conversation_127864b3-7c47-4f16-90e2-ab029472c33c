(()=>{var e={};e.id=169,e.ids=[169],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},72254:e=>{"use strict";e.exports=require("node:buffer")},6005:e=>{"use strict";e.exports=require("node:crypto")},87561:e=>{"use strict";e.exports=require("node:fs")},88849:e=>{"use strict";e.exports=require("node:http")},22286:e=>{"use strict";e.exports=require("node:https")},87503:e=>{"use strict";e.exports=require("node:net")},49411:e=>{"use strict";e.exports=require("node:path")},97742:e=>{"use strict";e.exports=require("node:process")},84492:e=>{"use strict";e.exports=require("node:stream")},72477:e=>{"use strict";e.exports=require("node:stream/web")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},65628:e=>{"use strict";e.exports=require("node:zlib")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},71267:e=>{"use strict";e.exports=require("worker_threads")},59796:e=>{"use strict";e.exports=require("zlib")},77826:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>s.a,__next_app__:()=>d,originalPathname:()=>u,pages:()=>p,routeModule:()=>m,tree:()=>l});var n=a(50482),i=a(69108),r=a(62563),s=a.n(r),o=a(68300),c={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);a.d(t,c);let l=["",{children:["[[...index]]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,97591)),"/Users/<USER>/Downloads/Coding/IBC/Group Creator/src/app/[[...index]]/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,21342)),"/Users/<USER>/Downloads/Coding/IBC/Group Creator/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],p=["/Users/<USER>/Downloads/Coding/IBC/Group Creator/src/app/[[...index]]/page.tsx"],u="/[[...index]]/page",d={require:a,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/[[...index]]/page",pathname:"/[[...index]]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},59579:(e,t,a)=>{Promise.resolve().then(a.bind(a,1024)),Promise.resolve().then(a.bind(a,65102)),Promise.resolve().then(a.bind(a,22315)),Promise.resolve().then(a.bind(a,34910)),Promise.resolve().then(a.bind(a,34755))},8396:(e,t,a)=>{Promise.resolve().then(a.bind(a,1024)),Promise.resolve().then(a.bind(a,65102)),Promise.resolve().then(a.bind(a,22315)),Promise.resolve().then(a.bind(a,34910)),Promise.resolve().then(a.bind(a,4213))},70786:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,2583,23)),Promise.resolve().then(a.t.bind(a,26840,23)),Promise.resolve().then(a.t.bind(a,38771,23)),Promise.resolve().then(a.t.bind(a,13225,23)),Promise.resolve().then(a.t.bind(a,9295,23)),Promise.resolve().then(a.t.bind(a,43982,23))},54656:(e,t,a)=>{"use strict";e.exports={parallel:a(23480),serial:a(47186),serialOrdered:a(74345)}},49105:e=>{"use strict";function t(e){"function"==typeof this.jobs[e]&&this.jobs[e]()}e.exports=function(e){Object.keys(e.jobs).forEach(t.bind(e)),e.jobs={}}},66779:(e,t,a)=>{"use strict";var n=a(94788);e.exports=function(e){var t=!1;return n(function(){t=!0}),function(a,i){t?e(a,i):n(function(){e(a,i)})}}},94788:e=>{"use strict";e.exports=function(e){var t="function"==typeof setImmediate?setImmediate:"object"==typeof process&&"function"==typeof process.nextTick?process.nextTick:null;t?t(e):setTimeout(e,0)}},93354:(e,t,a)=>{"use strict";var n=a(66779),i=a(49105);e.exports=function(e,t,a,r){var s,o,c=a.keyedList?a.keyedList[a.index]:a.index;a.jobs[c]=(s=e[c],o=function(e,t){c in a.jobs&&(delete a.jobs[c],e?i(a):a.results[c]=t,r(e,a.results))},2==t.length?t(s,n(o)):t(s,c,n(o)))}},19780:e=>{"use strict";e.exports=function(e,t){var a=!Array.isArray(e),n={index:0,keyedList:a||t?Object.keys(e):null,jobs:{},results:a?{}:[],size:a?Object.keys(e).length:e.length};return t&&n.keyedList.sort(a?t:function(a,n){return t(e[a],e[n])}),n}},136:(e,t,a)=>{"use strict";var n=a(49105),i=a(66779);e.exports=function(e){Object.keys(this.jobs).length&&(this.index=this.size,n(this),i(e)(null,this.results))}},23480:(e,t,a)=>{"use strict";var n=a(93354),i=a(19780),r=a(136);e.exports=function(e,t,a){for(var s=i(e);s.index<(s.keyedList||e).length;)n(e,t,s,function(e,t){if(e){a(e,t);return}if(0===Object.keys(s.jobs).length){a(null,s.results);return}}),s.index++;return r.bind(s,a)}},47186:(e,t,a)=>{"use strict";var n=a(74345);e.exports=function(e,t,a){return n(e,t,null,a)}},74345:(e,t,a)=>{"use strict";var n=a(93354),i=a(19780),r=a(136);function s(e,t){return e<t?-1:e>t?1:0}e.exports=function(e,t,a,s){var o=i(e,a);return n(e,t,o,function a(i,r){if(i){s(i,r);return}if(o.index++,o.index<(o.keyedList||e).length){n(e,t,o,a);return}s(null,o.results)}),r.bind(o,s)},e.exports.ascending=s,e.exports.descending=function(e,t){return -1*s(e,t)}},11252:(e,t,a)=>{"use strict";var n=a(73837),i=a(12781).Stream,r=a(890);function s(){this.writable=!1,this.readable=!0,this.dataSize=0,this.maxDataSize=2097152,this.pauseStreams=!0,this._released=!1,this._streams=[],this._currentStream=null,this._insideLoop=!1,this._pendingNext=!1}e.exports=s,n.inherits(s,i),s.create=function(e){var t=new this;for(var a in e=e||{})t[a]=e[a];return t},s.isStreamLike=function(e){return"function"!=typeof e&&"string"!=typeof e&&"boolean"!=typeof e&&"number"!=typeof e&&!Buffer.isBuffer(e)},s.prototype.append=function(e){if(s.isStreamLike(e)){if(!(e instanceof r)){var t=r.create(e,{maxDataSize:1/0,pauseStream:this.pauseStreams});e.on("data",this._checkDataSize.bind(this)),e=t}this._handleErrors(e),this.pauseStreams&&e.pause()}return this._streams.push(e),this},s.prototype.pipe=function(e,t){return i.prototype.pipe.call(this,e,t),this.resume(),e},s.prototype._getNext=function(){if(this._currentStream=null,this._insideLoop){this._pendingNext=!0;return}this._insideLoop=!0;try{do this._pendingNext=!1,this._realGetNext();while(this._pendingNext)}finally{this._insideLoop=!1}},s.prototype._realGetNext=function(){var e=this._streams.shift();if(void 0===e){this.end();return}if("function"!=typeof e){this._pipeNext(e);return}e((function(e){s.isStreamLike(e)&&(e.on("data",this._checkDataSize.bind(this)),this._handleErrors(e)),this._pipeNext(e)}).bind(this))},s.prototype._pipeNext=function(e){if(this._currentStream=e,s.isStreamLike(e)){e.on("end",this._getNext.bind(this)),e.pipe(this,{end:!1});return}this.write(e),this._getNext()},s.prototype._handleErrors=function(e){var t=this;e.on("error",function(e){t._emitError(e)})},s.prototype.write=function(e){this.emit("data",e)},s.prototype.pause=function(){this.pauseStreams&&(this.pauseStreams&&this._currentStream&&"function"==typeof this._currentStream.pause&&this._currentStream.pause(),this.emit("pause"))},s.prototype.resume=function(){this._released||(this._released=!0,this.writable=!0,this._getNext()),this.pauseStreams&&this._currentStream&&"function"==typeof this._currentStream.resume&&this._currentStream.resume(),this.emit("resume")},s.prototype.end=function(){this._reset(),this.emit("end")},s.prototype.destroy=function(){this._reset(),this.emit("close")},s.prototype._reset=function(){this.writable=!1,this._streams=[],this._currentStream=null},s.prototype._checkDataSize=function(){if(this._updateDataSize(),!(this.dataSize<=this.maxDataSize)){var e="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this._emitError(Error(e))}},s.prototype._updateDataSize=function(){this.dataSize=0;var e=this;this._streams.forEach(function(t){t.dataSize&&(e.dataSize+=t.dataSize)}),this._currentStream&&this._currentStream.dataSize&&(this.dataSize+=this._currentStream.dataSize)},s.prototype._emitError=function(e){this._reset(),this.emit("error",e)}},60510:(e,t,a)=>{"use strict";t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let a="color: "+this.color;t.splice(1,0,a,"color: inherit");let n=0,i=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(n++,"%c"===e&&(i=n))}),t.splice(i,0,a)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){let e;return!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=a(60506)(t);let{formatters:n}=e.exports;n.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},60506:(e,t,a)=>{"use strict";e.exports=function(e){function t(e){let a,i,r;let s=null;function o(...e){if(!o.enabled)return;let n=Number(new Date),i=n-(a||n);o.diff=i,o.prev=a,o.curr=n,a=n,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let r=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(a,n)=>{if("%%"===a)return"%";r++;let i=t.formatters[n];if("function"==typeof i){let t=e[r];a=i.call(o,t),e.splice(r,1),r--}return a}),t.formatArgs.call(o,e),(o.log||t.log).apply(o,e)}return o.namespace=e,o.useColors=t.useColors(),o.color=t.selectColor(e),o.extend=n,o.destroy=t.destroy,Object.defineProperty(o,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==s?s:(i!==t.namespaces&&(i=t.namespaces,r=t.enabled(e)),r),set:e=>{s=e}}),"function"==typeof t.init&&t.init(o),o}function n(e,a){let n=t(this.namespace+(void 0===a?":":a)+e);return n.log=this.log,n}function i(e,t){let a=0,n=0,i=-1,r=0;for(;a<e.length;)if(n<t.length&&(t[n]===e[a]||"*"===t[n]))"*"===t[n]?(i=n,r=a):a++,n++;else{if(-1===i)return!1;n=i+1,a=++r}for(;n<t.length&&"*"===t[n];)n++;return n===t.length}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names,...t.skips.map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){for(let a of(t.save(e),t.namespaces=e,t.names=[],t.skips=[],("string"==typeof e?e:"").trim().replace(" ",",").split(",").filter(Boolean)))"-"===a[0]?t.skips.push(a.slice(1)):t.names.push(a)},t.enabled=function(e){for(let a of t.skips)if(i(e,a))return!1;for(let a of t.names)if(i(e,a))return!0;return!1},t.humanize=a(58476),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(a=>{t[a]=e[a]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let a=0;for(let t=0;t<e.length;t++)a=(a<<5)-a+e.charCodeAt(t)|0;return t.colors[Math.abs(a)%t.colors.length]},t.enable(t.load()),t}},62056:(e,t,a)=>{"use strict";"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=a(60510):e.exports=a(12086)},12086:(e,t,a)=>{"use strict";let n=a(76224),i=a(73837);t.init=function(e){e.inspectOpts={};let a=Object.keys(t.inspectOpts);for(let n=0;n<a.length;n++)e.inspectOpts[a[n]]=t.inspectOpts[a[n]]},t.log=function(...e){return process.stderr.write(i.formatWithOptions(t.inspectOpts,...e)+"\n")},t.formatArgs=function(a){let{namespace:n,useColors:i}=this;if(i){let t=this.color,i="\x1b[3"+(t<8?t:"8;5;"+t),r=`  ${i};1m${n} \u001B[0m`;a[0]=r+a[0].split("\n").join("\n"+r),a.push(i+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else a[0]=(t.inspectOpts.hideDate?"":new Date().toISOString()+" ")+n+" "+a[0]},t.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},t.load=function(){return process.env.DEBUG},t.useColors=function(){return"colors"in t.inspectOpts?!!t.inspectOpts.colors:n.isatty(process.stderr.fd)},t.destroy=i.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),t.colors=[6,2,3,4,5,1];try{let e=a(60125);e&&(e.stderr||e).level>=2&&(t.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}t.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let a=t.substring(6).toLowerCase().replace(/_([a-z])/g,(e,t)=>t.toUpperCase()),n=process.env[t];return n=!!/^(yes|on|true|enabled)$/i.test(n)||!/^(no|off|false|disabled)$/i.test(n)&&("null"===n?null:Number(n)),e[a]=n,e},{}),e.exports=a(60506)(t);let{formatters:r}=e.exports;r.o=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},r.O=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts)}},890:(e,t,a)=>{"use strict";var n=a(12781).Stream,i=a(73837);function r(){this.source=null,this.dataSize=0,this.maxDataSize=1048576,this.pauseStream=!0,this._maxDataSizeExceeded=!1,this._released=!1,this._bufferedEvents=[]}e.exports=r,i.inherits(r,n),r.create=function(e,t){var a=new this;for(var n in t=t||{})a[n]=t[n];a.source=e;var i=e.emit;return e.emit=function(){return a._handleEmit(arguments),i.apply(e,arguments)},e.on("error",function(){}),a.pauseStream&&e.pause(),a},Object.defineProperty(r.prototype,"readable",{configurable:!0,enumerable:!0,get:function(){return this.source.readable}}),r.prototype.setEncoding=function(){return this.source.setEncoding.apply(this.source,arguments)},r.prototype.resume=function(){this._released||this.release(),this.source.resume()},r.prototype.pause=function(){this.source.pause()},r.prototype.release=function(){this._released=!0,this._bufferedEvents.forEach((function(e){this.emit.apply(this,e)}).bind(this)),this._bufferedEvents=[]},r.prototype.pipe=function(){var e=n.prototype.pipe.apply(this,arguments);return this.resume(),e},r.prototype._handleEmit=function(e){if(this._released){this.emit.apply(this,e);return}"data"===e[0]&&(this.dataSize+=e[1].length,this._checkIfMaxDataSizeExceeded()),this._bufferedEvents.push(e)},r.prototype._checkIfMaxDataSizeExceeded=function(){if(!this._maxDataSizeExceeded&&!(this.dataSize<=this.maxDataSize)){this._maxDataSizeExceeded=!0;var e="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this.emit("error",Error(e))}}},82920:(e,t,a)=>{"use strict";var n;e.exports=function(){if(!n){try{n=a(62056)("follow-redirects")}catch(e){}"function"!=typeof n&&(n=function(){})}n.apply(null,arguments)}},42136:(e,t,a)=>{"use strict";var n=a(57310),i=n.URL,r=a(13685),s=a(95687),o=a(12781).Writable,c=a(39491),l=a(82920);!function(){var e="undefined"!=typeof process,t=T(Error.captureStackTrace);e||t||console.warn("The follow-redirects package should be excluded from browser builds.")}();var p=!1;try{c(new i(""))}catch(e){p="ERR_INVALID_URL"===e.code}var u=["auth","host","hostname","href","path","pathname","port","protocol","query","search","hash"],d=["abort","aborted","connect","error","socket","timeout"],m=Object.create(null);d.forEach(function(e){m[e]=function(t,a,n){this._redirectable.emit(e,t,a,n)}});var f=R("ERR_INVALID_URL","Invalid URL",TypeError),h=R("ERR_FR_REDIRECTION_FAILURE","Redirected request failed"),x=R("ERR_FR_TOO_MANY_REDIRECTS","Maximum number of redirects exceeded",h),v=R("ERR_FR_MAX_BODY_LENGTH_EXCEEDED","Request body larger than maxBodyLength limit"),g=R("ERR_STREAM_WRITE_AFTER_END","write after end"),b=o.prototype.destroy||_;function y(e,t){o.call(this),this._sanitizeOptions(e),this._options=e,this._ended=!1,this._ending=!1,this._redirectCount=0,this._redirects=[],this._requestBodyLength=0,this._requestBodyBuffers=[],t&&this.on("response",t);var a=this;this._onNativeResponse=function(e){try{a._processResponse(e)}catch(e){a.emit("error",e instanceof h?e:new h({cause:e}))}},this._performRequest()}function w(e){var t={maxRedirects:21,maxBodyLength:10485760},a={};return Object.keys(e).forEach(function(n){var r=n+":",s=a[r]=e[n],o=t[n]=Object.create(s);Object.defineProperties(o,{request:{value:function(e,n,s){var o;return(o=e,i&&o instanceof i)?e=E(e):O(e)?e=E(k(e)):(s=n,n=j(e),e={protocol:r}),T(n)&&(s=n,n=null),(n=Object.assign({maxRedirects:t.maxRedirects,maxBodyLength:t.maxBodyLength},e,n)).nativeProtocols=a,O(n.host)||O(n.hostname)||(n.hostname="::1"),c.equal(n.protocol,r,"protocol mismatch"),l("options",n),new y(n,s)},configurable:!0,enumerable:!0,writable:!0},get:{value:function(e,t,a){var n=o.request(e,t,a);return n.end(),n},configurable:!0,enumerable:!0,writable:!0}})}),t}function _(){}function k(e){var t;if(p)t=new i(e);else if(!O((t=j(n.parse(e))).protocol))throw new f({input:e});return t}function j(e){if(/^\[/.test(e.hostname)&&!/^\[[:0-9a-f]+\]$/i.test(e.hostname)||/^\[/.test(e.host)&&!/^\[[:0-9a-f]+\](:\d+)?$/i.test(e.host))throw new f({input:e.href||e});return e}function E(e,t){var a=t||{};for(var n of u)a[n]=e[n];return a.hostname.startsWith("[")&&(a.hostname=a.hostname.slice(1,-1)),""!==a.port&&(a.port=Number(a.port)),a.path=a.search?a.pathname+a.search:a.pathname,a}function C(e,t){var a;for(var n in t)e.test(n)&&(a=t[n],delete t[n]);return null==a?void 0:String(a).trim()}function R(e,t,a){function n(a){T(Error.captureStackTrace)&&Error.captureStackTrace(this,this.constructor),Object.assign(this,a||{}),this.code=e,this.message=this.cause?t+": "+this.cause.message:t}return n.prototype=new(a||Error),Object.defineProperties(n.prototype,{constructor:{value:n,enumerable:!1},name:{value:"Error ["+e+"]",enumerable:!1}}),n}function S(e,t){for(var a of d)e.removeListener(a,m[a]);e.on("error",_),e.destroy(t)}function O(e){return"string"==typeof e||e instanceof String}function T(e){return"function"==typeof e}y.prototype=Object.create(o.prototype),y.prototype.abort=function(){S(this._currentRequest),this._currentRequest.abort(),this.emit("abort")},y.prototype.destroy=function(e){return S(this._currentRequest,e),b.call(this,e),this},y.prototype.write=function(e,t,a){if(this._ending)throw new g;if(!O(e)&&!("object"==typeof e&&"length"in e))throw TypeError("data should be a string, Buffer or Uint8Array");if(T(t)&&(a=t,t=null),0===e.length){a&&a();return}this._requestBodyLength+e.length<=this._options.maxBodyLength?(this._requestBodyLength+=e.length,this._requestBodyBuffers.push({data:e,encoding:t}),this._currentRequest.write(e,t,a)):(this.emit("error",new v),this.abort())},y.prototype.end=function(e,t,a){if(T(e)?(a=e,e=t=null):T(t)&&(a=t,t=null),e){var n=this,i=this._currentRequest;this.write(e,t,function(){n._ended=!0,i.end(null,null,a)}),this._ending=!0}else this._ended=this._ending=!0,this._currentRequest.end(null,null,a)},y.prototype.setHeader=function(e,t){this._options.headers[e]=t,this._currentRequest.setHeader(e,t)},y.prototype.removeHeader=function(e){delete this._options.headers[e],this._currentRequest.removeHeader(e)},y.prototype.setTimeout=function(e,t){var a=this;function n(t){t.setTimeout(e),t.removeListener("timeout",t.destroy),t.addListener("timeout",t.destroy)}function i(t){a._timeout&&clearTimeout(a._timeout),a._timeout=setTimeout(function(){a.emit("timeout"),r()},e),n(t)}function r(){a._timeout&&(clearTimeout(a._timeout),a._timeout=null),a.removeListener("abort",r),a.removeListener("error",r),a.removeListener("response",r),a.removeListener("close",r),t&&a.removeListener("timeout",t),a.socket||a._currentRequest.removeListener("socket",i)}return t&&this.on("timeout",t),this.socket?i(this.socket):this._currentRequest.once("socket",i),this.on("socket",n),this.on("abort",r),this.on("error",r),this.on("response",r),this.on("close",r),this},["flushHeaders","getHeader","setNoDelay","setSocketKeepAlive"].forEach(function(e){y.prototype[e]=function(t,a){return this._currentRequest[e](t,a)}}),["aborted","connection","socket"].forEach(function(e){Object.defineProperty(y.prototype,e,{get:function(){return this._currentRequest[e]}})}),y.prototype._sanitizeOptions=function(e){if(e.headers||(e.headers={}),e.host&&(e.hostname||(e.hostname=e.host),delete e.host),!e.pathname&&e.path){var t=e.path.indexOf("?");t<0?e.pathname=e.path:(e.pathname=e.path.substring(0,t),e.search=e.path.substring(t))}},y.prototype._performRequest=function(){var e=this._options.protocol,t=this._options.nativeProtocols[e];if(!t)throw TypeError("Unsupported protocol "+e);if(this._options.agents){var a=e.slice(0,-1);this._options.agent=this._options.agents[a]}var i=this._currentRequest=t.request(this._options,this._onNativeResponse);for(var r of(i._redirectable=this,d))i.on(r,m[r]);if(this._currentUrl=/^\//.test(this._options.path)?n.format(this._options):this._options.path,this._isRedirect){var s=0,o=this,c=this._requestBodyBuffers;!function e(t){if(i===o._currentRequest){if(t)o.emit("error",t);else if(s<c.length){var a=c[s++];i.finished||i.write(a.data,a.encoding,e)}else o._ended&&i.end()}}()}},y.prototype._processResponse=function(e){var t,a,r,s=e.statusCode;this._options.trackRedirects&&this._redirects.push({url:this._currentUrl,headers:e.headers,statusCode:s});var o=e.headers.location;if(!o||!1===this._options.followRedirects||s<300||s>=400){e.responseUrl=this._currentUrl,e.redirects=this._redirects,this.emit("response",e),this._requestBodyBuffers=[];return}if(S(this._currentRequest),e.destroy(),++this._redirectCount>this._options.maxRedirects)throw new x;var u=this._options.beforeRedirect;u&&(r=Object.assign({Host:e.req.getHeader("host")},this._options.headers));var d=this._options.method;(301!==s&&302!==s||"POST"!==this._options.method)&&(303!==s||/^(?:GET|HEAD)$/.test(this._options.method))||(this._options.method="GET",this._requestBodyBuffers=[],C(/^content-/i,this._options.headers));var m=C(/^host$/i,this._options.headers),f=k(this._currentUrl),h=m||f.host,v=/^\w+:/.test(o)?this._currentUrl:n.format(Object.assign(f,{host:h})),g=p?new i(o,v):k(n.resolve(v,o));if(l("redirecting to",g.href),this._isRedirect=!0,E(g,this._options),(g.protocol===f.protocol||"https:"===g.protocol)&&(g.host===h||(c(O(t=g.host)&&O(h)),(a=t.length-h.length-1)>0&&"."===t[a]&&t.endsWith(h)))||C(/^(?:(?:proxy-)?authorization|cookie)$/i,this._options.headers),T(u)){var b={headers:e.headers,statusCode:s},y={url:v,method:d,headers:r};u(this._options,b,y),this._sanitizeOptions(this._options)}this._performRequest()},e.exports=w({http:r,https:s}),e.exports.wrap=w},20102:(e,t,a)=>{"use strict";var n=a(11252),i=a(73837),r=a(71017),s=a(13685),o=a(95687),c=a(57310).parse,l=a(57147),p=a(12781).Stream,u=a(63830),d=a(54656),m=a(33789);function f(e){if(!(this instanceof f))return new f(e);for(var t in this._overheadLength=0,this._valueLength=0,this._valuesToMeasure=[],n.call(this),e=e||{})this[t]=e[t]}e.exports=f,i.inherits(f,n),f.LINE_BREAK="\r\n",f.DEFAULT_CONTENT_TYPE="application/octet-stream",f.prototype.append=function(e,t,a){"string"==typeof(a=a||{})&&(a={filename:a});var i=n.prototype.append.bind(this);if("number"==typeof t&&(t=""+t),Array.isArray(t)){this._error(Error("Arrays are not supported."));return}var r=this._multiPartHeader(e,t,a),s=this._multiPartFooter();i(r),i(t),i(s),this._trackLength(r,t,a)},f.prototype._trackLength=function(e,t,a){var n=0;null!=a.knownLength?n+=+a.knownLength:Buffer.isBuffer(t)?n=t.length:"string"==typeof t&&(n=Buffer.byteLength(t)),this._valueLength+=n,this._overheadLength+=Buffer.byteLength(e)+f.LINE_BREAK.length,t&&(t.path||t.readable&&t.hasOwnProperty("httpVersion")||t instanceof p)&&(a.knownLength||this._valuesToMeasure.push(t))},f.prototype._lengthRetriever=function(e,t){e.hasOwnProperty("fd")?void 0!=e.end&&e.end!=1/0&&void 0!=e.start?t(null,e.end+1-(e.start?e.start:0)):l.stat(e.path,function(a,n){if(a){t(a);return}t(null,n.size-(e.start?e.start:0))}):e.hasOwnProperty("httpVersion")?t(null,+e.headers["content-length"]):e.hasOwnProperty("httpModule")?(e.on("response",function(a){e.pause(),t(null,+a.headers["content-length"])}),e.resume()):t("Unknown stream")},f.prototype._multiPartHeader=function(e,t,a){if("string"==typeof a.header)return a.header;var n,i=this._getContentDisposition(t,a),r=this._getContentType(t,a),s="",o={"Content-Disposition":["form-data",'name="'+e+'"'].concat(i||[]),"Content-Type":[].concat(r||[])};for(var c in"object"==typeof a.header&&m(o,a.header),o)if(o.hasOwnProperty(c)){if(null==(n=o[c]))continue;Array.isArray(n)||(n=[n]),n.length&&(s+=c+": "+n.join("; ")+f.LINE_BREAK)}return"--"+this.getBoundary()+f.LINE_BREAK+s+f.LINE_BREAK},f.prototype._getContentDisposition=function(e,t){var a,n;return"string"==typeof t.filepath?a=r.normalize(t.filepath).replace(/\\/g,"/"):t.filename||e.name||e.path?a=r.basename(t.filename||e.name||e.path):e.readable&&e.hasOwnProperty("httpVersion")&&(a=r.basename(e.client._httpMessage.path||"")),a&&(n='filename="'+a+'"'),n},f.prototype._getContentType=function(e,t){var a=t.contentType;return!a&&e.name&&(a=u.lookup(e.name)),!a&&e.path&&(a=u.lookup(e.path)),!a&&e.readable&&e.hasOwnProperty("httpVersion")&&(a=e.headers["content-type"]),!a&&(t.filepath||t.filename)&&(a=u.lookup(t.filepath||t.filename)),a||"object"!=typeof e||(a=f.DEFAULT_CONTENT_TYPE),a},f.prototype._multiPartFooter=function(){return(function(e){var t=f.LINE_BREAK;0===this._streams.length&&(t+=this._lastBoundary()),e(t)}).bind(this)},f.prototype._lastBoundary=function(){return"--"+this.getBoundary()+"--"+f.LINE_BREAK},f.prototype.getHeaders=function(e){var t,a={"content-type":"multipart/form-data; boundary="+this.getBoundary()};for(t in e)e.hasOwnProperty(t)&&(a[t.toLowerCase()]=e[t]);return a},f.prototype.setBoundary=function(e){this._boundary=e},f.prototype.getBoundary=function(){return this._boundary||this._generateBoundary(),this._boundary},f.prototype.getBuffer=function(){for(var e=new Buffer.alloc(0),t=this.getBoundary(),a=0,n=this._streams.length;a<n;a++)"function"!=typeof this._streams[a]&&(e=Buffer.isBuffer(this._streams[a])?Buffer.concat([e,this._streams[a]]):Buffer.concat([e,Buffer.from(this._streams[a])]),("string"!=typeof this._streams[a]||this._streams[a].substring(2,t.length+2)!==t)&&(e=Buffer.concat([e,Buffer.from(f.LINE_BREAK)])));return Buffer.concat([e,Buffer.from(this._lastBoundary())])},f.prototype._generateBoundary=function(){for(var e="--------------------------",t=0;t<24;t++)e+=Math.floor(10*Math.random()).toString(16);this._boundary=e},f.prototype.getLengthSync=function(){var e=this._overheadLength+this._valueLength;return this._streams.length&&(e+=this._lastBoundary().length),this.hasKnownLength()||this._error(Error("Cannot calculate proper length in synchronous way.")),e},f.prototype.hasKnownLength=function(){var e=!0;return this._valuesToMeasure.length&&(e=!1),e},f.prototype.getLength=function(e){var t=this._overheadLength+this._valueLength;if(this._streams.length&&(t+=this._lastBoundary().length),!this._valuesToMeasure.length){process.nextTick(e.bind(this,null,t));return}d.parallel(this._valuesToMeasure,this._lengthRetriever,function(a,n){if(a){e(a);return}n.forEach(function(e){t+=e}),e(null,t)})},f.prototype.submit=function(e,t){var a,n,i={method:"post"};return"string"==typeof e?n=m({port:(e=c(e)).port,path:e.pathname,host:e.hostname,protocol:e.protocol},i):(n=m(e,i)).port||(n.port="https:"==n.protocol?443:80),n.headers=this.getHeaders(e.headers),a="https:"==n.protocol?o.request(n):s.request(n),this.getLength((function(e,n){if(e&&"Unknown stream"!==e){this._error(e);return}if(n&&a.setHeader("Content-Length",n),this.pipe(a),t){var i,r=function(e,n){return a.removeListener("error",r),a.removeListener("response",i),t.call(this,e,n)};i=r.bind(this,null),a.on("error",r),a.on("response",i)}}).bind(this)),a},f.prototype._error=function(e){this.error||(this.error=e,this.pause(),this.emit("error",e))},f.prototype.toString=function(){return"[object FormData]"}},33789:e=>{"use strict";e.exports=function(e,t){return Object.keys(t).forEach(function(a){e[a]=e[a]||t[a]}),e}},70814:e=>{"use strict";e.exports=(e,t=process.argv)=>{let a=e.startsWith("-")?"":1===e.length?"-":"--",n=t.indexOf(a+e),i=t.indexOf("--");return -1!==n&&(-1===i||n<i)}},68818:(e,t,a)=>{"use strict";/*!
 * mime-db
 * Copyright(c) 2014 Jonathan Ong
 * Copyright(c) 2015-2022 Douglas Christopher Wilson
 * MIT Licensed
 */e.exports=a(40572)},63830:(e,t,a)=>{"use strict";/*!
 * mime-types
 * Copyright(c) 2014 Jonathan Ong
 * Copyright(c) 2015 Douglas Christopher Wilson
 * MIT Licensed
 */var n=a(68818),i=a(71017).extname,r=/^\s*([^;\s]*)(?:;|\s|$)/,s=/^text\//i;function o(e){if(!e||"string"!=typeof e)return!1;var t=r.exec(e),a=t&&n[t[1].toLowerCase()];return a&&a.charset?a.charset:!!(t&&s.test(t[1]))&&"UTF-8"}t.charset=o,t.charsets={lookup:o},t.contentType=function(e){if(!e||"string"!=typeof e)return!1;var a=-1===e.indexOf("/")?t.lookup(e):e;if(!a)return!1;if(-1===a.indexOf("charset")){var n=t.charset(a);n&&(a+="; charset="+n.toLowerCase())}return a},t.extension=function(e){if(!e||"string"!=typeof e)return!1;var a=r.exec(e),n=a&&t.extensions[a[1].toLowerCase()];return!!n&&!!n.length&&n[0]},t.extensions=Object.create(null),t.lookup=function(e){if(!e||"string"!=typeof e)return!1;var a=i("x."+e).toLowerCase().substr(1);return!!a&&(t.types[a]||!1)},t.types=Object.create(null),function(e,t){var a=["nginx","apache",void 0,"iana"];Object.keys(n).forEach(function(i){var r=n[i],s=r.extensions;if(s&&s.length){e[i]=s;for(var o=0;o<s.length;o++){var c=s[o];if(t[c]){var l=a.indexOf(n[t[c]].source),p=a.indexOf(r.source);if("application/octet-stream"!==t[c]&&(l>p||l===p&&"application/"===t[c].substr(0,12)))continue}t[c]=i}}})}(t.extensions,t.types)},58476:e=>{"use strict";function t(e,t,a,n){return Math.round(e/a)+" "+n+(t>=1.5*a?"s":"")}e.exports=function(e,a){a=a||{};var n,i,r=typeof e;if("string"===r&&e.length>0)return function(e){if(!((e=String(e)).length>100)){var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(t){var a=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*a;case"weeks":case"week":case"w":return 6048e5*a;case"days":case"day":case"d":return 864e5*a;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*a;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*a;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*a;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return a;default:return}}}}(e);if("number"===r&&isFinite(e))return a.long?(n=Math.abs(e))>=864e5?t(e,n,864e5,"day"):n>=36e5?t(e,n,36e5,"hour"):n>=6e4?t(e,n,6e4,"minute"):n>=1e3?t(e,n,1e3,"second"):e+" ms":(i=Math.abs(e))>=864e5?Math.round(e/864e5)+"d":i>=36e5?Math.round(e/36e5)+"h":i>=6e4?Math.round(e/6e4)+"m":i>=1e3?Math.round(e/1e3)+"s":e+"ms";throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},86886:(e,t,a)=>{"use strict";var n=a(57310).parse,i={ftp:21,gopher:70,http:80,https:443,ws:80,wss:443},r=String.prototype.endsWith||function(e){return e.length<=this.length&&-1!==this.indexOf(e,this.length-e.length)};function s(e){return process.env[e.toLowerCase()]||process.env[e.toUpperCase()]||""}t.j=function(e){var t,a,o,c="string"==typeof e?n(e):e||{},l=c.protocol,p=c.host,u=c.port;if("string"!=typeof p||!p||"string"!=typeof l||(l=l.split(":",1)[0],t=p=p.replace(/:\d*$/,""),a=u=parseInt(u)||i[l]||0,!(!(o=(s("npm_config_no_proxy")||s("no_proxy")).toLowerCase())||"*"!==o&&o.split(/[,\s]/).every(function(e){if(!e)return!0;var n=e.match(/^(.+):(\d+)$/),i=n?n[1]:e,s=n?parseInt(n[2]):0;return!!s&&s!==a||(/^[.*]/.test(i)?("*"===i.charAt(0)&&(i=i.slice(1)),!r.call(t,i)):t!==i)}))))return"";var d=s("npm_config_"+l+"_proxy")||s(l+"_proxy")||s("npm_config_proxy")||s("all_proxy");return d&&-1===d.indexOf("://")&&(d=l+"://"+d),d}},60125:(e,t,a)=>{"use strict";let n;let i=a(22037),r=a(76224),s=a(70814),{env:o}=process;function c(e){return 0!==e&&{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function l(e,t){if(0===n)return 0;if(s("color=16m")||s("color=full")||s("color=truecolor"))return 3;if(s("color=256"))return 2;if(e&&!t&&void 0===n)return 0;let a=n||0;if("dumb"===o.TERM)return a;if("win32"===process.platform){let e=i.release().split(".");return Number(e[0])>=10&&Number(e[2])>=10586?Number(e[2])>=14931?3:2:1}if("CI"in o)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(e=>e in o)||"codeship"===o.CI_NAME?1:a;if("TEAMCITY_VERSION"in o)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(o.TEAMCITY_VERSION)?1:0;if("truecolor"===o.COLORTERM)return 3;if("TERM_PROGRAM"in o){let e=parseInt((o.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(o.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(o.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(o.TERM)||"COLORTERM"in o?1:a}s("no-color")||s("no-colors")||s("color=false")||s("color=never")?n=0:(s("color")||s("colors")||s("color=true")||s("color=always"))&&(n=1),"FORCE_COLOR"in o&&(n="true"===o.FORCE_COLOR?1:"false"===o.FORCE_COLOR?0:0===o.FORCE_COLOR.length?1:Math.min(parseInt(o.FORCE_COLOR,10),3)),e.exports={supportsColor:function(e){return c(l(e,e&&e.isTTY))},stdout:c(l(!0,r.isatty(1))),stderr:c(l(!0,r.isatty(2)))}},4213:(e,t,a)=>{"use strict";let n,i;a.r(t),a.d(t,{TelegramGroupForm:()=>s4});var r,s,o,c,l,p,u,d,m={};a.r(m),a.d(m,{hasBrowserEnv:()=>ij,hasStandardBrowserEnv:()=>iE,hasStandardBrowserWebWorkerEnv:()=>iC});var f=a(95344),h=a(3729),x=a.t(h,2);function v(){return(v=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)({}).hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e}).apply(null,arguments)}function g(...e){return t=>e.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})}function b(...e){return(0,h.useCallback)(g(...e),e)}let y=(0,h.forwardRef)((e,t)=>{let{children:a,...n}=e,i=h.Children.toArray(a),r=i.find(k);if(r){let e=r.props.children,a=i.map(t=>t!==r?t:h.Children.count(e)>1?h.Children.only(null):(0,h.isValidElement)(e)?e.props.children:null);return(0,h.createElement)(w,v({},n,{ref:t}),(0,h.isValidElement)(e)?(0,h.cloneElement)(e,void 0,a):null)}return(0,h.createElement)(w,v({},n,{ref:t}),a)});y.displayName="Slot";let w=(0,h.forwardRef)((e,t)=>{let{children:a,...n}=e;return(0,h.isValidElement)(a)?(0,h.cloneElement)(a,{...function(e,t){let a={...t};for(let n in t){let i=e[n],r=t[n];/^on[A-Z]/.test(n)?i&&r?a[n]=(...e)=>{r(...e),i(...e)}:i&&(a[n]=i):"style"===n?a[n]={...i,...r}:"className"===n&&(a[n]=[i,r].filter(Boolean).join(" "))}return{...e,...a}}(n,a.props),ref:t?g(t,a.ref):a.ref}):h.Children.count(a)>1?h.Children.only(null):null});w.displayName="SlotClone";let _=({children:e})=>(0,h.createElement)(h.Fragment,null,e);function k(e){return(0,h.isValidElement)(e)&&e.type===_}let j=e=>"boolean"==typeof e?"".concat(e):0===e?"0":e,E=function(){for(var e,t,a=0,n="";a<arguments.length;)(e=arguments[a++])&&(t=function e(t){var a,n,i="";if("string"==typeof t||"number"==typeof t)i+=t;else if("object"==typeof t){if(Array.isArray(t))for(a=0;a<t.length;a++)t[a]&&(n=e(t[a]))&&(i&&(i+=" "),i+=n);else for(a in t)t[a]&&(i&&(i+=" "),i+=a)}return i}(e))&&(n&&(n+=" "),n+=t);return n},C=(e,t)=>a=>{var n;if((null==t?void 0:t.variants)==null)return E(e,null==a?void 0:a.class,null==a?void 0:a.className);let{variants:i,defaultVariants:r}=t,s=Object.keys(i).map(e=>{let t=null==a?void 0:a[e],n=null==r?void 0:r[e];if(null===t)return null;let s=j(t)||j(n);return i[e][s]}),o=a&&Object.entries(a).reduce((e,t)=>{let[a,n]=t;return void 0===n||(e[a]=n),e},{});return E(e,s,null==t?void 0:null===(n=t.compoundVariants)||void 0===n?void 0:n.reduce((e,t)=>{let{class:a,className:n,...i}=t;return Object.entries(i).every(e=>{let[t,a]=e;return Array.isArray(a)?a.includes({...r,...o}[t]):({...r,...o})[t]===a})?[...e,a,n]:e},[]),null==a?void 0:a.class,null==a?void 0:a.className)},R=/^\[(.+)\]$/;function S(e,t){let a=e;return t.split("-").forEach(e=>{a.nextPart.has(e)||a.nextPart.set(e,{nextPart:new Map,validators:[]}),a=a.nextPart.get(e)}),a}let O=/\s+/;function T(){let e,t,a=0,n="";for(;a<arguments.length;)(e=arguments[a++])&&(t=function e(t){let a;if("string"==typeof t)return t;let n="";for(let i=0;i<t.length;i++)t[i]&&(a=e(t[i]))&&(n&&(n+=" "),n+=a);return n}(e))&&(n&&(n+=" "),n+=t);return n}function N(e){let t=t=>t[e]||[];return t.isThemeGetter=!0,t}let A=/^\[(?:([a-z-]+):)?(.+)\]$/i,P=/^\d+\/\d+$/,L=new Set(["px","full","screen"]),U=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,M=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,z=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,F=/^-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,D=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/;function I(e){return B(e)||L.has(e)||P.test(e)}function q(e){return et(e,"length",ea)}function B(e){return!!e&&!Number.isNaN(Number(e))}function $(e){return et(e,"number",B)}function Z(e){return!!e&&Number.isInteger(Number(e))}function W(e){return e.endsWith("%")&&B(e.slice(0,-1))}function H(e){return A.test(e)}function V(e){return U.test(e)}let K=new Set(["length","size","percentage"]);function G(e){return et(e,K,en)}function X(e){return et(e,"position",en)}let Y=new Set(["image","url"]);function J(e){return et(e,Y,er)}function Q(e){return et(e,"",ei)}function ee(){return!0}function et(e,t,a){let n=A.exec(e);return!!n&&(n[1]?"string"==typeof t?n[1]===t:t.has(n[1]):a(n[2]))}function ea(e){return M.test(e)&&!z.test(e)}function en(){return!1}function ei(e){return F.test(e)}function er(e){return D.test(e)}Symbol.toStringTag;let es=function(e){let t,a,n;let i=function(s){var o;return a=(t={cache:function(e){if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,a=new Map,n=new Map;function i(i,r){a.set(i,r),++t>e&&(t=0,n=a,a=new Map)}return{get(e){let t=a.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(i(e,t),t):void 0},set(e,t){a.has(e)?a.set(e,t):i(e,t)}}}((o=[].reduce((e,t)=>t(e),e())).cacheSize),splitModifiers:function(e){let t=e.separator,a=1===t.length,n=t[0],i=t.length;return function(e){let r;let s=[],o=0,c=0;for(let l=0;l<e.length;l++){let p=e[l];if(0===o){if(p===n&&(a||e.slice(l,l+i)===t)){s.push(e.slice(c,l)),c=l+i;continue}if("/"===p){r=l;continue}}"["===p?o++:"]"===p&&o--}let l=0===s.length?e:e.substring(c),p=l.startsWith("!"),u=p?l.substring(1):l;return{modifiers:s,hasImportantModifier:p,baseClassName:u,maybePostfixModifierPosition:r&&r>c?r-c:void 0}}}(o),...function(e){let t=function(e){var t;let{theme:a,prefix:n}=e,i={nextPart:new Map,validators:[]};return(t=Object.entries(e.classGroups),n?t.map(([e,t])=>[e,t.map(e=>"string"==typeof e?n+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,t])=>[n+e,t])):e)]):t).forEach(([e,t])=>{(function e(t,a,n,i){t.forEach(t=>{if("string"==typeof t){(""===t?a:S(a,t)).classGroupId=n;return}if("function"==typeof t){if(t.isThemeGetter){e(t(i),a,n,i);return}a.validators.push({validator:t,classGroupId:n});return}Object.entries(t).forEach(([t,r])=>{e(r,S(a,t),n,i)})})})(t,i,e,a)}),i}(e),{conflictingClassGroups:a,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:function(e){let a=e.split("-");return""===a[0]&&1!==a.length&&a.shift(),function e(t,a){if(0===t.length)return a.classGroupId;let n=t[0],i=a.nextPart.get(n),r=i?e(t.slice(1),i):void 0;if(r)return r;if(0===a.validators.length)return;let s=t.join("-");return a.validators.find(({validator:e})=>e(s))?.classGroupId}(a,t)||function(e){if(R.test(e)){let t=R.exec(e)[1],a=t?.substring(0,t.indexOf(":"));if(a)return"arbitrary.."+a}}(e)},getConflictingClassGroupIds:function(e,t){let i=a[e]||[];return t&&n[e]?[...i,...n[e]]:i}}}(o)}).cache.get,n=t.cache.set,i=r,r(s)};function r(e){let i=a(e);if(i)return i;let r=function(e,t){let{splitModifiers:a,getClassGroupId:n,getConflictingClassGroupIds:i}=t,r=new Set;return e.trim().split(O).map(e=>{let{modifiers:t,hasImportantModifier:i,baseClassName:r,maybePostfixModifierPosition:s}=a(e),o=n(s?r.substring(0,s):r),c=!!s;if(!o){if(!s||!(o=n(r)))return{isTailwindClass:!1,originalClassName:e};c=!1}let l=(function(e){if(e.length<=1)return e;let t=[],a=[];return e.forEach(e=>{"["===e[0]?(t.push(...a.sort(),e),a=[]):a.push(e)}),t.push(...a.sort()),t})(t).join(":");return{isTailwindClass:!0,modifierId:i?l+"!":l,classGroupId:o,originalClassName:e,hasPostfixModifier:c}}).reverse().filter(e=>{if(!e.isTailwindClass)return!0;let{modifierId:t,classGroupId:a,hasPostfixModifier:n}=e,s=t+a;return!r.has(s)&&(r.add(s),i(a,n).forEach(e=>r.add(t+e)),!0)}).reverse().map(e=>e.originalClassName).join(" ")}(e,t);return n(e,r),r}return function(){return i(T.apply(null,arguments))}}(function(){let e=N("colors"),t=N("spacing"),a=N("blur"),n=N("brightness"),i=N("borderColor"),r=N("borderRadius"),s=N("borderSpacing"),o=N("borderWidth"),c=N("contrast"),l=N("grayscale"),p=N("hueRotate"),u=N("invert"),d=N("gap"),m=N("gradientColorStops"),f=N("gradientColorStopPositions"),h=N("inset"),x=N("margin"),v=N("opacity"),g=N("padding"),b=N("saturate"),y=N("scale"),w=N("sepia"),_=N("skew"),k=N("space"),j=N("translate"),E=()=>["auto","contain","none"],C=()=>["auto","hidden","clip","visible","scroll"],R=()=>["auto",H,t],S=()=>[H,t],O=()=>["",I,q],T=()=>["auto",B,H],A=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],P=()=>["solid","dashed","dotted","double","none"],L=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter"],U=()=>["start","end","center","between","around","evenly","stretch"],M=()=>["","0",H],z=()=>["auto","avoid","all","avoid-page","page","left","right","column"],F=()=>[B,$],D=()=>[B,H];return{cacheSize:500,separator:":",theme:{colors:[ee],spacing:[I,q],blur:["none","",V,H],brightness:F(),borderColor:[e],borderRadius:["none","","full",V,H],borderSpacing:S(),borderWidth:O(),contrast:F(),grayscale:M(),hueRotate:D(),invert:M(),gap:S(),gradientColorStops:[e],gradientColorStopPositions:[W,q],inset:R(),margin:R(),opacity:F(),padding:S(),saturate:F(),scale:F(),sepia:M(),skew:D(),space:S(),translate:S()},classGroups:{aspect:[{aspect:["auto","square","video",H]}],container:["container"],columns:[{columns:[V]}],"break-after":[{"break-after":z()}],"break-before":[{"break-before":z()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...A(),H]}],overflow:[{overflow:C()}],"overflow-x":[{"overflow-x":C()}],"overflow-y":[{"overflow-y":C()}],overscroll:[{overscroll:E()}],"overscroll-x":[{"overscroll-x":E()}],"overscroll-y":[{"overscroll-y":E()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[h]}],"inset-x":[{"inset-x":[h]}],"inset-y":[{"inset-y":[h]}],start:[{start:[h]}],end:[{end:[h]}],top:[{top:[h]}],right:[{right:[h]}],bottom:[{bottom:[h]}],left:[{left:[h]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Z,H]}],basis:[{basis:R()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",H]}],grow:[{grow:M()}],shrink:[{shrink:M()}],order:[{order:["first","last","none",Z,H]}],"grid-cols":[{"grid-cols":[ee]}],"col-start-end":[{col:["auto",{span:["full",Z,H]},H]}],"col-start":[{"col-start":T()}],"col-end":[{"col-end":T()}],"grid-rows":[{"grid-rows":[ee]}],"row-start-end":[{row:["auto",{span:[Z,H]},H]}],"row-start":[{"row-start":T()}],"row-end":[{"row-end":T()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",H]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",H]}],gap:[{gap:[d]}],"gap-x":[{"gap-x":[d]}],"gap-y":[{"gap-y":[d]}],"justify-content":[{justify:["normal",...U()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...U(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...U(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[g]}],px:[{px:[g]}],py:[{py:[g]}],ps:[{ps:[g]}],pe:[{pe:[g]}],pt:[{pt:[g]}],pr:[{pr:[g]}],pb:[{pb:[g]}],pl:[{pl:[g]}],m:[{m:[x]}],mx:[{mx:[x]}],my:[{my:[x]}],ms:[{ms:[x]}],me:[{me:[x]}],mt:[{mt:[x]}],mr:[{mr:[x]}],mb:[{mb:[x]}],ml:[{ml:[x]}],"space-x":[{"space-x":[k]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[k]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",H,t]}],"min-w":[{"min-w":[H,t,"min","max","fit"]}],"max-w":[{"max-w":[H,t,"none","full","min","max","fit","prose",{screen:[V]},V]}],h:[{h:[H,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[H,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[H,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[H,t,"auto","min","max","fit"]}],"font-size":[{text:["base",V,q]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",$]}],"font-family":[{font:[ee]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",H]}],"line-clamp":[{"line-clamp":["none",B,$]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",I,H]}],"list-image":[{"list-image":["none",H]}],"list-style-type":[{list:["none","disc","decimal",H]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[v]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[v]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...P(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",I,q]}],"underline-offset":[{"underline-offset":["auto",I,H]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:S()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",H]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",H]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[v]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...A(),X]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",G]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},J]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[f]}],"gradient-via-pos":[{via:[f]}],"gradient-to-pos":[{to:[f]}],"gradient-from":[{from:[m]}],"gradient-via":[{via:[m]}],"gradient-to":[{to:[m]}],rounded:[{rounded:[r]}],"rounded-s":[{"rounded-s":[r]}],"rounded-e":[{"rounded-e":[r]}],"rounded-t":[{"rounded-t":[r]}],"rounded-r":[{"rounded-r":[r]}],"rounded-b":[{"rounded-b":[r]}],"rounded-l":[{"rounded-l":[r]}],"rounded-ss":[{"rounded-ss":[r]}],"rounded-se":[{"rounded-se":[r]}],"rounded-ee":[{"rounded-ee":[r]}],"rounded-es":[{"rounded-es":[r]}],"rounded-tl":[{"rounded-tl":[r]}],"rounded-tr":[{"rounded-tr":[r]}],"rounded-br":[{"rounded-br":[r]}],"rounded-bl":[{"rounded-bl":[r]}],"border-w":[{border:[o]}],"border-w-x":[{"border-x":[o]}],"border-w-y":[{"border-y":[o]}],"border-w-s":[{"border-s":[o]}],"border-w-e":[{"border-e":[o]}],"border-w-t":[{"border-t":[o]}],"border-w-r":[{"border-r":[o]}],"border-w-b":[{"border-b":[o]}],"border-w-l":[{"border-l":[o]}],"border-opacity":[{"border-opacity":[v]}],"border-style":[{border:[...P(),"hidden"]}],"divide-x":[{"divide-x":[o]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[o]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[v]}],"divide-style":[{divide:P()}],"border-color":[{border:[i]}],"border-color-x":[{"border-x":[i]}],"border-color-y":[{"border-y":[i]}],"border-color-t":[{"border-t":[i]}],"border-color-r":[{"border-r":[i]}],"border-color-b":[{"border-b":[i]}],"border-color-l":[{"border-l":[i]}],"divide-color":[{divide:[i]}],"outline-style":[{outline:["",...P()]}],"outline-offset":[{"outline-offset":[I,H]}],"outline-w":[{outline:[I,q]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:O()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[v]}],"ring-offset-w":[{"ring-offset":[I,q]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",V,Q]}],"shadow-color":[{shadow:[ee]}],opacity:[{opacity:[v]}],"mix-blend":[{"mix-blend":L()}],"bg-blend":[{"bg-blend":L()}],filter:[{filter:["","none"]}],blur:[{blur:[a]}],brightness:[{brightness:[n]}],contrast:[{contrast:[c]}],"drop-shadow":[{"drop-shadow":["","none",V,H]}],grayscale:[{grayscale:[l]}],"hue-rotate":[{"hue-rotate":[p]}],invert:[{invert:[u]}],saturate:[{saturate:[b]}],sepia:[{sepia:[w]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[a]}],"backdrop-brightness":[{"backdrop-brightness":[n]}],"backdrop-contrast":[{"backdrop-contrast":[c]}],"backdrop-grayscale":[{"backdrop-grayscale":[l]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[p]}],"backdrop-invert":[{"backdrop-invert":[u]}],"backdrop-opacity":[{"backdrop-opacity":[v]}],"backdrop-saturate":[{"backdrop-saturate":[b]}],"backdrop-sepia":[{"backdrop-sepia":[w]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[s]}],"border-spacing-x":[{"border-spacing-x":[s]}],"border-spacing-y":[{"border-spacing-y":[s]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",H]}],duration:[{duration:D()}],ease:[{ease:["linear","in","out","in-out",H]}],delay:[{delay:D()}],animate:[{animate:["none","spin","ping","pulse","bounce",H]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[y]}],"scale-x":[{"scale-x":[y]}],"scale-y":[{"scale-y":[y]}],rotate:[{rotate:[Z,H]}],"translate-x":[{"translate-x":[j]}],"translate-y":[{"translate-y":[j]}],"skew-x":[{"skew-x":[_]}],"skew-y":[{"skew-y":[_]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",H]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",H]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":S()}],"scroll-mx":[{"scroll-mx":S()}],"scroll-my":[{"scroll-my":S()}],"scroll-ms":[{"scroll-ms":S()}],"scroll-me":[{"scroll-me":S()}],"scroll-mt":[{"scroll-mt":S()}],"scroll-mr":[{"scroll-mr":S()}],"scroll-mb":[{"scroll-mb":S()}],"scroll-ml":[{"scroll-ml":S()}],"scroll-p":[{"scroll-p":S()}],"scroll-px":[{"scroll-px":S()}],"scroll-py":[{"scroll-py":S()}],"scroll-ps":[{"scroll-ps":S()}],"scroll-pe":[{"scroll-pe":S()}],"scroll-pt":[{"scroll-pt":S()}],"scroll-pr":[{"scroll-pr":S()}],"scroll-pb":[{"scroll-pb":S()}],"scroll-pl":[{"scroll-pl":S()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",H]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[I,q,$]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}});function eo(...e){return es(function(){for(var e,t,a=0,n="",i=arguments.length;a<i;a++)(e=arguments[a])&&(t=function e(t){var a,n,i="";if("string"==typeof t||"number"==typeof t)i+=t;else if("object"==typeof t){if(Array.isArray(t)){var r=t.length;for(a=0;a<r;a++)t[a]&&(n=e(t[a]))&&(i&&(i+=" "),i+=n)}else for(n in t)t[n]&&(i&&(i+=" "),i+=n)}return i}(e))&&(n&&(n+=" "),n+=t);return n}(e))}let ec=C("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),el=h.forwardRef(({className:e,variant:t,size:a,asChild:n=!1,...i},r)=>{let s=n?y:"button";return f.jsx(s,{className:eo(ec({variant:t,size:a,className:e})),ref:r,...i})});el.displayName="Button";let ep=h.forwardRef(({className:e,...t},a)=>f.jsx("div",{ref:a,className:eo("rounded-xl border bg-card text-card-foreground shadow",e),...t}));ep.displayName="Card";let eu=h.forwardRef(({className:e,...t},a)=>f.jsx("div",{ref:a,className:eo("flex flex-col space-y-1.5 p-6",e),...t}));eu.displayName="CardHeader";let ed=h.forwardRef(({className:e,...t},a)=>f.jsx("div",{ref:a,className:eo("font-semibold leading-none tracking-tight",e),...t}));ed.displayName="CardTitle",h.forwardRef(({className:e,...t},a)=>f.jsx("div",{ref:a,className:eo("text-sm text-muted-foreground",e),...t})).displayName="CardDescription";let em=h.forwardRef(({className:e,...t},a)=>f.jsx("div",{ref:a,className:eo("p-6 pt-0",e),...t}));em.displayName="CardContent",h.forwardRef(({className:e,...t},a)=>f.jsx("div",{ref:a,className:eo("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter";let ef=h.forwardRef(({className:e,type:t,...a},n)=>f.jsx("input",{type:t,className:eo("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:n,...a}));ef.displayName="Input";var eh=a(81202);let ex=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let a=(0,h.forwardRef)((e,a)=>{let{asChild:n,...i}=e,r=n?y:t;return(0,h.useEffect)(()=>{window[Symbol.for("radix-ui")]=!0},[]),(0,h.createElement)(r,v({},i,{ref:a}))});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{}),ev=(0,h.forwardRef)((e,t)=>(0,h.createElement)(ex.label,v({},e,{ref:t,onMouseDown:t=>{var a;null===(a=e.onMouseDown)||void 0===a||a.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault()}}))),eg=C("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),eb=h.forwardRef(({className:e,...t},a)=>f.jsx(ev,{ref:a,className:eo(eg(),e),...t}));function ey(e,[t,a]){return Math.min(a,Math.max(t,e))}function ew(e,t,{checkForDefaultPrevented:a=!0}={}){return function(n){if(null==e||e(n),!1===a||!n.defaultPrevented)return null==t?void 0:t(n)}}function e_(e,t=[]){let a=[],n=()=>{let t=a.map(e=>(0,h.createContext)(e));return function(a){let n=(null==a?void 0:a[e])||t;return(0,h.useMemo)(()=>({[`__scope${e}`]:{...a,[e]:n}}),[a,n])}};return n.scopeName=e,[function(t,n){let i=(0,h.createContext)(n),r=a.length;function s(t){let{scope:a,children:n,...s}=t,o=(null==a?void 0:a[e][r])||i,c=(0,h.useMemo)(()=>s,Object.values(s));return(0,h.createElement)(o.Provider,{value:c},n)}return a=[...a,n],s.displayName=t+"Provider",[s,function(a,s){let o=(null==s?void 0:s[e][r])||i,c=(0,h.useContext)(o);if(c)return c;if(void 0!==n)return n;throw Error(`\`${a}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let a=()=>{let a=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=a.reduce((t,{useScope:a,scopeName:n})=>{let i=a(e)[`__scope${n}`];return{...t,...i}},{});return(0,h.useMemo)(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return a.scopeName=t.scopeName,a}(n,...t)]}eb.displayName=ev.displayName;let ek=(0,h.createContext)(void 0);function ej(e){let t=(0,h.useRef)(e);return(0,h.useEffect)(()=>{t.current=e}),(0,h.useMemo)(()=>(...e)=>{var a;return null===(a=t.current)||void 0===a?void 0:a.call(t,...e)},[])}let eE="dismissableLayer.update",eC=(0,h.createContext)({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),eR=(0,h.forwardRef)((e,t)=>{var a;let{disableOutsidePointerEvents:i=!1,onEscapeKeyDown:r,onPointerDownOutside:s,onFocusOutside:o,onInteractOutside:c,onDismiss:l,...p}=e,u=(0,h.useContext)(eC),[d,m]=(0,h.useState)(null),f=null!==(a=null==d?void 0:d.ownerDocument)&&void 0!==a?a:null==globalThis?void 0:globalThis.document,[,x]=(0,h.useState)({}),g=b(t,e=>m(e)),y=Array.from(u.layers),[w]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),_=y.indexOf(w),k=d?y.indexOf(d):-1,j=u.layersWithOutsidePointerEventsDisabled.size>0,E=k>=_,C=function(e,t=null==globalThis?void 0:globalThis.document){let a=ej(e),n=(0,h.useRef)(!1),i=(0,h.useRef)(()=>{});return(0,h.useEffect)(()=>{let e=e=>{if(e.target&&!n.current){let n={originalEvent:e};function r(){eO("dismissableLayer.pointerDownOutside",a,n,{discrete:!0})}"touch"===e.pointerType?(t.removeEventListener("click",i.current),i.current=r,t.addEventListener("click",i.current,{once:!0})):r()}else t.removeEventListener("click",i.current);n.current=!1},r=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(r),t.removeEventListener("pointerdown",e),t.removeEventListener("click",i.current)}},[t,a]),{onPointerDownCapture:()=>n.current=!0}}(e=>{let t=e.target,a=[...u.branches].some(e=>e.contains(t));!E||a||(null==s||s(e),null==c||c(e),e.defaultPrevented||null==l||l())},f),R=function(e,t=null==globalThis?void 0:globalThis.document){let a=ej(e),n=(0,h.useRef)(!1);return(0,h.useEffect)(()=>{let e=e=>{e.target&&!n.current&&eO("dismissableLayer.focusOutside",a,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,a]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}(e=>{let t=e.target;[...u.branches].some(e=>e.contains(t))||(null==o||o(e),null==c||c(e),e.defaultPrevented||null==l||l())},f);return function(e,t=null==globalThis?void 0:globalThis.document){let a=ej(e);(0,h.useEffect)(()=>{let e=e=>{"Escape"===e.key&&a(e)};return t.addEventListener("keydown",e),()=>t.removeEventListener("keydown",e)},[a,t])}(e=>{k!==u.layers.size-1||(null==r||r(e),!e.defaultPrevented&&l&&(e.preventDefault(),l()))},f),(0,h.useEffect)(()=>{if(d)return i&&(0===u.layersWithOutsidePointerEventsDisabled.size&&(n=f.body.style.pointerEvents,f.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(d)),u.layers.add(d),eS(),()=>{i&&1===u.layersWithOutsidePointerEventsDisabled.size&&(f.body.style.pointerEvents=n)}},[d,f,i,u]),(0,h.useEffect)(()=>()=>{d&&(u.layers.delete(d),u.layersWithOutsidePointerEventsDisabled.delete(d),eS())},[d,u]),(0,h.useEffect)(()=>{let e=()=>x({});return document.addEventListener(eE,e),()=>document.removeEventListener(eE,e)},[]),(0,h.createElement)(ex.div,v({},p,{ref:g,style:{pointerEvents:j?E?"auto":"none":void 0,...e.style},onFocusCapture:ew(e.onFocusCapture,R.onFocusCapture),onBlurCapture:ew(e.onBlurCapture,R.onBlurCapture),onPointerDownCapture:ew(e.onPointerDownCapture,C.onPointerDownCapture)}))});function eS(){let e=new CustomEvent(eE);document.dispatchEvent(e)}function eO(e,t,a,{discrete:n}){let i=a.originalEvent.target,r=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:a});(t&&i.addEventListener(e,t,{once:!0}),n)?i&&(0,eh.flushSync)(()=>i.dispatchEvent(r)):i.dispatchEvent(r)}let eT=0;function eN(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.cssText="outline: none; opacity: 0; position: fixed; pointer-events: none",e}let eA="focusScope.autoFocusOnMount",eP="focusScope.autoFocusOnUnmount",eL={bubbles:!1,cancelable:!0},eU=(0,h.forwardRef)((e,t)=>{let{loop:a=!1,trapped:n=!1,onMountAutoFocus:i,onUnmountAutoFocus:r,...s}=e,[o,c]=(0,h.useState)(null),l=ej(i),p=ej(r),u=(0,h.useRef)(null),d=b(t,e=>c(e)),m=(0,h.useRef)({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;(0,h.useEffect)(()=>{if(n){function e(e){if(m.paused||!o)return;let t=e.target;o.contains(t)?u.current=t:eF(u.current,{select:!0})}function t(e){if(m.paused||!o)return;let t=e.relatedTarget;null===t||o.contains(t)||eF(u.current,{select:!0})}document.addEventListener("focusin",e),document.addEventListener("focusout",t);let a=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&eF(o)});return o&&a.observe(o,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),a.disconnect()}}},[n,o,m.paused]),(0,h.useEffect)(()=>{if(o){eD.add(m);let e=document.activeElement;if(!o.contains(e)){let t=new CustomEvent(eA,eL);o.addEventListener(eA,l),o.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let a=document.activeElement;for(let n of e)if(eF(n,{select:t}),document.activeElement!==a)return}(eM(o).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&eF(o))}return()=>{o.removeEventListener(eA,l),setTimeout(()=>{let t=new CustomEvent(eP,eL);o.addEventListener(eP,p),o.dispatchEvent(t),t.defaultPrevented||eF(null!=e?e:document.body,{select:!0}),o.removeEventListener(eP,p),eD.remove(m)},0)}}},[o,l,p,m]);let f=(0,h.useCallback)(e=>{if(!a&&!n||m.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,i=document.activeElement;if(t&&i){let t=e.currentTarget,[n,r]=function(e){let t=eM(e);return[ez(t,e),ez(t.reverse(),e)]}(t);n&&r?e.shiftKey||i!==r?e.shiftKey&&i===n&&(e.preventDefault(),a&&eF(r,{select:!0})):(e.preventDefault(),a&&eF(n,{select:!0})):i===t&&e.preventDefault()}},[a,n,m.paused]);return(0,h.createElement)(ex.div,v({tabIndex:-1},s,{ref:d,onKeyDown:f}))});function eM(e){let t=[],a=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;a.nextNode();)t.push(a.currentNode);return t}function ez(e,t){for(let a of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(a,{upTo:t}))return a}function eF(e,{select:t=!1}={}){if(e&&e.focus){var a;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(a=e)instanceof HTMLInputElement&&"select"in a&&t&&e.select()}}let eD=function(){let e=[];return{add(t){let a=e[0];t!==a&&(null==a||a.pause()),(e=eI(e,t)).unshift(t)},remove(t){var a;null===(a=(e=eI(e,t))[0])||void 0===a||a.resume()}}}();function eI(e,t){let a=[...e],n=a.indexOf(t);return -1!==n&&a.splice(n,1),a}let eq=(null==globalThis?void 0:globalThis.document)?h.useLayoutEffect:()=>{},eB=x["useId".toString()]||(()=>void 0),e$=0;function eZ(e){let[t,a]=h.useState(eB());return eq(()=>{e||a(e=>null!=e?e:String(e$++))},[e]),e||(t?`radix-${t}`:"")}let eW=["top","right","bottom","left"],eH=Math.min,eV=Math.max,eK=Math.round,eG=Math.floor,eX=e=>({x:e,y:e}),eY={left:"right",right:"left",bottom:"top",top:"bottom"},eJ={start:"end",end:"start"};function eQ(e,t){return"function"==typeof e?e(t):e}function e0(e){return e.split("-")[0]}function e1(e){return e.split("-")[1]}function e2(e){return"x"===e?"y":"x"}function e3(e){return"y"===e?"height":"width"}function e4(e){return["top","bottom"].includes(e0(e))?"y":"x"}function e6(e){return e.replace(/start|end/g,e=>eJ[e])}function e9(e){return e.replace(/left|right|bottom|top/g,e=>eY[e])}function e8(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function e5(e){let{x:t,y:a,width:n,height:i}=e;return{width:n,height:i,top:a,left:t,right:t+n,bottom:a+i,x:t,y:a}}function e7(e,t,a){let n,{reference:i,floating:r}=e,s=e4(t),o=e2(e4(t)),c=e3(o),l=e0(t),p="y"===s,u=i.x+i.width/2-r.width/2,d=i.y+i.height/2-r.height/2,m=i[c]/2-r[c]/2;switch(l){case"top":n={x:u,y:i.y-r.height};break;case"bottom":n={x:u,y:i.y+i.height};break;case"right":n={x:i.x+i.width,y:d};break;case"left":n={x:i.x-r.width,y:d};break;default:n={x:i.x,y:i.y}}switch(e1(t)){case"start":n[o]-=m*(a&&p?-1:1);break;case"end":n[o]+=m*(a&&p?-1:1)}return n}let te=async(e,t,a)=>{let{placement:n="bottom",strategy:i="absolute",middleware:r=[],platform:s}=a,o=r.filter(Boolean),c=await (null==s.isRTL?void 0:s.isRTL(t)),l=await s.getElementRects({reference:e,floating:t,strategy:i}),{x:p,y:u}=e7(l,n,c),d=n,m={},f=0;for(let a=0;a<o.length;a++){let{name:r,fn:h}=o[a],{x:x,y:v,data:g,reset:b}=await h({x:p,y:u,initialPlacement:n,placement:d,strategy:i,middlewareData:m,rects:l,platform:s,elements:{reference:e,floating:t}});p=null!=x?x:p,u=null!=v?v:u,m={...m,[r]:{...m[r],...g}},b&&f<=50&&(f++,"object"==typeof b&&(b.placement&&(d=b.placement),b.rects&&(l=!0===b.rects?await s.getElementRects({reference:e,floating:t,strategy:i}):b.rects),{x:p,y:u}=e7(l,d,c)),a=-1)}return{x:p,y:u,placement:d,strategy:i,middlewareData:m}};async function tt(e,t){var a;void 0===t&&(t={});let{x:n,y:i,platform:r,rects:s,elements:o,strategy:c}=e,{boundary:l="clippingAncestors",rootBoundary:p="viewport",elementContext:u="floating",altBoundary:d=!1,padding:m=0}=eQ(t,e),f=e8(m),h=o[d?"floating"===u?"reference":"floating":u],x=e5(await r.getClippingRect({element:null==(a=await (null==r.isElement?void 0:r.isElement(h)))||a?h:h.contextElement||await (null==r.getDocumentElement?void 0:r.getDocumentElement(o.floating)),boundary:l,rootBoundary:p,strategy:c})),v="floating"===u?{x:n,y:i,width:s.floating.width,height:s.floating.height}:s.reference,g=await (null==r.getOffsetParent?void 0:r.getOffsetParent(o.floating)),b=await (null==r.isElement?void 0:r.isElement(g))&&await (null==r.getScale?void 0:r.getScale(g))||{x:1,y:1},y=e5(r.convertOffsetParentRelativeRectToViewportRelativeRect?await r.convertOffsetParentRelativeRectToViewportRelativeRect({elements:o,rect:v,offsetParent:g,strategy:c}):v);return{top:(x.top-y.top+f.top)/b.y,bottom:(y.bottom-x.bottom+f.bottom)/b.y,left:(x.left-y.left+f.left)/b.x,right:(y.right-x.right+f.right)/b.x}}function ta(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function tn(e){return eW.some(t=>e[t]>=0)}async function ti(e,t){let{placement:a,platform:n,elements:i}=e,r=await (null==n.isRTL?void 0:n.isRTL(i.floating)),s=e0(a),o=e1(a),c="y"===e4(a),l=["left","top"].includes(s)?-1:1,p=r&&c?-1:1,u=eQ(t,e),{mainAxis:d,crossAxis:m,alignmentAxis:f}="number"==typeof u?{mainAxis:u,crossAxis:0,alignmentAxis:null}:{mainAxis:u.mainAxis||0,crossAxis:u.crossAxis||0,alignmentAxis:u.alignmentAxis};return o&&"number"==typeof f&&(m="end"===o?-1*f:f),c?{x:m*p,y:d*l}:{x:d*l,y:m*p}}function tr(e){var t;return t=0,"#document"}function ts(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function to(e){var t,a;return null==(t=(a=0,e.document||window.document))?void 0:t.documentElement}function tc(e){let{overflow:t,overflowX:a,overflowY:n,display:i}=td(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+a)&&!["inline","contents"].includes(i)}function tl(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function tp(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function tu(e){return["html","body","#document"].includes(tr(e))}function td(e){return ts(e).getComputedStyle(e)}function tm(e){return{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function tf(e){return"html"===tr(e)?e:e.assignedSlot||e.parentNode||to(e)}function th(e,t,a){var n;void 0===t&&(t=[]),void 0===a&&(a=!0);let i=function e(t){let a=tf(t);return tu(a)?t.ownerDocument?t.ownerDocument.body:t.body:e(a)}(e),r=i===(null==(n=e.ownerDocument)?void 0:n.body),s=ts(i);if(r){let e=tx(s);return t.concat(s,s.visualViewport||[],tc(i)?i:[],e&&a?th(e):[])}return t.concat(i,th(i,[],a))}function tx(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function tv(e){return e.contextElement}function tg(e){return tv(e),eX(1)}let tb=eX(0);function ty(e){let t=ts(e);return tp()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:tb}function tw(e,t,a,n){var i;void 0===t&&(t=!1),void 0===a&&(a=!1);let r=e.getBoundingClientRect(),s=tv(e),o=eX(1);t&&(n||(o=tg(e)));let c=(void 0===(i=a)&&(i=!1),n&&(!i||n===ts(s))&&i)?ty(s):eX(0),l=(r.left+c.x)/o.x,p=(r.top+c.y)/o.y,u=r.width/o.x,d=r.height/o.y;if(s){let e=ts(s),t=tx(e);for(;t&&n&&n!==e;){let a=tg(t),n=t.getBoundingClientRect(),i=td(t),r=n.left+(t.clientLeft+parseFloat(i.paddingLeft))*a.x,s=n.top+(t.clientTop+parseFloat(i.paddingTop))*a.y;l*=a.x,p*=a.y,u*=a.x,d*=a.y,l+=r,p+=s,t=tx(e=ts(t))}}return e5({width:u,height:d,x:l,y:p})}function t_(e,t){let a=tm(e).scrollLeft;return t?t.left+a:tw(to(e)).left+a}function tk(e,t,a){void 0===a&&(a=!1);let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-(a?0:t_(e,n)),y:n.top+t.scrollTop}}function tj(e,t,a){let n;if("viewport"===t)n=function(e,t){let a=ts(e),n=to(e),i=a.visualViewport,r=n.clientWidth,s=n.clientHeight,o=0,c=0;if(i){r=i.width,s=i.height;let e=tp();(!e||e&&"fixed"===t)&&(o=i.offsetLeft,c=i.offsetTop)}return{width:r,height:s,x:o,y:c}}(e,a);else if("document"===t)n=function(e){let t=to(e),a=tm(e),n=e.ownerDocument.body,i=eV(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),r=eV(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),s=-a.scrollLeft+t_(e),o=-a.scrollTop;return"rtl"===td(n).direction&&(s+=eV(t.clientWidth,n.clientWidth)-i),{width:i,height:r,x:s,y:o}}(to(e));else{let a=ty(e);n={x:t.x-a.x,y:t.y-a.y,width:t.width,height:t.height}}return e5(n)}function tE(e,t){let a=ts(e);if(tl(e))return a;{let t=tf(e);for(;t&&!tu(t);)t=tf(t);return a}}let tC=async function(e){let t=this.getOffsetParent||tE,a=this.getDimensions,n=await a(e.floating);return{reference:function(e,t,a){let n=to(t),i="fixed"===a,r=tw(e,!0,i,t),s={scrollLeft:0,scrollTop:0},o=eX(0);if(!i){("body"!==tr(t)||tc(n))&&(s=tm(t));n&&(o.x=t_(n))}let c=!n||i?eX(0):tk(n,s);return{x:r.left+s.scrollLeft-o.x-c.x,y:r.top+s.scrollTop-o.y-c.y,width:r.width,height:r.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},tR={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:a,offsetParent:n,strategy:i}=e,r="fixed"===i,s=to(n),o=!!t&&tl(t.floating);if(n===s||o&&r)return a;let c={scrollLeft:0,scrollTop:0},l=eX(1),p=eX(0);r||("body"!==tr(n)||tc(s))&&(c=tm(n));let u=!s||r?eX(0):tk(s,c,!0);return{width:a.width*l.x,height:a.height*l.y,x:a.x*l.x-c.scrollLeft*l.x+p.x+u.x,y:a.y*l.y-c.scrollTop*l.y+p.y+u.y}},getDocumentElement:to,getClippingRect:function(e){let{element:t,boundary:a,rootBoundary:n,strategy:i}=e,r=[..."clippingAncestors"===a?tl(t)?[]:function(e,t){let a=t.get(e);if(a)return a;let n=th(e,[],!1).filter(e=>!1);return"fixed"===td(e).position&&tf(e),t.set(e,n),n}(t,this._c):[].concat(a),n],s=r[0],o=r.reduce((e,a)=>{let n=tj(t,a,i);return e.top=eV(n.top,e.top),e.right=eH(n.right,e.right),e.bottom=eH(n.bottom,e.bottom),e.left=eV(n.left,e.left),e},tj(t,s,i));return{width:o.right-o.left,height:o.bottom-o.top,x:o.left,y:o.top}},getOffsetParent:tE,getElementRects:tC,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:a}=function(e){let t=td(e),a=parseFloat(t.width)||0,n=parseFloat(t.height)||0,i=a,r=n,s=eK(a)!==i||eK(n)!==r;return s&&(a=i,n=r),{width:a,height:n,$:s}}(e);return{width:t,height:a}},getScale:tg,isElement:function(e){return!1},isRTL:function(e){return"rtl"===td(e).direction}};function tS(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let tO=e=>({name:"arrow",options:e,async fn(t){let{x:a,y:n,placement:i,rects:r,platform:s,elements:o,middlewareData:c}=t,{element:l,padding:p=0}=eQ(e,t)||{};if(null==l)return{};let u=e8(p),d={x:a,y:n},m=e2(e4(i)),f=e3(m),h=await s.getDimensions(l),x="y"===m,v=x?"clientHeight":"clientWidth",g=r.reference[f]+r.reference[m]-d[m]-r.floating[f],b=d[m]-r.reference[m],y=await (null==s.getOffsetParent?void 0:s.getOffsetParent(l)),w=y?y[v]:0;w&&await (null==s.isElement?void 0:s.isElement(y))||(w=o.floating[v]||r.floating[f]);let _=w/2-h[f]/2-1,k=eH(u[x?"top":"left"],_),j=eH(u[x?"bottom":"right"],_),E=w-h[f]-j,C=w/2-h[f]/2+(g/2-b/2),R=eV(k,eH(C,E)),S=!c.arrow&&null!=e1(i)&&C!==R&&r.reference[f]/2-(C<k?k:j)-h[f]/2<0,O=S?C<k?C-k:C-E:0;return{[m]:d[m]+O,data:{[m]:R,centerOffset:C-R-O,...S&&{alignmentOffset:O}},reset:S}}}),tT=(e,t,a)=>{let n=new Map,i={platform:tR,...a},r={...i.platform,_c:n};return te(e,t,{...i,platform:r})};var tN="undefined"!=typeof document?h.useLayoutEffect:h.useEffect;function tA(e,t){let a,n,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((a=e.length)!==t.length)return!1;for(n=a;0!=n--;)if(!tA(e[n],t[n]))return!1;return!0}if((a=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=a;0!=n--;)if(!({}).hasOwnProperty.call(t,i[n]))return!1;for(n=a;0!=n--;){let a=i[n];if(("_owner"!==a||!e.$$typeof)&&!tA(e[a],t[a]))return!1}return!0}return e!=e&&t!=t}function tP(e,t){return Math.round(1*t)/1}function tL(e){let t=h.useRef(e);return tN(()=>{t.current=e}),t}let tU=e=>({name:"arrow",options:e,fn(t){let{element:a,padding:n}="function"==typeof e?e(t):e;return a&&({}).hasOwnProperty.call(a,"current")?null!=a.current?tO({element:a.current,padding:n}).fn(t):{}:a?tO({element:a,padding:n}).fn(t):{}}}),tM=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var a,n;let{x:i,y:r,placement:s,middlewareData:o}=t,c=await ti(t,e);return s===(null==(a=o.offset)?void 0:a.placement)&&null!=(n=o.arrow)&&n.alignmentOffset?{}:{x:i+c.x,y:r+c.y,data:{...c,placement:s}}}}}(e),options:[e,t]}),tz=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:a,y:n,placement:i}=t,{mainAxis:r=!0,crossAxis:s=!1,limiter:o={fn:e=>{let{x:t,y:a}=e;return{x:t,y:a}}},...c}=eQ(e,t),l={x:a,y:n},p=await tt(t,c),u=e4(e0(i)),d=e2(u),m=l[d],f=l[u];if(r){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",a=m+p[e],n=m-p[t];m=eV(a,eH(m,n))}if(s){let e="y"===u?"top":"left",t="y"===u?"bottom":"right",a=f+p[e],n=f-p[t];f=eV(a,eH(f,n))}let h=o.fn({...t,[d]:m,[u]:f});return{...h,data:{x:h.x-a,y:h.y-n,enabled:{[d]:r,[u]:s}}}}}}(e),options:[e,t]}),tF=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:a,y:n,placement:i,rects:r,middlewareData:s}=t,{offset:o=0,mainAxis:c=!0,crossAxis:l=!0}=eQ(e,t),p={x:a,y:n},u=e4(i),d=e2(u),m=p[d],f=p[u],h=eQ(o,t),x="number"==typeof h?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(c){let e="y"===d?"height":"width",t=r.reference[d]-r.floating[e]+x.mainAxis,a=r.reference[d]+r.reference[e]-x.mainAxis;m<t?m=t:m>a&&(m=a)}if(l){var v,g;let e="y"===d?"width":"height",t=["top","left"].includes(e0(i)),a=r.reference[u]-r.floating[e]+(t&&(null==(v=s.offset)?void 0:v[u])||0)+(t?0:x.crossAxis),n=r.reference[u]+r.reference[e]+(t?0:(null==(g=s.offset)?void 0:g[u])||0)-(t?x.crossAxis:0);f<a?f=a:f>n&&(f=n)}return{[d]:m,[u]:f}}}}(e),options:[e,t]}),tD=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var a,n,i,r,s;let{placement:o,middlewareData:c,rects:l,initialPlacement:p,platform:u,elements:d}=t,{mainAxis:m=!0,crossAxis:f=!0,fallbackPlacements:h,fallbackStrategy:x="bestFit",fallbackAxisSideDirection:v="none",flipAlignment:g=!0,...b}=eQ(e,t);if(null!=(a=c.arrow)&&a.alignmentOffset)return{};let y=e0(o),w=e4(p),_=e0(p)===p,k=await (null==u.isRTL?void 0:u.isRTL(d.floating)),j=h||(_||!g?[e9(p)]:function(e){let t=e9(e);return[e6(e),t,e6(t)]}(p)),E="none"!==v;!h&&E&&j.push(...function(e,t,a,n){let i=e1(e),r=function(e,t,a){let n=["left","right"],i=["right","left"];switch(e){case"top":case"bottom":if(a)return t?i:n;return t?n:i;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(e0(e),"start"===a,n);return i&&(r=r.map(e=>e+"-"+i),t&&(r=r.concat(r.map(e6)))),r}(p,g,v,k));let C=[p,...j],R=await tt(t,b),S=[],O=(null==(n=c.flip)?void 0:n.overflows)||[];if(m&&S.push(R[y]),f){let e=function(e,t,a){void 0===a&&(a=!1);let n=e1(e),i=e2(e4(e)),r=e3(i),s="x"===i?n===(a?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[r]>t.floating[r]&&(s=e9(s)),[s,e9(s)]}(o,l,k);S.push(R[e[0]],R[e[1]])}if(O=[...O,{placement:o,overflows:S}],!S.every(e=>e<=0)){let e=((null==(i=c.flip)?void 0:i.index)||0)+1,t=C[e];if(t)return{data:{index:e,overflows:O},reset:{placement:t}};let a=null==(r=O.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:r.placement;if(!a)switch(x){case"bestFit":{let e=null==(s=O.filter(e=>{if(E){let t=e4(e.placement);return t===w||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:s[0];e&&(a=e);break}case"initialPlacement":a=p}if(o!==a)return{reset:{placement:a}}}return{}}}}(e),options:[e,t]}),tI=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var a,n;let i,r;let{placement:s,rects:o,platform:c,elements:l}=t,{apply:p=()=>{},...u}=eQ(e,t),d=await tt(t,u),m=e0(s),f=e1(s),h="y"===e4(s),{width:x,height:v}=o.floating;"top"===m||"bottom"===m?(i=m,r=f===(await (null==c.isRTL?void 0:c.isRTL(l.floating))?"start":"end")?"left":"right"):(r=m,i="end"===f?"top":"bottom");let g=v-d.top-d.bottom,b=x-d.left-d.right,y=eH(v-d[i],g),w=eH(x-d[r],b),_=!t.middlewareData.shift,k=y,j=w;if(null!=(a=t.middlewareData.shift)&&a.enabled.x&&(j=b),null!=(n=t.middlewareData.shift)&&n.enabled.y&&(k=g),_&&!f){let e=eV(d.left,0),t=eV(d.right,0),a=eV(d.top,0),n=eV(d.bottom,0);h?j=x-2*(0!==e||0!==t?e+t:eV(d.left,d.right)):k=v-2*(0!==a||0!==n?a+n:eV(d.top,d.bottom))}await p({...t,availableWidth:j,availableHeight:k});let E=await c.getDimensions(l.floating);return x!==E.width||v!==E.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),tq=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:a}=t,{strategy:n="referenceHidden",...i}=eQ(e,t);switch(n){case"referenceHidden":{let e=ta(await tt(t,{...i,elementContext:"reference"}),a.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:tn(e)}}}case"escaped":{let e=ta(await tt(t,{...i,altBoundary:!0}),a.floating);return{data:{escapedOffsets:e,escaped:tn(e)}}}default:return{}}}}}(e),options:[e,t]}),tB=(e,t)=>({...tU(e),options:[e,t]}),t$=(0,h.forwardRef)((e,t)=>{let{children:a,width:n=10,height:i=5,...r}=e;return(0,h.createElement)(ex.svg,v({},r,{ref:t,width:n,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none"}),e.asChild?a:(0,h.createElement)("polygon",{points:"0,0 30,0 15,10"}))}),tZ="Popper",[tW,tH]=e_(tZ),[tV,tK]=tW(tZ),tG=(0,h.forwardRef)((e,t)=>{let{__scopePopper:a,virtualRef:n,...i}=e,r=tK("PopperAnchor",a),s=(0,h.useRef)(null),o=b(t,s);return(0,h.useEffect)(()=>{r.onAnchorChange((null==n?void 0:n.current)||s.current)}),n?null:(0,h.createElement)(ex.div,v({},i,{ref:o}))}),tX="PopperContent",[tY,tJ]=tW(tX),tQ=(0,h.forwardRef)((e,t)=>{var a,n,i,r,s,o,c,l;let{__scopePopper:p,side:u="bottom",sideOffset:d=0,align:m="center",alignOffset:f=0,arrowPadding:x=0,avoidCollisions:g=!0,collisionBoundary:y=[],collisionPadding:w=0,sticky:_="partial",hideWhenDetached:k=!1,updatePositionStrategy:j="optimized",onPlaced:E,...C}=e,R=tK(tX,p),[S,O]=(0,h.useState)(null),T=b(t,e=>O(e)),[N,A]=(0,h.useState)(null),P=function(e){let[t,a]=(0,h.useState)(void 0);return eq(()=>{if(e){a({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,i;if(!Array.isArray(t)||!t.length)return;let r=t[0];if("borderBoxSize"in r){let e=r.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,i=t.blockSize}else n=e.offsetWidth,i=e.offsetHeight;a({width:n,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}a(void 0)},[e]),t}(N),L=null!==(a=null==P?void 0:P.width)&&void 0!==a?a:0,U=null!==(n=null==P?void 0:P.height)&&void 0!==n?n:0,M="number"==typeof w?w:{top:0,right:0,bottom:0,left:0,...w},z=Array.isArray(y)?y:[y],F=z.length>0,D={padding:M,boundary:z.filter(t1),altBoundary:F},{refs:I,floatingStyles:q,placement:B,isPositioned:$,middlewareData:Z}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:a="absolute",middleware:n=[],platform:i,elements:{reference:r,floating:s}={},transform:o=!0,whileElementsMounted:c,open:l}=e,[p,u]=h.useState({x:0,y:0,strategy:a,placement:t,middlewareData:{},isPositioned:!1}),[d,m]=h.useState(n);tA(d,n)||m(n);let[f,x]=h.useState(null),[v,g]=h.useState(null),b=h.useCallback(e=>{e!==k.current&&(k.current=e,x(e))},[]),y=h.useCallback(e=>{e!==j.current&&(j.current=e,g(e))},[]),w=r||f,_=s||v,k=h.useRef(null),j=h.useRef(null),E=h.useRef(p),C=null!=c,R=tL(c),S=tL(i),O=tL(l),T=h.useCallback(()=>{if(!k.current||!j.current)return;let e={placement:t,strategy:a,middleware:d};S.current&&(e.platform=S.current),tT(k.current,j.current,e).then(e=>{let t={...e,isPositioned:!1!==O.current};N.current&&!tA(E.current,t)&&(E.current=t,eh.flushSync(()=>{u(t)}))})},[d,t,a,S,O]);tN(()=>{!1===l&&E.current.isPositioned&&(E.current.isPositioned=!1,u(e=>({...e,isPositioned:!1})))},[l]);let N=h.useRef(!1);tN(()=>(N.current=!0,()=>{N.current=!1}),[]),tN(()=>{if(w&&(k.current=w),_&&(j.current=_),w&&_){if(R.current)return R.current(w,_,T);T()}},[w,_,T,R,C]);let A=h.useMemo(()=>({reference:k,floating:j,setReference:b,setFloating:y}),[b,y]),P=h.useMemo(()=>({reference:w,floating:_}),[w,_]),L=h.useMemo(()=>{let e={position:a,left:0,top:0};if(!P.floating)return e;let t=tP(P.floating,p.x),n=tP(P.floating,p.y);return o?{...e,transform:"translate("+t+"px, "+n+"px)",...(P.floating,!1)}:{position:a,left:t,top:n}},[a,o,P.floating,p.x,p.y]);return h.useMemo(()=>({...p,update:T,refs:A,elements:P,floatingStyles:L}),[p,T,A,P,L])}({strategy:"fixed",placement:u+("center"!==m?"-"+m:""),whileElementsMounted:(...e)=>(function(e,t,a,n){let i;void 0===n&&(n={});let{ancestorScroll:r=!0,ancestorResize:s=!0,elementResize:o="function"==typeof ResizeObserver,layoutShift:c="function"==typeof IntersectionObserver,animationFrame:l=!1}=n,p=tv(e),u=r||s?[...p?th(p):[],...th(t)]:[];u.forEach(e=>{r&&e.addEventListener("scroll",a,{passive:!0}),s&&e.addEventListener("resize",a)});let d=p&&c?function(e,t){let a,n=null,i=to(e);function r(){var e;clearTimeout(a),null==(e=n)||e.disconnect(),n=null}return function s(o,c){void 0===o&&(o=!1),void 0===c&&(c=1),r();let l=e.getBoundingClientRect(),{left:p,top:u,width:d,height:m}=l;if(o||t(),!d||!m)return;let f=eG(u),h=eG(i.clientWidth-(p+d)),x={rootMargin:-f+"px "+-h+"px "+-eG(i.clientHeight-(u+m))+"px "+-eG(p)+"px",threshold:eV(0,eH(1,c))||1},v=!0;function g(t){let n=t[0].intersectionRatio;if(n!==c){if(!v)return s();n?s(!1,n):a=setTimeout(()=>{s(!1,1e-7)},1e3)}1!==n||tS(l,e.getBoundingClientRect())||s(),v=!1}try{n=new IntersectionObserver(g,{...x,root:i.ownerDocument})}catch(e){n=new IntersectionObserver(g,x)}n.observe(e)}(!0),r}(p,a):null,m=-1,f=null;o&&(f=new ResizeObserver(e=>{let[n]=e;n&&n.target===p&&f&&(f.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var e;null==(e=f)||e.observe(t)})),a()}),p&&!l&&f.observe(p),f.observe(t));let h=l?tw(e):null;return l&&function t(){let n=tw(e);h&&!tS(h,n)&&a(),h=n,i=requestAnimationFrame(t)}(),a(),()=>{var e;u.forEach(e=>{r&&e.removeEventListener("scroll",a),s&&e.removeEventListener("resize",a)}),null==d||d(),null==(e=f)||e.disconnect(),f=null,l&&cancelAnimationFrame(i)}})(...e,{animationFrame:"always"===j}),elements:{reference:R.anchor},middleware:[tM({mainAxis:d+U,alignmentAxis:f}),g&&tz({mainAxis:!0,crossAxis:!1,limiter:"partial"===_?tF():void 0,...D}),g&&tD({...D}),tI({...D,apply:({elements:e,rects:t,availableWidth:a,availableHeight:n})=>{let{width:i,height:r}=t.reference,s=e.floating.style;s.setProperty("--radix-popper-available-width",`${a}px`),s.setProperty("--radix-popper-available-height",`${n}px`),s.setProperty("--radix-popper-anchor-width",`${i}px`),s.setProperty("--radix-popper-anchor-height",`${r}px`)}}),N&&tB({element:N,padding:x}),t2({arrowWidth:L,arrowHeight:U}),k&&tq({strategy:"referenceHidden",...D})]}),[W,H]=t3(B),V=ej(E);eq(()=>{$&&(null==V||V())},[$,V]);let K=null===(i=Z.arrow)||void 0===i?void 0:i.x,G=null===(r=Z.arrow)||void 0===r?void 0:r.y,X=(null===(s=Z.arrow)||void 0===s?void 0:s.centerOffset)!==0,[Y,J]=(0,h.useState)();return eq(()=>{S&&J(window.getComputedStyle(S).zIndex)},[S]),(0,h.createElement)("div",{ref:I.setFloating,"data-radix-popper-content-wrapper":"",style:{...q,transform:$?q.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Y,"--radix-popper-transform-origin":[null===(o=Z.transformOrigin)||void 0===o?void 0:o.x,null===(c=Z.transformOrigin)||void 0===c?void 0:c.y].join(" ")},dir:e.dir},(0,h.createElement)(tY,{scope:p,placedSide:W,onArrowChange:A,arrowX:K,arrowY:G,shouldHideArrow:X},(0,h.createElement)(ex.div,v({"data-side":W,"data-align":H},C,{ref:T,style:{...C.style,animation:$?void 0:"none",opacity:null!==(l=Z.hide)&&void 0!==l&&l.referenceHidden?0:void 0}}))))}),t0={top:"bottom",right:"left",bottom:"top",left:"right"};function t1(e){return null!==e}let t2=e=>({name:"transformOrigin",options:e,fn(t){var a,n,i,r,s;let{placement:o,rects:c,middlewareData:l}=t,p=(null===(a=l.arrow)||void 0===a?void 0:a.centerOffset)!==0,u=p?0:e.arrowWidth,d=p?0:e.arrowHeight,[m,f]=t3(o),h={start:"0%",center:"50%",end:"100%"}[f],x=(null!==(n=null===(i=l.arrow)||void 0===i?void 0:i.x)&&void 0!==n?n:0)+u/2,v=(null!==(r=null===(s=l.arrow)||void 0===s?void 0:s.y)&&void 0!==r?r:0)+d/2,g="",b="";return"bottom"===m?(g=p?h:`${x}px`,b=`${-d}px`):"top"===m?(g=p?h:`${x}px`,b=`${c.floating.height+d}px`):"right"===m?(g=`${-d}px`,b=p?h:`${v}px`):"left"===m&&(g=`${c.floating.width+d}px`,b=p?h:`${v}px`),{data:{x:g,y:b}}}});function t3(e){let[t,a="center"]=e.split("-");return[t,a]}let t4=e=>{let{__scopePopper:t,children:a}=e,[n,i]=(0,h.useState)(null);return(0,h.createElement)(tV,{scope:t,anchor:n,onAnchorChange:i},a)},t6=(0,h.forwardRef)((e,t)=>{var a;let{container:n=null==globalThis?void 0:null===(a=globalThis.document)||void 0===a?void 0:a.body,...i}=e;return n?eh.createPortal((0,h.createElement)(ex.div,v({},i,{ref:t})),n):null});function t9({prop:e,defaultProp:t,onChange:a=()=>{}}){let[n,i]=function({defaultProp:e,onChange:t}){let a=(0,h.useState)(e),[n]=a,i=(0,h.useRef)(n),r=ej(t);return(0,h.useEffect)(()=>{i.current!==n&&(r(n),i.current=n)},[n,i,r]),a}({defaultProp:t,onChange:a}),r=void 0!==e,s=r?e:n,o=ej(a);return[s,(0,h.useCallback)(t=>{if(r){let a="function"==typeof t?t(e):t;a!==e&&o(a)}else i(t)},[r,e,i,o])]}let t8=(0,h.forwardRef)((e,t)=>(0,h.createElement)(ex.span,v({},e,{ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}})));var t5=new WeakMap,t7=new WeakMap,ae={},at=0,aa=function(e){return e&&(e.host||aa(e.parentNode))},an=function(e,t,a,n){var i=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var a=aa(e);return a&&t.contains(a)?a:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});ae[a]||(ae[a]=new WeakMap);var r=ae[a],s=[],o=new Set,c=new Set(i),l=function(e){!e||o.has(e)||(o.add(e),l(e.parentNode))};i.forEach(l);var p=function(e){!e||c.has(e)||Array.prototype.forEach.call(e.children,function(e){if(o.has(e))p(e);else try{var t=e.getAttribute(n),i=null!==t&&"false"!==t,c=(t5.get(e)||0)+1,l=(r.get(e)||0)+1;t5.set(e,c),r.set(e,l),s.push(e),1===c&&i&&t7.set(e,!0),1===l&&e.setAttribute(a,"true"),i||e.setAttribute(n,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return p(t),o.clear(),at++,function(){s.forEach(function(e){var t=t5.get(e)-1,i=r.get(e)-1;t5.set(e,t),r.set(e,i),t||(t7.has(e)||e.removeAttribute(n),t7.delete(e)),i||e.removeAttribute(a)}),--at||(t5=new WeakMap,t5=new WeakMap,t7=new WeakMap,ae={})}},ai=function(e,t,a){void 0===a&&(a="data-aria-hidden");var n,i=Array.from(Array.isArray(e)?e:[e]),r=t||(n=e,"undefined"==typeof document?null:(Array.isArray(n)?n[0]:n).ownerDocument.body);return r?(i.push.apply(i,Array.from(r.querySelectorAll("[aria-live]"))),an(i,r,a,"aria-hidden")):function(){return null}},ar=function(){return(ar=Object.assign||function(e){for(var t,a=1,n=arguments.length;a<n;a++)for(var i in t=arguments[a])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};Object.create,Object.create;var as=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),ao="width-before-scroll-bar";function ac(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var al=h.useEffect,ap=new WeakMap,au=function(){return(au=Object.assign||function(e){for(var t,a=1,n=arguments.length;a<n;a++)for(var i in t=arguments[a])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};function ad(e){return e}Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError;var am=function(e){void 0===e&&(e={});var t,a,n,i=(void 0===t&&(t=ad),a=[],n=!1,{read:function(){if(n)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return a.length?a[a.length-1]:null},useMedium:function(e){var i=t(e,n);return a.push(i),function(){a=a.filter(function(e){return e!==i})}},assignSyncMedium:function(e){for(n=!0;a.length;){var t=a;a=[],t.forEach(e)}a={push:function(t){return e(t)},filter:function(){return a}}},assignMedium:function(e){n=!0;var t=[];if(a.length){var i=a;a=[],i.forEach(e),t=a}var r=function(){var a=t;t=[],a.forEach(e)},s=function(){return Promise.resolve().then(r)};s(),a={push:function(e){t.push(e),s()},filter:function(e){return t=t.filter(e),a}}}});return i.options=au({async:!0,ssr:!1},e),i}(),af=function(){},ah=h.forwardRef(function(e,t){var a,n,i,r,s=h.useRef(null),o=h.useState({onScrollCapture:af,onWheelCapture:af,onTouchMoveCapture:af}),c=o[0],l=o[1],p=e.forwardProps,u=e.children,d=e.className,m=e.removeScrollBar,f=e.enabled,x=e.shards,v=e.sideCar,g=e.noIsolation,b=e.inert,y=e.allowPinchZoom,w=e.as,_=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(a[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,n=Object.getOwnPropertySymbols(e);i<n.length;i++)0>t.indexOf(n[i])&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(a[n[i]]=e[n[i]]);return a}(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as"]),k=(a=[s,t],n=function(e){return a.forEach(function(t){return ac(t,e)})},(i=(0,h.useState)(function(){return{value:null,callback:n,facade:{get current(){return i.value},set current(value){var e=i.value;e!==value&&(i.value=value,i.callback(value,e))}}}})[0]).callback=n,r=i.facade,al(function(){var e=ap.get(r);if(e){var t=new Set(e),n=new Set(a),i=r.current;t.forEach(function(e){n.has(e)||ac(e,null)}),n.forEach(function(e){t.has(e)||ac(e,i)})}ap.set(r,a)},[a]),r),j=ar(ar({},_),c);return h.createElement(h.Fragment,null,f&&h.createElement(v,{sideCar:am,removeScrollBar:m,shards:x,noIsolation:g,inert:b,setCallbacks:l,allowPinchZoom:!!y,lockRef:s}),p?h.cloneElement(h.Children.only(u),ar(ar({},j),{ref:k})):h.createElement(void 0===w?"div":w,ar({},j,{className:d,ref:k}),u))});ah.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},ah.classNames={fullWidth:ao,zeroRight:as};var ax=function(e){var t=e.sideCar,a=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(a[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,n=Object.getOwnPropertySymbols(e);i<n.length;i++)0>t.indexOf(n[i])&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(a[n[i]]=e[n[i]]);return a}(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw Error("Sidecar medium not found");return h.createElement(n,au({},a))};ax.isSideCarExport=!0;var av=function(){var e=0,t=null;return{add:function(n){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=c||a.nc;return t&&e.setAttribute("nonce",t),e}())){var i,r;(i=t).styleSheet?i.styleSheet.cssText=n:i.appendChild(document.createTextNode(n)),r=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(r)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},ag=function(){var e=av();return function(t,a){h.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&a])}},ab=function(){var e=ag();return function(t){return e(t.styles,t.dynamic),null}},ay={left:0,top:0,right:0,gap:0},aw=ab(),a_="data-scroll-locked",ak=function(e,t,a,n){var i=e.left,r=e.top,s=e.right,o=e.gap;return void 0===a&&(a="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(n,";\n   padding-right: ").concat(o,"px ").concat(n,";\n  }\n  body[").concat(a_,"] {\n    overflow: hidden ").concat(n,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(n,";"),"margin"===a&&"\n    padding-left: ".concat(i,"px;\n    padding-top: ").concat(r,"px;\n    padding-right: ").concat(s,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(o,"px ").concat(n,";\n    "),"padding"===a&&"padding-right: ".concat(o,"px ").concat(n,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(as," {\n    right: ").concat(o,"px ").concat(n,";\n  }\n  \n  .").concat(ao," {\n    margin-right: ").concat(o,"px ").concat(n,";\n  }\n  \n  .").concat(as," .").concat(as," {\n    right: 0 ").concat(n,";\n  }\n  \n  .").concat(ao," .").concat(ao," {\n    margin-right: 0 ").concat(n,";\n  }\n  \n  body[").concat(a_,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(o,"px;\n  }\n")},aj=function(){var e=parseInt(document.body.getAttribute(a_)||"0",10);return isFinite(e)?e:0},aE=function(){h.useEffect(function(){return document.body.setAttribute(a_,(aj()+1).toString()),function(){var e=aj()-1;e<=0?document.body.removeAttribute(a_):document.body.setAttribute(a_,e.toString())}},[])},aC=function(e){var t=e.noRelative,a=e.noImportant,n=e.gapMode,i=void 0===n?"margin":n;aE();var r=h.useMemo(function(){return ay},[i]);return h.createElement(aw,{styles:ak(r,!t,i,a?"":"!important")})},aR=function(e,t){var a=window.getComputedStyle(e);return"hidden"!==a[t]&&!(a.overflowY===a.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===a[t])},aS=function(e,t){var a=t;do{if("undefined"!=typeof ShadowRoot&&a instanceof ShadowRoot&&(a=a.host),aO(e,a)){var n=aT(e,a);if(n[1]>n[2])return!0}a=a.parentNode}while(a&&a!==document.body);return!1},aO=function(e,t){return"v"===e?aR(t,"overflowY"):aR(t,"overflowX")},aT=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},aN=function(e,t,a,n,i){var r,s=(r=window.getComputedStyle(t).direction,"h"===e&&"rtl"===r?-1:1),o=s*n,c=a.target,l=t.contains(c),p=!1,u=o>0,d=0,m=0;do{var f=aT(e,c),h=f[0],x=f[1]-f[2]-s*h;(h||x)&&aO(e,c)&&(d+=x,m+=h),c=c.parentNode}while(!l&&c!==document.body||l&&(t.contains(c)||t===c));return u&&(i&&0===d||!i&&o>d)?p=!0:!u&&(i&&0===m||!i&&-o>m)&&(p=!0),p},aA=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},aP=function(e){return[e.deltaX,e.deltaY]},aL=function(e){return e&&"current"in e?e.current:e},aU=0,aM=[];let az=(am.useMedium(function(e){var t=h.useRef([]),a=h.useRef([0,0]),n=h.useRef(),i=h.useState(aU++)[0],r=h.useState(function(){return ab()})[0],s=h.useRef(e);h.useEffect(function(){s.current=e},[e]),h.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(i));var t=(function(e,t,a){if(a||2==arguments.length)for(var n,i=0,r=t.length;i<r;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(aL),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(i))}),function(){document.body.classList.remove("block-interactivity-".concat(i)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(i))})}}},[e.inert,e.lockRef.current,e.shards]);var o=h.useCallback(function(e,t){if("touches"in e&&2===e.touches.length)return!s.current.allowPinchZoom;var i,r=aA(e),o=a.current,c="deltaX"in e?e.deltaX:o[0]-r[0],l="deltaY"in e?e.deltaY:o[1]-r[1],p=e.target,u=Math.abs(c)>Math.abs(l)?"h":"v";if("touches"in e&&"h"===u&&"range"===p.type)return!1;var d=aS(u,p);if(!d)return!0;if(d?i=u:(i="v"===u?"h":"v",d=aS(u,p)),!d)return!1;if(!n.current&&"changedTouches"in e&&(c||l)&&(n.current=i),!i)return!0;var m=n.current||i;return aN(m,t,e,"h"===m?c:l,!0)},[]),c=h.useCallback(function(e){if(aM.length&&aM[aM.length-1]===r){var a="deltaY"in e?aP(e):aA(e),n=t.current.filter(function(t){var n;return t.name===e.type&&t.target===e.target&&(n=t.delta)[0]===a[0]&&n[1]===a[1]})[0];if(n&&n.should){e.cancelable&&e.preventDefault();return}if(!n){var i=(s.current.shards||[]).map(aL).filter(Boolean).filter(function(t){return t.contains(e.target)});(i.length>0?o(e,i[0]):!s.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),l=h.useCallback(function(e,a,n,i){var r={name:e,delta:a,target:n,should:i};t.current.push(r),setTimeout(function(){t.current=t.current.filter(function(e){return e!==r})},1)},[]),p=h.useCallback(function(e){a.current=aA(e),n.current=void 0},[]),u=h.useCallback(function(t){l(t.type,aP(t),t.target,o(t,e.lockRef.current))},[]),d=h.useCallback(function(t){l(t.type,aA(t),t.target,o(t,e.lockRef.current))},[]);h.useEffect(function(){return aM.push(r),e.setCallbacks({onScrollCapture:u,onWheelCapture:u,onTouchMoveCapture:d}),document.addEventListener("wheel",c,!1),document.addEventListener("touchmove",c,!1),document.addEventListener("touchstart",p,!1),function(){aM=aM.filter(function(e){return e!==r}),document.removeEventListener("wheel",c,!1),document.removeEventListener("touchmove",c,!1),document.removeEventListener("touchstart",p,!1)}},[]);var m=e.removeScrollBar,f=e.inert;return h.createElement(h.Fragment,null,f?h.createElement(r,{styles:"\n  .block-interactivity-".concat(i," {pointer-events: none;}\n  .allow-interactivity-").concat(i," {pointer-events: all;}\n")}):null,m?h.createElement(aC,{gapMode:"margin"}):null)}),ax);var aF=h.forwardRef(function(e,t){return h.createElement(ah,ar({},e,{ref:t,sideCar:az}))});aF.classNames=ah.classNames;let aD=[" ","Enter","ArrowUp","ArrowDown"],aI=[" ","Enter"],aq="Select",[aB,a$,aZ]=function(e){let t=e+"CollectionProvider",[a,n]=e_(t),[i,r]=a(t,{collectionRef:{current:null},itemMap:new Map}),s=e+"CollectionSlot",o=h.forwardRef((e,t)=>{let{scope:a,children:n}=e,i=b(t,r(s,a).collectionRef);return h.createElement(y,{ref:i},n)}),c=e+"CollectionItemSlot",l="data-radix-collection-item";return[{Provider:e=>{let{scope:t,children:a}=e,n=h.useRef(null),r=h.useRef(new Map).current;return h.createElement(i,{scope:t,itemMap:r,collectionRef:n},a)},Slot:o,ItemSlot:h.forwardRef((e,t)=>{let{scope:a,children:n,...i}=e,s=h.useRef(null),o=b(t,s),p=r(c,a);return h.useEffect(()=>(p.itemMap.set(s,{ref:s,...i}),()=>void p.itemMap.delete(s))),h.createElement(y,{[l]:"",ref:o},n)})},function(t){let a=r(e+"CollectionConsumer",t);return h.useCallback(()=>{let e=a.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${l}]`));return Array.from(a.itemMap.values()).sort((e,a)=>t.indexOf(e.ref.current)-t.indexOf(a.ref.current))},[a.collectionRef,a.itemMap])},n]}(aq),[aW,aH]=e_(aq,[aZ,tH]),aV=tH(),[aK,aG]=aW(aq),[aX,aY]=aW(aq),aJ=(0,h.forwardRef)((e,t)=>{let{__scopeSelect:a,disabled:n=!1,...i}=e,r=aV(a),s=aG("SelectTrigger",a),o=s.disabled||n,c=b(t,s.onTriggerChange),l=a$(a),[p,u,d]=ny(e=>{let t=l().filter(e=>!e.disabled),a=t.find(e=>e.value===s.value),n=nw(t,e,a);void 0!==n&&s.onValueChange(n.value)}),m=()=>{o||(s.onOpenChange(!0),d())};return(0,h.createElement)(tG,v({asChild:!0},r),(0,h.createElement)(ex.button,v({type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:o,"data-disabled":o?"":void 0,"data-placeholder":ng(s.value)?"":void 0},i,{ref:c,onClick:ew(i.onClick,e=>{e.currentTarget.focus()}),onPointerDown:ew(i.onPointerDown,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&(m(),s.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)},e.preventDefault())}),onKeyDown:ew(i.onKeyDown,e=>{let t=""!==p.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||u(e.key),(!t||" "!==e.key)&&aD.includes(e.key)&&(m(),e.preventDefault())})})))}),aQ=(0,h.forwardRef)((e,t)=>{let{__scopeSelect:a,className:n,style:i,children:r,placeholder:s="",...o}=e,c=aG("SelectValue",a),{onValueNodeHasChildrenChange:l}=c,p=void 0!==r,u=b(t,c.onValueNodeChange);return eq(()=>{l(p)},[l,p]),(0,h.createElement)(ex.span,v({},o,{ref:u,style:{pointerEvents:"none"}}),ng(c.value)?(0,h.createElement)(h.Fragment,null,s):r)}),a0=(0,h.forwardRef)((e,t)=>{let{__scopeSelect:a,children:n,...i}=e;return(0,h.createElement)(ex.span,v({"aria-hidden":!0},i,{ref:t}),n||"▼")}),a1="SelectContent",a2=(0,h.forwardRef)((e,t)=>{let a=aG(a1,e.__scopeSelect),[n,i]=(0,h.useState)();return(eq(()=>{i(new DocumentFragment)},[]),a.open)?(0,h.createElement)(a6,v({},e,{ref:t})):n?(0,eh.createPortal)((0,h.createElement)(a3,{scope:e.__scopeSelect},(0,h.createElement)(aB.Slot,{scope:e.__scopeSelect},(0,h.createElement)("div",null,e.children))),n):null}),[a3,a4]=aW(a1),a6=(0,h.forwardRef)((e,t)=>{let{__scopeSelect:a,position:n="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:r,onPointerDownOutside:s,side:o,sideOffset:c,align:l,alignOffset:p,arrowPadding:u,collisionBoundary:d,collisionPadding:m,sticky:f,hideWhenDetached:x,avoidCollisions:g,...w}=e,_=aG(a1,a),[k,j]=(0,h.useState)(null),[E,C]=(0,h.useState)(null),R=b(t,e=>j(e)),[S,O]=(0,h.useState)(null),[T,N]=(0,h.useState)(null),A=a$(a),[P,L]=(0,h.useState)(!1),U=(0,h.useRef)(!1);(0,h.useEffect)(()=>{if(k)return ai(k)},[k]),(0,h.useEffect)(()=>{var e,t;let a=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=a[0])&&void 0!==e?e:eN()),document.body.insertAdjacentElement("beforeend",null!==(t=a[1])&&void 0!==t?t:eN()),eT++,()=>{1===eT&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),eT--}},[]);let M=(0,h.useCallback)(e=>{let[t,...a]=A().map(e=>e.ref.current),[n]=a.slice(-1),i=document.activeElement;for(let a of e)if(a===i||(null==a||a.scrollIntoView({block:"nearest"}),a===t&&E&&(E.scrollTop=0),a===n&&E&&(E.scrollTop=E.scrollHeight),null==a||a.focus(),document.activeElement!==i))return},[A,E]),z=(0,h.useCallback)(()=>M([S,k]),[M,S,k]);(0,h.useEffect)(()=>{P&&z()},[P,z]);let{onOpenChange:F,triggerPointerDownPosRef:D}=_;(0,h.useEffect)(()=>{if(k){let e={x:0,y:0},t=t=>{var a,n,i,r;e={x:Math.abs(Math.round(t.pageX)-(null!==(a=null===(n=D.current)||void 0===n?void 0:n.x)&&void 0!==a?a:0)),y:Math.abs(Math.round(t.pageY)-(null!==(i=null===(r=D.current)||void 0===r?void 0:r.y)&&void 0!==i?i:0))}},a=a=>{e.x<=10&&e.y<=10?a.preventDefault():k.contains(a.target)||F(!1),document.removeEventListener("pointermove",t),D.current=null};return null!==D.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",a,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",a,{capture:!0})}}},[k,F,D]),(0,h.useEffect)(()=>{let e=()=>F(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[F]);let[I,q]=ny(e=>{let t=A().filter(e=>!e.disabled),a=t.find(e=>e.ref.current===document.activeElement),n=nw(t,e,a);n&&setTimeout(()=>n.ref.current.focus())}),B=(0,h.useCallback)((e,t,a)=>{let n=!U.current&&!a;(void 0!==_.value&&_.value===t||n)&&(O(e),n&&(U.current=!0))},[_.value]),$=(0,h.useCallback)(()=>null==k?void 0:k.focus(),[k]),Z=(0,h.useCallback)((e,t,a)=>{let n=!U.current&&!a;(void 0!==_.value&&_.value===t||n)&&N(e)},[_.value]),W="popper"===n?a8:a9;return(0,h.createElement)(a3,{scope:a,content:k,viewport:E,onViewportChange:C,itemRefCallback:B,selectedItem:S,onItemLeave:$,itemTextRefCallback:Z,focusSelectedItem:z,selectedItemText:T,position:n,isPositioned:P,searchRef:I},(0,h.createElement)(aF,{as:y,allowPinchZoom:!0},(0,h.createElement)(eU,{asChild:!0,trapped:_.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:ew(i,e=>{var t;null===(t=_.trigger)||void 0===t||t.focus({preventScroll:!0}),e.preventDefault()})},(0,h.createElement)(eR,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:r,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>_.onOpenChange(!1)},(0,h.createElement)(W,v({role:"listbox",id:_.contentId,"data-state":_.open?"open":"closed",dir:_.dir,onContextMenu:e=>e.preventDefault()},w,W===a8?{side:o,sideOffset:c,align:l,alignOffset:p,arrowPadding:u,collisionBoundary:d,collisionPadding:m,sticky:f,hideWhenDetached:x,avoidCollisions:g}:{},{onPlaced:()=>L(!0),ref:R,style:{display:"flex",flexDirection:"column",outline:"none",...w.style},onKeyDown:ew(w.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||q(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=A().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let a=e.target,n=t.indexOf(a);t=t.slice(n+1)}setTimeout(()=>M(t)),e.preventDefault()}})}))))))}),a9=(0,h.forwardRef)((e,t)=>{let{__scopeSelect:a,onPlaced:n,...i}=e,r=aG(a1,a),s=a4(a1,a),[o,c]=(0,h.useState)(null),[l,p]=(0,h.useState)(null),u=b(t,e=>p(e)),d=a$(a),m=(0,h.useRef)(!1),f=(0,h.useRef)(!0),{viewport:x,selectedItem:g,selectedItemText:y,focusSelectedItem:w}=s,_=(0,h.useCallback)(()=>{if(r.trigger&&r.valueNode&&o&&l&&x&&g&&y){let e=r.trigger.getBoundingClientRect(),t=l.getBoundingClientRect(),a=r.valueNode.getBoundingClientRect(),i=y.getBoundingClientRect();if("rtl"!==r.dir){let n=i.left-t.left,r=a.left-n,s=e.left-r,c=e.width+s,l=Math.max(c,t.width),p=ey(r,[10,window.innerWidth-10-l]);o.style.minWidth=c+"px",o.style.left=p+"px"}else{let n=t.right-i.right,r=window.innerWidth-a.right-n,s=window.innerWidth-e.right-r,c=e.width+s,l=Math.max(c,t.width),p=ey(r,[10,window.innerWidth-10-l]);o.style.minWidth=c+"px",o.style.right=p+"px"}let s=d(),c=window.innerHeight-20,p=x.scrollHeight,u=window.getComputedStyle(l),f=parseInt(u.borderTopWidth,10),h=parseInt(u.paddingTop,10),v=parseInt(u.borderBottomWidth,10),b=f+h+p+parseInt(u.paddingBottom,10)+v,w=Math.min(5*g.offsetHeight,b),_=window.getComputedStyle(x),k=parseInt(_.paddingTop,10),j=parseInt(_.paddingBottom,10),E=e.top+e.height/2-10,C=g.offsetHeight/2,R=f+h+(g.offsetTop+C);if(R<=E){let e=g===s[s.length-1].ref.current;o.style.bottom="0px";let t=l.clientHeight-x.offsetTop-x.offsetHeight;o.style.height=R+Math.max(c-E,C+(e?j:0)+t+v)+"px"}else{let e=g===s[0].ref.current;o.style.top="0px";let t=Math.max(E,f+x.offsetTop+(e?k:0)+C);o.style.height=t+(b-R)+"px",x.scrollTop=R-E+x.offsetTop}o.style.margin="10px 0",o.style.minHeight=w+"px",o.style.maxHeight=c+"px",null==n||n(),requestAnimationFrame(()=>m.current=!0)}},[d,r.trigger,r.valueNode,o,l,x,g,y,r.dir,n]);eq(()=>_(),[_]);let[k,j]=(0,h.useState)();eq(()=>{l&&j(window.getComputedStyle(l).zIndex)},[l]);let E=(0,h.useCallback)(e=>{e&&!0===f.current&&(_(),null==w||w(),f.current=!1)},[_,w]);return(0,h.createElement)(a5,{scope:a,contentWrapper:o,shouldExpandOnScrollRef:m,onScrollButtonChange:E},(0,h.createElement)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:k}},(0,h.createElement)(ex.div,v({},i,{ref:u,style:{boxSizing:"border-box",maxHeight:"100%",...i.style}}))))}),a8=(0,h.forwardRef)((e,t)=>{let{__scopeSelect:a,align:n="start",collisionPadding:i=10,...r}=e,s=aV(a);return(0,h.createElement)(tQ,v({},s,r,{ref:t,align:n,collisionPadding:i,style:{boxSizing:"border-box",...r.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}}))}),[a5,a7]=aW(a1,{}),ne="SelectViewport",nt=(0,h.forwardRef)((e,t)=>{let{__scopeSelect:a,...n}=e,i=a4(ne,a),r=a7(ne,a),s=b(t,i.onViewportChange),o=(0,h.useRef)(0);return(0,h.createElement)(h.Fragment,null,(0,h.createElement)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"}}),(0,h.createElement)(aB.Slot,{scope:a},(0,h.createElement)(ex.div,v({"data-radix-select-viewport":"",role:"presentation"},n,{ref:s,style:{position:"relative",flex:1,overflow:"auto",...n.style},onScroll:ew(n.onScroll,e=>{let t=e.currentTarget,{contentWrapper:a,shouldExpandOnScrollRef:n}=r;if(null!=n&&n.current&&a){let e=Math.abs(o.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,i=Math.max(parseFloat(a.style.minHeight),parseFloat(a.style.height));if(i<n){let r=i+e,s=Math.min(n,r),o=r-s;a.style.height=s+"px","0px"===a.style.bottom&&(t.scrollTop=o>0?o:0,a.style.justifyContent="flex-end")}}}o.current=t.scrollTop})}))))}),[na,nn]=aW("SelectGroup"),ni=(0,h.forwardRef)((e,t)=>{let{__scopeSelect:a,...n}=e,i=nn("SelectLabel",a);return(0,h.createElement)(ex.div,v({id:i.id},n,{ref:t}))}),nr="SelectItem",[ns,no]=aW(nr),nc=(0,h.forwardRef)((e,t)=>{let{__scopeSelect:a,value:n,disabled:i=!1,textValue:r,...s}=e,o=aG(nr,a),c=a4(nr,a),l=o.value===n,[p,u]=(0,h.useState)(null!=r?r:""),[d,m]=(0,h.useState)(!1),f=b(t,e=>{var t;return null===(t=c.itemRefCallback)||void 0===t?void 0:t.call(c,e,n,i)}),x=eZ(),g=()=>{i||(o.onValueChange(n),o.onOpenChange(!1))};if(""===n)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,h.createElement)(ns,{scope:a,value:n,disabled:i,textId:x,isSelected:l,onItemTextChange:(0,h.useCallback)(e=>{u(t=>{var a;return t||(null!==(a=null==e?void 0:e.textContent)&&void 0!==a?a:"").trim()})},[])},(0,h.createElement)(aB.ItemSlot,{scope:a,value:n,disabled:i,textValue:p},(0,h.createElement)(ex.div,v({role:"option","aria-labelledby":x,"data-highlighted":d?"":void 0,"aria-selected":l&&d,"data-state":l?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1},s,{ref:f,onFocus:ew(s.onFocus,()=>m(!0)),onBlur:ew(s.onBlur,()=>m(!1)),onPointerUp:ew(s.onPointerUp,g),onPointerMove:ew(s.onPointerMove,e=>{if(i){var t;null===(t=c.onItemLeave)||void 0===t||t.call(c)}else e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:ew(s.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null===(t=c.onItemLeave)||void 0===t||t.call(c)}}),onKeyDown:ew(s.onKeyDown,e=>{var t;(null===(t=c.searchRef)||void 0===t?void 0:t.current)!==""&&" "===e.key||(aI.includes(e.key)&&g()," "===e.key&&e.preventDefault())})}))))}),nl="SelectItemText",np=(0,h.forwardRef)((e,t)=>{let{__scopeSelect:a,className:n,style:i,...r}=e,s=aG(nl,a),o=a4(nl,a),c=no(nl,a),l=aY(nl,a),[p,u]=(0,h.useState)(null),d=b(t,e=>u(e),c.onItemTextChange,e=>{var t;return null===(t=o.itemTextRefCallback)||void 0===t?void 0:t.call(o,e,c.value,c.disabled)}),m=null==p?void 0:p.textContent,f=(0,h.useMemo)(()=>(0,h.createElement)("option",{key:c.value,value:c.value,disabled:c.disabled},m),[c.disabled,c.value,m]),{onNativeOptionAdd:x,onNativeOptionRemove:g}=l;return eq(()=>(x(f),()=>g(f)),[x,g,f]),(0,h.createElement)(h.Fragment,null,(0,h.createElement)(ex.span,v({id:c.textId},r,{ref:d})),c.isSelected&&s.valueNode&&!s.valueNodeHasChildren?(0,eh.createPortal)(r.children,s.valueNode):null)}),nu=(0,h.forwardRef)((e,t)=>{let{__scopeSelect:a,...n}=e;return no("SelectItemIndicator",a).isSelected?(0,h.createElement)(ex.span,v({"aria-hidden":!0},n,{ref:t})):null}),nd="SelectScrollUpButton",nm=(0,h.forwardRef)((e,t)=>{let a=a4(nd,e.__scopeSelect),n=a7(nd,e.__scopeSelect),[i,r]=(0,h.useState)(!1),s=b(t,n.onScrollButtonChange);return eq(()=>{if(a.viewport&&a.isPositioned){let t=a.viewport;function e(){r(t.scrollTop>0)}return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[a.viewport,a.isPositioned]),i?(0,h.createElement)(nx,v({},e,{ref:s,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=a;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}})):null}),nf="SelectScrollDownButton",nh=(0,h.forwardRef)((e,t)=>{let a=a4(nf,e.__scopeSelect),n=a7(nf,e.__scopeSelect),[i,r]=(0,h.useState)(!1),s=b(t,n.onScrollButtonChange);return eq(()=>{if(a.viewport&&a.isPositioned){let t=a.viewport;function e(){let e=t.scrollHeight-t.clientHeight;r(Math.ceil(t.scrollTop)<e)}return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[a.viewport,a.isPositioned]),i?(0,h.createElement)(nx,v({},e,{ref:s,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=a;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}})):null}),nx=(0,h.forwardRef)((e,t)=>{let{__scopeSelect:a,onAutoScroll:n,...i}=e,r=a4("SelectScrollButton",a),s=(0,h.useRef)(null),o=a$(a),c=(0,h.useCallback)(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return(0,h.useEffect)(()=>()=>c(),[c]),eq(()=>{var e;let t=o().find(e=>e.ref.current===document.activeElement);null==t||null===(e=t.ref.current)||void 0===e||e.scrollIntoView({block:"nearest"})},[o]),(0,h.createElement)(ex.div,v({"aria-hidden":!0},i,{ref:t,style:{flexShrink:0,...i.style},onPointerDown:ew(i.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(n,50))}),onPointerMove:ew(i.onPointerMove,()=>{var e;null===(e=r.onItemLeave)||void 0===e||e.call(r),null===s.current&&(s.current=window.setInterval(n,50))}),onPointerLeave:ew(i.onPointerLeave,()=>{c()})}))}),nv=(0,h.forwardRef)((e,t)=>{let{__scopeSelect:a,...n}=e;return(0,h.createElement)(ex.div,v({"aria-hidden":!0},n,{ref:t}))});function ng(e){return""===e||void 0===e}let nb=(0,h.forwardRef)((e,t)=>{let{value:a,...n}=e,i=(0,h.useRef)(null),r=b(t,i),s=function(e){let t=(0,h.useRef)({value:e,previous:e});return(0,h.useMemo)(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(a);return(0,h.useEffect)(()=>{let e=i.current,t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(s!==a&&t){let n=new Event("change",{bubbles:!0});t.call(e,a),e.dispatchEvent(n)}},[s,a]),(0,h.createElement)(t8,{asChild:!0},(0,h.createElement)("select",v({},n,{ref:r,defaultValue:a})))});function ny(e){let t=ej(e),a=(0,h.useRef)(""),n=(0,h.useRef)(0),i=(0,h.useCallback)(e=>{let i=a.current+e;t(i),function e(t){a.current=t,window.clearTimeout(n.current),""!==t&&(n.current=window.setTimeout(()=>e(""),1e3))}(i)},[t]),r=(0,h.useCallback)(()=>{a.current="",window.clearTimeout(n.current)},[]);return(0,h.useEffect)(()=>()=>window.clearTimeout(n.current),[]),[a,i,r]}function nw(e,t,a){var n;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,r=(n=Math.max(a?e.indexOf(a):-1,0),e.map((t,a)=>e[(n+a)%e.length]));1===i.length&&(r=r.filter(e=>e!==a));let s=r.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return s!==a?s:void 0}nb.displayName="BubbleSelect";let n_=e=>(0,h.createElement)(t6,v({asChild:!0},e));/**
 * @license lucide-react v0.331.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var nk={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.331.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let nj=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),nE=(e,t)=>{let a=(0,h.forwardRef)(({color:a="currentColor",size:n=24,strokeWidth:i=2,absoluteStrokeWidth:r,className:s="",children:o,...c},l)=>(0,h.createElement)("svg",{ref:l,...nk,width:n,height:n,stroke:a,strokeWidth:r?24*Number(i)/Number(n):i,className:["lucide",`lucide-${nj(e)}`,s].join(" "),...c},[...t.map(([e,t])=>(0,h.createElement)(e,t)),...Array.isArray(o)?o:[o]]));return a.displayName=`${e}`,a},nC=nE("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),nR=nE("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]),nS=nE("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]),nO=e=>{let{__scopeSelect:t,children:a,open:n,defaultOpen:i,onOpenChange:r,value:s,defaultValue:o,onValueChange:c,dir:l,name:p,autoComplete:u,disabled:d,required:m}=e,f=aV(t),[x,v]=(0,h.useState)(null),[g,b]=(0,h.useState)(null),[y,w]=(0,h.useState)(!1),_=function(e){let t=(0,h.useContext)(ek);return e||t||"ltr"}(l),[k=!1,j]=t9({prop:n,defaultProp:i,onChange:r}),[E,C]=t9({prop:s,defaultProp:o,onChange:c}),R=(0,h.useRef)(null),S=!x||!!x.closest("form"),[O,T]=(0,h.useState)(new Set),N=Array.from(O).map(e=>e.props.value).join(";");return(0,h.createElement)(t4,f,(0,h.createElement)(aK,{required:m,scope:t,trigger:x,onTriggerChange:v,valueNode:g,onValueNodeChange:b,valueNodeHasChildren:y,onValueNodeHasChildrenChange:w,contentId:eZ(),value:E,onValueChange:C,open:k,onOpenChange:j,dir:_,triggerPointerDownPosRef:R,disabled:d},(0,h.createElement)(aB.Provider,{scope:t},(0,h.createElement)(aX,{scope:e.__scopeSelect,onNativeOptionAdd:(0,h.useCallback)(e=>{T(t=>new Set(t).add(e))},[]),onNativeOptionRemove:(0,h.useCallback)(e=>{T(t=>{let a=new Set(t);return a.delete(e),a})},[])},a)),S?(0,h.createElement)(nb,{key:N,"aria-hidden":!0,required:m,tabIndex:-1,name:p,autoComplete:u,value:E,onChange:e=>C(e.target.value),disabled:d},void 0===E?(0,h.createElement)("option",{value:""}):null,Array.from(O)):null))},nT=h.forwardRef(({className:e,children:t,...a},n)=>(0,f.jsxs)(aJ,{ref:n,className:eo("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...a,children:[t,f.jsx(a0,{asChild:!0,children:f.jsx(nC,{className:"h-4 w-4 opacity-50"})})]}));nT.displayName=aJ.displayName;let nN=h.forwardRef(({className:e,...t},a)=>f.jsx(nm,{ref:a,className:eo("flex cursor-default items-center justify-center py-1",e),...t,children:f.jsx(nR,{className:"h-4 w-4"})}));nN.displayName=nm.displayName;let nA=h.forwardRef(({className:e,...t},a)=>f.jsx(nh,{ref:a,className:eo("flex cursor-default items-center justify-center py-1",e),...t,children:f.jsx(nC,{className:"h-4 w-4"})}));nA.displayName=nh.displayName;let nP=h.forwardRef(({className:e,children:t,position:a="popper",...n},i)=>f.jsx(n_,{children:(0,f.jsxs)(a2,{ref:i,className:eo("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...n,children:[f.jsx(nN,{}),f.jsx(nt,{className:eo("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),f.jsx(nA,{})]})}));nP.displayName=a2.displayName,h.forwardRef(({className:e,...t},a)=>f.jsx(ni,{ref:a,className:eo("px-2 py-1.5 text-sm font-semibold",e),...t})).displayName=ni.displayName;let nL=h.forwardRef(({className:e,children:t,...a},n)=>(0,f.jsxs)(nc,{ref:n,className:eo("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[f.jsx("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:f.jsx(nu,{children:f.jsx(nS,{className:"h-4 w-4"})})}),f.jsx(np,{children:t})]}));nL.displayName=nc.displayName,h.forwardRef(({className:e,...t},a)=>f.jsx(nv,{ref:a,className:eo("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=nv.displayName;var nU=a(30290);function nM(e,t){return function(){return e.apply(t,arguments)}}let{toString:nz}=Object.prototype,{getPrototypeOf:nF}=Object,nD=(r=Object.create(null),e=>{let t=nz.call(e);return r[t]||(r[t]=t.slice(8,-1).toLowerCase())}),nI=e=>(e=e.toLowerCase(),t=>nD(t)===e),nq=e=>t=>typeof t===e,{isArray:nB}=Array,n$=nq("undefined"),nZ=nI("ArrayBuffer"),nW=nq("string"),nH=nq("function"),nV=nq("number"),nK=e=>null!==e&&"object"==typeof e,nG=e=>{if("object"!==nD(e))return!1;let t=nF(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)},nX=nI("Date"),nY=nI("File"),nJ=nI("Blob"),nQ=nI("FileList"),n0=nI("URLSearchParams");function n1(e,t,{allOwnKeys:a=!1}={}){let n,i;if(null!=e){if("object"!=typeof e&&(e=[e]),nB(e))for(n=0,i=e.length;n<i;n++)t.call(null,e[n],n,e);else{let i;let r=a?Object.getOwnPropertyNames(e):Object.keys(e),s=r.length;for(n=0;n<s;n++)i=r[n],t.call(null,e[i],i,e)}}}function n2(e,t){let a;t=t.toLowerCase();let n=Object.keys(e),i=n.length;for(;i-- >0;)if(t===(a=n[i]).toLowerCase())return a;return null}let n3="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:global,n4=e=>!n$(e)&&e!==n3,n6=(s="undefined"!=typeof Uint8Array&&nF(Uint8Array),e=>s&&e instanceof s),n9=nI("HTMLFormElement"),n8=(({hasOwnProperty:e})=>(t,a)=>e.call(t,a))(Object.prototype),n5=nI("RegExp"),n7=(e,t)=>{let a=Object.getOwnPropertyDescriptors(e),n={};n1(a,(a,i)=>{let r;!1!==(r=t(a,i,e))&&(n[i]=r||a)}),Object.defineProperties(e,n)},ie="abcdefghijklmnopqrstuvwxyz",it="0123456789",ia={DIGIT:it,ALPHA:ie,ALPHA_DIGIT:ie+ie.toUpperCase()+it},ii=nI("AsyncFunction"),ir={isArray:nB,isArrayBuffer:nZ,isBuffer:function(e){return null!==e&&!n$(e)&&null!==e.constructor&&!n$(e.constructor)&&nH(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"==typeof FormData&&e instanceof FormData||nH(e.append)&&("formdata"===(t=nD(e))||"object"===t&&nH(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&nZ(e.buffer)},isString:nW,isNumber:nV,isBoolean:e=>!0===e||!1===e,isObject:nK,isPlainObject:nG,isUndefined:n$,isDate:nX,isFile:nY,isBlob:nJ,isRegExp:n5,isFunction:nH,isStream:e=>nK(e)&&nH(e.pipe),isURLSearchParams:n0,isTypedArray:n6,isFileList:nQ,forEach:n1,merge:function e(){let{caseless:t}=n4(this)&&this||{},a={},n=(n,i)=>{let r=t&&n2(a,i)||i;nG(a[r])&&nG(n)?a[r]=e(a[r],n):nG(n)?a[r]=e({},n):nB(n)?a[r]=n.slice():a[r]=n};for(let e=0,t=arguments.length;e<t;e++)arguments[e]&&n1(arguments[e],n);return a},extend:(e,t,a,{allOwnKeys:n}={})=>(n1(t,(t,n)=>{a&&nH(t)?e[n]=nM(t,a):e[n]=t},{allOwnKeys:n}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,a,n)=>{e.prototype=Object.create(t.prototype,n),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),a&&Object.assign(e.prototype,a)},toFlatObject:(e,t,a,n)=>{let i,r,s;let o={};if(t=t||{},null==e)return t;do{for(r=(i=Object.getOwnPropertyNames(e)).length;r-- >0;)s=i[r],(!n||n(s,e,t))&&!o[s]&&(t[s]=e[s],o[s]=!0);e=!1!==a&&nF(e)}while(e&&(!a||a(e,t))&&e!==Object.prototype);return t},kindOf:nD,kindOfTest:nI,endsWith:(e,t,a)=>{e=String(e),(void 0===a||a>e.length)&&(a=e.length),a-=t.length;let n=e.indexOf(t,a);return -1!==n&&n===a},toArray:e=>{if(!e)return null;if(nB(e))return e;let t=e.length;if(!nV(t))return null;let a=Array(t);for(;t-- >0;)a[t]=e[t];return a},forEachEntry:(e,t)=>{let a;let n=(e&&e[Symbol.iterator]).call(e);for(;(a=n.next())&&!a.done;){let n=a.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let a;let n=[];for(;null!==(a=e.exec(t));)n.push(a);return n},isHTMLForm:n9,hasOwnProperty:n8,hasOwnProp:n8,reduceDescriptors:n7,freezeMethods:e=>{n7(e,(t,a)=>{if(nH(e)&&-1!==["arguments","caller","callee"].indexOf(a))return!1;if(nH(e[a])){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+a+"'")})}})},toObjectSet:(e,t)=>{let a={};return(e=>{e.forEach(e=>{a[e]=!0})})(nB(e)?e:String(e).split(t)),a},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,t,a){return t.toUpperCase()+a}),noop:()=>{},toFiniteNumber:(e,t)=>Number.isFinite(e=+e)?e:t,findKey:n2,global:n3,isContextDefined:n4,ALPHABET:ia,generateString:(e=16,t=ia.ALPHA_DIGIT)=>{let a="",{length:n}=t;for(;e--;)a+=t[Math.random()*n|0];return a},isSpecCompliantForm:function(e){return!!(e&&nH(e.append)&&"FormData"===e[Symbol.toStringTag]&&e[Symbol.iterator])},toJSONObject:e=>{let t=Array(10),a=(e,n)=>{if(nK(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[n]=e;let i=nB(e)?[]:{};return n1(e,(e,t)=>{let r=a(e,n+1);n$(r)||(i[t]=r)}),t[n]=void 0,i}}return e};return a(e,0)},isAsyncFn:ii,isThenable:e=>e&&(nK(e)||nH(e))&&nH(e.then)&&nH(e.catch)};function is(e,t,a,n,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),a&&(this.config=a),n&&(this.request=n),i&&(this.response=i)}ir.inherits(is,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:ir.toJSONObject(this.config),code:this.code,status:this.response&&this.response.status?this.response.status:null}}});let io=is.prototype,ic={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{ic[e]={value:e}}),Object.defineProperties(is,ic),Object.defineProperty(io,"isAxiosError",{value:!0}),is.from=(e,t,a,n,i,r)=>{let s=Object.create(io);return ir.toFlatObject(e,s,function(e){return e!==Error.prototype},e=>"isAxiosError"!==e),is.call(s,e.message,t,a,n,i),s.cause=e,s.name=e.name,r&&Object.assign(s,r),s};var il=a(20102);function ip(e){return ir.isPlainObject(e)||ir.isArray(e)}function iu(e){return ir.endsWith(e,"[]")?e.slice(0,-2):e}function id(e,t,a){return e?e.concat(t).map(function(e,t){return e=iu(e),!a&&t?"["+e+"]":e}).join(a?".":""):t}let im=ir.toFlatObject(ir,{},null,function(e){return/^is[A-Z]/.test(e)}),ih=function(e,t,a){if(!ir.isObject(e))throw TypeError("target must be an object");t=t||new(il||FormData);let n=(a=ir.toFlatObject(a,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,t){return!ir.isUndefined(t[e])})).metaTokens,i=a.visitor||l,r=a.dots,s=a.indexes,o=(a.Blob||"undefined"!=typeof Blob&&Blob)&&ir.isSpecCompliantForm(t);if(!ir.isFunction(i))throw TypeError("visitor must be a function");function c(e){if(null===e)return"";if(ir.isDate(e))return e.toISOString();if(!o&&ir.isBlob(e))throw new is("Blob is not supported. Use a Buffer instead.");return ir.isArrayBuffer(e)||ir.isTypedArray(e)?o&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function l(e,a,i){let o=e;if(e&&!i&&"object"==typeof e){if(ir.endsWith(a,"{}"))a=n?a:a.slice(0,-2),e=JSON.stringify(e);else{var l;if(ir.isArray(e)&&(l=e,ir.isArray(l)&&!l.some(ip))||(ir.isFileList(e)||ir.endsWith(a,"[]"))&&(o=ir.toArray(e)))return a=iu(a),o.forEach(function(e,n){ir.isUndefined(e)||null===e||t.append(!0===s?id([a],n,r):null===s?a:a+"[]",c(e))}),!1}}return!!ip(e)||(t.append(id(i,a,r),c(e)),!1)}let p=[],u=Object.assign(im,{defaultVisitor:l,convertValue:c,isVisitable:ip});if(!ir.isObject(e))throw TypeError("data must be an object");return function e(a,n){if(!ir.isUndefined(a)){if(-1!==p.indexOf(a))throw Error("Circular reference detected in "+n.join("."));p.push(a),ir.forEach(a,function(a,r){!0===(!(ir.isUndefined(a)||null===a)&&i.call(t,a,ir.isString(r)?r.trim():r,n,u))&&e(a,n?n.concat(r):[r])}),p.pop()}}(e),t};function ix(e){let t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\x00"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return t[e]})}function iv(e,t){this._pairs=[],e&&ih(e,this,t)}let ig=iv.prototype;function ib(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function iy(e,t,a){let n;if(!t)return e;let i=a&&a.encode||ib,r=a&&a.serialize;if(n=r?r(t,a):ir.isURLSearchParams(t)?t.toString():new iv(t,a).toString(i)){let t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+n}return e}ig.append=function(e,t){this._pairs.push([e,t])},ig.toString=function(e){let t=e?function(t){return e.call(this,t,ix)}:ix;return this._pairs.map(function(e){return t(e[0])+"="+t(e[1])},"").join("&")};class iw{constructor(){this.handlers=[]}use(e,t,a){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!a&&a.synchronous,runWhen:a?a.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){ir.forEach(this.handlers,function(t){null!==t&&e(t)})}}let i_={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},ik={isNode:!0,classes:{URLSearchParams:a(57310).URLSearchParams,FormData:il,Blob:"undefined"!=typeof Blob&&Blob||null},protocols:["http","https","file","data"]},ij=!1,iE=(o="undefined"!=typeof navigator&&navigator.product,ij&&0>["ReactNative","NativeScript","NS"].indexOf(o)),iC="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,iR={...m,...ik},iS=function(e){if(ir.isFormData(e)&&ir.isFunction(e.entries)){let t={};return ir.forEachEntry(e,(e,a)=>{!function e(t,a,n,i){let r=t[i++];if("__proto__"===r)return!0;let s=Number.isFinite(+r),o=i>=t.length;return(r=!r&&ir.isArray(n)?n.length:r,o)?ir.hasOwnProp(n,r)?n[r]=[n[r],a]:n[r]=a:(n[r]&&ir.isObject(n[r])||(n[r]=[]),e(t,a,n[r],i)&&ir.isArray(n[r])&&(n[r]=function(e){let t,a;let n={},i=Object.keys(e),r=i.length;for(t=0;t<r;t++)n[a=i[t]]=e[a];return n}(n[r]))),!s}(ir.matchAll(/\w+|\[(\w*)]/g,e).map(e=>"[]"===e[0]?"":e[1]||e[0]),a,t,0)}),t}return null},iO={transitional:i_,adapter:["xhr","http"],transformRequest:[function(e,t){let a;let n=t.getContentType()||"",i=n.indexOf("application/json")>-1,r=ir.isObject(e);if(r&&ir.isHTMLForm(e)&&(e=new FormData(e)),ir.isFormData(e))return i?JSON.stringify(iS(e)):e;if(ir.isArrayBuffer(e)||ir.isBuffer(e)||ir.isStream(e)||ir.isFile(e)||ir.isBlob(e))return e;if(ir.isArrayBufferView(e))return e.buffer;if(ir.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();if(r){if(n.indexOf("application/x-www-form-urlencoded")>-1){var s,o;return(s=e,o=this.formSerializer,ih(s,new iR.classes.URLSearchParams,Object.assign({visitor:function(e,t,a,n){return iR.isNode&&ir.isBuffer(e)?(this.append(t,e.toString("base64")),!1):n.defaultVisitor.apply(this,arguments)}},o))).toString()}if((a=ir.isFileList(e))||n.indexOf("multipart/form-data")>-1){let t=this.env&&this.env.FormData;return ih(a?{"files[]":e}:e,t&&new t,this.formSerializer)}}return r||i?(t.setContentType("application/json",!1),function(e,t,a){if(ir.isString(e))try{return(0,JSON.parse)(e),ir.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(0,JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){let t=this.transitional||iO.transitional,a=t&&t.forcedJSONParsing,n="json"===this.responseType;if(e&&ir.isString(e)&&(a&&!this.responseType||n)){let a=t&&t.silentJSONParsing;try{return JSON.parse(e)}catch(e){if(!a&&n){if("SyntaxError"===e.name)throw is.from(e,is.ERR_BAD_RESPONSE,this,null,this.response);throw e}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:iR.classes.FormData,Blob:iR.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};ir.forEach(["delete","get","head","post","put","patch"],e=>{iO.headers[e]={}});let iT=ir.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),iN=e=>{let t,a,n;let i={};return e&&e.split("\n").forEach(function(e){n=e.indexOf(":"),t=e.substring(0,n).trim().toLowerCase(),a=e.substring(n+1).trim(),!t||i[t]&&iT[t]||("set-cookie"===t?i[t]?i[t].push(a):i[t]=[a]:i[t]=i[t]?i[t]+", "+a:a)}),i},iA=Symbol("internals");function iP(e){return e&&String(e).trim().toLowerCase()}function iL(e){return!1===e||null==e?e:ir.isArray(e)?e.map(iL):String(e)}let iU=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function iM(e,t,a,n,i){if(ir.isFunction(n))return n.call(this,t,a);if(i&&(t=a),ir.isString(t)){if(ir.isString(n))return -1!==t.indexOf(n);if(ir.isRegExp(n))return n.test(t)}}class iz{constructor(e){e&&this.set(e)}set(e,t,a){let n=this;function i(e,t,a){let i=iP(t);if(!i)throw Error("header name must be a non-empty string");let r=ir.findKey(n,i);r&&void 0!==n[r]&&!0!==a&&(void 0!==a||!1===n[r])||(n[r||t]=iL(e))}let r=(e,t)=>ir.forEach(e,(e,a)=>i(e,a,t));return ir.isPlainObject(e)||e instanceof this.constructor?r(e,t):ir.isString(e)&&(e=e.trim())&&!iU(e)?r(iN(e),t):null!=e&&i(t,e,a),this}get(e,t){if(e=iP(e)){let a=ir.findKey(this,e);if(a){let e=this[a];if(!t)return e;if(!0===t)return function(e){let t;let a=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;for(;t=n.exec(e);)a[t[1]]=t[2];return a}(e);if(ir.isFunction(t))return t.call(this,e,a);if(ir.isRegExp(t))return t.exec(e);throw TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=iP(e)){let a=ir.findKey(this,e);return!!(a&&void 0!==this[a]&&(!t||iM(this,this[a],a,t)))}return!1}delete(e,t){let a=this,n=!1;function i(e){if(e=iP(e)){let i=ir.findKey(a,e);i&&(!t||iM(a,a[i],i,t))&&(delete a[i],n=!0)}}return ir.isArray(e)?e.forEach(i):i(e),n}clear(e){let t=Object.keys(this),a=t.length,n=!1;for(;a--;){let i=t[a];(!e||iM(this,this[i],i,e,!0))&&(delete this[i],n=!0)}return n}normalize(e){let t=this,a={};return ir.forEach(this,(n,i)=>{let r=ir.findKey(a,i);if(r){t[r]=iL(n),delete t[i];return}let s=e?i.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,a)=>t.toUpperCase()+a):String(i).trim();s!==i&&delete t[i],t[s]=iL(n),a[s]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){let t=Object.create(null);return ir.forEach(this,(a,n)=>{null!=a&&!1!==a&&(t[n]=e&&ir.isArray(a)?a.join(", "):a)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,t])=>e+": "+t).join("\n")}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){let a=new this(e);return t.forEach(e=>a.set(e)),a}static accessor(e){let t=(this[iA]=this[iA]={accessors:{}}).accessors,a=this.prototype;function n(e){let n=iP(e);t[n]||(function(e,t){let a=ir.toCamelCase(" "+t);["get","set","has"].forEach(n=>{Object.defineProperty(e,n+a,{value:function(e,a,i){return this[n].call(this,t,e,a,i)},configurable:!0})})}(a,e),t[n]=!0)}return ir.isArray(e)?e.forEach(n):n(e),this}}function iF(e,t){let a=this||iO,n=t||a,i=iz.from(n.headers),r=n.data;return ir.forEach(e,function(e){r=e.call(a,r,i.normalize(),t?t.status:void 0)}),i.normalize(),r}function iD(e){return!!(e&&e.__CANCEL__)}function iI(e,t,a){is.call(this,null==e?"canceled":e,is.ERR_CANCELED,t,a),this.name="CanceledError"}function iq(e,t,a){let n=a.config.validateStatus;!a.status||!n||n(a.status)?e(a):t(new is("Request failed with status code "+a.status,[is.ERR_BAD_REQUEST,is.ERR_BAD_RESPONSE][Math.floor(a.status/100)-4],a.config,a.request,a))}function iB(e,t){return e&&!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t)?t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e:t}iz.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),ir.reduceDescriptors(iz.prototype,({value:e},t)=>{let a=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(e){this[a]=e}}}),ir.freezeMethods(iz),ir.inherits(iI,is,{__CANCEL__:!0});var i$=a(86886),iZ=a(13685),iW=a(95687),iH=a(73837),iV=a(42136),iK=a(59796);let iG="1.6.7";function iX(e){let t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}let iY=/^(?:([^;]+);)?(?:[^;]+;)?(base64|),([\s\S]*)$/;var iJ=a(12781);let iQ=function(e,t){let a=0,n=1e3/t,i=null;return function(t,r){let s=Date.now();if(t||s-a>n)return i&&(clearTimeout(i),i=null),a=s,e.apply(null,r);i||(i=setTimeout(()=>(i=null,a=Date.now(),e.apply(null,r)),n-(s-a)))}},i0=function(e,t){let a;let n=Array(e=e||10),i=Array(e),r=0,s=0;return t=void 0!==t?t:1e3,function(o){let c=Date.now(),l=i[s];a||(a=c),n[r]=o,i[r]=c;let p=s,u=0;for(;p!==r;)u+=n[p++],p%=e;if((r=(r+1)%e)===s&&(s=(s+1)%e),c-a<t)return;let d=l&&c-l;return d?Math.round(1e3*u/d):void 0}},i1=Symbol("internals");class i2 extends iJ.Transform{constructor(e){super({readableHighWaterMark:(e=ir.toFlatObject(e,{maxRate:0,chunkSize:65536,minChunkSize:100,timeWindow:500,ticksRate:2,samplesCount:15},null,(e,t)=>!ir.isUndefined(t[e]))).chunkSize});let t=this,a=this[i1]={length:e.length,timeWindow:e.timeWindow,ticksRate:e.ticksRate,chunkSize:e.chunkSize,maxRate:e.maxRate,minChunkSize:e.minChunkSize,bytesSeen:0,isCaptured:!1,notifiedBytesLoaded:0,ts:Date.now(),bytes:0,onReadCallback:null},n=i0(a.ticksRate*e.samplesCount,a.timeWindow);this.on("newListener",e=>{"progress"!==e||a.isCaptured||(a.isCaptured=!0)});let i=0;a.updateProgress=iQ(function(){let e=a.length,r=a.bytesSeen,s=r-i;if(!s||t.destroyed)return;let o=n(s);i=r,process.nextTick(()=>{t.emit("progress",{loaded:r,total:e,progress:e?r/e:void 0,bytes:s,rate:o||void 0,estimated:o&&e&&r<=e?(e-r)/o:void 0})})},a.ticksRate);let r=()=>{a.updateProgress(!0)};this.once("end",r),this.once("error",r)}_read(e){let t=this[i1];return t.onReadCallback&&t.onReadCallback(),super._read(e)}_transform(e,t,a){let n=this,i=this[i1],r=i.maxRate,s=this.readableHighWaterMark,o=i.timeWindow,c=r/(1e3/o),l=!1!==i.minChunkSize?Math.max(i.minChunkSize,.01*c):0,p=(e,t)=>{let a;let p=Buffer.byteLength(e),u=null,d=s,m=0;if(r){let e=Date.now();(!i.ts||(m=e-i.ts)>=o)&&(i.ts=e,a=c-i.bytes,i.bytes=a<0?-a:0,m=0),a=c-i.bytes}if(r){if(a<=0)return setTimeout(()=>{t(null,e)},o-m);a<d&&(d=a)}d&&p>d&&p-d>l&&(u=e.subarray(d),e=e.subarray(0,d)),function(e,t){let a=Buffer.byteLength(e);i.bytesSeen+=a,i.bytes+=a,i.isCaptured&&i.updateProgress(),n.push(e)?process.nextTick(t):i.onReadCallback=()=>{i.onReadCallback=null,process.nextTick(t)}}(e,u?()=>{process.nextTick(t,null,u)}:t)};p(e,function e(t,n){if(t)return a(t);n?p(n,e):a(null)})}setLength(e){return this[i1].length=+e,this}}var i3=a(82361);let{asyncIterator:i4}=Symbol,i6=async function*(e){e.stream?yield*e.stream():e.arrayBuffer?yield await e.arrayBuffer():e[i4]?yield*e[i4]():yield e},i9=ir.ALPHABET.ALPHA_DIGIT+"-_",i8=new iH.TextEncoder,i5=i8.encode("\r\n");class i7{constructor(e,t){let{escapeName:a}=this.constructor,n=ir.isString(t),i=`Content-Disposition: form-data; name="${a(e)}"${!n&&t.name?`; filename="${a(t.name)}"`:""}\r
`;n?t=i8.encode(String(t).replace(/\r?\n|\r\n?/g,"\r\n")):i+=`Content-Type: ${t.type||"application/octet-stream"}\r
`,this.headers=i8.encode(i+"\r\n"),this.contentLength=n?t.byteLength:t.size,this.size=this.headers.byteLength+this.contentLength+2,this.name=e,this.value=t}async *encode(){yield this.headers;let{value:e}=this;ir.isTypedArray(e)?yield e:yield*i6(e),yield i5}static escapeName(e){return String(e).replace(/[\r\n"]/g,e=>({"\r":"%0D","\n":"%0A",'"':"%22"})[e])}}let re=(e,t,a)=>{let{tag:n="form-data-boundary",size:i=25,boundary:r=n+"-"+ir.generateString(i,i9)}=a||{};if(!ir.isFormData(e))throw TypeError("FormData instance required");if(r.length<1||r.length>70)throw Error("boundary must be 10-70 characters long");let s=i8.encode("--"+r+"\r\n"),o=i8.encode("--"+r+"--\r\n\r\n"),c=o.byteLength,l=Array.from(e.entries()).map(([e,t])=>{let a=new i7(e,t);return c+=a.size,a});c+=s.byteLength*l.length;let p={"Content-Type":`multipart/form-data; boundary=${r}`};return Number.isFinite(c=ir.toFiniteNumber(c))&&(p["Content-Length"]=c),t&&t(p),iJ.Readable.from(async function*(){for(let e of l)yield s,yield*e.encode();yield o}())};class rt extends iJ.Transform{__transform(e,t,a){this.push(e),a()}_transform(e,t,a){if(0!==e.length&&(this._transform=this.__transform,120!==e[0])){let e=Buffer.alloc(2);e[0]=120,e[1]=156,this.push(e,t)}this.__transform(e,t,a)}}let ra=(e,t)=>ir.isAsyncFn(e)?function(...a){let n=a.pop();e.apply(this,a).then(e=>{try{t?n(null,...t(e)):n(null,e)}catch(e){n(e)}},n)}:e,rn={flush:iK.constants.Z_SYNC_FLUSH,finishFlush:iK.constants.Z_SYNC_FLUSH},ri={flush:iK.constants.BROTLI_OPERATION_FLUSH,finishFlush:iK.constants.BROTLI_OPERATION_FLUSH},rr=ir.isFunction(iK.createBrotliDecompress),{http:rs,https:ro}=iV,rc=/https:?/,rl=iR.protocols.map(e=>e+":");function rp(e,t){e.beforeRedirects.proxy&&e.beforeRedirects.proxy(e),e.beforeRedirects.config&&e.beforeRedirects.config(e,t)}let ru="undefined"!=typeof process&&"process"===ir.kindOf(process),rd=e=>new Promise((t,a)=>{let n,i;let r=(e,t)=>{!i&&(i=!0,n&&n(e,t))},s=e=>{r(e,!0),a(e)};e(e=>{r(e),t(e)},s,e=>n=e).catch(s)}),rm=({address:e,family:t})=>{if(!ir.isString(e))throw TypeError("address must be a string");return{address:e,family:t||(0>e.indexOf(".")?6:4)}},rf=(e,t)=>rm(ir.isObject(e)?e:{address:e,family:t}),rh=ru&&function(e){return rd(async function(t,a,n){let i,r,s,o,c,l,p,{data:u,lookup:d,family:m}=e,{responseType:f,responseEncoding:h}=e,x=e.method.toUpperCase(),v=!1;if(d){let e=ra(d,e=>ir.isArray(e)?e:[e]);d=(t,a,n)=>{e(t,a,(e,t,i)=>{if(e)return n(e);let r=ir.isArray(t)?t.map(e=>rf(e)):[rf(t,i)];a.all?n(e,r):n(e,r[0].address,r[0].family)})}}let g=new i3,b=()=>{e.cancelToken&&e.cancelToken.unsubscribe(y),e.signal&&e.signal.removeEventListener("abort",y),g.removeAllListeners()};function y(t){g.emit("abort",!t||t.type?new iI(null,e,c):t)}n((e,t)=>{o=!0,t&&(v=!0,b())}),g.once("abort",a),(e.cancelToken||e.signal)&&(e.cancelToken&&e.cancelToken.subscribe(y),e.signal&&(e.signal.aborted?y():e.signal.addEventListener("abort",y)));let w=iB(e.baseURL,e.url),_=new URL(w,"http://localhost"),k=_.protocol||rl[0];if("data:"===k){let n;if("GET"!==x)return iq(t,a,{status:405,statusText:"method not allowed",headers:{},config:e});try{n=function(e,t,a){let n=a&&a.Blob||iR.classes.Blob,i=iX(e);if(void 0===t&&n&&(t=!0),"data"===i){e=i.length?e.slice(i.length+1):e;let a=iY.exec(e);if(!a)throw new is("Invalid URL",is.ERR_INVALID_URL);let r=a[1],s=a[2],o=a[3],c=Buffer.from(decodeURIComponent(o),s?"base64":"utf8");if(t){if(!n)throw new is("Blob is not supported",is.ERR_NOT_SUPPORT);return new n([c],{type:r})}return c}throw new is("Unsupported protocol "+i,is.ERR_NOT_SUPPORT)}(e.url,"blob"===f,{Blob:e.env&&e.env.Blob})}catch(t){throw is.from(t,is.ERR_BAD_REQUEST,e)}return"text"===f?(n=n.toString(h),h&&"utf8"!==h||(n=ir.stripBOM(n))):"stream"===f&&(n=iJ.Readable.from(n)),iq(t,a,{data:n,status:200,statusText:"OK",headers:new iz,config:e})}if(-1===rl.indexOf(k))return a(new is("Unsupported protocol "+k,is.ERR_BAD_REQUEST,e));let j=iz.from(e.headers).normalize();j.set("User-Agent","axios/"+iG,!1);let E=e.onDownloadProgress,C=e.onUploadProgress,R=e.maxRate;if(ir.isSpecCompliantForm(u)){let e=j.getContentType(/boundary=([-_\w\d]{10,70})/i);u=re(u,e=>{j.set(e)},{tag:`axios-${iG}-boundary`,boundary:e&&e[1]||void 0})}else if(ir.isFormData(u)&&ir.isFunction(u.getHeaders)){if(j.set(u.getHeaders()),!j.hasContentLength())try{let e=await iH.promisify(u.getLength).call(u);Number.isFinite(e)&&e>=0&&j.setContentLength(e)}catch(e){}}else if(ir.isBlob(u))u.size&&j.setContentType(u.type||"application/octet-stream"),j.setContentLength(u.size||0),u=iJ.Readable.from(i6(u));else if(u&&!ir.isStream(u)){if(Buffer.isBuffer(u));else if(ir.isArrayBuffer(u))u=Buffer.from(new Uint8Array(u));else{if(!ir.isString(u))return a(new is("Data after transformation must be a string, an ArrayBuffer, a Buffer, or a Stream",is.ERR_BAD_REQUEST,e));u=Buffer.from(u,"utf-8")}if(j.setContentLength(u.length,!1),e.maxBodyLength>-1&&u.length>e.maxBodyLength)return a(new is("Request body larger than maxBodyLength limit",is.ERR_BAD_REQUEST,e))}let S=ir.toFiniteNumber(j.getContentLength());ir.isArray(R)?(i=R[0],r=R[1]):i=r=R,u&&(C||i)&&(ir.isStream(u)||(u=iJ.Readable.from(u,{objectMode:!1})),u=iJ.pipeline([u,new i2({length:S,maxRate:ir.toFiniteNumber(i)})],ir.noop),C&&u.on("progress",e=>{C(Object.assign(e,{upload:!0}))})),e.auth&&(s=(e.auth.username||"")+":"+(e.auth.password||"")),!s&&_.username&&(s=_.username+":"+_.password),s&&j.delete("authorization");try{l=iy(_.pathname+_.search,e.params,e.paramsSerializer).replace(/^\?/,"")}catch(n){let t=Error(n.message);return t.config=e,t.url=e.url,t.exists=!0,a(t)}j.set("Accept-Encoding","gzip, compress, deflate"+(rr?", br":""),!1);let O={path:l,method:x,headers:j.toJSON(),agents:{http:e.httpAgent,https:e.httpsAgent},auth:s,protocol:k,family:m,beforeRedirect:rp,beforeRedirects:{}};ir.isUndefined(d)||(O.lookup=d),e.socketPath?O.socketPath=e.socketPath:(O.hostname=_.hostname,O.port=_.port,function e(t,a,n){let i=a;if(!i&&!1!==i){let e=(0,i$.j)(n);e&&(i=new URL(e))}if(i){if(i.username&&(i.auth=(i.username||"")+":"+(i.password||"")),i.auth){(i.auth.username||i.auth.password)&&(i.auth=(i.auth.username||"")+":"+(i.auth.password||""));let e=Buffer.from(i.auth,"utf8").toString("base64");t.headers["Proxy-Authorization"]="Basic "+e}t.headers.host=t.hostname+(t.port?":"+t.port:"");let e=i.hostname||i.host;t.hostname=e,t.host=e,t.port=i.port,t.path=n,i.protocol&&(t.protocol=i.protocol.includes(":")?i.protocol:`${i.protocol}:`)}t.beforeRedirects.proxy=function(t){e(t,a,t.href)}}(O,e.proxy,k+"//"+_.hostname+(_.port?":"+_.port:"")+O.path));let T=rc.test(O.protocol);if(O.agent=T?e.httpsAgent:e.httpAgent,e.transport?p=e.transport:0===e.maxRedirects?p=T?iW:iZ:(e.maxRedirects&&(O.maxRedirects=e.maxRedirects),e.beforeRedirect&&(O.beforeRedirects.config=e.beforeRedirect),p=T?ro:rs),e.maxBodyLength>-1?O.maxBodyLength=e.maxBodyLength:O.maxBodyLength=1/0,e.insecureHTTPParser&&(O.insecureHTTPParser=e.insecureHTTPParser),c=p.request(O,function(n){if(c.destroyed)return;let i=[n],s=+n.headers["content-length"];if(E){let e=new i2({length:ir.toFiniteNumber(s),maxRate:ir.toFiniteNumber(r)});E&&e.on("progress",e=>{E(Object.assign(e,{download:!0}))}),i.push(e)}let o=n,l=n.req||c;if(!1!==e.decompress&&n.headers["content-encoding"])switch(("HEAD"===x||204===n.statusCode)&&delete n.headers["content-encoding"],(n.headers["content-encoding"]||"").toLowerCase()){case"gzip":case"x-gzip":case"compress":case"x-compress":i.push(iK.createUnzip(rn)),delete n.headers["content-encoding"];break;case"deflate":i.push(new rt),i.push(iK.createUnzip(rn)),delete n.headers["content-encoding"];break;case"br":rr&&(i.push(iK.createBrotliDecompress(ri)),delete n.headers["content-encoding"])}o=i.length>1?iJ.pipeline(i,ir.noop):i[0];let p=iJ.finished(o,()=>{p(),b()}),u={status:n.statusCode,statusText:n.statusMessage,headers:new iz(n.headers),config:e,request:l};if("stream"===f)u.data=o,iq(t,a,u);else{let n=[],i=0;o.on("data",function(t){n.push(t),i+=t.length,e.maxContentLength>-1&&i>e.maxContentLength&&(v=!0,o.destroy(),a(new is("maxContentLength size of "+e.maxContentLength+" exceeded",is.ERR_BAD_RESPONSE,e,l)))}),o.on("aborted",function(){if(v)return;let t=new is("maxContentLength size of "+e.maxContentLength+" exceeded",is.ERR_BAD_RESPONSE,e,l);o.destroy(t),a(t)}),o.on("error",function(t){c.destroyed||a(is.from(t,null,e,l))}),o.on("end",function(){try{let e=1===n.length?n[0]:Buffer.concat(n);"arraybuffer"===f||(e=e.toString(h),h&&"utf8"!==h||(e=ir.stripBOM(e))),u.data=e}catch(t){return a(is.from(t,null,e,u.request,u))}iq(t,a,u)})}g.once("abort",e=>{o.destroyed||(o.emit("error",e),o.destroy())})}),g.once("abort",e=>{a(e),c.destroy(e)}),c.on("error",function(t){a(is.from(t,null,e,c))}),c.on("socket",function(e){e.setKeepAlive(!0,6e4)}),e.timeout){let t=parseInt(e.timeout,10);if(Number.isNaN(t)){a(new is("error trying to parse `config.timeout` to int",is.ERR_BAD_OPTION_VALUE,e,c));return}c.setTimeout(t,function(){if(o)return;let t=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded",n=e.transitional||i_;e.timeoutErrorMessage&&(t=e.timeoutErrorMessage),a(new is(t,n.clarifyTimeoutError?is.ETIMEDOUT:is.ECONNABORTED,e,c)),y()})}if(ir.isStream(u)){let t=!1,a=!1;u.on("end",()=>{t=!0}),u.once("error",e=>{a=!0,c.destroy(e)}),u.on("close",()=>{t||a||y(new iI("Request stream has been aborted",e,c))}),u.pipe(c)}else c.end(u)})},rx=iR.hasStandardBrowserEnv?{write(e,t,a,n,i,r){let s=[e+"="+encodeURIComponent(t)];ir.isNumber(a)&&s.push("expires="+new Date(a).toGMTString()),ir.isString(n)&&s.push("path="+n),ir.isString(i)&&s.push("domain="+i),!0===r&&s.push("secure"),document.cookie=s.join("; ")},read(e){let t=document.cookie.match(RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}},rv=iR.hasStandardBrowserEnv?function(){let e;let t=/(msie|trident)/i.test(navigator.userAgent),a=document.createElement("a");function n(e){let n=e;return t&&(a.setAttribute("href",n),n=a.href),a.setAttribute("href",n),{href:a.href,protocol:a.protocol?a.protocol.replace(/:$/,""):"",host:a.host,search:a.search?a.search.replace(/^\?/,""):"",hash:a.hash?a.hash.replace(/^#/,""):"",hostname:a.hostname,port:a.port,pathname:"/"===a.pathname.charAt(0)?a.pathname:"/"+a.pathname}}return e=n(window.location.href),function(t){let a=ir.isString(t)?n(t):t;return a.protocol===e.protocol&&a.host===e.host}}():function(){return!0};function rg(e,t){let a=0,n=i0(50,250);return i=>{let r=i.loaded,s=i.lengthComputable?i.total:void 0,o=r-a,c=n(o);a=r;let l={loaded:r,total:s,progress:s?r/s:void 0,bytes:o,rate:c||void 0,estimated:c&&s&&r<=s?(s-r)/c:void 0,event:i};l[t?"download":"upload"]=!0,e(l)}}let rb={http:rh,xhr:"undefined"!=typeof XMLHttpRequest&&function(e){return new Promise(function(t,a){let n,i,r=e.data,s=iz.from(e.headers).normalize(),{responseType:o,withXSRFToken:c}=e;function l(){e.cancelToken&&e.cancelToken.unsubscribe(n),e.signal&&e.signal.removeEventListener("abort",n)}if(ir.isFormData(r)){if(iR.hasStandardBrowserEnv||iR.hasStandardBrowserWebWorkerEnv)s.setContentType(!1);else if(!1!==(i=s.getContentType())){let[e,...t]=i?i.split(";").map(e=>e.trim()).filter(Boolean):[];s.setContentType([e||"multipart/form-data",...t].join("; "))}}let p=new XMLHttpRequest;if(e.auth){let t=e.auth.username||"",a=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";s.set("Authorization","Basic "+btoa(t+":"+a))}let u=iB(e.baseURL,e.url);function d(){if(!p)return;let n=iz.from("getAllResponseHeaders"in p&&p.getAllResponseHeaders());iq(function(e){t(e),l()},function(e){a(e),l()},{data:o&&"text"!==o&&"json"!==o?p.response:p.responseText,status:p.status,statusText:p.statusText,headers:n,config:e,request:p}),p=null}if(p.open(e.method.toUpperCase(),iy(u,e.params,e.paramsSerializer),!0),p.timeout=e.timeout,"onloadend"in p?p.onloadend=d:p.onreadystatechange=function(){p&&4===p.readyState&&(0!==p.status||p.responseURL&&0===p.responseURL.indexOf("file:"))&&setTimeout(d)},p.onabort=function(){p&&(a(new is("Request aborted",is.ECONNABORTED,e,p)),p=null)},p.onerror=function(){a(new is("Network Error",is.ERR_NETWORK,e,p)),p=null},p.ontimeout=function(){let t=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded",n=e.transitional||i_;e.timeoutErrorMessage&&(t=e.timeoutErrorMessage),a(new is(t,n.clarifyTimeoutError?is.ETIMEDOUT:is.ECONNABORTED,e,p)),p=null},iR.hasStandardBrowserEnv&&(c&&ir.isFunction(c)&&(c=c(e)),c||!1!==c&&rv(u))){let t=e.xsrfHeaderName&&e.xsrfCookieName&&rx.read(e.xsrfCookieName);t&&s.set(e.xsrfHeaderName,t)}void 0===r&&s.setContentType(null),"setRequestHeader"in p&&ir.forEach(s.toJSON(),function(e,t){p.setRequestHeader(t,e)}),ir.isUndefined(e.withCredentials)||(p.withCredentials=!!e.withCredentials),o&&"json"!==o&&(p.responseType=e.responseType),"function"==typeof e.onDownloadProgress&&p.addEventListener("progress",rg(e.onDownloadProgress,!0)),"function"==typeof e.onUploadProgress&&p.upload&&p.upload.addEventListener("progress",rg(e.onUploadProgress)),(e.cancelToken||e.signal)&&(n=t=>{p&&(a(!t||t.type?new iI(null,e,p):t),p.abort(),p=null)},e.cancelToken&&e.cancelToken.subscribe(n),e.signal&&(e.signal.aborted?n():e.signal.addEventListener("abort",n)));let m=iX(u);if(m&&-1===iR.protocols.indexOf(m)){a(new is("Unsupported protocol "+m+":",is.ERR_BAD_REQUEST,e));return}p.send(r||null)})}};ir.forEach(rb,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(e){}Object.defineProperty(e,"adapterName",{value:t})}});let ry=e=>`- ${e}`,rw=e=>ir.isFunction(e)||null===e||!1===e,r_={getAdapter:e=>{let t,a;let{length:n}=e=ir.isArray(e)?e:[e],i={};for(let r=0;r<n;r++){let n;if(a=t=e[r],!rw(t)&&void 0===(a=rb[(n=String(t)).toLowerCase()]))throw new is(`Unknown adapter '${n}'`);if(a)break;i[n||"#"+r]=a}if(!a){let e=Object.entries(i).map(([e,t])=>`adapter ${e} `+(!1===t?"is not supported by the environment":"is not available in the build"));throw new is("There is no suitable adapter to dispatch the request "+(n?e.length>1?"since :\n"+e.map(ry).join("\n"):" "+ry(e[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return a},adapters:rb};function rk(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new iI(null,e)}function rj(e){return rk(e),e.headers=iz.from(e.headers),e.data=iF.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1),r_.getAdapter(e.adapter||iO.adapter)(e).then(function(t){return rk(e),t.data=iF.call(e,e.transformResponse,t),t.headers=iz.from(t.headers),t},function(t){return!iD(t)&&(rk(e),t&&t.response&&(t.response.data=iF.call(e,e.transformResponse,t.response),t.response.headers=iz.from(t.response.headers))),Promise.reject(t)})}let rE=e=>e instanceof iz?e.toJSON():e;function rC(e,t){t=t||{};let a={};function n(e,t,a){return ir.isPlainObject(e)&&ir.isPlainObject(t)?ir.merge.call({caseless:a},e,t):ir.isPlainObject(t)?ir.merge({},t):ir.isArray(t)?t.slice():t}function i(e,t,a){return ir.isUndefined(t)?ir.isUndefined(e)?void 0:n(void 0,e,a):n(e,t,a)}function r(e,t){if(!ir.isUndefined(t))return n(void 0,t)}function s(e,t){return ir.isUndefined(t)?ir.isUndefined(e)?void 0:n(void 0,e):n(void 0,t)}function o(a,i,r){return r in t?n(a,i):r in e?n(void 0,a):void 0}let c={url:r,method:r,data:r,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,withXSRFToken:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:o,headers:(e,t)=>i(rE(e),rE(t),!0)};return ir.forEach(Object.keys(Object.assign({},e,t)),function(n){let r=c[n]||i,s=r(e[n],t[n],n);ir.isUndefined(s)&&r!==o||(a[n]=s)}),a}let rR={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{rR[e]=function(a){return typeof a===e||"a"+(t<1?"n ":" ")+e}});let rS={};rR.transitional=function(e,t,a){function n(e,t){return"[Axios v"+iG+"] Transitional option '"+e+"'"+t+(a?". "+a:"")}return(a,i,r)=>{if(!1===e)throw new is(n(i," has been removed"+(t?" in "+t:"")),is.ERR_DEPRECATED);return t&&!rS[i]&&(rS[i]=!0,console.warn(n(i," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(a,i,r)}};let rO={assertOptions:function(e,t,a){if("object"!=typeof e)throw new is("options must be an object",is.ERR_BAD_OPTION_VALUE);let n=Object.keys(e),i=n.length;for(;i-- >0;){let r=n[i],s=t[r];if(s){let t=e[r],a=void 0===t||s(t,r,e);if(!0!==a)throw new is("option "+r+" must be "+a,is.ERR_BAD_OPTION_VALUE);continue}if(!0!==a)throw new is("Unknown option "+r,is.ERR_BAD_OPTION)}},validators:rR},rT=rO.validators;class rN{constructor(e){this.defaults=e,this.interceptors={request:new iw,response:new iw}}async request(e,t){try{return await this._request(e,t)}catch(e){if(e instanceof Error){let t;Error.captureStackTrace?Error.captureStackTrace(t={}):t=Error();let a=t.stack?t.stack.replace(/^.+\n/,""):"";e.stack?a&&!String(e.stack).endsWith(a.replace(/^.+\n.+\n/,""))&&(e.stack+="\n"+a):e.stack=a}throw e}}_request(e,t){let a,n;"string"==typeof e?(t=t||{}).url=e:t=e||{};let{transitional:i,paramsSerializer:r,headers:s}=t=rC(this.defaults,t);void 0!==i&&rO.assertOptions(i,{silentJSONParsing:rT.transitional(rT.boolean),forcedJSONParsing:rT.transitional(rT.boolean),clarifyTimeoutError:rT.transitional(rT.boolean)},!1),null!=r&&(ir.isFunction(r)?t.paramsSerializer={serialize:r}:rO.assertOptions(r,{encode:rT.function,serialize:rT.function},!0)),t.method=(t.method||this.defaults.method||"get").toLowerCase();let o=s&&ir.merge(s.common,s[t.method]);s&&ir.forEach(["delete","get","head","post","put","patch","common"],e=>{delete s[e]}),t.headers=iz.concat(o,s);let c=[],l=!0;this.interceptors.request.forEach(function(e){("function"!=typeof e.runWhen||!1!==e.runWhen(t))&&(l=l&&e.synchronous,c.unshift(e.fulfilled,e.rejected))});let p=[];this.interceptors.response.forEach(function(e){p.push(e.fulfilled,e.rejected)});let u=0;if(!l){let e=[rj.bind(this),void 0];for(e.unshift.apply(e,c),e.push.apply(e,p),n=e.length,a=Promise.resolve(t);u<n;)a=a.then(e[u++],e[u++]);return a}n=c.length;let d=t;for(u=0;u<n;){let e=c[u++],t=c[u++];try{d=e(d)}catch(e){t.call(this,e);break}}try{a=rj.call(this,d)}catch(e){return Promise.reject(e)}for(u=0,n=p.length;u<n;)a=a.then(p[u++],p[u++]);return a}getUri(e){return iy(iB((e=rC(this.defaults,e)).baseURL,e.url),e.params,e.paramsSerializer)}}ir.forEach(["delete","get","head","options"],function(e){rN.prototype[e]=function(t,a){return this.request(rC(a||{},{method:e,url:t,data:(a||{}).data}))}}),ir.forEach(["post","put","patch"],function(e){function t(t){return function(a,n,i){return this.request(rC(i||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:a,data:n}))}}rN.prototype[e]=t(),rN.prototype[e+"Form"]=t(!0)});class rA{constructor(e){let t;if("function"!=typeof e)throw TypeError("executor must be a function.");this.promise=new Promise(function(e){t=e});let a=this;this.promise.then(e=>{if(!a._listeners)return;let t=a._listeners.length;for(;t-- >0;)a._listeners[t](e);a._listeners=null}),this.promise.then=e=>{let t;let n=new Promise(e=>{a.subscribe(e),t=e}).then(e);return n.cancel=function(){a.unsubscribe(t)},n},e(function(e,n,i){a.reason||(a.reason=new iI(e,n,i),t(a.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason){e(this.reason);return}this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;let t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}static source(){let e;return{token:new rA(function(t){e=t}),cancel:e}}}let rP={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(rP).forEach(([e,t])=>{rP[t]=e});let rL=function e(t){let a=new rN(t),n=nM(rN.prototype.request,a);return ir.extend(n,rN.prototype,a,{allOwnKeys:!0}),ir.extend(n,a,null,{allOwnKeys:!0}),n.create=function(a){return e(rC(t,a))},n}(iO);rL.Axios=rN,rL.CanceledError=iI,rL.CancelToken=rA,rL.isCancel=iD,rL.VERSION=iG,rL.toFormData=ih,rL.AxiosError=is,rL.Cancel=rL.CanceledError,rL.all=function(e){return Promise.all(e)},rL.spread=function(e){return function(t){return e.apply(null,t)}},rL.isAxiosError=function(e){return ir.isObject(e)&&!0===e.isAxiosError},rL.mergeConfig=rC,rL.AxiosHeaders=iz,rL.formToJSON=e=>iS(ir.isHTMLForm(e)?new FormData(e):e),rL.getAdapter=r_.getAdapter,rL.HttpStatusCode=rP,rL.default=rL;var rU=a(34755);(function(e){e.assertEqual=e=>{},e.assertIs=function(e){},e.assertNever=function(e){throw Error()},e.arrayToEnum=e=>{let t={};for(let a of e)t[a]=a;return t},e.getValidEnumValues=t=>{let a=e.objectKeys(t).filter(e=>"number"!=typeof t[t[e]]),n={};for(let e of a)n[e]=t[e];return e.objectValues(n)},e.objectValues=t=>e.objectKeys(t).map(function(e){return t[e]}),e.objectKeys="function"==typeof Object.keys?e=>Object.keys(e):e=>{let t=[];for(let a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.push(a);return t},e.find=(e,t)=>{for(let a of e)if(t(a))return a},e.isInteger="function"==typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"==typeof e&&Number.isFinite(e)&&Math.floor(e)===e,e.joinValues=function(e,t=" | "){return e.map(e=>"string"==typeof e?`'${e}'`:e).join(t)},e.jsonStringifyReplacer=(e,t)=>"bigint"==typeof t?t.toString():t})(l||(l={})),(p||(p={})).mergeShapes=(e,t)=>({...e,...t});let rM=l.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),rz=e=>{switch(typeof e){case"undefined":return rM.undefined;case"string":return rM.string;case"number":return Number.isNaN(e)?rM.nan:rM.number;case"boolean":return rM.boolean;case"function":return rM.function;case"bigint":return rM.bigint;case"symbol":return rM.symbol;case"object":if(Array.isArray(e))return rM.array;if(null===e)return rM.null;if(e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch)return rM.promise;if("undefined"!=typeof Map&&e instanceof Map)return rM.map;if("undefined"!=typeof Set&&e instanceof Set)return rM.set;if("undefined"!=typeof Date&&e instanceof Date)return rM.date;return rM.object;default:return rM.unknown}},rF=l.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class rD extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};let t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){let t=e||function(e){return e.message},a={_errors:[]},n=e=>{for(let i of e.issues)if("invalid_union"===i.code)i.unionErrors.map(n);else if("invalid_return_type"===i.code)n(i.returnTypeError);else if("invalid_arguments"===i.code)n(i.argumentsError);else if(0===i.path.length)a._errors.push(t(i));else{let e=a,n=0;for(;n<i.path.length;){let a=i.path[n];n===i.path.length-1?(e[a]=e[a]||{_errors:[]},e[a]._errors.push(t(i))):e[a]=e[a]||{_errors:[]},e=e[a],n++}}};return n(this),a}static assert(e){if(!(e instanceof rD))throw Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,l.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=e=>e.message){let t={},a=[];for(let n of this.issues)n.path.length>0?(t[n.path[0]]=t[n.path[0]]||[],t[n.path[0]].push(e(n))):a.push(e(n));return{formErrors:a,fieldErrors:t}}get formErrors(){return this.flatten()}}rD.create=e=>new rD(e);let rI=(e,t)=>{let a;switch(e.code){case rF.invalid_type:a=e.received===rM.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case rF.invalid_literal:a=`Invalid literal value, expected ${JSON.stringify(e.expected,l.jsonStringifyReplacer)}`;break;case rF.unrecognized_keys:a=`Unrecognized key(s) in object: ${l.joinValues(e.keys,", ")}`;break;case rF.invalid_union:a="Invalid input";break;case rF.invalid_union_discriminator:a=`Invalid discriminator value. Expected ${l.joinValues(e.options)}`;break;case rF.invalid_enum_value:a=`Invalid enum value. Expected ${l.joinValues(e.options)}, received '${e.received}'`;break;case rF.invalid_arguments:a="Invalid function arguments";break;case rF.invalid_return_type:a="Invalid function return type";break;case rF.invalid_date:a="Invalid date";break;case rF.invalid_string:"object"==typeof e.validation?"includes"in e.validation?(a=`Invalid input: must include "${e.validation.includes}"`,"number"==typeof e.validation.position&&(a=`${a} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?a=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?a=`Invalid input: must end with "${e.validation.endsWith}"`:l.assertNever(e.validation):a="regex"!==e.validation?`Invalid ${e.validation}`:"Invalid";break;case rF.too_small:a="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:"date"===e.type?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case rF.too_big:a="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"bigint"===e.type?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"date"===e.type?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case rF.custom:a="Invalid input";break;case rF.invalid_intersection_types:a="Intersection results could not be merged";break;case rF.not_multiple_of:a=`Number must be a multiple of ${e.multipleOf}`;break;case rF.not_finite:a="Number must be finite";break;default:a=t.defaultError,l.assertNever(e)}return{message:a}},rq=e=>{let{data:t,path:a,errorMaps:n,issueData:i}=e,r=[...a,...i.path||[]],s={...i,path:r};if(void 0!==i.message)return{...i,path:r,message:i.message};let o="";for(let e of n.filter(e=>!!e).slice().reverse())o=e(s,{data:t,defaultError:o}).message;return{...i,path:r,message:o}};function rB(e,t){let a=rq({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,rI,rI==rI?void 0:rI].filter(e=>!!e)});e.common.issues.push(a)}class r${constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,t){let a=[];for(let n of t){if("aborted"===n.status)return rZ;"dirty"===n.status&&e.dirty(),a.push(n.value)}return{status:e.value,value:a}}static async mergeObjectAsync(e,t){let a=[];for(let e of t){let t=await e.key,n=await e.value;a.push({key:t,value:n})}return r$.mergeObjectSync(e,a)}static mergeObjectSync(e,t){let a={};for(let n of t){let{key:t,value:i}=n;if("aborted"===t.status||"aborted"===i.status)return rZ;"dirty"===t.status&&e.dirty(),"dirty"===i.status&&e.dirty(),"__proto__"!==t.value&&(void 0!==i.value||n.alwaysSet)&&(a[t.value]=i.value)}return{status:e.value,value:a}}}let rZ=Object.freeze({status:"aborted"}),rW=e=>({status:"dirty",value:e}),rH=e=>({status:"valid",value:e}),rV=e=>"aborted"===e.status,rK=e=>"dirty"===e.status,rG=e=>"valid"===e.status,rX=e=>"undefined"!=typeof Promise&&e instanceof Promise;!function(e){e.errToObj=e=>"string"==typeof e?{message:e}:e||{},e.toString=e=>"string"==typeof e?e:e?.message}(u||(u={}));class rY{constructor(e,t,a,n){this._cachedPath=[],this.parent=e,this.data=t,this._path=a,this._key=n}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}let rJ=(e,t)=>{if(rG(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;let t=new rD(e.common.issues);return this._error=t,this._error}}};function rQ(e){if(!e)return{};let{errorMap:t,invalid_type_error:a,required_error:n,description:i}=e;if(t&&(a||n))throw Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');return t?{errorMap:t,description:i}:{errorMap:(t,i)=>{let{message:r}=e;return"invalid_enum_value"===t.code?{message:r??i.defaultError}:void 0===i.data?{message:r??n??i.defaultError}:"invalid_type"!==t.code?{message:i.defaultError}:{message:r??a??i.defaultError}},description:i}}class r0{get description(){return this._def.description}_getType(e){return rz(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:rz(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new r$,ctx:{common:e.parent.common,data:e.data,parsedType:rz(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){let t=this._parse(e);if(rX(t))throw Error("Synchronous parse encountered promise.");return t}_parseAsync(e){return Promise.resolve(this._parse(e))}parse(e,t){let a=this.safeParse(e,t);if(a.success)return a.data;throw a.error}safeParse(e,t){let a={common:{issues:[],async:t?.async??!1,contextualErrorMap:t?.errorMap},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:rz(e)},n=this._parseSync({data:e,path:a.path,parent:a});return rJ(a,n)}"~validate"(e){let t={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:rz(e)};if(!this["~standard"].async)try{let a=this._parseSync({data:e,path:[],parent:t});return rG(a)?{value:a.value}:{issues:t.common.issues}}catch(e){e?.message?.toLowerCase()?.includes("encountered")&&(this["~standard"].async=!0),t.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:t}).then(e=>rG(e)?{value:e.value}:{issues:t.common.issues})}async parseAsync(e,t){let a=await this.safeParseAsync(e,t);if(a.success)return a.data;throw a.error}async safeParseAsync(e,t){let a={common:{issues:[],contextualErrorMap:t?.errorMap,async:!0},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:rz(e)},n=this._parse({data:e,path:a.path,parent:a});return rJ(a,await (rX(n)?n:Promise.resolve(n)))}refine(e,t){let a=e=>"string"==typeof t||void 0===t?{message:t}:"function"==typeof t?t(e):t;return this._refinement((t,n)=>{let i=e(t),r=()=>n.addIssue({code:rF.custom,...a(t)});return"undefined"!=typeof Promise&&i instanceof Promise?i.then(e=>!!e||(r(),!1)):!!i||(r(),!1)})}refinement(e,t){return this._refinement((a,n)=>!!e(a)||(n.addIssue("function"==typeof t?t(a,n):t),!1))}_refinement(e){return new sz({schema:this,typeName:d.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return sF.create(this,this._def)}nullable(){return sD.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return sy.create(this)}promise(){return sM.create(this,this._def)}or(e){return s_.create([this,e],this._def)}and(e){return sE.create(this,e,this._def)}transform(e){return new sz({...rQ(this._def),schema:this,typeName:d.ZodEffects,effect:{type:"transform",transform:e}})}default(e){return new sI({...rQ(this._def),innerType:this,defaultValue:"function"==typeof e?e:()=>e,typeName:d.ZodDefault})}brand(){return new s$({typeName:d.ZodBranded,type:this,...rQ(this._def)})}catch(e){return new sq({...rQ(this._def),innerType:this,catchValue:"function"==typeof e?e:()=>e,typeName:d.ZodCatch})}describe(e){return new this.constructor({...this._def,description:e})}pipe(e){return sZ.create(this,e)}readonly(){return sW.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}let r1=/^c[^\s-]{8,}$/i,r2=/^[0-9a-z]+$/,r3=/^[0-9A-HJKMNP-TV-Z]{26}$/i,r4=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,r6=/^[a-z0-9_-]{21}$/i,r9=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,r8=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,r5=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,r7=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,se=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,st=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,sa=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,sn=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,si=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,sr="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",ss=RegExp(`^${sr}$`);function so(e){let t="[0-5]\\d";e.precision?t=`${t}\\.\\d{${e.precision}}`:null==e.precision&&(t=`${t}(\\.\\d+)?`);let a=e.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${a}`}class sc extends r0{_parse(e){var t,a,n,r;let s;if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==rM.string){let t=this._getOrReturnCtx(e);return rB(t,{code:rF.invalid_type,expected:rM.string,received:t.parsedType}),rZ}let o=new r$;for(let c of this._def.checks)if("min"===c.kind)e.data.length<c.value&&(rB(s=this._getOrReturnCtx(e,s),{code:rF.too_small,minimum:c.value,type:"string",inclusive:!0,exact:!1,message:c.message}),o.dirty());else if("max"===c.kind)e.data.length>c.value&&(rB(s=this._getOrReturnCtx(e,s),{code:rF.too_big,maximum:c.value,type:"string",inclusive:!0,exact:!1,message:c.message}),o.dirty());else if("length"===c.kind){let t=e.data.length>c.value,a=e.data.length<c.value;(t||a)&&(s=this._getOrReturnCtx(e,s),t?rB(s,{code:rF.too_big,maximum:c.value,type:"string",inclusive:!0,exact:!0,message:c.message}):a&&rB(s,{code:rF.too_small,minimum:c.value,type:"string",inclusive:!0,exact:!0,message:c.message}),o.dirty())}else if("email"===c.kind)r5.test(e.data)||(rB(s=this._getOrReturnCtx(e,s),{validation:"email",code:rF.invalid_string,message:c.message}),o.dirty());else if("emoji"===c.kind)i||(i=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),i.test(e.data)||(rB(s=this._getOrReturnCtx(e,s),{validation:"emoji",code:rF.invalid_string,message:c.message}),o.dirty());else if("uuid"===c.kind)r4.test(e.data)||(rB(s=this._getOrReturnCtx(e,s),{validation:"uuid",code:rF.invalid_string,message:c.message}),o.dirty());else if("nanoid"===c.kind)r6.test(e.data)||(rB(s=this._getOrReturnCtx(e,s),{validation:"nanoid",code:rF.invalid_string,message:c.message}),o.dirty());else if("cuid"===c.kind)r1.test(e.data)||(rB(s=this._getOrReturnCtx(e,s),{validation:"cuid",code:rF.invalid_string,message:c.message}),o.dirty());else if("cuid2"===c.kind)r2.test(e.data)||(rB(s=this._getOrReturnCtx(e,s),{validation:"cuid2",code:rF.invalid_string,message:c.message}),o.dirty());else if("ulid"===c.kind)r3.test(e.data)||(rB(s=this._getOrReturnCtx(e,s),{validation:"ulid",code:rF.invalid_string,message:c.message}),o.dirty());else if("url"===c.kind)try{new URL(e.data)}catch{rB(s=this._getOrReturnCtx(e,s),{validation:"url",code:rF.invalid_string,message:c.message}),o.dirty()}else"regex"===c.kind?(c.regex.lastIndex=0,c.regex.test(e.data)||(rB(s=this._getOrReturnCtx(e,s),{validation:"regex",code:rF.invalid_string,message:c.message}),o.dirty())):"trim"===c.kind?e.data=e.data.trim():"includes"===c.kind?e.data.includes(c.value,c.position)||(rB(s=this._getOrReturnCtx(e,s),{code:rF.invalid_string,validation:{includes:c.value,position:c.position},message:c.message}),o.dirty()):"toLowerCase"===c.kind?e.data=e.data.toLowerCase():"toUpperCase"===c.kind?e.data=e.data.toUpperCase():"startsWith"===c.kind?e.data.startsWith(c.value)||(rB(s=this._getOrReturnCtx(e,s),{code:rF.invalid_string,validation:{startsWith:c.value},message:c.message}),o.dirty()):"endsWith"===c.kind?e.data.endsWith(c.value)||(rB(s=this._getOrReturnCtx(e,s),{code:rF.invalid_string,validation:{endsWith:c.value},message:c.message}),o.dirty()):"datetime"===c.kind?(function(e){let t=`${sr}T${so(e)}`,a=[];return a.push(e.local?"Z?":"Z"),e.offset&&a.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${a.join("|")})`,RegExp(`^${t}$`)})(c).test(e.data)||(rB(s=this._getOrReturnCtx(e,s),{code:rF.invalid_string,validation:"datetime",message:c.message}),o.dirty()):"date"===c.kind?ss.test(e.data)||(rB(s=this._getOrReturnCtx(e,s),{code:rF.invalid_string,validation:"date",message:c.message}),o.dirty()):"time"===c.kind?RegExp(`^${so(c)}$`).test(e.data)||(rB(s=this._getOrReturnCtx(e,s),{code:rF.invalid_string,validation:"time",message:c.message}),o.dirty()):"duration"===c.kind?r8.test(e.data)||(rB(s=this._getOrReturnCtx(e,s),{validation:"duration",code:rF.invalid_string,message:c.message}),o.dirty()):"ip"===c.kind?(t=e.data,("v4"===(a=c.version)||!a)&&r7.test(t)||("v6"===a||!a)&&st.test(t)||(rB(s=this._getOrReturnCtx(e,s),{validation:"ip",code:rF.invalid_string,message:c.message}),o.dirty())):"jwt"===c.kind?!function(e,t){if(!r9.test(e))return!1;try{let[a]=e.split("."),n=a.replace(/-/g,"+").replace(/_/g,"/").padEnd(a.length+(4-a.length%4)%4,"="),i=JSON.parse(atob(n));if("object"!=typeof i||null===i||"typ"in i&&i?.typ!=="JWT"||!i.alg||t&&i.alg!==t)return!1;return!0}catch{return!1}}(e.data,c.alg)&&(rB(s=this._getOrReturnCtx(e,s),{validation:"jwt",code:rF.invalid_string,message:c.message}),o.dirty()):"cidr"===c.kind?(n=e.data,("v4"===(r=c.version)||!r)&&se.test(n)||("v6"===r||!r)&&sa.test(n)||(rB(s=this._getOrReturnCtx(e,s),{validation:"cidr",code:rF.invalid_string,message:c.message}),o.dirty())):"base64"===c.kind?sn.test(e.data)||(rB(s=this._getOrReturnCtx(e,s),{validation:"base64",code:rF.invalid_string,message:c.message}),o.dirty()):"base64url"===c.kind?si.test(e.data)||(rB(s=this._getOrReturnCtx(e,s),{validation:"base64url",code:rF.invalid_string,message:c.message}),o.dirty()):l.assertNever(c);return{status:o.value,value:e.data}}_regex(e,t,a){return this.refinement(t=>e.test(t),{validation:t,code:rF.invalid_string,...u.errToObj(a)})}_addCheck(e){return new sc({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...u.errToObj(e)})}url(e){return this._addCheck({kind:"url",...u.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...u.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...u.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...u.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...u.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...u.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...u.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...u.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...u.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...u.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...u.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...u.errToObj(e)})}datetime(e){return"string"==typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:void 0===e?.precision?null:e?.precision,offset:e?.offset??!1,local:e?.local??!1,...u.errToObj(e?.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return"string"==typeof e?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:void 0===e?.precision?null:e?.precision,...u.errToObj(e?.message)})}duration(e){return this._addCheck({kind:"duration",...u.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...u.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t?.position,...u.errToObj(t?.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...u.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...u.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...u.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...u.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...u.errToObj(t)})}nonempty(e){return this.min(1,u.errToObj(e))}trim(){return new sc({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new sc({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new sc({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>"datetime"===e.kind)}get isDate(){return!!this._def.checks.find(e=>"date"===e.kind)}get isTime(){return!!this._def.checks.find(e=>"time"===e.kind)}get isDuration(){return!!this._def.checks.find(e=>"duration"===e.kind)}get isEmail(){return!!this._def.checks.find(e=>"email"===e.kind)}get isURL(){return!!this._def.checks.find(e=>"url"===e.kind)}get isEmoji(){return!!this._def.checks.find(e=>"emoji"===e.kind)}get isUUID(){return!!this._def.checks.find(e=>"uuid"===e.kind)}get isNANOID(){return!!this._def.checks.find(e=>"nanoid"===e.kind)}get isCUID(){return!!this._def.checks.find(e=>"cuid"===e.kind)}get isCUID2(){return!!this._def.checks.find(e=>"cuid2"===e.kind)}get isULID(){return!!this._def.checks.find(e=>"ulid"===e.kind)}get isIP(){return!!this._def.checks.find(e=>"ip"===e.kind)}get isCIDR(){return!!this._def.checks.find(e=>"cidr"===e.kind)}get isBase64(){return!!this._def.checks.find(e=>"base64"===e.kind)}get isBase64url(){return!!this._def.checks.find(e=>"base64url"===e.kind)}get minLength(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}sc.create=e=>new sc({checks:[],typeName:d.ZodString,coerce:e?.coerce??!1,...rQ(e)});class sl extends r0{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){let t;if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==rM.number){let t=this._getOrReturnCtx(e);return rB(t,{code:rF.invalid_type,expected:rM.number,received:t.parsedType}),rZ}let a=new r$;for(let n of this._def.checks)"int"===n.kind?l.isInteger(e.data)||(rB(t=this._getOrReturnCtx(e,t),{code:rF.invalid_type,expected:"integer",received:"float",message:n.message}),a.dirty()):"min"===n.kind?(n.inclusive?e.data<n.value:e.data<=n.value)&&(rB(t=this._getOrReturnCtx(e,t),{code:rF.too_small,minimum:n.value,type:"number",inclusive:n.inclusive,exact:!1,message:n.message}),a.dirty()):"max"===n.kind?(n.inclusive?e.data>n.value:e.data>=n.value)&&(rB(t=this._getOrReturnCtx(e,t),{code:rF.too_big,maximum:n.value,type:"number",inclusive:n.inclusive,exact:!1,message:n.message}),a.dirty()):"multipleOf"===n.kind?0!==function(e,t){let a=(e.toString().split(".")[1]||"").length,n=(t.toString().split(".")[1]||"").length,i=a>n?a:n;return Number.parseInt(e.toFixed(i).replace(".",""))%Number.parseInt(t.toFixed(i).replace(".",""))/10**i}(e.data,n.value)&&(rB(t=this._getOrReturnCtx(e,t),{code:rF.not_multiple_of,multipleOf:n.value,message:n.message}),a.dirty()):"finite"===n.kind?Number.isFinite(e.data)||(rB(t=this._getOrReturnCtx(e,t),{code:rF.not_finite,message:n.message}),a.dirty()):l.assertNever(n);return{status:a.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,u.toString(t))}gt(e,t){return this.setLimit("min",e,!1,u.toString(t))}lte(e,t){return this.setLimit("max",e,!0,u.toString(t))}lt(e,t){return this.setLimit("max",e,!1,u.toString(t))}setLimit(e,t,a,n){return new sl({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:a,message:u.toString(n)}]})}_addCheck(e){return new sl({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:u.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:u.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:u.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:u.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:u.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:u.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:u.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:u.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:u.toString(e)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>"int"===e.kind||"multipleOf"===e.kind&&l.isInteger(e.value))}get isFinite(){let e=null,t=null;for(let a of this._def.checks){if("finite"===a.kind||"int"===a.kind||"multipleOf"===a.kind)return!0;"min"===a.kind?(null===t||a.value>t)&&(t=a.value):"max"===a.kind&&(null===e||a.value<e)&&(e=a.value)}return Number.isFinite(t)&&Number.isFinite(e)}}sl.create=e=>new sl({checks:[],typeName:d.ZodNumber,coerce:e?.coerce||!1,...rQ(e)});class sp extends r0{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){let t;if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==rM.bigint)return this._getInvalidInput(e);let a=new r$;for(let n of this._def.checks)"min"===n.kind?(n.inclusive?e.data<n.value:e.data<=n.value)&&(rB(t=this._getOrReturnCtx(e,t),{code:rF.too_small,type:"bigint",minimum:n.value,inclusive:n.inclusive,message:n.message}),a.dirty()):"max"===n.kind?(n.inclusive?e.data>n.value:e.data>=n.value)&&(rB(t=this._getOrReturnCtx(e,t),{code:rF.too_big,type:"bigint",maximum:n.value,inclusive:n.inclusive,message:n.message}),a.dirty()):"multipleOf"===n.kind?e.data%n.value!==BigInt(0)&&(rB(t=this._getOrReturnCtx(e,t),{code:rF.not_multiple_of,multipleOf:n.value,message:n.message}),a.dirty()):l.assertNever(n);return{status:a.value,value:e.data}}_getInvalidInput(e){let t=this._getOrReturnCtx(e);return rB(t,{code:rF.invalid_type,expected:rM.bigint,received:t.parsedType}),rZ}gte(e,t){return this.setLimit("min",e,!0,u.toString(t))}gt(e,t){return this.setLimit("min",e,!1,u.toString(t))}lte(e,t){return this.setLimit("max",e,!0,u.toString(t))}lt(e,t){return this.setLimit("max",e,!1,u.toString(t))}setLimit(e,t,a,n){return new sp({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:a,message:u.toString(n)}]})}_addCheck(e){return new sp({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:u.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:u.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:u.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:u.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:u.toString(t)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}sp.create=e=>new sp({checks:[],typeName:d.ZodBigInt,coerce:e?.coerce??!1,...rQ(e)});class su extends r0{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==rM.boolean){let t=this._getOrReturnCtx(e);return rB(t,{code:rF.invalid_type,expected:rM.boolean,received:t.parsedType}),rZ}return rH(e.data)}}su.create=e=>new su({typeName:d.ZodBoolean,coerce:e?.coerce||!1,...rQ(e)});class sd extends r0{_parse(e){let t;if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==rM.date){let t=this._getOrReturnCtx(e);return rB(t,{code:rF.invalid_type,expected:rM.date,received:t.parsedType}),rZ}if(Number.isNaN(e.data.getTime()))return rB(this._getOrReturnCtx(e),{code:rF.invalid_date}),rZ;let a=new r$;for(let n of this._def.checks)"min"===n.kind?e.data.getTime()<n.value&&(rB(t=this._getOrReturnCtx(e,t),{code:rF.too_small,message:n.message,inclusive:!0,exact:!1,minimum:n.value,type:"date"}),a.dirty()):"max"===n.kind?e.data.getTime()>n.value&&(rB(t=this._getOrReturnCtx(e,t),{code:rF.too_big,message:n.message,inclusive:!0,exact:!1,maximum:n.value,type:"date"}),a.dirty()):l.assertNever(n);return{status:a.value,value:new Date(e.data.getTime())}}_addCheck(e){return new sd({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:u.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:u.toString(t)})}get minDate(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}sd.create=e=>new sd({checks:[],coerce:e?.coerce||!1,typeName:d.ZodDate,...rQ(e)});class sm extends r0{_parse(e){if(this._getType(e)!==rM.symbol){let t=this._getOrReturnCtx(e);return rB(t,{code:rF.invalid_type,expected:rM.symbol,received:t.parsedType}),rZ}return rH(e.data)}}sm.create=e=>new sm({typeName:d.ZodSymbol,...rQ(e)});class sf extends r0{_parse(e){if(this._getType(e)!==rM.undefined){let t=this._getOrReturnCtx(e);return rB(t,{code:rF.invalid_type,expected:rM.undefined,received:t.parsedType}),rZ}return rH(e.data)}}sf.create=e=>new sf({typeName:d.ZodUndefined,...rQ(e)});class sh extends r0{_parse(e){if(this._getType(e)!==rM.null){let t=this._getOrReturnCtx(e);return rB(t,{code:rF.invalid_type,expected:rM.null,received:t.parsedType}),rZ}return rH(e.data)}}sh.create=e=>new sh({typeName:d.ZodNull,...rQ(e)});class sx extends r0{constructor(){super(...arguments),this._any=!0}_parse(e){return rH(e.data)}}sx.create=e=>new sx({typeName:d.ZodAny,...rQ(e)});class sv extends r0{constructor(){super(...arguments),this._unknown=!0}_parse(e){return rH(e.data)}}sv.create=e=>new sv({typeName:d.ZodUnknown,...rQ(e)});class sg extends r0{_parse(e){let t=this._getOrReturnCtx(e);return rB(t,{code:rF.invalid_type,expected:rM.never,received:t.parsedType}),rZ}}sg.create=e=>new sg({typeName:d.ZodNever,...rQ(e)});class sb extends r0{_parse(e){if(this._getType(e)!==rM.undefined){let t=this._getOrReturnCtx(e);return rB(t,{code:rF.invalid_type,expected:rM.void,received:t.parsedType}),rZ}return rH(e.data)}}sb.create=e=>new sb({typeName:d.ZodVoid,...rQ(e)});class sy extends r0{_parse(e){let{ctx:t,status:a}=this._processInputParams(e),n=this._def;if(t.parsedType!==rM.array)return rB(t,{code:rF.invalid_type,expected:rM.array,received:t.parsedType}),rZ;if(null!==n.exactLength){let e=t.data.length>n.exactLength.value,i=t.data.length<n.exactLength.value;(e||i)&&(rB(t,{code:e?rF.too_big:rF.too_small,minimum:i?n.exactLength.value:void 0,maximum:e?n.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:n.exactLength.message}),a.dirty())}if(null!==n.minLength&&t.data.length<n.minLength.value&&(rB(t,{code:rF.too_small,minimum:n.minLength.value,type:"array",inclusive:!0,exact:!1,message:n.minLength.message}),a.dirty()),null!==n.maxLength&&t.data.length>n.maxLength.value&&(rB(t,{code:rF.too_big,maximum:n.maxLength.value,type:"array",inclusive:!0,exact:!1,message:n.maxLength.message}),a.dirty()),t.common.async)return Promise.all([...t.data].map((e,a)=>n.type._parseAsync(new rY(t,e,t.path,a)))).then(e=>r$.mergeArray(a,e));let i=[...t.data].map((e,a)=>n.type._parseSync(new rY(t,e,t.path,a)));return r$.mergeArray(a,i)}get element(){return this._def.type}min(e,t){return new sy({...this._def,minLength:{value:e,message:u.toString(t)}})}max(e,t){return new sy({...this._def,maxLength:{value:e,message:u.toString(t)}})}length(e,t){return new sy({...this._def,exactLength:{value:e,message:u.toString(t)}})}nonempty(e){return this.min(1,e)}}sy.create=(e,t)=>new sy({type:e,minLength:null,maxLength:null,exactLength:null,typeName:d.ZodArray,...rQ(t)});class sw extends r0{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;let e=this._def.shape(),t=l.objectKeys(e);return this._cached={shape:e,keys:t},this._cached}_parse(e){if(this._getType(e)!==rM.object){let t=this._getOrReturnCtx(e);return rB(t,{code:rF.invalid_type,expected:rM.object,received:t.parsedType}),rZ}let{status:t,ctx:a}=this._processInputParams(e),{shape:n,keys:i}=this._getCached(),r=[];if(!(this._def.catchall instanceof sg&&"strip"===this._def.unknownKeys))for(let e in a.data)i.includes(e)||r.push(e);let s=[];for(let e of i){let t=n[e],i=a.data[e];s.push({key:{status:"valid",value:e},value:t._parse(new rY(a,i,a.path,e)),alwaysSet:e in a.data})}if(this._def.catchall instanceof sg){let e=this._def.unknownKeys;if("passthrough"===e)for(let e of r)s.push({key:{status:"valid",value:e},value:{status:"valid",value:a.data[e]}});else if("strict"===e)r.length>0&&(rB(a,{code:rF.unrecognized_keys,keys:r}),t.dirty());else if("strip"===e);else throw Error("Internal ZodObject error: invalid unknownKeys value.")}else{let e=this._def.catchall;for(let t of r){let n=a.data[t];s.push({key:{status:"valid",value:t},value:e._parse(new rY(a,n,a.path,t)),alwaysSet:t in a.data})}}return a.common.async?Promise.resolve().then(async()=>{let e=[];for(let t of s){let a=await t.key,n=await t.value;e.push({key:a,value:n,alwaysSet:t.alwaysSet})}return e}).then(e=>r$.mergeObjectSync(t,e)):r$.mergeObjectSync(t,s)}get shape(){return this._def.shape()}strict(e){return u.errToObj,new sw({...this._def,unknownKeys:"strict",...void 0!==e?{errorMap:(t,a)=>{let n=this._def.errorMap?.(t,a).message??a.defaultError;return"unrecognized_keys"===t.code?{message:u.errToObj(e).message??n}:{message:n}}}:{}})}strip(){return new sw({...this._def,unknownKeys:"strip"})}passthrough(){return new sw({...this._def,unknownKeys:"passthrough"})}extend(e){return new sw({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new sw({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:d.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new sw({...this._def,catchall:e})}pick(e){let t={};for(let a of l.objectKeys(e))e[a]&&this.shape[a]&&(t[a]=this.shape[a]);return new sw({...this._def,shape:()=>t})}omit(e){let t={};for(let a of l.objectKeys(this.shape))e[a]||(t[a]=this.shape[a]);return new sw({...this._def,shape:()=>t})}deepPartial(){return function e(t){if(t instanceof sw){let a={};for(let n in t.shape){let i=t.shape[n];a[n]=sF.create(e(i))}return new sw({...t._def,shape:()=>a})}return t instanceof sy?new sy({...t._def,type:e(t.element)}):t instanceof sF?sF.create(e(t.unwrap())):t instanceof sD?sD.create(e(t.unwrap())):t instanceof sC?sC.create(t.items.map(t=>e(t))):t}(this)}partial(e){let t={};for(let a of l.objectKeys(this.shape)){let n=this.shape[a];e&&!e[a]?t[a]=n:t[a]=n.optional()}return new sw({...this._def,shape:()=>t})}required(e){let t={};for(let a of l.objectKeys(this.shape))if(e&&!e[a])t[a]=this.shape[a];else{let e=this.shape[a];for(;e instanceof sF;)e=e._def.innerType;t[a]=e}return new sw({...this._def,shape:()=>t})}keyof(){return sP(l.objectKeys(this.shape))}}sw.create=(e,t)=>new sw({shape:()=>e,unknownKeys:"strip",catchall:sg.create(),typeName:d.ZodObject,...rQ(t)}),sw.strictCreate=(e,t)=>new sw({shape:()=>e,unknownKeys:"strict",catchall:sg.create(),typeName:d.ZodObject,...rQ(t)}),sw.lazycreate=(e,t)=>new sw({shape:e,unknownKeys:"strip",catchall:sg.create(),typeName:d.ZodObject,...rQ(t)});class s_ extends r0{_parse(e){let{ctx:t}=this._processInputParams(e),a=this._def.options;if(t.common.async)return Promise.all(a.map(async e=>{let a={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:a}),ctx:a}})).then(function(e){for(let t of e)if("valid"===t.result.status)return t.result;for(let a of e)if("dirty"===a.result.status)return t.common.issues.push(...a.ctx.common.issues),a.result;let a=e.map(e=>new rD(e.ctx.common.issues));return rB(t,{code:rF.invalid_union,unionErrors:a}),rZ});{let e;let n=[];for(let i of a){let a={...t,common:{...t.common,issues:[]},parent:null},r=i._parseSync({data:t.data,path:t.path,parent:a});if("valid"===r.status)return r;"dirty"!==r.status||e||(e={result:r,ctx:a}),a.common.issues.length&&n.push(a.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;let i=n.map(e=>new rD(e));return rB(t,{code:rF.invalid_union,unionErrors:i}),rZ}}get options(){return this._def.options}}s_.create=(e,t)=>new s_({options:e,typeName:d.ZodUnion,...rQ(t)});let sk=e=>{if(e instanceof sN)return sk(e.schema);if(e instanceof sz)return sk(e.innerType());if(e instanceof sA)return[e.value];if(e instanceof sL)return e.options;if(e instanceof sU)return l.objectValues(e.enum);if(e instanceof sI)return sk(e._def.innerType);if(e instanceof sf)return[void 0];else if(e instanceof sh)return[null];else if(e instanceof sF)return[void 0,...sk(e.unwrap())];else if(e instanceof sD)return[null,...sk(e.unwrap())];else if(e instanceof s$)return sk(e.unwrap());else if(e instanceof sW)return sk(e.unwrap());else if(e instanceof sq)return sk(e._def.innerType);else return[]};class sj extends r0{_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==rM.object)return rB(t,{code:rF.invalid_type,expected:rM.object,received:t.parsedType}),rZ;let a=this.discriminator,n=t.data[a],i=this.optionsMap.get(n);return i?t.common.async?i._parseAsync({data:t.data,path:t.path,parent:t}):i._parseSync({data:t.data,path:t.path,parent:t}):(rB(t,{code:rF.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[a]}),rZ)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,a){let n=new Map;for(let a of t){let t=sk(a.shape[e]);if(!t.length)throw Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(let i of t){if(n.has(i))throw Error(`Discriminator property ${String(e)} has duplicate value ${String(i)}`);n.set(i,a)}}return new sj({typeName:d.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:n,...rQ(a)})}}class sE extends r0{_parse(e){let{status:t,ctx:a}=this._processInputParams(e),n=(e,n)=>{if(rV(e)||rV(n))return rZ;let i=function e(t,a){let n=rz(t),i=rz(a);if(t===a)return{valid:!0,data:t};if(n===rM.object&&i===rM.object){let n=l.objectKeys(a),i=l.objectKeys(t).filter(e=>-1!==n.indexOf(e)),r={...t,...a};for(let n of i){let i=e(t[n],a[n]);if(!i.valid)return{valid:!1};r[n]=i.data}return{valid:!0,data:r}}if(n===rM.array&&i===rM.array){if(t.length!==a.length)return{valid:!1};let n=[];for(let i=0;i<t.length;i++){let r=e(t[i],a[i]);if(!r.valid)return{valid:!1};n.push(r.data)}return{valid:!0,data:n}}return n===rM.date&&i===rM.date&&+t==+a?{valid:!0,data:t}:{valid:!1}}(e.value,n.value);return i.valid?((rK(e)||rK(n))&&t.dirty(),{status:t.value,value:i.data}):(rB(a,{code:rF.invalid_intersection_types}),rZ)};return a.common.async?Promise.all([this._def.left._parseAsync({data:a.data,path:a.path,parent:a}),this._def.right._parseAsync({data:a.data,path:a.path,parent:a})]).then(([e,t])=>n(e,t)):n(this._def.left._parseSync({data:a.data,path:a.path,parent:a}),this._def.right._parseSync({data:a.data,path:a.path,parent:a}))}}sE.create=(e,t,a)=>new sE({left:e,right:t,typeName:d.ZodIntersection,...rQ(a)});class sC extends r0{_parse(e){let{status:t,ctx:a}=this._processInputParams(e);if(a.parsedType!==rM.array)return rB(a,{code:rF.invalid_type,expected:rM.array,received:a.parsedType}),rZ;if(a.data.length<this._def.items.length)return rB(a,{code:rF.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),rZ;!this._def.rest&&a.data.length>this._def.items.length&&(rB(a,{code:rF.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());let n=[...a.data].map((e,t)=>{let n=this._def.items[t]||this._def.rest;return n?n._parse(new rY(a,e,a.path,t)):null}).filter(e=>!!e);return a.common.async?Promise.all(n).then(e=>r$.mergeArray(t,e)):r$.mergeArray(t,n)}get items(){return this._def.items}rest(e){return new sC({...this._def,rest:e})}}sC.create=(e,t)=>{if(!Array.isArray(e))throw Error("You must pass an array of schemas to z.tuple([ ... ])");return new sC({items:e,typeName:d.ZodTuple,rest:null,...rQ(t)})};class sR extends r0{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:a}=this._processInputParams(e);if(a.parsedType!==rM.object)return rB(a,{code:rF.invalid_type,expected:rM.object,received:a.parsedType}),rZ;let n=[],i=this._def.keyType,r=this._def.valueType;for(let e in a.data)n.push({key:i._parse(new rY(a,e,a.path,e)),value:r._parse(new rY(a,a.data[e],a.path,e)),alwaysSet:e in a.data});return a.common.async?r$.mergeObjectAsync(t,n):r$.mergeObjectSync(t,n)}get element(){return this._def.valueType}static create(e,t,a){return new sR(t instanceof r0?{keyType:e,valueType:t,typeName:d.ZodRecord,...rQ(a)}:{keyType:sc.create(),valueType:e,typeName:d.ZodRecord,...rQ(t)})}}class sS extends r0{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:a}=this._processInputParams(e);if(a.parsedType!==rM.map)return rB(a,{code:rF.invalid_type,expected:rM.map,received:a.parsedType}),rZ;let n=this._def.keyType,i=this._def.valueType,r=[...a.data.entries()].map(([e,t],r)=>({key:n._parse(new rY(a,e,a.path,[r,"key"])),value:i._parse(new rY(a,t,a.path,[r,"value"]))}));if(a.common.async){let e=new Map;return Promise.resolve().then(async()=>{for(let a of r){let n=await a.key,i=await a.value;if("aborted"===n.status||"aborted"===i.status)return rZ;("dirty"===n.status||"dirty"===i.status)&&t.dirty(),e.set(n.value,i.value)}return{status:t.value,value:e}})}{let e=new Map;for(let a of r){let n=a.key,i=a.value;if("aborted"===n.status||"aborted"===i.status)return rZ;("dirty"===n.status||"dirty"===i.status)&&t.dirty(),e.set(n.value,i.value)}return{status:t.value,value:e}}}}sS.create=(e,t,a)=>new sS({valueType:t,keyType:e,typeName:d.ZodMap,...rQ(a)});class sO extends r0{_parse(e){let{status:t,ctx:a}=this._processInputParams(e);if(a.parsedType!==rM.set)return rB(a,{code:rF.invalid_type,expected:rM.set,received:a.parsedType}),rZ;let n=this._def;null!==n.minSize&&a.data.size<n.minSize.value&&(rB(a,{code:rF.too_small,minimum:n.minSize.value,type:"set",inclusive:!0,exact:!1,message:n.minSize.message}),t.dirty()),null!==n.maxSize&&a.data.size>n.maxSize.value&&(rB(a,{code:rF.too_big,maximum:n.maxSize.value,type:"set",inclusive:!0,exact:!1,message:n.maxSize.message}),t.dirty());let i=this._def.valueType;function r(e){let a=new Set;for(let n of e){if("aborted"===n.status)return rZ;"dirty"===n.status&&t.dirty(),a.add(n.value)}return{status:t.value,value:a}}let s=[...a.data.values()].map((e,t)=>i._parse(new rY(a,e,a.path,t)));return a.common.async?Promise.all(s).then(e=>r(e)):r(s)}min(e,t){return new sO({...this._def,minSize:{value:e,message:u.toString(t)}})}max(e,t){return new sO({...this._def,maxSize:{value:e,message:u.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}sO.create=(e,t)=>new sO({valueType:e,minSize:null,maxSize:null,typeName:d.ZodSet,...rQ(t)});class sT extends r0{constructor(){super(...arguments),this.validate=this.implement}_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==rM.function)return rB(t,{code:rF.invalid_type,expected:rM.function,received:t.parsedType}),rZ;function a(e,a){return rq({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,rI,rI].filter(e=>!!e),issueData:{code:rF.invalid_arguments,argumentsError:a}})}function n(e,a){return rq({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,rI,rI].filter(e=>!!e),issueData:{code:rF.invalid_return_type,returnTypeError:a}})}let i={errorMap:t.common.contextualErrorMap},r=t.data;if(this._def.returns instanceof sM){let e=this;return rH(async function(...t){let s=new rD([]),o=await e._def.args.parseAsync(t,i).catch(e=>{throw s.addIssue(a(t,e)),s}),c=await Reflect.apply(r,this,o);return await e._def.returns._def.type.parseAsync(c,i).catch(e=>{throw s.addIssue(n(c,e)),s})})}{let e=this;return rH(function(...t){let s=e._def.args.safeParse(t,i);if(!s.success)throw new rD([a(t,s.error)]);let o=Reflect.apply(r,this,s.data),c=e._def.returns.safeParse(o,i);if(!c.success)throw new rD([n(o,c.error)]);return c.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new sT({...this._def,args:sC.create(e).rest(sv.create())})}returns(e){return new sT({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,a){return new sT({args:e||sC.create([]).rest(sv.create()),returns:t||sv.create(),typeName:d.ZodFunction,...rQ(a)})}}class sN extends r0{get schema(){return this._def.getter()}_parse(e){let{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}sN.create=(e,t)=>new sN({getter:e,typeName:d.ZodLazy,...rQ(t)});class sA extends r0{_parse(e){if(e.data!==this._def.value){let t=this._getOrReturnCtx(e);return rB(t,{received:t.data,code:rF.invalid_literal,expected:this._def.value}),rZ}return{status:"valid",value:e.data}}get value(){return this._def.value}}function sP(e,t){return new sL({values:e,typeName:d.ZodEnum,...rQ(t)})}sA.create=(e,t)=>new sA({value:e,typeName:d.ZodLiteral,...rQ(t)});class sL extends r0{_parse(e){if("string"!=typeof e.data){let t=this._getOrReturnCtx(e),a=this._def.values;return rB(t,{expected:l.joinValues(a),received:t.parsedType,code:rF.invalid_type}),rZ}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(e.data)){let t=this._getOrReturnCtx(e),a=this._def.values;return rB(t,{received:t.data,code:rF.invalid_enum_value,options:a}),rZ}return rH(e.data)}get options(){return this._def.values}get enum(){let e={};for(let t of this._def.values)e[t]=t;return e}get Values(){let e={};for(let t of this._def.values)e[t]=t;return e}get Enum(){let e={};for(let t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return sL.create(e,{...this._def,...t})}exclude(e,t=this._def){return sL.create(this.options.filter(t=>!e.includes(t)),{...this._def,...t})}}sL.create=sP;class sU extends r0{_parse(e){let t=l.getValidEnumValues(this._def.values),a=this._getOrReturnCtx(e);if(a.parsedType!==rM.string&&a.parsedType!==rM.number){let e=l.objectValues(t);return rB(a,{expected:l.joinValues(e),received:a.parsedType,code:rF.invalid_type}),rZ}if(this._cache||(this._cache=new Set(l.getValidEnumValues(this._def.values))),!this._cache.has(e.data)){let e=l.objectValues(t);return rB(a,{received:a.data,code:rF.invalid_enum_value,options:e}),rZ}return rH(e.data)}get enum(){return this._def.values}}sU.create=(e,t)=>new sU({values:e,typeName:d.ZodNativeEnum,...rQ(t)});class sM extends r0{unwrap(){return this._def.type}_parse(e){let{ctx:t}=this._processInputParams(e);return t.parsedType!==rM.promise&&!1===t.common.async?(rB(t,{code:rF.invalid_type,expected:rM.promise,received:t.parsedType}),rZ):rH((t.parsedType===rM.promise?t.data:Promise.resolve(t.data)).then(e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap})))}}sM.create=(e,t)=>new sM({type:e,typeName:d.ZodPromise,...rQ(t)});class sz extends r0{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===d.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){let{status:t,ctx:a}=this._processInputParams(e),n=this._def.effect||null,i={addIssue:e=>{rB(a,e),e.fatal?t.abort():t.dirty()},get path(){return a.path}};if(i.addIssue=i.addIssue.bind(i),"preprocess"===n.type){let e=n.transform(a.data,i);if(a.common.async)return Promise.resolve(e).then(async e=>{if("aborted"===t.value)return rZ;let n=await this._def.schema._parseAsync({data:e,path:a.path,parent:a});return"aborted"===n.status?rZ:"dirty"===n.status||"dirty"===t.value?rW(n.value):n});{if("aborted"===t.value)return rZ;let n=this._def.schema._parseSync({data:e,path:a.path,parent:a});return"aborted"===n.status?rZ:"dirty"===n.status||"dirty"===t.value?rW(n.value):n}}if("refinement"===n.type){let e=e=>{let t=n.refinement(e,i);if(a.common.async)return Promise.resolve(t);if(t instanceof Promise)throw Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1!==a.common.async)return this._def.schema._parseAsync({data:a.data,path:a.path,parent:a}).then(a=>"aborted"===a.status?rZ:("dirty"===a.status&&t.dirty(),e(a.value).then(()=>({status:t.value,value:a.value}))));{let n=this._def.schema._parseSync({data:a.data,path:a.path,parent:a});return"aborted"===n.status?rZ:("dirty"===n.status&&t.dirty(),e(n.value),{status:t.value,value:n.value})}}if("transform"===n.type){if(!1!==a.common.async)return this._def.schema._parseAsync({data:a.data,path:a.path,parent:a}).then(e=>rG(e)?Promise.resolve(n.transform(e.value,i)).then(e=>({status:t.value,value:e})):rZ);{let e=this._def.schema._parseSync({data:a.data,path:a.path,parent:a});if(!rG(e))return rZ;let r=n.transform(e.value,i);if(r instanceof Promise)throw Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:r}}}l.assertNever(n)}}sz.create=(e,t,a)=>new sz({schema:e,typeName:d.ZodEffects,effect:t,...rQ(a)}),sz.createWithPreprocess=(e,t,a)=>new sz({schema:t,effect:{type:"preprocess",transform:e},typeName:d.ZodEffects,...rQ(a)});class sF extends r0{_parse(e){return this._getType(e)===rM.undefined?rH(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}sF.create=(e,t)=>new sF({innerType:e,typeName:d.ZodOptional,...rQ(t)});class sD extends r0{_parse(e){return this._getType(e)===rM.null?rH(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}sD.create=(e,t)=>new sD({innerType:e,typeName:d.ZodNullable,...rQ(t)});class sI extends r0{_parse(e){let{ctx:t}=this._processInputParams(e),a=t.data;return t.parsedType===rM.undefined&&(a=this._def.defaultValue()),this._def.innerType._parse({data:a,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}sI.create=(e,t)=>new sI({innerType:e,typeName:d.ZodDefault,defaultValue:"function"==typeof t.default?t.default:()=>t.default,...rQ(t)});class sq extends r0{_parse(e){let{ctx:t}=this._processInputParams(e),a={...t,common:{...t.common,issues:[]}},n=this._def.innerType._parse({data:a.data,path:a.path,parent:{...a}});return rX(n)?n.then(e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new rD(a.common.issues)},input:a.data})})):{status:"valid",value:"valid"===n.status?n.value:this._def.catchValue({get error(){return new rD(a.common.issues)},input:a.data})}}removeCatch(){return this._def.innerType}}sq.create=(e,t)=>new sq({innerType:e,typeName:d.ZodCatch,catchValue:"function"==typeof t.catch?t.catch:()=>t.catch,...rQ(t)});class sB extends r0{_parse(e){if(this._getType(e)!==rM.nan){let t=this._getOrReturnCtx(e);return rB(t,{code:rF.invalid_type,expected:rM.nan,received:t.parsedType}),rZ}return{status:"valid",value:e.data}}}sB.create=e=>new sB({typeName:d.ZodNaN,...rQ(e)}),Symbol("zod_brand");class s$ extends r0{_parse(e){let{ctx:t}=this._processInputParams(e),a=t.data;return this._def.type._parse({data:a,path:t.path,parent:t})}unwrap(){return this._def.type}}class sZ extends r0{_parse(e){let{status:t,ctx:a}=this._processInputParams(e);if(a.common.async)return(async()=>{let e=await this._def.in._parseAsync({data:a.data,path:a.path,parent:a});return"aborted"===e.status?rZ:"dirty"===e.status?(t.dirty(),rW(e.value)):this._def.out._parseAsync({data:e.value,path:a.path,parent:a})})();{let e=this._def.in._parseSync({data:a.data,path:a.path,parent:a});return"aborted"===e.status?rZ:"dirty"===e.status?(t.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:a.path,parent:a})}}static create(e,t){return new sZ({in:e,out:t,typeName:d.ZodPipeline})}}class sW extends r0{_parse(e){let t=this._def.innerType._parse(e),a=e=>(rG(e)&&(e.value=Object.freeze(e.value)),e);return rX(t)?t.then(e=>a(e)):a(t)}unwrap(){return this._def.innerType}}sW.create=(e,t)=>new sW({innerType:e,typeName:d.ZodReadonly,...rQ(t)}),sw.lazycreate,function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"}(d||(d={}));let sH=sc.create;sl.create,sB.create,sp.create;let sV=su.create;sd.create,sm.create,sf.create,sh.create,sx.create,sv.create,sg.create,sb.create;let sK=sy.create,sG=sw.create;sw.strictCreate,s_.create,sj.create,sE.create,sC.create,sR.create,sS.create,sO.create,sT.create,sN.create;let sX=sA.create;sL.create,sU.create,sM.create,sz.create,sF.create,sD.create,sz.createWithPreprocess,sZ.create;let sY=sH().min(2,"Username must be at least 2 characters").max(32,"Username must be at most 32 characters").regex(/^@[a-zA-Z0-9_]+$/,"Username must start with @ and contain only letters, numbers, and underscores"),sJ=sH().min(1,"Project name is required").max(100,"Project name must be at most 100 characters").trim(),sQ=sH().url("Must be a valid URL").regex(/^https:\/\/(calendly\.com|cal\.com)/,"Must be a Calendly or Cal.com link").optional().or(sX("")),s0=sH().email("Must be a valid email address").optional();function s1(e){try{return sY.parse(e),{isValid:!0}}catch(e){if(e instanceof rD)return{isValid:!1,error:e.errors[0]?.message};return{isValid:!1,error:"Invalid username"}}}function s2(e){try{return sJ.parse(e),{isValid:!0}}catch(e){if(e instanceof rD)return{isValid:!1,error:e.errors[0]?.message};return{isValid:!1,error:"Invalid project name"}}}function s3(e,t){if(!e)return"";let a=t?.emoji||"";return`${a} ${e} <> IBC Group`}function s4(){var e,t;let{user:a}=(0,nU.aF)(),[n,i]=(0,h.useState)([]),[r,s]=(0,h.useState)([]),[o,c]=(0,h.useState)(!0);(0,h.useEffect)(()=>{(async()=>{try{c(!0);let e=await rL.get("/api/team-data");console.log("\uD83D\uDCCA Team data response:",e.data);let t=(e.data.salesReps||[]).filter(e=>e.username&&""!==e.username.trim());console.log("✅ Filtered salesReps:",t),i(t),s(e.data.outreachMembers||[]),e.data.warning&&rU.toast.warning(e.data.warning)}catch(e){console.error("❌ Failed to fetch team data:",e),rU.toast.error("Failed to load team data. Some features may not work properly.")}finally{c(!1)}})()},[]);let[l,p]=(0,h.useState)(!1),[u,d]=(0,h.useState)(null),[m,x]=(0,h.useState)({projectName:"",projectLeads:[""],outreachTeamMember:"",outreachMemberName:"",outreachMemberUsername:"",customOutreachUsername:"",useCustomOutreachUsername:!1,salesperson:"",customSalesRepUsername:"",useCustomSalesRepUsername:!1,enterSalesRepManually:!1,customCalendlyLink:"",includeCalendly:!0,errors:{projectName:"",projectLead:"",outreachTeamMember:"",salesperson:"",outreachMemberUsername:"",customOutreachUsername:"",customSalesRepUsername:"",customCalendlyLink:""}});(0,h.useEffect)(()=>{a?.fullName&&x(e=>({...e,outreachTeamMember:a.fullName||""}))},[a]);let v=(e=m.salesperson,n.find(t=>t.username===e)),g=(t=m.outreachMemberName,r.find(e=>e.name===t)),b=()=>{let e={projectName:"",projectLead:"",outreachTeamMember:"",salesperson:"",outreachMemberUsername:"",customOutreachUsername:"",customSalesRepUsername:"",customCalendlyLink:""},t=s2(m.projectName);t.isValid||(e.projectName=t.error||"Invalid project name");let a=m.projectLeads.map(e=>e.trim()).filter(e=>e.length>0);if(0===a.length)e.projectLead="At least one project lead username is required";else for(let t of a){let a=s1(t);if(!a.isValid){e.projectLead=a.error||"Invalid username format";break}}if(m.useCustomOutreachUsername){if(m.customOutreachUsername){let t=s1(m.customOutreachUsername);t.isValid||(e.customOutreachUsername=t.error||"Invalid username format")}else e.customOutreachUsername="Custom BDR username is required"}else m.outreachMemberName&&m.outreachMemberUsername||(e.outreachMemberUsername="Outreach member and username are required");if(m.enterSalesRepManually){if(m.customSalesRepUsername){let t=s1(m.customSalesRepUsername);t.isValid||(e.customSalesRepUsername=t.error||"Invalid username format")}else e.customSalesRepUsername="Sales rep username is required"}else if(m.salesperson){if(m.useCustomSalesRepUsername){if(m.customSalesRepUsername){let t=s1(m.customSalesRepUsername);t.isValid||(e.customSalesRepUsername=t.error||"Invalid username format")}else e.customSalesRepUsername="Custom sales rep username is required"}}else e.salesperson="Sales representative is required";if(m.customCalendlyLink){let t=function(e){if(!e)return{isValid:!0};try{return sQ.parse(e),{isValid:!0}}catch(e){if(e instanceof rD)return{isValid:!1,error:e.errors[0]?.message};return{isValid:!1,error:"Invalid Calendly link"}}}(m.customCalendlyLink);t.isValid||(e.customCalendlyLink=t.error||"Invalid Calendly link")}return x(t=>({...t,errors:e})),!Object.values(e).some(e=>e)},y=async e=>{if(e.preventDefault(),b()){p(!0),d(null);try{let e;console.log("\uD83D\uDE80 Starting form submission..."),m.enterSalesRepManually?e=m.customCalendlyLink||void 0:v&&(e=v.calendarLink);let t=m.useCustomOutreachUsername?m.customOutreachUsername:m.outreachMemberUsername,a=m.enterSalesRepManually?m.customSalesRepUsername:m.useCustomSalesRepUsername&&v?m.customSalesRepUsername:v?.username;if(!a){rU.toast.error("Sales representative username is required");return}let n=m.projectLeads.map(e=>e.trim()).filter(e=>e.length>0),i=`👥 Project Lead: ${n.join(", ")}
👨‍💼 Partnership Representative: ${a}
👨‍💻 BDR: ${t}${m.includeCalendly&&e?`
📅 Schedule a meeting: ${e}`:""}`,r={projectName:m.projectName,projectLeads:n,outreachTeamMember:t,outreachMemberEmail:g?.email,salesperson:m.enterSalesRepManually?"Custom Sales Rep":m.salesperson,salesRepUsername:a,paUsername:m.enterSalesRepManually?void 0:v?.paUsername,calendlyLink:m.includeCalendly?e:void 0,includeCalendly:m.includeCalendly,welcomeMessage:i,inviteSalesRep:!0,salesRepEmoji:m.enterSalesRepManually?void 0:v?.emoji,salesRepPAUsername:m.enterSalesRepManually?void 0:v?.paUsername,salesRepCalendlyLink:m.enterSalesRepManually?m.customCalendlyLink:m.includeCalendly&&v?.calendarLink?v.calendarLink:void 0,salesRepCategory:m.enterSalesRepManually?void 0:v?.tier,outreachMemberUsernames:g?.telegramUsernames,outreachMemberEmoji:g?.emoji};console.log("\uD83D\uDCE4 Sending request:",r);let s=await rL.post("/api/create-group",r);s.data.success?(rU.toast.success("\uD83C\uDF89 Telegram group created successfully!"),d({success:!0,failed_invites:s.data.failed_invites}),x({projectName:"",projectLeads:[""],outreachTeamMember:"",outreachMemberName:"",outreachMemberUsername:"",customOutreachUsername:"",useCustomOutreachUsername:!1,salesperson:"",customSalesRepUsername:"",useCustomSalesRepUsername:!1,enterSalesRepManually:!1,customCalendlyLink:"",includeCalendly:!0,errors:{projectName:"",projectLead:"",outreachTeamMember:"",salesperson:"",outreachMemberUsername:"",customOutreachUsername:"",customSalesRepUsername:"",customCalendlyLink:""}})):rU.toast.error(s.data.error||"Failed to create group")}catch(t){console.error("❌ Error creating group:",t);let e=t.response?.data?.error||t.message||"Failed to create Telegram group. Please try again.";rU.toast.error(e),d({success:!1,failed_invites:[]})}finally{p(!1)}}};return console.log("Sales Representatives:",n),(0,f.jsxs)("div",{className:"container mx-auto p-4 max-w-3xl",children:[f.jsx("div",{children:l?f.jsx("div",{className:"fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50",children:f.jsx("div",{className:"bg-white/90 rounded-xl p-8 max-w-md w-full mx-4 text-center shadow-2xl",children:(0,f.jsxs)("div",{className:"flex flex-col items-center justify-center gap-6",children:[(0,f.jsxs)("div",{className:"relative w-24 h-24",children:[f.jsx("div",{className:"absolute inset-0 rounded-full border-8 border-gray-200"}),f.jsx("div",{className:"absolute inset-0 rounded-full border-8 border-blue-600 animate-spin border-t-transparent"})]}),(0,f.jsxs)("div",{children:[f.jsx("h3",{className:"text-xl font-bold text-blue-600",children:"Creating Your Telegram Group"}),f.jsx("p",{className:"text-gray-600 mt-3",children:"Please wait while we set up your group with all required members and settings..."})]})]})})}):null}),(0,f.jsxs)(ep,{children:[f.jsx(eu,{children:f.jsx(ed,{className:"text-2xl font-bold text-center",children:"Create Telegram Group"})}),(0,f.jsxs)(em,{children:[f.jsx("div",{children:u&&u.success&&u.failed_invites&&u.failed_invites.length>0?(0,f.jsxs)("div",{className:"mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:[f.jsx("h3",{className:"text-amber-700 font-medium",children:"Some members couldn't be invited automatically"}),f.jsx("p",{className:"text-sm text-amber-600 mt-1",children:"The bot has automatically sent /invite_new_group commands for these usernames in the group chat:"}),f.jsx("ul",{className:"mt-2 text-sm text-amber-800 list-disc list-inside",children:u.failed_invites.map((e,t)=>f.jsx("li",{children:e.username},t))})]}):null}),(0,f.jsxs)("form",{onSubmit:y,className:"space-y-6",children:[(0,f.jsxs)("div",{className:"space-y-2",children:[f.jsx(eb,{htmlFor:"projectName",children:"Project Name"}),f.jsx(ef,{id:"projectName",value:m.projectName,onChange:e=>{let t=e.target.value,a=s2(t);x({...m,projectName:t,errors:{...m.errors,projectName:a.isValid?"":a.error||"Invalid project name"}})},placeholder:"Enter project name",className:m.errors.projectName?"border-red-500":""}),m.errors.projectName&&f.jsx("p",{className:"text-sm text-red-500",children:m.errors.projectName}),m.projectName&&!m.errors.projectName&&(0,f.jsxs)("p",{className:"text-sm text-gray-500",children:["Group will be created as: ",s3(m.projectName,v)]})]}),(0,f.jsxs)("div",{className:"space-y-2",children:[f.jsx(eb,{htmlFor:"projectLead",children:"Project Lead Telegram Usernames"}),m.projectLeads.map((e,t)=>(0,f.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[f.jsx(ef,{value:e,onChange:e=>{let a=e.target.value,n=[...m.projectLeads];n[t]=a;let i=n.filter(e=>e.trim().length>0),r="";if(0===i.length)r="At least one project lead is required";else for(let e of i){let t=s1(e);if(!t.isValid){r=t.error||"Invalid username format";break}}x({...m,projectLeads:n,errors:{...m.errors,projectLead:r}})},placeholder:"@username",className:`flex-1 ${m.errors.projectLead?"border-red-500":""}`}),f.jsx(el,{type:"button",variant:"destructive",size:"sm",onClick:()=>{if(1===m.projectLeads.length)return;let e=m.projectLeads.filter((e,a)=>a!==t);x({...m,projectLeads:e,errors:{...m.errors,projectLead:""}})},disabled:1===m.projectLeads.length,children:"–"})]},t)),f.jsx(el,{type:"button",variant:"outline",size:"sm",onClick:()=>{x({...m,projectLeads:[...m.projectLeads,""],errors:{...m.errors,projectLead:""}})},children:"+ Add Project Lead"}),m.errors.projectLead&&f.jsx("p",{className:"text-sm text-red-500",children:m.errors.projectLead})]}),(0,f.jsxs)("div",{className:"space-y-2",children:[f.jsx(eb,{htmlFor:"outreachMember",children:"Outreach Team Member"}),(0,f.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[f.jsx("input",{type:"checkbox",id:"useCustomOutreachUsername",checked:m.useCustomOutreachUsername,onChange:e=>x({...m,useCustomOutreachUsername:e.target.checked,errors:{...m.errors,outreachMemberUsername:"",customOutreachUsername:""}}),className:"h-4 w-4"}),f.jsx(eb,{htmlFor:"useCustomOutreachUsername",children:"Enter BDR username manually"})]}),m.useCustomOutreachUsername?(0,f.jsxs)("div",{className:"space-y-2",children:[f.jsx(eb,{htmlFor:"customOutreachUsername",children:"Custom BDR Telegram Username"}),f.jsx(ef,{id:"customOutreachUsername",value:m.customOutreachUsername,onChange:e=>{let t=e.target.value,a=s1(t);x({...m,customOutreachUsername:t,errors:{...m.errors,customOutreachUsername:a.isValid?"":a.error||"Invalid username"}})},placeholder:"@username",className:m.errors.customOutreachUsername?"border-red-500":""}),m.errors.customOutreachUsername&&f.jsx("p",{className:"text-sm text-red-500",children:m.errors.customOutreachUsername})]}):(0,f.jsxs)(f.Fragment,{children:[(0,f.jsxs)(nO,{value:m.outreachMemberName,onValueChange:e=>x({...m,outreachMemberName:e,outreachMemberUsername:"",errors:{...m.errors,outreachMemberUsername:""}}),children:[f.jsx(nT,{id:"outreachMember",className:m.errors.outreachMemberUsername?"border-red-500":"",children:f.jsx(aQ,{placeholder:"Select an outreach member"})}),f.jsx(nP,{children:r.map((e,t)=>(0,f.jsxs)(nL,{value:e.name,children:[e.emoji," ",e.name]},t))})]}),g&&(0,f.jsxs)("div",{className:"mt-2",children:[f.jsx(eb,{htmlFor:"outreachUsername",children:"Telegram Username"}),(0,f.jsxs)("div",{className:"mt-2 p-4 bg-gray-50 rounded-lg space-y-2",children:[(0,f.jsxs)("p",{className:"text-sm font-medium",children:[g.emoji," ",g.name]}),(0,f.jsxs)(nO,{value:m.outreachMemberUsername,onValueChange:e=>x({...m,outreachMemberUsername:e,errors:{...m.errors,outreachMemberUsername:""}}),children:[f.jsx(nT,{id:"outreachUsername",children:f.jsx(aQ,{placeholder:"Select username"})}),f.jsx(nP,{children:g?.telegramUsernames?.filter(e=>e).map(e=>f.jsx(nL,{value:e,children:e},e))})]})]})]})]}),m.errors.outreachMemberUsername&&f.jsx("p",{className:"text-sm text-red-500",children:m.errors.outreachMemberUsername})]}),(0,f.jsxs)("div",{className:"space-y-2",children:[f.jsx(eb,{htmlFor:"salesperson",children:"Sales Representative"}),(0,f.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[f.jsx("input",{type:"checkbox",id:"enterSalesRepManually",checked:m.enterSalesRepManually,onChange:e=>x({...m,enterSalesRepManually:e.target.checked,errors:{...m.errors,customSalesRepUsername:""}}),className:"h-4 w-4"}),f.jsx(eb,{htmlFor:"enterSalesRepManually",children:"Enter Partnership Representative manually"})]}),m.enterSalesRepManually?(0,f.jsxs)("div",{className:"space-y-2",children:[f.jsx(eb,{htmlFor:"customSalesRepUsername",children:"Partnership Representative Username"}),f.jsx(ef,{id:"customSalesRepUsername",value:m.customSalesRepUsername,onChange:e=>x({...m,customSalesRepUsername:e.target.value,errors:{...m.errors,customSalesRepUsername:""}}),placeholder:"@username",className:m.errors.customSalesRepUsername?"border-red-500":""}),m.errors.customSalesRepUsername&&f.jsx("p",{className:"text-sm text-red-500",children:m.errors.customSalesRepUsername}),f.jsx("div",{className:"flex items-center space-x-4 mt-4",children:(0,f.jsxs)("div",{className:"flex items-center space-x-2",children:[f.jsx("input",{type:"checkbox",id:"includeCalendlyManual",checked:m.includeCalendly,onChange:e=>x({...m,includeCalendly:e.target.checked}),className:"h-4 w-4"}),f.jsx(eb,{htmlFor:"includeCalendlyManual",children:"Include Calendly Link"})]})}),m.includeCalendly&&(0,f.jsxs)("div",{className:"mt-2",children:[f.jsx(eb,{htmlFor:"customCalendlyLink",children:"Calendly Link"}),f.jsx(ef,{id:"customCalendlyLink",value:m.customCalendlyLink,onChange:e=>x({...m,customCalendlyLink:e.target.value,errors:{...m.errors,customCalendlyLink:""}}),placeholder:"https://calendly.com/your-link",className:m.errors.customCalendlyLink?"border-red-500":""}),m.errors.customCalendlyLink&&f.jsx("p",{className:"text-sm text-red-500",children:m.errors.customCalendlyLink})]})]}):o?(0,f.jsxs)("div",{className:"flex items-center space-x-2 p-3 border rounded-md",children:[f.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"}),f.jsx("span",{className:"text-sm text-gray-500",children:"Loading sales representatives..."})]}):n&&n.length>0?(0,f.jsxs)(nO,{value:m.salesperson,onValueChange:e=>x({...m,salesperson:e,errors:{...m.errors,salesperson:""}}),children:[f.jsx(nT,{id:"salesperson",className:m.errors.salesperson?"border-red-500":"",children:f.jsx(aQ,{placeholder:"Select a sales representative"})}),f.jsx(nP,{children:n.filter(e=>e.username).map((e,t)=>f.jsx(nL,{value:e.username,children:`${e.emoji} ${e.name}`},t))})]}):f.jsx("div",{className:"p-3 border rounded-md bg-yellow-50 border-yellow-200",children:f.jsx("p",{className:"text-sm text-yellow-800",children:"⚠️ No sales representatives available. Please check your Google Sheets configuration."})}),v&&(0,f.jsxs)("div",{className:"mt-2 p-4 bg-gray-50 rounded-lg space-y-2",children:[(0,f.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[f.jsx("input",{type:"checkbox",id:"useCustomSalesRepUsername",checked:m.useCustomSalesRepUsername,onChange:e=>x({...m,useCustomSalesRepUsername:e.target.checked,errors:{...m.errors,customSalesRepUsername:""}}),className:"h-4 w-4"}),f.jsx(eb,{htmlFor:"useCustomSalesRepUsername",children:"Use custom Telegram username"})]}),m.useCustomSalesRepUsername?(0,f.jsxs)("div",{className:"space-y-2",children:[f.jsx(eb,{htmlFor:"customSalesRepUsername",children:"Custom Partnership Representative Username"}),f.jsx(ef,{id:"customSalesRepUsername",value:m.customSalesRepUsername,onChange:e=>x({...m,customSalesRepUsername:e.target.value,errors:{...m.errors,customSalesRepUsername:""}}),placeholder:"@username",className:m.errors.customSalesRepUsername?"border-red-500":""}),m.errors.customSalesRepUsername&&f.jsx("p",{className:"text-sm text-red-500",children:m.errors.customSalesRepUsername})]}):(0,f.jsxs)("p",{className:"text-sm",children:[f.jsx("span",{className:"font-semibold",children:"TG Username:"})," ",v.username]}),(0,f.jsxs)("p",{className:"text-sm",children:[f.jsx("span",{className:"font-semibold",children:"PA Username:"})," ",v.paUsername]}),f.jsx("div",{className:"flex items-center space-x-4",children:(0,f.jsxs)("div",{className:"flex items-center space-x-2",children:[f.jsx("input",{type:"checkbox",id:"includeCalendlySelected",checked:m.includeCalendly,onChange:e=>x({...m,includeCalendly:e.target.checked}),className:"h-4 w-4"}),f.jsx(eb,{htmlFor:"includeCalendlySelected",children:"Include Calendly Link"})]})}),m.includeCalendly&&v&&(0,f.jsxs)("p",{className:"text-sm",children:[f.jsx("span",{className:"font-semibold",children:"Meeting Link:"})," ",f.jsx("a",{href:v?.calendarLink,target:"_blank",rel:"noopener noreferrer",className:"text-blue-500 hover:underline",children:"Schedule Meeting"})]}),(0,f.jsxs)("p",{className:"text-sm",children:[f.jsx("span",{className:"font-semibold",children:"Pipeline:"})," ",v.tier]})]})]}),(0,f.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[f.jsx("input",{type:"checkbox",id:"includeCalendlyGlobal",checked:m.includeCalendly,onChange:e=>x({...m,includeCalendly:e.target.checked}),className:"h-4 w-4"}),f.jsx(eb,{htmlFor:"includeCalendlyGlobal",children:"Include Calendly Link"})]}),(0,f.jsxs)("div",{className:"text-sm text-gray-500 p-4 bg-gray-50 rounded-lg",children:[f.jsx("p",{className:"font-semibold mb-2",children:"Group Creation Process:"}),(0,f.jsxs)("div",{className:"space-y-4",children:[(0,f.jsxs)("div",{children:[f.jsx("p",{className:"font-semibold text-gray-700",children:"1. Initial Members:"}),(0,f.jsxs)("ul",{className:"list-disc list-inside space-y-1 ml-4",children:[f.jsx("li",{children:"@IbcAdmin_Bot (Bot Admin)"}),v&&"None"!==v.paUsername&&(0,f.jsxs)("li",{children:[v.paUsername," (PA - Will be made admin)"]}),g&&m.outreachMemberUsername&&(0,f.jsxs)("li",{children:[m.outreachMemberUsername," (",g.emoji," Outreach Admin)"]}),m.projectLeads.filter(e=>e.trim().length>0).length>0&&(0,f.jsxs)("li",{children:[m.projectLeads.filter(e=>e.trim().length>0).join(", ")," (Project Lead)"]})]})]}),(0,f.jsxs)("div",{children:[f.jsx("p",{className:"font-semibold text-gray-700",children:"2. Automated Actions:"}),(0,f.jsxs)("ul",{className:"list-disc list-inside space-y-1 ml-4",children:[f.jsx("li",{children:"Welcome message will be sent by the IBC Admin account"}),f.jsx("li",{children:"Bot will execute /optin command"}),!m.enterSalesRepManually&&v&&"None"!==v.paUsername&&f.jsx("li",{children:"PA will be made admin via /setadmin"}),m.enterSalesRepManually&&m.customSalesRepUsername||v?(0,f.jsxs)("li",{children:["Sales rep will be invited via their username: ",m.enterSalesRepManually?m.customSalesRepUsername:m.useCustomSalesRepUsername&&m.customSalesRepUsername?m.customSalesRepUsername:v?.username]}):null,m.enterSalesRepManually&&m.customSalesRepUsername||v?f.jsx("li",{children:"Bot will execute /invite_new_group for the sales rep"}):null]})]}),(0,f.jsxs)("div",{children:[f.jsx("p",{className:"font-semibold text-gray-700",children:"3. Group Settings:"}),(0,f.jsxs)("ul",{className:"list-disc list-inside space-y-1 ml-4",children:[(0,f.jsxs)("li",{children:["Group Name: ",m.projectName?s3(m.projectName,v):"Will be set based on project name"]}),f.jsx("li",{children:"Type: Supergroup (Megagroup)"}),f.jsx("li",{children:"About: IBC Group Discussion"})]})]}),(0,f.jsxs)("div",{children:[f.jsx("p",{className:"font-semibold text-gray-700",children:"4. Welcome Message:"}),(v||m.enterSalesRepManually&&m.customSalesRepUsername)&&m.projectLeads.filter(e=>e.trim().length>0).length>0&&(m.outreachMemberUsername||m.useCustomOutreachUsername&&m.customOutreachUsername)?(0,f.jsxs)("div",{className:"bg-white p-3 rounded border mt-2 text-gray-800 font-mono text-sm",children:[(0,f.jsxs)("div",{children:["\uD83D\uDC65 Project Lead: ",m.projectLeads.filter(e=>e.trim().length>0).join(", ")]}),(0,f.jsxs)("div",{children:["\uD83D\uDC68‍\uD83D\uDCBC Partnership Representative: ",m.enterSalesRepManually?m.customSalesRepUsername:m.useCustomSalesRepUsername&&m.customSalesRepUsername?m.customSalesRepUsername:v?.username]}),(0,f.jsxs)("div",{children:["\uD83D\uDC68‍\uD83D\uDCBB BDR: ",m.useCustomOutreachUsername?m.customOutreachUsername:m.outreachMemberUsername]}),m.includeCalendly&&m.enterSalesRepManually&&m.customCalendlyLink&&(0,f.jsxs)("div",{children:["\uD83D\uDCC5 Schedule a meeting: ",m.customCalendlyLink]}),m.includeCalendly&&!m.enterSalesRepManually&&v&&(0,f.jsxs)("div",{children:["\uD83D\uDCC5 Schedule a meeting: ",v?.calendarLink]})]}):(0,f.jsxs)("div",{className:"bg-white p-3 rounded border mt-2 text-gray-800 font-mono text-sm opacity-50",children:[f.jsx("div",{children:"\uD83D\uDC65 Project Lead: (Enter project lead)"}),f.jsx("div",{children:"\uD83D\uDC68‍\uD83D\uDCBC Partnership Representative: (Select representative)"}),f.jsx("div",{children:"\uD83D\uDC68‍\uD83D\uDCBB BDR: (Select BDR)"}),f.jsx("div",{children:"\uD83D\uDCC5 Schedule a meeting: (Will show if enabled)"})]})]}),(0,f.jsxs)("div",{children:[(0,f.jsxs)("p",{className:"font-semibold text-gray-700",children:[(v||m.enterSalesRepManually&&m.customSalesRepUsername)&&m.projectLeads.filter(e=>e.trim().length>0).length>0&&(m.outreachMemberUsername||m.useCustomOutreachUsername&&m.customOutreachUsername)?"5":"4",". Sales Rep Details:"]}),v||m.enterSalesRepManually&&m.customSalesRepUsername?(0,f.jsxs)("ul",{className:"list-none space-y-1 ml-4",children:[(0,f.jsxs)("li",{children:[f.jsx("span",{className:"font-medium",children:"TG Username:"})," ",f.jsx("span",{className:"text-blue-600",children:m.enterSalesRepManually?m.customSalesRepUsername:m.useCustomSalesRepUsername&&m.customSalesRepUsername?m.customSalesRepUsername:v?.username}),(m.enterSalesRepManually||m.useCustomSalesRepUsername&&m.customSalesRepUsername)&&f.jsx("span",{className:"ml-2 text-sm text-orange-600",children:"(Custom)"})]}),m.enterSalesRepManually&&m.includeCalendly&&m.customCalendlyLink&&(0,f.jsxs)("li",{children:[f.jsx("span",{className:"font-medium",children:"Meeting Link:"})," ",f.jsx("a",{href:m.customCalendlyLink,target:"_blank",rel:"noopener noreferrer",className:"text-blue-500 hover:underline",children:"Schedule Meeting"})]}),!m.enterSalesRepManually&&v&&(0,f.jsxs)(f.Fragment,{children:[(0,f.jsxs)("li",{children:[f.jsx("span",{className:"font-medium",children:"PA Username:"})," ",f.jsx("span",{className:"text-blue-600",children:v.paUsername})]}),(0,f.jsxs)("li",{children:[f.jsx("span",{className:"font-medium",children:"Pipeline:"})," ",f.jsx("span",{className:"text-blue-600",children:v.tier})]}),m.includeCalendly&&(0,f.jsxs)("li",{children:[f.jsx("span",{className:"font-medium",children:"Meeting Link:"})," ",f.jsx("a",{href:v?.calendarLink,target:"_blank",rel:"noopener noreferrer",className:"text-blue-500 hover:underline",children:"Schedule Meeting"})]})]})]}):(0,f.jsxs)("ul",{className:"list-none space-y-1 ml-4 opacity-50",children:[(0,f.jsxs)("li",{children:[f.jsx("span",{className:"font-medium",children:"TG Username:"})," (Select or enter sales rep)"]}),(0,f.jsxs)("li",{children:[f.jsx("span",{className:"font-medium",children:"Details:"})," Additional details will appear here"]})]})]})]})]}),f.jsx(el,{type:"submit",className:"w-full bg-blue-600 hover:bg-blue-700 text-white",disabled:Object.values(m.errors).some(e=>""!==e),children:"Create Telegram Group"}),f.jsx("p",{className:"text-xs text-gray-500 text-center",children:"This will create a new Telegram group with all required members and settings. The process takes a few seconds to complete."})]})]})]})]})}sG({projectName:sJ,projectLeads:sK(sY).min(1,"At least one project lead is required").max(5,"Maximum 5 project leads allowed"),outreachMemberName:sH().min(1,"Outreach member is required").optional(),outreachMemberUsername:sY.optional(),customOutreachUsername:sY.optional(),useCustomOutreachUsername:sV(),salesperson:sH().optional(),customSalesRepUsername:sY.optional(),useCustomSalesRepUsername:sV(),enterSalesRepManually:sV(),customCalendlyLink:sQ,includeCalendly:sV()}).refine(e=>(!e.useCustomOutreachUsername||!!e.customOutreachUsername)&&(!!e.useCustomOutreachUsername||!!e.outreachMemberName&&!!e.outreachMemberUsername)&&(!e.enterSalesRepManually||!!e.customSalesRepUsername)&&(!!e.enterSalesRepManually||!!e.salesperson)&&(!e.useCustomSalesRepUsername||!!e.customSalesRepUsername),{message:"Please fill in all required fields correctly"}),sG({projectName:sJ,projectLeads:sK(sY).min(1).max(5),outreachTeamMember:sY,outreachMemberEmail:s0,salesperson:sH().min(1,"Salesperson is required"),salesRepUsername:sY,paUsername:sY.optional(),calendlyLink:sQ,includeCalendly:sV(),welcomeMessage:sH().min(1,"Welcome message is required"),inviteSalesRep:sV(),salesRepEmoji:sH().optional(),salesRepPAUsername:sY.optional(),salesRepCalendlyLink:sQ,salesRepCategory:sH().optional(),outreachMemberUsernames:sK(sY).optional(),outreachMemberEmoji:sH().optional()}),sG({clientEmail:sH().email("Invalid client email"),privateKey:sH().min(1,"Private key is required"),spreadsheetId:sH().min(1,"Spreadsheet ID is required")})},14409:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{RSC_HEADER:function(){return a},ACTION:function(){return n},NEXT_ROUTER_STATE_TREE:function(){return i},NEXT_ROUTER_PREFETCH_HEADER:function(){return r},NEXT_URL:function(){return s},RSC_CONTENT_TYPE_HEADER:function(){return o},RSC_VARY_HEADER:function(){return c},FLIGHT_PARAMETERS:function(){return l},NEXT_RSC_UNION_QUERY:function(){return p},NEXT_DID_POSTPONE_HEADER:function(){return u}});let a="RSC",n="Next-Action",i="Next-Router-State-Tree",r="Next-Router-Prefetch",s="Next-Url",o="text/x-component",c=a+", "+i+", "+r+", "+s,l=[[a],[i],[r]],p="_rsc",u="x-nextjs-postponed";("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},37363:e=>{"use strict";(()=>{"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{/*!
 * cookie
 * Copyright(c) 2012-2014 Roman Shtylman
 * Copyright(c) 2015 Douglas Christopher Wilson
 * MIT Licensed
 */t.parse=function(t,a){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var i={},r=t.split(n),s=(a||{}).decode||e,o=0;o<r.length;o++){var c=r[o],l=c.indexOf("=");if(!(l<0)){var p=c.substr(0,l).trim(),u=c.substr(++l,c.length).trim();'"'==u[0]&&(u=u.slice(1,-1)),void 0==i[p]&&(i[p]=function(e,t){try{return t(e)}catch(t){return e}}(u,s))}}return i},t.serialize=function(e,t,n){var r=n||{},s=r.encode||a;if("function"!=typeof s)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var o=s(t);if(o&&!i.test(o))throw TypeError("argument val is invalid");var c=e+"="+o;if(null!=r.maxAge){var l=r.maxAge-0;if(isNaN(l)||!isFinite(l))throw TypeError("option maxAge is invalid");c+="; Max-Age="+Math.floor(l)}if(r.domain){if(!i.test(r.domain))throw TypeError("option domain is invalid");c+="; Domain="+r.domain}if(r.path){if(!i.test(r.path))throw TypeError("option path is invalid");c+="; Path="+r.path}if(r.expires){if("function"!=typeof r.expires.toUTCString)throw TypeError("option expires is invalid");c+="; Expires="+r.expires.toUTCString()}if(r.httpOnly&&(c+="; HttpOnly"),r.secure&&(c+="; Secure"),r.sameSite)switch("string"==typeof r.sameSite?r.sameSite.toLowerCase():r.sameSite){case!0:case"strict":c+="; SameSite=Strict";break;case"lax":c+="; SameSite=Lax";break;case"none":c+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return c};var e=decodeURIComponent,a=encodeURIComponent,n=/; */,i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},76358:(e,t)=>{"use strict";function a(e,t){void 0===t&&(t={});for(var a=function(e){for(var t=[],a=0;a<e.length;){var n=e[a];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:a,value:e[a++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:a++,value:e[a++]});continue}if("{"===n){t.push({type:"OPEN",index:a,value:e[a++]});continue}if("}"===n){t.push({type:"CLOSE",index:a,value:e[a++]});continue}if(":"===n){for(var i="",r=a+1;r<e.length;){var s=e.charCodeAt(r);if(s>=48&&s<=57||s>=65&&s<=90||s>=97&&s<=122||95===s){i+=e[r++];continue}break}if(!i)throw TypeError("Missing parameter name at "+a);t.push({type:"NAME",index:a,value:i}),a=r;continue}if("("===n){var o=1,c="",r=a+1;if("?"===e[r])throw TypeError('Pattern cannot start with "?" at '+r);for(;r<e.length;){if("\\"===e[r]){c+=e[r++]+e[r++];continue}if(")"===e[r]){if(0==--o){r++;break}}else if("("===e[r]&&(o++,"?"!==e[r+1]))throw TypeError("Capturing groups are not allowed at "+r);c+=e[r++]}if(o)throw TypeError("Unbalanced pattern at "+a);if(!c)throw TypeError("Missing pattern at "+a);t.push({type:"PATTERN",index:a,value:c}),a=r;continue}t.push({type:"CHAR",index:a,value:e[a++]})}return t.push({type:"END",index:a,value:""}),t}(e),n=t.prefixes,i=void 0===n?"./":n,s="[^"+r(t.delimiter||"/#?")+"]+?",o=[],c=0,l=0,p="",u=function(e){if(l<a.length&&a[l].type===e)return a[l++].value},d=function(e){var t=u(e);if(void 0!==t)return t;var n=a[l];throw TypeError("Unexpected "+n.type+" at "+n.index+", expected "+e)},m=function(){for(var e,t="";e=u("CHAR")||u("ESCAPED_CHAR");)t+=e;return t};l<a.length;){var f=u("CHAR"),h=u("NAME"),x=u("PATTERN");if(h||x){var v=f||"";-1===i.indexOf(v)&&(p+=v,v=""),p&&(o.push(p),p=""),o.push({name:h||c++,prefix:v,suffix:"",pattern:x||s,modifier:u("MODIFIER")||""});continue}var g=f||u("ESCAPED_CHAR");if(g){p+=g;continue}if(p&&(o.push(p),p=""),u("OPEN")){var v=m(),b=u("NAME")||"",y=u("PATTERN")||"",w=m();d("CLOSE"),o.push({name:b||(y?c++:""),pattern:b&&!y?s:y,prefix:v,suffix:w,modifier:u("MODIFIER")||""});continue}d("END")}return o}function n(e,t){void 0===t&&(t={});var a=s(t),n=t.encode,i=void 0===n?function(e){return e}:n,r=t.validate,o=void 0===r||r,c=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",a)});return function(t){for(var a="",n=0;n<e.length;n++){var r=e[n];if("string"==typeof r){a+=r;continue}var s=t?t[r.name]:void 0,l="?"===r.modifier||"*"===r.modifier,p="*"===r.modifier||"+"===r.modifier;if(Array.isArray(s)){if(!p)throw TypeError('Expected "'+r.name+'" to not repeat, but got an array');if(0===s.length){if(l)continue;throw TypeError('Expected "'+r.name+'" to not be empty')}for(var u=0;u<s.length;u++){var d=i(s[u],r);if(o&&!c[n].test(d))throw TypeError('Expected all "'+r.name+'" to match "'+r.pattern+'", but got "'+d+'"');a+=r.prefix+d+r.suffix}continue}if("string"==typeof s||"number"==typeof s){var d=i(String(s),r);if(o&&!c[n].test(d))throw TypeError('Expected "'+r.name+'" to match "'+r.pattern+'", but got "'+d+'"');a+=r.prefix+d+r.suffix;continue}if(!l){var m=p?"an array":"a string";throw TypeError('Expected "'+r.name+'" to be '+m)}}return a}}function i(e,t,a){void 0===a&&(a={});var n=a.decode,i=void 0===n?function(e){return e}:n;return function(a){var n=e.exec(a);if(!n)return!1;for(var r=n[0],s=n.index,o=Object.create(null),c=1;c<n.length;c++)!function(e){if(void 0!==n[e]){var a=t[e-1];"*"===a.modifier||"+"===a.modifier?o[a.name]=n[e].split(a.prefix+a.suffix).map(function(e){return i(e,a)}):o[a.name]=i(n[e],a)}}(c);return{path:r,index:s,params:o}}}function r(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function s(e){return e&&e.sensitive?"":"i"}function o(e,t,a){void 0===a&&(a={});for(var n=a.strict,i=void 0!==n&&n,o=a.start,c=a.end,l=a.encode,p=void 0===l?function(e){return e}:l,u="["+r(a.endsWith||"")+"]|$",d="["+r(a.delimiter||"/#?")+"]",m=void 0===o||o?"^":"",f=0;f<e.length;f++){var h=e[f];if("string"==typeof h)m+=r(p(h));else{var x=r(p(h.prefix)),v=r(p(h.suffix));if(h.pattern){if(t&&t.push(h),x||v){if("+"===h.modifier||"*"===h.modifier){var g="*"===h.modifier?"?":"";m+="(?:"+x+"((?:"+h.pattern+")(?:"+v+x+"(?:"+h.pattern+"))*)"+v+")"+g}else m+="(?:"+x+"("+h.pattern+")"+v+")"+h.modifier}else m+="("+h.pattern+")"+h.modifier}else m+="(?:"+x+v+")"+h.modifier}}if(void 0===c||c)i||(m+=d+"?"),m+=a.endsWith?"(?="+u+")":"$";else{var b=e[e.length-1],y="string"==typeof b?d.indexOf(b[b.length-1])>-1:void 0===b;i||(m+="(?:"+d+"(?="+u+"))?"),y||(m+="(?="+d+"|"+u+")")}return new RegExp(m,s(a))}function c(e,t,n){return e instanceof RegExp?function(e,t){if(!t)return e;var a=e.source.match(/\((?!\?)/g);if(a)for(var n=0;n<a.length;n++)t.push({name:n,prefix:"",suffix:"",modifier:"",pattern:""});return e}(e,t):Array.isArray(e)?RegExp("(?:"+e.map(function(e){return c(e,t,n).source}).join("|")+")",s(n)):o(a(e,n),t,n)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=a,t.compile=function(e,t){return n(a(e,t),t)},t.tokensToFunction=n,t.match=function(e,t){var a=[];return i(c(e,a,t),a,t)},t.regexpToFunction=i,t.tokensToRegexp=o,t.pathToRegexp=c},70337:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{fillMetadataSegment:function(){return u},normalizeMetadataRoute:function(){return d}});let n=a(48488),i=function(e){return e&&e.__esModule?e:{default:e}}(a(24084)),r=a(99017),s=a(4654),o=a(16325),c=a(29393),l=a(69716);function p(e){let t="";return(e.includes("(")&&e.includes(")")||e.includes("@"))&&(t=(0,o.djb2Hash)(e).toString(36).slice(0,6)),t}function u(e,t,a){let n=(0,c.normalizeAppPath)(e),o=(0,s.getNamedRouteRegex)(n,!1),u=(0,r.interpolateDynamicPath)(n,t,o),d=p(e),m=d?`-${d}`:"",{name:f,ext:h}=i.default.parse(a);return(0,l.normalizePathSep)(i.default.join(u,`${f}${m}${h}`))}function d(e){if(!(0,n.isMetadataRoute)(e))return e;let t=e,a="";if("/robots"===e?t+=".txt":"/manifest"===e?t+=".webmanifest":e.endsWith("/sitemap")?t+=".xml":a=p(e.slice(0,-(i.default.basename(e).length+1))),!t.endsWith("/route")){let{dir:r,name:s,ext:o}=i.default.parse(t),c=(0,n.isStaticMetadataRoute)(e);t=i.default.posix.join(r,`${s}${a?`-${a}`:""}${o}`,c?"":"[[...__metadata_id__]]","route")}return t}},48488:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{STATIC_METADATA_IMAGES:function(){return i},isMetadataRouteFile:function(){return o},isStaticMetadataRouteFile:function(){return c},isStaticMetadataRoute:function(){return l},isMetadataRoute:function(){return p}});let n=a(69716),i={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},r=["js","jsx","ts","tsx"],s=e=>`(?:${e.join("|")})`;function o(e,t,a){let r=[RegExp(`^[\\\\/]robots${a?`\\.${s(t.concat("txt"))}$`:""}`),RegExp(`^[\\\\/]manifest${a?`\\.${s(t.concat("webmanifest","json"))}$`:""}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${a?`\\.${s(t.concat("xml"))}$`:""}`),RegExp(`[\\\\/]${i.icon.filename}\\d?${a?`\\.${s(t.concat(i.icon.extensions))}$`:""}`),RegExp(`[\\\\/]${i.apple.filename}\\d?${a?`\\.${s(t.concat(i.apple.extensions))}$`:""}`),RegExp(`[\\\\/]${i.openGraph.filename}\\d?${a?`\\.${s(t.concat(i.openGraph.extensions))}$`:""}`),RegExp(`[\\\\/]${i.twitter.filename}\\d?${a?`\\.${s(t.concat(i.twitter.extensions))}$`:""}`)],o=(0,n.normalizePathSep)(e);return r.some(e=>e.test(o))}function c(e){return o(e,[],!0)}function l(e){return"/robots"===e||"/manifest"===e||c(e)}function p(e){let t=e.replace(/^\/?app\//,"").replace(/\/route$/,"");return"/"!==t[0]&&(t="/"+t),!t.endsWith("/page")&&o(t,r,!1)}},10892:(e,t,a)=>{"use strict";function n(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:n}=a(37363);return n(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return n}})},68988:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return i},isInterceptionRouteAppPath:function(){return r},extractInterceptionRouteInformation:function(){return s}});let n=a(29393),i=["(..)(..)","(.)","(..)","(...)"];function r(e){return void 0!==e.split("/").find(e=>i.find(t=>e.startsWith(t)))}function s(e){let t,a,r;for(let n of e.split("/"))if(a=i.find(e=>n.startsWith(e))){[t,r]=e.split(a,2);break}if(!t||!a||!r)throw Error(`Invalid interception route: ${e}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`);switch(t=(0,n.normalizeAppPath)(t),a){case"(.)":r="/"===t?`/${r}`:t+"/"+r;break;case"(..)":if("/"===t)throw Error(`Invalid interception route: ${e}. Cannot use (..) marker at the root level, use (.) instead.`);r=t.split("/").slice(0,-1).concat(r).join("/");break;case"(...)":r="/"+r;break;case"(..)(..)":let s=t.split("/");if(s.length<=2)throw Error(`Invalid interception route: ${e}. Cannot use (..)(..) marker at the root level or one level up.`);r=s.slice(0,-2).concat(r).join("/");break;default:throw Error("Invariant: unexpected marker")}return{interceptingRoute:t,interceptedRoute:r}}},99017:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{normalizeVercelUrl:function(){return d},interpolateDynamicPath:function(){return m},normalizeDynamicRouteParams:function(){return f},getUtils:function(){return h}});let n=a(57310),i=a(73935),r=a(48237),s=a(4654),o=a(87158),c=a(62762),l=a(5545),p=a(29393),u=a(82740);function d(e,t,a,i,r){if(i&&t&&r){let t=(0,n.parse)(e.url,!0);for(let e of(delete t.search,Object.keys(t.query)))(e!==u.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(u.NEXT_QUERY_PARAM_PREFIX)||(a||Object.keys(r.groups)).includes(e))&&delete t.query[e];e.url=(0,n.format)(t)}}function m(e,t,a){if(!a)return e;for(let n of Object.keys(a.groups)){let{optional:i,repeat:r}=a.groups[n],s=`[${r?"...":""}${n}]`;i&&(s=`[${s}]`);let o=e.indexOf(s);if(o>-1){let a;let i=t[n];a=Array.isArray(i)?i.map(e=>e&&encodeURIComponent(e)).join("/"):i?encodeURIComponent(i):"",e=e.slice(0,o)+a+e.slice(o+s.length)}}return e}function f(e,t,a,n){let i=!0;return a?{params:e=Object.keys(a.groups).reduce((r,s)=>{let o=e[s];"string"==typeof o&&(o=(0,p.normalizeRscURL)(o)),Array.isArray(o)&&(o=o.map(e=>("string"==typeof e&&(e=(0,p.normalizeRscURL)(e)),e)));let c=n[s],l=a.groups[s].optional;return((Array.isArray(c)?c.some(e=>Array.isArray(o)?o.some(t=>t.includes(e)):null==o?void 0:o.includes(e)):null==o?void 0:o.includes(c))||void 0===o&&!(l&&t))&&(i=!1),l&&(!o||Array.isArray(o)&&1===o.length&&("index"===o[0]||o[0]===`[[...${s}]]`))&&(o=void 0,delete e[s]),o&&"string"==typeof o&&a.groups[s].repeat&&(o=o.split("/")),o&&(r[s]=o),r},{}),hasValidParams:i}:{params:e,hasValidParams:!1}}function h({page:e,i18n:t,basePath:a,rewrites:n,pageIsDynamic:p,trailingSlash:h,caseSensitive:x}){let v,g,b;return p&&(v=(0,s.getNamedRouteRegex)(e,!1),b=(g=(0,o.getRouteMatcher)(v))(e)),{handleRewrites:function(s,o){let u={},d=o.pathname,m=n=>{let l=(0,r.getPathMatch)(n.source+(h?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!x})(o.pathname);if((n.has||n.missing)&&l){let e=(0,c.matchHas)(s,o.query,n.has,n.missing);e?Object.assign(l,e):l=!1}if(l){let{parsedDestination:r,destQuery:s}=(0,c.prepareDestination)({appendParamsToQuery:!0,destination:n.destination,params:l,query:o.query});if(r.protocol)return!0;if(Object.assign(u,s,l),Object.assign(o.query,r.query),delete r.query,Object.assign(o,r),d=o.pathname,a&&(d=d.replace(RegExp(`^${a}`),"")||"/"),t){let e=(0,i.normalizeLocalePath)(d,t.locales);d=e.pathname,o.query.nextInternalLocale=e.detectedLocale||l.nextInternalLocale}if(d===e)return!0;if(p&&g){let e=g(d);if(e)return o.query={...o.query,...e},!0}}return!1};for(let e of n.beforeFiles||[])m(e);if(d!==e){let t=!1;for(let e of n.afterFiles||[])if(t=m(e))break;if(!t&&!(()=>{let t=(0,l.removeTrailingSlash)(d||"");return t===(0,l.removeTrailingSlash)(e)||(null==g?void 0:g(t))})()){for(let e of n.fallback||[])if(t=m(e))break}}return u},defaultRouteRegex:v,dynamicRouteMatcher:g,defaultRouteMatches:b,getParamsFromRouteMatches:function(e,a,n){return(0,o.getRouteMatcher)(function(){let{groups:e,routeKeys:i}=v;return{re:{exec:r=>{let s=Object.fromEntries(new URLSearchParams(r)),o=t&&n&&s["1"]===n;for(let e of Object.keys(s)){let t=s[e];e!==u.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(u.NEXT_QUERY_PARAM_PREFIX)&&(s[e.substring(u.NEXT_QUERY_PARAM_PREFIX.length)]=t,delete s[e])}let c=Object.keys(i||{}),l=e=>{if(t){let i=Array.isArray(e),r=i?e[0]:e;if("string"==typeof r&&t.locales.some(e=>e.toLowerCase()===r.toLowerCase()&&(n=e,a.locale=n,!0)))return i&&e.splice(0,1),!i||0===e.length}return!1};return c.every(e=>s[e])?c.reduce((t,a)=>{let n=null==i?void 0:i[a];return n&&!l(s[a])&&(t[e[n].pos]=s[a]),t},{}):Object.keys(s).reduce((e,t)=>{if(!l(s[t])){let a=t;return o&&(a=parseInt(t,10)-1+""),Object.assign(e,{[a]:s[t]})}return e},{})}},groups:e}}())(e.headers["x-now-route-matches"])},normalizeDynamicRouteParams:(e,t)=>f(e,t,v,b),normalizeVercelUrl:(e,t,a)=>d(e,t,a,p,v),interpolateDynamicPath:(e,t)=>m(e,t,v)}}},7436:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return i}});let a=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function i(e){return a.test(e)?e.replace(n,"\\$&"):e}},16325:(e,t)=>{"use strict";function a(e){let t=5381;for(let a=0;a<e.length;a++)t=(t<<5)+t+e.charCodeAt(a)&4294967295;return t>>>0}function n(e){return a(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{djb2Hash:function(){return a},hexHash:function(){return n}})},24084:(e,t,a)=>{"use strict";let n;n=a(71017),e.exports=n},75940:(e,t)=>{"use strict";function a(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return a}})},69716:(e,t)=>{"use strict";function a(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return a}})},29393:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{normalizeAppPath:function(){return r},normalizeRscURL:function(){return s}});let n=a(75940),i=a(65458);function r(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,a,n)=>!t||(0,i.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&a===n.length-1?e:e+"/"+t,""))}function s(e){return e.replace(/\.rsc($|\?)/,"$1")}},60713:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return i}}),a(22881);let n=a(61662);function i(e,t){let a=new URL("http://n"),i=t?new URL(t,a):e.startsWith(".")?new URL("http://n"):a,{pathname:r,searchParams:s,search:o,hash:c,href:l,origin:p}=new URL(e,i);if(p!==a.origin)throw Error("invariant: invalid relative URL, router received "+e);return{pathname:r,query:(0,n.searchParamsToUrlQuery)(s),search:o,hash:c,href:l.slice(a.origin.length)}}},48223:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return r}});let n=a(61662),i=a(60713);function r(e){if(e.startsWith("/"))return(0,i.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,n.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},48237:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return i}});let n=a(76358);function i(e,t){let a=[],i=(0,n.pathToRegexp)(e,a,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),r=(0,n.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(i.source),i.flags):i,a);return(e,n)=>{if("string"!=typeof e)return!1;let i=r(e);if(!i)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of a)"number"==typeof e.name&&delete i.params[e.name];return{...n,...i.params}}}},62762:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{matchHas:function(){return p},compileNonPath:function(){return u},prepareDestination:function(){return d}});let n=a(76358),i=a(7436),r=a(48223),s=a(68988),o=a(14409),c=a(10892);function l(e){return e.replace(/__ESC_COLON_/gi,":")}function p(e,t,a,n){void 0===a&&(a=[]),void 0===n&&(n=[]);let i={},r=a=>{let n;let r=a.key;switch(a.type){case"header":r=r.toLowerCase(),n=e.headers[r];break;case"cookie":n="cookies"in e?e.cookies[a.key]:(0,c.getCookieParser)(e.headers)()[a.key];break;case"query":n=t[r];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};n=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!a.value&&n)return i[function(e){let t="";for(let a=0;a<e.length;a++){let n=e.charCodeAt(a);(n>64&&n<91||n>96&&n<123)&&(t+=e[a])}return t}(r)]=n,!0;if(n){let e=RegExp("^"+a.value+"$"),t=Array.isArray(n)?n.slice(-1)[0].match(e):n.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{i[e]=t.groups[e]}):"host"===a.type&&t[0]&&(i.host=t[0])),!0}return!1};return!!a.every(e=>r(e))&&!n.some(e=>r(e))&&i}function u(e,t){if(!e.includes(":"))return e;for(let a of Object.keys(t))e.includes(":"+a)&&(e=e.replace(RegExp(":"+a+"\\*","g"),":"+a+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+a+"\\?","g"),":"+a+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+a+"\\+","g"),":"+a+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+a+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+a));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,n.compile)("/"+e,{validate:!1})(t).slice(1)}function d(e){let t;let a=Object.assign({},e.query);delete a.__nextLocale,delete a.__nextDefaultLocale,delete a.__nextDataReq,delete a.__nextInferredLocaleFromDefault,delete a[o.NEXT_RSC_UNION_QUERY];let c=e.destination;for(let t of Object.keys({...e.params,...a}))c=c.replace(RegExp(":"+(0,i.escapeStringRegexp)(t),"g"),"__ESC_COLON_"+t);let p=(0,r.parseUrl)(c),d=p.query,m=l(""+p.pathname+(p.hash||"")),f=l(p.hostname||""),h=[],x=[];(0,n.pathToRegexp)(m,h),(0,n.pathToRegexp)(f,x);let v=[];h.forEach(e=>v.push(e.name)),x.forEach(e=>v.push(e.name));let g=(0,n.compile)(m,{validate:!1}),b=(0,n.compile)(f,{validate:!1});for(let[t,a]of Object.entries(d))Array.isArray(a)?d[t]=a.map(t=>u(l(t),e.params)):"string"==typeof a&&(d[t]=u(l(a),e.params));let y=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!y.some(e=>v.includes(e)))for(let t of y)t in d||(d[t]=e.params[t]);if((0,s.isInterceptionRouteAppPath)(m))for(let t of m.split("/")){let a=s.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(a){e.params["0"]=a;break}}try{let[a,n]=(t=g(e.params)).split("#",2);p.hostname=b(e.params),p.pathname=a,p.hash=(n?"#":"")+(n||""),delete p.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match");throw e}return p.query={...a,...p.query},{newUrl:t,destQuery:d,parsedDestination:p}}},61662:(e,t)=>{"use strict";function a(e){let t={};return e.forEach((e,a)=>{void 0===t[a]?t[a]=e:Array.isArray(t[a])?t[a].push(e):t[a]=[t[a],e]}),t}function n(e){return"string"!=typeof e&&("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[a,i]=e;Array.isArray(i)?i.forEach(e=>t.append(a,n(e))):t.set(a,n(i))}),t}function r(e){for(var t=arguments.length,a=Array(t>1?t-1:0),n=1;n<t;n++)a[n-1]=arguments[n];return a.forEach(t=>{Array.from(t.keys()).forEach(t=>e.delete(t)),t.forEach((t,a)=>e.append(a,t))}),e}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{searchParamsToUrlQuery:function(){return a},urlQueryToSearchParams:function(){return i},assign:function(){return r}})},87158:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return i}});let n=a(22881);function i(e){let{re:t,groups:a}=e;return e=>{let i=t.exec(e);if(!i)return!1;let r=e=>{try{return decodeURIComponent(e)}catch(e){throw new n.DecodeError("failed to decode param")}},s={};return Object.keys(a).forEach(e=>{let t=a[e],n=i[t.pos];void 0!==n&&(s[e]=~n.indexOf("/")?n.split("/").map(e=>r(e)):t.repeat?[r(n)]:r(n))}),s}}},4654:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{getRouteRegex:function(){return c},getNamedRouteRegex:function(){return u},getNamedMiddlewareRegex:function(){return d}});let n=a(68988),i=a(7436),r=a(5545);function s(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let a=e.startsWith("...");return a&&(e=e.slice(3)),{key:e,repeat:a,optional:t}}function o(e){let t=(0,r.removeTrailingSlash)(e).slice(1).split("/"),a={},o=1;return{parameterizedRoute:t.map(e=>{let t=n.INTERCEPTION_ROUTE_MARKERS.find(t=>e.startsWith(t)),r=e.match(/\[((?:\[.*\])|.+)\]/);if(t&&r){let{key:e,optional:n,repeat:c}=s(r[1]);return a[e]={pos:o++,repeat:c,optional:n},"/"+(0,i.escapeStringRegexp)(t)+"([^/]+?)"}if(!r)return"/"+(0,i.escapeStringRegexp)(e);{let{key:e,repeat:t,optional:n}=s(r[1]);return a[e]={pos:o++,repeat:t,optional:n},t?n?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)"}}).join(""),groups:a}}function c(e){let{parameterizedRoute:t,groups:a}=o(e);return{re:RegExp("^"+t+"(?:/)?$"),groups:a}}function l(e){let{interceptionMarker:t,getSafeRouteKey:a,segment:n,routeKeys:r,keyPrefix:o}=e,{key:c,optional:l,repeat:p}=s(n),u=c.replace(/\W/g,"");o&&(u=""+o+u);let d=!1;(0===u.length||u.length>30)&&(d=!0),isNaN(parseInt(u.slice(0,1)))||(d=!0),d&&(u=a()),o?r[u]=""+o+c:r[u]=c;let m=t?(0,i.escapeStringRegexp)(t):"";return p?l?"(?:/"+m+"(?<"+u+">.+?))?":"/"+m+"(?<"+u+">.+?)":"/"+m+"(?<"+u+">[^/]+?)"}function p(e,t){let a;let s=(0,r.removeTrailingSlash)(e).slice(1).split("/"),o=(a=0,()=>{let e="",t=++a;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),c={};return{namedParameterizedRoute:s.map(e=>{let a=n.INTERCEPTION_ROUTE_MARKERS.some(t=>e.startsWith(t)),r=e.match(/\[((?:\[.*\])|.+)\]/);if(a&&r){let[a]=e.split(r[0]);return l({getSafeRouteKey:o,interceptionMarker:a,segment:r[1],routeKeys:c,keyPrefix:t?"nxtI":void 0})}return r?l({getSafeRouteKey:o,segment:r[1],routeKeys:c,keyPrefix:t?"nxtP":void 0}):"/"+(0,i.escapeStringRegexp)(e)}).join(""),routeKeys:c}}function u(e,t){let a=p(e,t);return{...c(e),namedRegex:"^"+a.namedParameterizedRoute+"(?:/)?$",routeKeys:a.routeKeys}}function d(e,t){let{parameterizedRoute:a}=o(e),{catchAll:n=!0}=t;if("/"===a)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:i}=p(e,!1);return{namedRegex:"^"+i+(n?"(?:(/.*)?)":"")+"$"}}},22881:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{WEB_VITALS:function(){return a},execOnce:function(){return n},isAbsoluteUrl:function(){return r},getLocationOrigin:function(){return s},getURL:function(){return o},getDisplayName:function(){return c},isResSent:function(){return l},normalizeRepeatedSlashes:function(){return p},loadGetInitialProps:function(){return u},SP:function(){return d},ST:function(){return m},DecodeError:function(){return f},NormalizeError:function(){return h},PageNotFoundError:function(){return x},MissingStaticPage:function(){return v},MiddlewareNotFoundError:function(){return g},stringifyError:function(){return b}});let a=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,a=!1;return function(){for(var n=arguments.length,i=Array(n),r=0;r<n;r++)i[r]=arguments[r];return a||(a=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,r=e=>i.test(e);function s(){let{protocol:e,hostname:t,port:a}=window.location;return e+"//"+t+(a?":"+a:"")}function o(){let{href:e}=window.location,t=s();return e.substring(t.length)}function c(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function l(e){return e.finished||e.headersSent}function p(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function u(e,t){let a=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await u(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(a&&l(a))return n;if(!n)throw Error('"'+c(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.');return n}let d="undefined"!=typeof performance,m=d&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class f extends Error{}class h extends Error{}class x extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class v extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class g extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},97591:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>u});var n=a(25036),i=a(91296),r=a(86843);let s=(0,r.createProxy)(String.raw`/Users/<USER>/Downloads/Coding/IBC/Group Creator/src/components/TelegramGroupForm.tsx`),{__esModule:o,$$typeof:c}=s;s.default;let l=(0,r.createProxy)(String.raw`/Users/<USER>/Downloads/Coding/IBC/Group Creator/src/components/TelegramGroupForm.tsx#TelegramGroupForm`);var p=a(29600);async function u(){let{userId:e}=await (0,p.I)();return e?n.jsx(l,{}):n.jsx("div",{className:"flex flex-col justify-center items-center min-h-screen bg-gray-50",children:(0,n.jsxs)("div",{className:"w-full max-w-md px-4",children:[(0,n.jsxs)("div",{className:"text-center mb-8",children:[n.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Welcome back"}),n.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"Please sign in to continue to Telegram Group Creator"})]}),n.jsx(i.cL,{appearance:{elements:{rootBox:"mx-auto",card:"bg-white shadow-xl border-0",formButtonPrimary:"bg-blue-500 hover:bg-blue-600 text-sm normal-case",headerTitle:"hidden",headerSubtitle:"hidden",socialButtonsBlockButton:"text-sm normal-case",formFieldInput:"focus:border-blue-500 focus:ring-blue-500",dividerLine:"bg-gray-200",dividerText:"text-gray-500 text-sm"}}})]})})}},21342:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p,metadata:()=>l});var n=a(25036),i=a(42195),r=a.n(i),s=a(14677),o=a(91296);a(5023);var c=a(27171);let l={title:"Telegram Group Creator",description:"Create Telegram groups easily"};function p({children:e}){return n.jsx(s.El,{children:n.jsx("html",{lang:"en",children:(0,n.jsxs)("body",{className:r().className,children:[n.jsx(s.CH,{children:n.jsx("header",{className:"border-b",children:(0,n.jsxs)("div",{className:"container mx-auto px-4 py-3 flex items-center justify-between",children:[n.jsx("h1",{className:"text-xl font-semibold",children:"Telegram Group Creator"}),n.jsx(o.l8,{afterSignOutUrl:"/",appearance:{elements:{avatarBox:"w-10 h-10"}}})]})})}),e,n.jsx(c.x,{})]})})})}},73881:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i});var n=a(70337);let i=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},5023:()=>{},40572:e=>{"use strict";e.exports=JSON.parse('{"application/1d-interleaved-parityfec":{"source":"iana"},"application/3gpdash-qoe-report+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/3gpp-ims+xml":{"source":"iana","compressible":true},"application/3gpphal+json":{"source":"iana","compressible":true},"application/3gpphalforms+json":{"source":"iana","compressible":true},"application/a2l":{"source":"iana"},"application/ace+cbor":{"source":"iana"},"application/activemessage":{"source":"iana"},"application/activity+json":{"source":"iana","compressible":true},"application/alto-costmap+json":{"source":"iana","compressible":true},"application/alto-costmapfilter+json":{"source":"iana","compressible":true},"application/alto-directory+json":{"source":"iana","compressible":true},"application/alto-endpointcost+json":{"source":"iana","compressible":true},"application/alto-endpointcostparams+json":{"source":"iana","compressible":true},"application/alto-endpointprop+json":{"source":"iana","compressible":true},"application/alto-endpointpropparams+json":{"source":"iana","compressible":true},"application/alto-error+json":{"source":"iana","compressible":true},"application/alto-networkmap+json":{"source":"iana","compressible":true},"application/alto-networkmapfilter+json":{"source":"iana","compressible":true},"application/alto-updatestreamcontrol+json":{"source":"iana","compressible":true},"application/alto-updatestreamparams+json":{"source":"iana","compressible":true},"application/aml":{"source":"iana"},"application/andrew-inset":{"source":"iana","extensions":["ez"]},"application/applefile":{"source":"iana"},"application/applixware":{"source":"apache","extensions":["aw"]},"application/at+jwt":{"source":"iana"},"application/atf":{"source":"iana"},"application/atfx":{"source":"iana"},"application/atom+xml":{"source":"iana","compressible":true,"extensions":["atom"]},"application/atomcat+xml":{"source":"iana","compressible":true,"extensions":["atomcat"]},"application/atomdeleted+xml":{"source":"iana","compressible":true,"extensions":["atomdeleted"]},"application/atomicmail":{"source":"iana"},"application/atomsvc+xml":{"source":"iana","compressible":true,"extensions":["atomsvc"]},"application/atsc-dwd+xml":{"source":"iana","compressible":true,"extensions":["dwd"]},"application/atsc-dynamic-event-message":{"source":"iana"},"application/atsc-held+xml":{"source":"iana","compressible":true,"extensions":["held"]},"application/atsc-rdt+json":{"source":"iana","compressible":true},"application/atsc-rsat+xml":{"source":"iana","compressible":true,"extensions":["rsat"]},"application/atxml":{"source":"iana"},"application/auth-policy+xml":{"source":"iana","compressible":true},"application/bacnet-xdd+zip":{"source":"iana","compressible":false},"application/batch-smtp":{"source":"iana"},"application/bdoc":{"compressible":false,"extensions":["bdoc"]},"application/beep+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/calendar+json":{"source":"iana","compressible":true},"application/calendar+xml":{"source":"iana","compressible":true,"extensions":["xcs"]},"application/call-completion":{"source":"iana"},"application/cals-1840":{"source":"iana"},"application/captive+json":{"source":"iana","compressible":true},"application/cbor":{"source":"iana"},"application/cbor-seq":{"source":"iana"},"application/cccex":{"source":"iana"},"application/ccmp+xml":{"source":"iana","compressible":true},"application/ccxml+xml":{"source":"iana","compressible":true,"extensions":["ccxml"]},"application/cdfx+xml":{"source":"iana","compressible":true,"extensions":["cdfx"]},"application/cdmi-capability":{"source":"iana","extensions":["cdmia"]},"application/cdmi-container":{"source":"iana","extensions":["cdmic"]},"application/cdmi-domain":{"source":"iana","extensions":["cdmid"]},"application/cdmi-object":{"source":"iana","extensions":["cdmio"]},"application/cdmi-queue":{"source":"iana","extensions":["cdmiq"]},"application/cdni":{"source":"iana"},"application/cea":{"source":"iana"},"application/cea-2018+xml":{"source":"iana","compressible":true},"application/cellml+xml":{"source":"iana","compressible":true},"application/cfw":{"source":"iana"},"application/city+json":{"source":"iana","compressible":true},"application/clr":{"source":"iana"},"application/clue+xml":{"source":"iana","compressible":true},"application/clue_info+xml":{"source":"iana","compressible":true},"application/cms":{"source":"iana"},"application/cnrp+xml":{"source":"iana","compressible":true},"application/coap-group+json":{"source":"iana","compressible":true},"application/coap-payload":{"source":"iana"},"application/commonground":{"source":"iana"},"application/conference-info+xml":{"source":"iana","compressible":true},"application/cose":{"source":"iana"},"application/cose-key":{"source":"iana"},"application/cose-key-set":{"source":"iana"},"application/cpl+xml":{"source":"iana","compressible":true,"extensions":["cpl"]},"application/csrattrs":{"source":"iana"},"application/csta+xml":{"source":"iana","compressible":true},"application/cstadata+xml":{"source":"iana","compressible":true},"application/csvm+json":{"source":"iana","compressible":true},"application/cu-seeme":{"source":"apache","extensions":["cu"]},"application/cwt":{"source":"iana"},"application/cybercash":{"source":"iana"},"application/dart":{"compressible":true},"application/dash+xml":{"source":"iana","compressible":true,"extensions":["mpd"]},"application/dash-patch+xml":{"source":"iana","compressible":true,"extensions":["mpp"]},"application/dashdelta":{"source":"iana"},"application/davmount+xml":{"source":"iana","compressible":true,"extensions":["davmount"]},"application/dca-rft":{"source":"iana"},"application/dcd":{"source":"iana"},"application/dec-dx":{"source":"iana"},"application/dialog-info+xml":{"source":"iana","compressible":true},"application/dicom":{"source":"iana"},"application/dicom+json":{"source":"iana","compressible":true},"application/dicom+xml":{"source":"iana","compressible":true},"application/dii":{"source":"iana"},"application/dit":{"source":"iana"},"application/dns":{"source":"iana"},"application/dns+json":{"source":"iana","compressible":true},"application/dns-message":{"source":"iana"},"application/docbook+xml":{"source":"apache","compressible":true,"extensions":["dbk"]},"application/dots+cbor":{"source":"iana"},"application/dskpp+xml":{"source":"iana","compressible":true},"application/dssc+der":{"source":"iana","extensions":["dssc"]},"application/dssc+xml":{"source":"iana","compressible":true,"extensions":["xdssc"]},"application/dvcs":{"source":"iana"},"application/ecmascript":{"source":"iana","compressible":true,"extensions":["es","ecma"]},"application/edi-consent":{"source":"iana"},"application/edi-x12":{"source":"iana","compressible":false},"application/edifact":{"source":"iana","compressible":false},"application/efi":{"source":"iana"},"application/elm+json":{"source":"iana","charset":"UTF-8","compressible":true},"application/elm+xml":{"source":"iana","compressible":true},"application/emergencycalldata.cap+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/emergencycalldata.comment+xml":{"source":"iana","compressible":true},"application/emergencycalldata.control+xml":{"source":"iana","compressible":true},"application/emergencycalldata.deviceinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.ecall.msd":{"source":"iana"},"application/emergencycalldata.providerinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.serviceinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.subscriberinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.veds+xml":{"source":"iana","compressible":true},"application/emma+xml":{"source":"iana","compressible":true,"extensions":["emma"]},"application/emotionml+xml":{"source":"iana","compressible":true,"extensions":["emotionml"]},"application/encaprtp":{"source":"iana"},"application/epp+xml":{"source":"iana","compressible":true},"application/epub+zip":{"source":"iana","compressible":false,"extensions":["epub"]},"application/eshop":{"source":"iana"},"application/exi":{"source":"iana","extensions":["exi"]},"application/expect-ct-report+json":{"source":"iana","compressible":true},"application/express":{"source":"iana","extensions":["exp"]},"application/fastinfoset":{"source":"iana"},"application/fastsoap":{"source":"iana"},"application/fdt+xml":{"source":"iana","compressible":true,"extensions":["fdt"]},"application/fhir+json":{"source":"iana","charset":"UTF-8","compressible":true},"application/fhir+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/fido.trusted-apps+json":{"compressible":true},"application/fits":{"source":"iana"},"application/flexfec":{"source":"iana"},"application/font-sfnt":{"source":"iana"},"application/font-tdpfr":{"source":"iana","extensions":["pfr"]},"application/font-woff":{"source":"iana","compressible":false},"application/framework-attributes+xml":{"source":"iana","compressible":true},"application/geo+json":{"source":"iana","compressible":true,"extensions":["geojson"]},"application/geo+json-seq":{"source":"iana"},"application/geopackage+sqlite3":{"source":"iana"},"application/geoxacml+xml":{"source":"iana","compressible":true},"application/gltf-buffer":{"source":"iana"},"application/gml+xml":{"source":"iana","compressible":true,"extensions":["gml"]},"application/gpx+xml":{"source":"apache","compressible":true,"extensions":["gpx"]},"application/gxf":{"source":"apache","extensions":["gxf"]},"application/gzip":{"source":"iana","compressible":false,"extensions":["gz"]},"application/h224":{"source":"iana"},"application/held+xml":{"source":"iana","compressible":true},"application/hjson":{"extensions":["hjson"]},"application/http":{"source":"iana"},"application/hyperstudio":{"source":"iana","extensions":["stk"]},"application/ibe-key-request+xml":{"source":"iana","compressible":true},"application/ibe-pkg-reply+xml":{"source":"iana","compressible":true},"application/ibe-pp-data":{"source":"iana"},"application/iges":{"source":"iana"},"application/im-iscomposing+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/index":{"source":"iana"},"application/index.cmd":{"source":"iana"},"application/index.obj":{"source":"iana"},"application/index.response":{"source":"iana"},"application/index.vnd":{"source":"iana"},"application/inkml+xml":{"source":"iana","compressible":true,"extensions":["ink","inkml"]},"application/iotp":{"source":"iana"},"application/ipfix":{"source":"iana","extensions":["ipfix"]},"application/ipp":{"source":"iana"},"application/isup":{"source":"iana"},"application/its+xml":{"source":"iana","compressible":true,"extensions":["its"]},"application/java-archive":{"source":"apache","compressible":false,"extensions":["jar","war","ear"]},"application/java-serialized-object":{"source":"apache","compressible":false,"extensions":["ser"]},"application/java-vm":{"source":"apache","compressible":false,"extensions":["class"]},"application/javascript":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["js","mjs"]},"application/jf2feed+json":{"source":"iana","compressible":true},"application/jose":{"source":"iana"},"application/jose+json":{"source":"iana","compressible":true},"application/jrd+json":{"source":"iana","compressible":true},"application/jscalendar+json":{"source":"iana","compressible":true},"application/json":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["json","map"]},"application/json-patch+json":{"source":"iana","compressible":true},"application/json-seq":{"source":"iana"},"application/json5":{"extensions":["json5"]},"application/jsonml+json":{"source":"apache","compressible":true,"extensions":["jsonml"]},"application/jwk+json":{"source":"iana","compressible":true},"application/jwk-set+json":{"source":"iana","compressible":true},"application/jwt":{"source":"iana"},"application/kpml-request+xml":{"source":"iana","compressible":true},"application/kpml-response+xml":{"source":"iana","compressible":true},"application/ld+json":{"source":"iana","compressible":true,"extensions":["jsonld"]},"application/lgr+xml":{"source":"iana","compressible":true,"extensions":["lgr"]},"application/link-format":{"source":"iana"},"application/load-control+xml":{"source":"iana","compressible":true},"application/lost+xml":{"source":"iana","compressible":true,"extensions":["lostxml"]},"application/lostsync+xml":{"source":"iana","compressible":true},"application/lpf+zip":{"source":"iana","compressible":false},"application/lxf":{"source":"iana"},"application/mac-binhex40":{"source":"iana","extensions":["hqx"]},"application/mac-compactpro":{"source":"apache","extensions":["cpt"]},"application/macwriteii":{"source":"iana"},"application/mads+xml":{"source":"iana","compressible":true,"extensions":["mads"]},"application/manifest+json":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["webmanifest"]},"application/marc":{"source":"iana","extensions":["mrc"]},"application/marcxml+xml":{"source":"iana","compressible":true,"extensions":["mrcx"]},"application/mathematica":{"source":"iana","extensions":["ma","nb","mb"]},"application/mathml+xml":{"source":"iana","compressible":true,"extensions":["mathml"]},"application/mathml-content+xml":{"source":"iana","compressible":true},"application/mathml-presentation+xml":{"source":"iana","compressible":true},"application/mbms-associated-procedure-description+xml":{"source":"iana","compressible":true},"application/mbms-deregister+xml":{"source":"iana","compressible":true},"application/mbms-envelope+xml":{"source":"iana","compressible":true},"application/mbms-msk+xml":{"source":"iana","compressible":true},"application/mbms-msk-response+xml":{"source":"iana","compressible":true},"application/mbms-protection-description+xml":{"source":"iana","compressible":true},"application/mbms-reception-report+xml":{"source":"iana","compressible":true},"application/mbms-register+xml":{"source":"iana","compressible":true},"application/mbms-register-response+xml":{"source":"iana","compressible":true},"application/mbms-schedule+xml":{"source":"iana","compressible":true},"application/mbms-user-service-description+xml":{"source":"iana","compressible":true},"application/mbox":{"source":"iana","extensions":["mbox"]},"application/media-policy-dataset+xml":{"source":"iana","compressible":true,"extensions":["mpf"]},"application/media_control+xml":{"source":"iana","compressible":true},"application/mediaservercontrol+xml":{"source":"iana","compressible":true,"extensions":["mscml"]},"application/merge-patch+json":{"source":"iana","compressible":true},"application/metalink+xml":{"source":"apache","compressible":true,"extensions":["metalink"]},"application/metalink4+xml":{"source":"iana","compressible":true,"extensions":["meta4"]},"application/mets+xml":{"source":"iana","compressible":true,"extensions":["mets"]},"application/mf4":{"source":"iana"},"application/mikey":{"source":"iana"},"application/mipc":{"source":"iana"},"application/missing-blocks+cbor-seq":{"source":"iana"},"application/mmt-aei+xml":{"source":"iana","compressible":true,"extensions":["maei"]},"application/mmt-usd+xml":{"source":"iana","compressible":true,"extensions":["musd"]},"application/mods+xml":{"source":"iana","compressible":true,"extensions":["mods"]},"application/moss-keys":{"source":"iana"},"application/moss-signature":{"source":"iana"},"application/mosskey-data":{"source":"iana"},"application/mosskey-request":{"source":"iana"},"application/mp21":{"source":"iana","extensions":["m21","mp21"]},"application/mp4":{"source":"iana","extensions":["mp4s","m4p"]},"application/mpeg4-generic":{"source":"iana"},"application/mpeg4-iod":{"source":"iana"},"application/mpeg4-iod-xmt":{"source":"iana"},"application/mrb-consumer+xml":{"source":"iana","compressible":true},"application/mrb-publish+xml":{"source":"iana","compressible":true},"application/msc-ivr+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/msc-mixer+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/msword":{"source":"iana","compressible":false,"extensions":["doc","dot"]},"application/mud+json":{"source":"iana","compressible":true},"application/multipart-core":{"source":"iana"},"application/mxf":{"source":"iana","extensions":["mxf"]},"application/n-quads":{"source":"iana","extensions":["nq"]},"application/n-triples":{"source":"iana","extensions":["nt"]},"application/nasdata":{"source":"iana"},"application/news-checkgroups":{"source":"iana","charset":"US-ASCII"},"application/news-groupinfo":{"source":"iana","charset":"US-ASCII"},"application/news-transmission":{"source":"iana"},"application/nlsml+xml":{"source":"iana","compressible":true},"application/node":{"source":"iana","extensions":["cjs"]},"application/nss":{"source":"iana"},"application/oauth-authz-req+jwt":{"source":"iana"},"application/oblivious-dns-message":{"source":"iana"},"application/ocsp-request":{"source":"iana"},"application/ocsp-response":{"source":"iana"},"application/octet-stream":{"source":"iana","compressible":false,"extensions":["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"]},"application/oda":{"source":"iana","extensions":["oda"]},"application/odm+xml":{"source":"iana","compressible":true},"application/odx":{"source":"iana"},"application/oebps-package+xml":{"source":"iana","compressible":true,"extensions":["opf"]},"application/ogg":{"source":"iana","compressible":false,"extensions":["ogx"]},"application/omdoc+xml":{"source":"apache","compressible":true,"extensions":["omdoc"]},"application/onenote":{"source":"apache","extensions":["onetoc","onetoc2","onetmp","onepkg"]},"application/opc-nodeset+xml":{"source":"iana","compressible":true},"application/oscore":{"source":"iana"},"application/oxps":{"source":"iana","extensions":["oxps"]},"application/p21":{"source":"iana"},"application/p21+zip":{"source":"iana","compressible":false},"application/p2p-overlay+xml":{"source":"iana","compressible":true,"extensions":["relo"]},"application/parityfec":{"source":"iana"},"application/passport":{"source":"iana"},"application/patch-ops-error+xml":{"source":"iana","compressible":true,"extensions":["xer"]},"application/pdf":{"source":"iana","compressible":false,"extensions":["pdf"]},"application/pdx":{"source":"iana"},"application/pem-certificate-chain":{"source":"iana"},"application/pgp-encrypted":{"source":"iana","compressible":false,"extensions":["pgp"]},"application/pgp-keys":{"source":"iana","extensions":["asc"]},"application/pgp-signature":{"source":"iana","extensions":["asc","sig"]},"application/pics-rules":{"source":"apache","extensions":["prf"]},"application/pidf+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/pidf-diff+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/pkcs10":{"source":"iana","extensions":["p10"]},"application/pkcs12":{"source":"iana"},"application/pkcs7-mime":{"source":"iana","extensions":["p7m","p7c"]},"application/pkcs7-signature":{"source":"iana","extensions":["p7s"]},"application/pkcs8":{"source":"iana","extensions":["p8"]},"application/pkcs8-encrypted":{"source":"iana"},"application/pkix-attr-cert":{"source":"iana","extensions":["ac"]},"application/pkix-cert":{"source":"iana","extensions":["cer"]},"application/pkix-crl":{"source":"iana","extensions":["crl"]},"application/pkix-pkipath":{"source":"iana","extensions":["pkipath"]},"application/pkixcmp":{"source":"iana","extensions":["pki"]},"application/pls+xml":{"source":"iana","compressible":true,"extensions":["pls"]},"application/poc-settings+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/postscript":{"source":"iana","compressible":true,"extensions":["ai","eps","ps"]},"application/ppsp-tracker+json":{"source":"iana","compressible":true},"application/problem+json":{"source":"iana","compressible":true},"application/problem+xml":{"source":"iana","compressible":true},"application/provenance+xml":{"source":"iana","compressible":true,"extensions":["provx"]},"application/prs.alvestrand.titrax-sheet":{"source":"iana"},"application/prs.cww":{"source":"iana","extensions":["cww"]},"application/prs.cyn":{"source":"iana","charset":"7-BIT"},"application/prs.hpub+zip":{"source":"iana","compressible":false},"application/prs.nprend":{"source":"iana"},"application/prs.plucker":{"source":"iana"},"application/prs.rdf-xml-crypt":{"source":"iana"},"application/prs.xsf+xml":{"source":"iana","compressible":true},"application/pskc+xml":{"source":"iana","compressible":true,"extensions":["pskcxml"]},"application/pvd+json":{"source":"iana","compressible":true},"application/qsig":{"source":"iana"},"application/raml+yaml":{"compressible":true,"extensions":["raml"]},"application/raptorfec":{"source":"iana"},"application/rdap+json":{"source":"iana","compressible":true},"application/rdf+xml":{"source":"iana","compressible":true,"extensions":["rdf","owl"]},"application/reginfo+xml":{"source":"iana","compressible":true,"extensions":["rif"]},"application/relax-ng-compact-syntax":{"source":"iana","extensions":["rnc"]},"application/remote-printing":{"source":"iana"},"application/reputon+json":{"source":"iana","compressible":true},"application/resource-lists+xml":{"source":"iana","compressible":true,"extensions":["rl"]},"application/resource-lists-diff+xml":{"source":"iana","compressible":true,"extensions":["rld"]},"application/rfc+xml":{"source":"iana","compressible":true},"application/riscos":{"source":"iana"},"application/rlmi+xml":{"source":"iana","compressible":true},"application/rls-services+xml":{"source":"iana","compressible":true,"extensions":["rs"]},"application/route-apd+xml":{"source":"iana","compressible":true,"extensions":["rapd"]},"application/route-s-tsid+xml":{"source":"iana","compressible":true,"extensions":["sls"]},"application/route-usd+xml":{"source":"iana","compressible":true,"extensions":["rusd"]},"application/rpki-ghostbusters":{"source":"iana","extensions":["gbr"]},"application/rpki-manifest":{"source":"iana","extensions":["mft"]},"application/rpki-publication":{"source":"iana"},"application/rpki-roa":{"source":"iana","extensions":["roa"]},"application/rpki-updown":{"source":"iana"},"application/rsd+xml":{"source":"apache","compressible":true,"extensions":["rsd"]},"application/rss+xml":{"source":"apache","compressible":true,"extensions":["rss"]},"application/rtf":{"source":"iana","compressible":true,"extensions":["rtf"]},"application/rtploopback":{"source":"iana"},"application/rtx":{"source":"iana"},"application/samlassertion+xml":{"source":"iana","compressible":true},"application/samlmetadata+xml":{"source":"iana","compressible":true},"application/sarif+json":{"source":"iana","compressible":true},"application/sarif-external-properties+json":{"source":"iana","compressible":true},"application/sbe":{"source":"iana"},"application/sbml+xml":{"source":"iana","compressible":true,"extensions":["sbml"]},"application/scaip+xml":{"source":"iana","compressible":true},"application/scim+json":{"source":"iana","compressible":true},"application/scvp-cv-request":{"source":"iana","extensions":["scq"]},"application/scvp-cv-response":{"source":"iana","extensions":["scs"]},"application/scvp-vp-request":{"source":"iana","extensions":["spq"]},"application/scvp-vp-response":{"source":"iana","extensions":["spp"]},"application/sdp":{"source":"iana","extensions":["sdp"]},"application/secevent+jwt":{"source":"iana"},"application/senml+cbor":{"source":"iana"},"application/senml+json":{"source":"iana","compressible":true},"application/senml+xml":{"source":"iana","compressible":true,"extensions":["senmlx"]},"application/senml-etch+cbor":{"source":"iana"},"application/senml-etch+json":{"source":"iana","compressible":true},"application/senml-exi":{"source":"iana"},"application/sensml+cbor":{"source":"iana"},"application/sensml+json":{"source":"iana","compressible":true},"application/sensml+xml":{"source":"iana","compressible":true,"extensions":["sensmlx"]},"application/sensml-exi":{"source":"iana"},"application/sep+xml":{"source":"iana","compressible":true},"application/sep-exi":{"source":"iana"},"application/session-info":{"source":"iana"},"application/set-payment":{"source":"iana"},"application/set-payment-initiation":{"source":"iana","extensions":["setpay"]},"application/set-registration":{"source":"iana"},"application/set-registration-initiation":{"source":"iana","extensions":["setreg"]},"application/sgml":{"source":"iana"},"application/sgml-open-catalog":{"source":"iana"},"application/shf+xml":{"source":"iana","compressible":true,"extensions":["shf"]},"application/sieve":{"source":"iana","extensions":["siv","sieve"]},"application/simple-filter+xml":{"source":"iana","compressible":true},"application/simple-message-summary":{"source":"iana"},"application/simplesymbolcontainer":{"source":"iana"},"application/sipc":{"source":"iana"},"application/slate":{"source":"iana"},"application/smil":{"source":"iana"},"application/smil+xml":{"source":"iana","compressible":true,"extensions":["smi","smil"]},"application/smpte336m":{"source":"iana"},"application/soap+fastinfoset":{"source":"iana"},"application/soap+xml":{"source":"iana","compressible":true},"application/sparql-query":{"source":"iana","extensions":["rq"]},"application/sparql-results+xml":{"source":"iana","compressible":true,"extensions":["srx"]},"application/spdx+json":{"source":"iana","compressible":true},"application/spirits-event+xml":{"source":"iana","compressible":true},"application/sql":{"source":"iana"},"application/srgs":{"source":"iana","extensions":["gram"]},"application/srgs+xml":{"source":"iana","compressible":true,"extensions":["grxml"]},"application/sru+xml":{"source":"iana","compressible":true,"extensions":["sru"]},"application/ssdl+xml":{"source":"apache","compressible":true,"extensions":["ssdl"]},"application/ssml+xml":{"source":"iana","compressible":true,"extensions":["ssml"]},"application/stix+json":{"source":"iana","compressible":true},"application/swid+xml":{"source":"iana","compressible":true,"extensions":["swidtag"]},"application/tamp-apex-update":{"source":"iana"},"application/tamp-apex-update-confirm":{"source":"iana"},"application/tamp-community-update":{"source":"iana"},"application/tamp-community-update-confirm":{"source":"iana"},"application/tamp-error":{"source":"iana"},"application/tamp-sequence-adjust":{"source":"iana"},"application/tamp-sequence-adjust-confirm":{"source":"iana"},"application/tamp-status-query":{"source":"iana"},"application/tamp-status-response":{"source":"iana"},"application/tamp-update":{"source":"iana"},"application/tamp-update-confirm":{"source":"iana"},"application/tar":{"compressible":true},"application/taxii+json":{"source":"iana","compressible":true},"application/td+json":{"source":"iana","compressible":true},"application/tei+xml":{"source":"iana","compressible":true,"extensions":["tei","teicorpus"]},"application/tetra_isi":{"source":"iana"},"application/thraud+xml":{"source":"iana","compressible":true,"extensions":["tfi"]},"application/timestamp-query":{"source":"iana"},"application/timestamp-reply":{"source":"iana"},"application/timestamped-data":{"source":"iana","extensions":["tsd"]},"application/tlsrpt+gzip":{"source":"iana"},"application/tlsrpt+json":{"source":"iana","compressible":true},"application/tnauthlist":{"source":"iana"},"application/token-introspection+jwt":{"source":"iana"},"application/toml":{"compressible":true,"extensions":["toml"]},"application/trickle-ice-sdpfrag":{"source":"iana"},"application/trig":{"source":"iana","extensions":["trig"]},"application/ttml+xml":{"source":"iana","compressible":true,"extensions":["ttml"]},"application/tve-trigger":{"source":"iana"},"application/tzif":{"source":"iana"},"application/tzif-leap":{"source":"iana"},"application/ubjson":{"compressible":false,"extensions":["ubj"]},"application/ulpfec":{"source":"iana"},"application/urc-grpsheet+xml":{"source":"iana","compressible":true},"application/urc-ressheet+xml":{"source":"iana","compressible":true,"extensions":["rsheet"]},"application/urc-targetdesc+xml":{"source":"iana","compressible":true,"extensions":["td"]},"application/urc-uisocketdesc+xml":{"source":"iana","compressible":true},"application/vcard+json":{"source":"iana","compressible":true},"application/vcard+xml":{"source":"iana","compressible":true},"application/vemmi":{"source":"iana"},"application/vividence.scriptfile":{"source":"apache"},"application/vnd.1000minds.decision-model+xml":{"source":"iana","compressible":true,"extensions":["1km"]},"application/vnd.3gpp-prose+xml":{"source":"iana","compressible":true},"application/vnd.3gpp-prose-pc3ch+xml":{"source":"iana","compressible":true},"application/vnd.3gpp-v2x-local-service-information":{"source":"iana"},"application/vnd.3gpp.5gnas":{"source":"iana"},"application/vnd.3gpp.access-transfer-events+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.bsf+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.gmop+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.gtpc":{"source":"iana"},"application/vnd.3gpp.interworking-data":{"source":"iana"},"application/vnd.3gpp.lpp":{"source":"iana"},"application/vnd.3gpp.mc-signalling-ear":{"source":"iana"},"application/vnd.3gpp.mcdata-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-payload":{"source":"iana"},"application/vnd.3gpp.mcdata-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-signalling":{"source":"iana"},"application/vnd.3gpp.mcdata-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-floor-request+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-location-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-mbms-usage-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-signed+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-ue-init-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-affiliation-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-location-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-mbms-usage-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-transmission-request+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mid-call+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.ngap":{"source":"iana"},"application/vnd.3gpp.pfcp":{"source":"iana"},"application/vnd.3gpp.pic-bw-large":{"source":"iana","extensions":["plb"]},"application/vnd.3gpp.pic-bw-small":{"source":"iana","extensions":["psb"]},"application/vnd.3gpp.pic-bw-var":{"source":"iana","extensions":["pvb"]},"application/vnd.3gpp.s1ap":{"source":"iana"},"application/vnd.3gpp.sms":{"source":"iana"},"application/vnd.3gpp.sms+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.srvcc-ext+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.srvcc-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.state-and-event-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.ussd+xml":{"source":"iana","compressible":true},"application/vnd.3gpp2.bcmcsinfo+xml":{"source":"iana","compressible":true},"application/vnd.3gpp2.sms":{"source":"iana"},"application/vnd.3gpp2.tcap":{"source":"iana","extensions":["tcap"]},"application/vnd.3lightssoftware.imagescal":{"source":"iana"},"application/vnd.3m.post-it-notes":{"source":"iana","extensions":["pwn"]},"application/vnd.accpac.simply.aso":{"source":"iana","extensions":["aso"]},"application/vnd.accpac.simply.imp":{"source":"iana","extensions":["imp"]},"application/vnd.acucobol":{"source":"iana","extensions":["acu"]},"application/vnd.acucorp":{"source":"iana","extensions":["atc","acutc"]},"application/vnd.adobe.air-application-installer-package+zip":{"source":"apache","compressible":false,"extensions":["air"]},"application/vnd.adobe.flash.movie":{"source":"iana"},"application/vnd.adobe.formscentral.fcdt":{"source":"iana","extensions":["fcdt"]},"application/vnd.adobe.fxp":{"source":"iana","extensions":["fxp","fxpl"]},"application/vnd.adobe.partial-upload":{"source":"iana"},"application/vnd.adobe.xdp+xml":{"source":"iana","compressible":true,"extensions":["xdp"]},"application/vnd.adobe.xfdf":{"source":"iana","extensions":["xfdf"]},"application/vnd.aether.imp":{"source":"iana"},"application/vnd.afpc.afplinedata":{"source":"iana"},"application/vnd.afpc.afplinedata-pagedef":{"source":"iana"},"application/vnd.afpc.cmoca-cmresource":{"source":"iana"},"application/vnd.afpc.foca-charset":{"source":"iana"},"application/vnd.afpc.foca-codedfont":{"source":"iana"},"application/vnd.afpc.foca-codepage":{"source":"iana"},"application/vnd.afpc.modca":{"source":"iana"},"application/vnd.afpc.modca-cmtable":{"source":"iana"},"application/vnd.afpc.modca-formdef":{"source":"iana"},"application/vnd.afpc.modca-mediummap":{"source":"iana"},"application/vnd.afpc.modca-objectcontainer":{"source":"iana"},"application/vnd.afpc.modca-overlay":{"source":"iana"},"application/vnd.afpc.modca-pagesegment":{"source":"iana"},"application/vnd.age":{"source":"iana","extensions":["age"]},"application/vnd.ah-barcode":{"source":"iana"},"application/vnd.ahead.space":{"source":"iana","extensions":["ahead"]},"application/vnd.airzip.filesecure.azf":{"source":"iana","extensions":["azf"]},"application/vnd.airzip.filesecure.azs":{"source":"iana","extensions":["azs"]},"application/vnd.amadeus+json":{"source":"iana","compressible":true},"application/vnd.amazon.ebook":{"source":"apache","extensions":["azw"]},"application/vnd.amazon.mobi8-ebook":{"source":"iana"},"application/vnd.americandynamics.acc":{"source":"iana","extensions":["acc"]},"application/vnd.amiga.ami":{"source":"iana","extensions":["ami"]},"application/vnd.amundsen.maze+xml":{"source":"iana","compressible":true},"application/vnd.android.ota":{"source":"iana"},"application/vnd.android.package-archive":{"source":"apache","compressible":false,"extensions":["apk"]},"application/vnd.anki":{"source":"iana"},"application/vnd.anser-web-certificate-issue-initiation":{"source":"iana","extensions":["cii"]},"application/vnd.anser-web-funds-transfer-initiation":{"source":"apache","extensions":["fti"]},"application/vnd.antix.game-component":{"source":"iana","extensions":["atx"]},"application/vnd.apache.arrow.file":{"source":"iana"},"application/vnd.apache.arrow.stream":{"source":"iana"},"application/vnd.apache.thrift.binary":{"source":"iana"},"application/vnd.apache.thrift.compact":{"source":"iana"},"application/vnd.apache.thrift.json":{"source":"iana"},"application/vnd.api+json":{"source":"iana","compressible":true},"application/vnd.aplextor.warrp+json":{"source":"iana","compressible":true},"application/vnd.apothekende.reservation+json":{"source":"iana","compressible":true},"application/vnd.apple.installer+xml":{"source":"iana","compressible":true,"extensions":["mpkg"]},"application/vnd.apple.keynote":{"source":"iana","extensions":["key"]},"application/vnd.apple.mpegurl":{"source":"iana","extensions":["m3u8"]},"application/vnd.apple.numbers":{"source":"iana","extensions":["numbers"]},"application/vnd.apple.pages":{"source":"iana","extensions":["pages"]},"application/vnd.apple.pkpass":{"compressible":false,"extensions":["pkpass"]},"application/vnd.arastra.swi":{"source":"iana"},"application/vnd.aristanetworks.swi":{"source":"iana","extensions":["swi"]},"application/vnd.artisan+json":{"source":"iana","compressible":true},"application/vnd.artsquare":{"source":"iana"},"application/vnd.astraea-software.iota":{"source":"iana","extensions":["iota"]},"application/vnd.audiograph":{"source":"iana","extensions":["aep"]},"application/vnd.autopackage":{"source":"iana"},"application/vnd.avalon+json":{"source":"iana","compressible":true},"application/vnd.avistar+xml":{"source":"iana","compressible":true},"application/vnd.balsamiq.bmml+xml":{"source":"iana","compressible":true,"extensions":["bmml"]},"application/vnd.balsamiq.bmpr":{"source":"iana"},"application/vnd.banana-accounting":{"source":"iana"},"application/vnd.bbf.usp.error":{"source":"iana"},"application/vnd.bbf.usp.msg":{"source":"iana"},"application/vnd.bbf.usp.msg+json":{"source":"iana","compressible":true},"application/vnd.bekitzur-stech+json":{"source":"iana","compressible":true},"application/vnd.bint.med-content":{"source":"iana"},"application/vnd.biopax.rdf+xml":{"source":"iana","compressible":true},"application/vnd.blink-idb-value-wrapper":{"source":"iana"},"application/vnd.blueice.multipass":{"source":"iana","extensions":["mpm"]},"application/vnd.bluetooth.ep.oob":{"source":"iana"},"application/vnd.bluetooth.le.oob":{"source":"iana"},"application/vnd.bmi":{"source":"iana","extensions":["bmi"]},"application/vnd.bpf":{"source":"iana"},"application/vnd.bpf3":{"source":"iana"},"application/vnd.businessobjects":{"source":"iana","extensions":["rep"]},"application/vnd.byu.uapi+json":{"source":"iana","compressible":true},"application/vnd.cab-jscript":{"source":"iana"},"application/vnd.canon-cpdl":{"source":"iana"},"application/vnd.canon-lips":{"source":"iana"},"application/vnd.capasystems-pg+json":{"source":"iana","compressible":true},"application/vnd.cendio.thinlinc.clientconf":{"source":"iana"},"application/vnd.century-systems.tcp_stream":{"source":"iana"},"application/vnd.chemdraw+xml":{"source":"iana","compressible":true,"extensions":["cdxml"]},"application/vnd.chess-pgn":{"source":"iana"},"application/vnd.chipnuts.karaoke-mmd":{"source":"iana","extensions":["mmd"]},"application/vnd.ciedi":{"source":"iana"},"application/vnd.cinderella":{"source":"iana","extensions":["cdy"]},"application/vnd.cirpack.isdn-ext":{"source":"iana"},"application/vnd.citationstyles.style+xml":{"source":"iana","compressible":true,"extensions":["csl"]},"application/vnd.claymore":{"source":"iana","extensions":["cla"]},"application/vnd.cloanto.rp9":{"source":"iana","extensions":["rp9"]},"application/vnd.clonk.c4group":{"source":"iana","extensions":["c4g","c4d","c4f","c4p","c4u"]},"application/vnd.cluetrust.cartomobile-config":{"source":"iana","extensions":["c11amc"]},"application/vnd.cluetrust.cartomobile-config-pkg":{"source":"iana","extensions":["c11amz"]},"application/vnd.coffeescript":{"source":"iana"},"application/vnd.collabio.xodocuments.document":{"source":"iana"},"application/vnd.collabio.xodocuments.document-template":{"source":"iana"},"application/vnd.collabio.xodocuments.presentation":{"source":"iana"},"application/vnd.collabio.xodocuments.presentation-template":{"source":"iana"},"application/vnd.collabio.xodocuments.spreadsheet":{"source":"iana"},"application/vnd.collabio.xodocuments.spreadsheet-template":{"source":"iana"},"application/vnd.collection+json":{"source":"iana","compressible":true},"application/vnd.collection.doc+json":{"source":"iana","compressible":true},"application/vnd.collection.next+json":{"source":"iana","compressible":true},"application/vnd.comicbook+zip":{"source":"iana","compressible":false},"application/vnd.comicbook-rar":{"source":"iana"},"application/vnd.commerce-battelle":{"source":"iana"},"application/vnd.commonspace":{"source":"iana","extensions":["csp"]},"application/vnd.contact.cmsg":{"source":"iana","extensions":["cdbcmsg"]},"application/vnd.coreos.ignition+json":{"source":"iana","compressible":true},"application/vnd.cosmocaller":{"source":"iana","extensions":["cmc"]},"application/vnd.crick.clicker":{"source":"iana","extensions":["clkx"]},"application/vnd.crick.clicker.keyboard":{"source":"iana","extensions":["clkk"]},"application/vnd.crick.clicker.palette":{"source":"iana","extensions":["clkp"]},"application/vnd.crick.clicker.template":{"source":"iana","extensions":["clkt"]},"application/vnd.crick.clicker.wordbank":{"source":"iana","extensions":["clkw"]},"application/vnd.criticaltools.wbs+xml":{"source":"iana","compressible":true,"extensions":["wbs"]},"application/vnd.cryptii.pipe+json":{"source":"iana","compressible":true},"application/vnd.crypto-shade-file":{"source":"iana"},"application/vnd.cryptomator.encrypted":{"source":"iana"},"application/vnd.cryptomator.vault":{"source":"iana"},"application/vnd.ctc-posml":{"source":"iana","extensions":["pml"]},"application/vnd.ctct.ws+xml":{"source":"iana","compressible":true},"application/vnd.cups-pdf":{"source":"iana"},"application/vnd.cups-postscript":{"source":"iana"},"application/vnd.cups-ppd":{"source":"iana","extensions":["ppd"]},"application/vnd.cups-raster":{"source":"iana"},"application/vnd.cups-raw":{"source":"iana"},"application/vnd.curl":{"source":"iana"},"application/vnd.curl.car":{"source":"apache","extensions":["car"]},"application/vnd.curl.pcurl":{"source":"apache","extensions":["pcurl"]},"application/vnd.cyan.dean.root+xml":{"source":"iana","compressible":true},"application/vnd.cybank":{"source":"iana"},"application/vnd.cyclonedx+json":{"source":"iana","compressible":true},"application/vnd.cyclonedx+xml":{"source":"iana","compressible":true},"application/vnd.d2l.coursepackage1p0+zip":{"source":"iana","compressible":false},"application/vnd.d3m-dataset":{"source":"iana"},"application/vnd.d3m-problem":{"source":"iana"},"application/vnd.dart":{"source":"iana","compressible":true,"extensions":["dart"]},"application/vnd.data-vision.rdz":{"source":"iana","extensions":["rdz"]},"application/vnd.datapackage+json":{"source":"iana","compressible":true},"application/vnd.dataresource+json":{"source":"iana","compressible":true},"application/vnd.dbf":{"source":"iana","extensions":["dbf"]},"application/vnd.debian.binary-package":{"source":"iana"},"application/vnd.dece.data":{"source":"iana","extensions":["uvf","uvvf","uvd","uvvd"]},"application/vnd.dece.ttml+xml":{"source":"iana","compressible":true,"extensions":["uvt","uvvt"]},"application/vnd.dece.unspecified":{"source":"iana","extensions":["uvx","uvvx"]},"application/vnd.dece.zip":{"source":"iana","extensions":["uvz","uvvz"]},"application/vnd.denovo.fcselayout-link":{"source":"iana","extensions":["fe_launch"]},"application/vnd.desmume.movie":{"source":"iana"},"application/vnd.dir-bi.plate-dl-nosuffix":{"source":"iana"},"application/vnd.dm.delegation+xml":{"source":"iana","compressible":true},"application/vnd.dna":{"source":"iana","extensions":["dna"]},"application/vnd.document+json":{"source":"iana","compressible":true},"application/vnd.dolby.mlp":{"source":"apache","extensions":["mlp"]},"application/vnd.dolby.mobile.1":{"source":"iana"},"application/vnd.dolby.mobile.2":{"source":"iana"},"application/vnd.doremir.scorecloud-binary-document":{"source":"iana"},"application/vnd.dpgraph":{"source":"iana","extensions":["dpg"]},"application/vnd.dreamfactory":{"source":"iana","extensions":["dfac"]},"application/vnd.drive+json":{"source":"iana","compressible":true},"application/vnd.ds-keypoint":{"source":"apache","extensions":["kpxx"]},"application/vnd.dtg.local":{"source":"iana"},"application/vnd.dtg.local.flash":{"source":"iana"},"application/vnd.dtg.local.html":{"source":"iana"},"application/vnd.dvb.ait":{"source":"iana","extensions":["ait"]},"application/vnd.dvb.dvbisl+xml":{"source":"iana","compressible":true},"application/vnd.dvb.dvbj":{"source":"iana"},"application/vnd.dvb.esgcontainer":{"source":"iana"},"application/vnd.dvb.ipdcdftnotifaccess":{"source":"iana"},"application/vnd.dvb.ipdcesgaccess":{"source":"iana"},"application/vnd.dvb.ipdcesgaccess2":{"source":"iana"},"application/vnd.dvb.ipdcesgpdd":{"source":"iana"},"application/vnd.dvb.ipdcroaming":{"source":"iana"},"application/vnd.dvb.iptv.alfec-base":{"source":"iana"},"application/vnd.dvb.iptv.alfec-enhancement":{"source":"iana"},"application/vnd.dvb.notif-aggregate-root+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-container+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-generic+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-msglist+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-registration-request+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-registration-response+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-init+xml":{"source":"iana","compressible":true},"application/vnd.dvb.pfr":{"source":"iana"},"application/vnd.dvb.service":{"source":"iana","extensions":["svc"]},"application/vnd.dxr":{"source":"iana"},"application/vnd.dynageo":{"source":"iana","extensions":["geo"]},"application/vnd.dzr":{"source":"iana"},"application/vnd.easykaraoke.cdgdownload":{"source":"iana"},"application/vnd.ecdis-update":{"source":"iana"},"application/vnd.ecip.rlp":{"source":"iana"},"application/vnd.eclipse.ditto+json":{"source":"iana","compressible":true},"application/vnd.ecowin.chart":{"source":"iana","extensions":["mag"]},"application/vnd.ecowin.filerequest":{"source":"iana"},"application/vnd.ecowin.fileupdate":{"source":"iana"},"application/vnd.ecowin.series":{"source":"iana"},"application/vnd.ecowin.seriesrequest":{"source":"iana"},"application/vnd.ecowin.seriesupdate":{"source":"iana"},"application/vnd.efi.img":{"source":"iana"},"application/vnd.efi.iso":{"source":"iana"},"application/vnd.emclient.accessrequest+xml":{"source":"iana","compressible":true},"application/vnd.enliven":{"source":"iana","extensions":["nml"]},"application/vnd.enphase.envoy":{"source":"iana"},"application/vnd.eprints.data+xml":{"source":"iana","compressible":true},"application/vnd.epson.esf":{"source":"iana","extensions":["esf"]},"application/vnd.epson.msf":{"source":"iana","extensions":["msf"]},"application/vnd.epson.quickanime":{"source":"iana","extensions":["qam"]},"application/vnd.epson.salt":{"source":"iana","extensions":["slt"]},"application/vnd.epson.ssf":{"source":"iana","extensions":["ssf"]},"application/vnd.ericsson.quickcall":{"source":"iana"},"application/vnd.espass-espass+zip":{"source":"iana","compressible":false},"application/vnd.eszigno3+xml":{"source":"iana","compressible":true,"extensions":["es3","et3"]},"application/vnd.etsi.aoc+xml":{"source":"iana","compressible":true},"application/vnd.etsi.asic-e+zip":{"source":"iana","compressible":false},"application/vnd.etsi.asic-s+zip":{"source":"iana","compressible":false},"application/vnd.etsi.cug+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvcommand+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvdiscovery+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvprofile+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-bc+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-cod+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-npvr+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvservice+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsync+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvueprofile+xml":{"source":"iana","compressible":true},"application/vnd.etsi.mcid+xml":{"source":"iana","compressible":true},"application/vnd.etsi.mheg5":{"source":"iana"},"application/vnd.etsi.overload-control-policy-dataset+xml":{"source":"iana","compressible":true},"application/vnd.etsi.pstn+xml":{"source":"iana","compressible":true},"application/vnd.etsi.sci+xml":{"source":"iana","compressible":true},"application/vnd.etsi.simservs+xml":{"source":"iana","compressible":true},"application/vnd.etsi.timestamp-token":{"source":"iana"},"application/vnd.etsi.tsl+xml":{"source":"iana","compressible":true},"application/vnd.etsi.tsl.der":{"source":"iana"},"application/vnd.eu.kasparian.car+json":{"source":"iana","compressible":true},"application/vnd.eudora.data":{"source":"iana"},"application/vnd.evolv.ecig.profile":{"source":"iana"},"application/vnd.evolv.ecig.settings":{"source":"iana"},"application/vnd.evolv.ecig.theme":{"source":"iana"},"application/vnd.exstream-empower+zip":{"source":"iana","compressible":false},"application/vnd.exstream-package":{"source":"iana"},"application/vnd.ezpix-album":{"source":"iana","extensions":["ez2"]},"application/vnd.ezpix-package":{"source":"iana","extensions":["ez3"]},"application/vnd.f-secure.mobile":{"source":"iana"},"application/vnd.familysearch.gedcom+zip":{"source":"iana","compressible":false},"application/vnd.fastcopy-disk-image":{"source":"iana"},"application/vnd.fdf":{"source":"iana","extensions":["fdf"]},"application/vnd.fdsn.mseed":{"source":"iana","extensions":["mseed"]},"application/vnd.fdsn.seed":{"source":"iana","extensions":["seed","dataless"]},"application/vnd.ffsns":{"source":"iana"},"application/vnd.ficlab.flb+zip":{"source":"iana","compressible":false},"application/vnd.filmit.zfc":{"source":"iana"},"application/vnd.fints":{"source":"iana"},"application/vnd.firemonkeys.cloudcell":{"source":"iana"},"application/vnd.flographit":{"source":"iana","extensions":["gph"]},"application/vnd.fluxtime.clip":{"source":"iana","extensions":["ftc"]},"application/vnd.font-fontforge-sfd":{"source":"iana"},"application/vnd.framemaker":{"source":"iana","extensions":["fm","frame","maker","book"]},"application/vnd.frogans.fnc":{"source":"iana","extensions":["fnc"]},"application/vnd.frogans.ltf":{"source":"iana","extensions":["ltf"]},"application/vnd.fsc.weblaunch":{"source":"iana","extensions":["fsc"]},"application/vnd.fujifilm.fb.docuworks":{"source":"iana"},"application/vnd.fujifilm.fb.docuworks.binder":{"source":"iana"},"application/vnd.fujifilm.fb.docuworks.container":{"source":"iana"},"application/vnd.fujifilm.fb.jfi+xml":{"source":"iana","compressible":true},"application/vnd.fujitsu.oasys":{"source":"iana","extensions":["oas"]},"application/vnd.fujitsu.oasys2":{"source":"iana","extensions":["oa2"]},"application/vnd.fujitsu.oasys3":{"source":"iana","extensions":["oa3"]},"application/vnd.fujitsu.oasysgp":{"source":"iana","extensions":["fg5"]},"application/vnd.fujitsu.oasysprs":{"source":"iana","extensions":["bh2"]},"application/vnd.fujixerox.art-ex":{"source":"iana"},"application/vnd.fujixerox.art4":{"source":"iana"},"application/vnd.fujixerox.ddd":{"source":"iana","extensions":["ddd"]},"application/vnd.fujixerox.docuworks":{"source":"iana","extensions":["xdw"]},"application/vnd.fujixerox.docuworks.binder":{"source":"iana","extensions":["xbd"]},"application/vnd.fujixerox.docuworks.container":{"source":"iana"},"application/vnd.fujixerox.hbpl":{"source":"iana"},"application/vnd.fut-misnet":{"source":"iana"},"application/vnd.futoin+cbor":{"source":"iana"},"application/vnd.futoin+json":{"source":"iana","compressible":true},"application/vnd.fuzzysheet":{"source":"iana","extensions":["fzs"]},"application/vnd.genomatix.tuxedo":{"source":"iana","extensions":["txd"]},"application/vnd.gentics.grd+json":{"source":"iana","compressible":true},"application/vnd.geo+json":{"source":"iana","compressible":true},"application/vnd.geocube+xml":{"source":"iana","compressible":true},"application/vnd.geogebra.file":{"source":"iana","extensions":["ggb"]},"application/vnd.geogebra.slides":{"source":"iana"},"application/vnd.geogebra.tool":{"source":"iana","extensions":["ggt"]},"application/vnd.geometry-explorer":{"source":"iana","extensions":["gex","gre"]},"application/vnd.geonext":{"source":"iana","extensions":["gxt"]},"application/vnd.geoplan":{"source":"iana","extensions":["g2w"]},"application/vnd.geospace":{"source":"iana","extensions":["g3w"]},"application/vnd.gerber":{"source":"iana"},"application/vnd.globalplatform.card-content-mgt":{"source":"iana"},"application/vnd.globalplatform.card-content-mgt-response":{"source":"iana"},"application/vnd.gmx":{"source":"iana","extensions":["gmx"]},"application/vnd.google-apps.document":{"compressible":false,"extensions":["gdoc"]},"application/vnd.google-apps.presentation":{"compressible":false,"extensions":["gslides"]},"application/vnd.google-apps.spreadsheet":{"compressible":false,"extensions":["gsheet"]},"application/vnd.google-earth.kml+xml":{"source":"iana","compressible":true,"extensions":["kml"]},"application/vnd.google-earth.kmz":{"source":"iana","compressible":false,"extensions":["kmz"]},"application/vnd.gov.sk.e-form+xml":{"source":"iana","compressible":true},"application/vnd.gov.sk.e-form+zip":{"source":"iana","compressible":false},"application/vnd.gov.sk.xmldatacontainer+xml":{"source":"iana","compressible":true},"application/vnd.grafeq":{"source":"iana","extensions":["gqf","gqs"]},"application/vnd.gridmp":{"source":"iana"},"application/vnd.groove-account":{"source":"iana","extensions":["gac"]},"application/vnd.groove-help":{"source":"iana","extensions":["ghf"]},"application/vnd.groove-identity-message":{"source":"iana","extensions":["gim"]},"application/vnd.groove-injector":{"source":"iana","extensions":["grv"]},"application/vnd.groove-tool-message":{"source":"iana","extensions":["gtm"]},"application/vnd.groove-tool-template":{"source":"iana","extensions":["tpl"]},"application/vnd.groove-vcard":{"source":"iana","extensions":["vcg"]},"application/vnd.hal+json":{"source":"iana","compressible":true},"application/vnd.hal+xml":{"source":"iana","compressible":true,"extensions":["hal"]},"application/vnd.handheld-entertainment+xml":{"source":"iana","compressible":true,"extensions":["zmm"]},"application/vnd.hbci":{"source":"iana","extensions":["hbci"]},"application/vnd.hc+json":{"source":"iana","compressible":true},"application/vnd.hcl-bireports":{"source":"iana"},"application/vnd.hdt":{"source":"iana"},"application/vnd.heroku+json":{"source":"iana","compressible":true},"application/vnd.hhe.lesson-player":{"source":"iana","extensions":["les"]},"application/vnd.hl7cda+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.hl7v2+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.hp-hpgl":{"source":"iana","extensions":["hpgl"]},"application/vnd.hp-hpid":{"source":"iana","extensions":["hpid"]},"application/vnd.hp-hps":{"source":"iana","extensions":["hps"]},"application/vnd.hp-jlyt":{"source":"iana","extensions":["jlt"]},"application/vnd.hp-pcl":{"source":"iana","extensions":["pcl"]},"application/vnd.hp-pclxl":{"source":"iana","extensions":["pclxl"]},"application/vnd.httphone":{"source":"iana"},"application/vnd.hydrostatix.sof-data":{"source":"iana","extensions":["sfd-hdstx"]},"application/vnd.hyper+json":{"source":"iana","compressible":true},"application/vnd.hyper-item+json":{"source":"iana","compressible":true},"application/vnd.hyperdrive+json":{"source":"iana","compressible":true},"application/vnd.hzn-3d-crossword":{"source":"iana"},"application/vnd.ibm.afplinedata":{"source":"iana"},"application/vnd.ibm.electronic-media":{"source":"iana"},"application/vnd.ibm.minipay":{"source":"iana","extensions":["mpy"]},"application/vnd.ibm.modcap":{"source":"iana","extensions":["afp","listafp","list3820"]},"application/vnd.ibm.rights-management":{"source":"iana","extensions":["irm"]},"application/vnd.ibm.secure-container":{"source":"iana","extensions":["sc"]},"application/vnd.iccprofile":{"source":"iana","extensions":["icc","icm"]},"application/vnd.ieee.1905":{"source":"iana"},"application/vnd.igloader":{"source":"iana","extensions":["igl"]},"application/vnd.imagemeter.folder+zip":{"source":"iana","compressible":false},"application/vnd.imagemeter.image+zip":{"source":"iana","compressible":false},"application/vnd.immervision-ivp":{"source":"iana","extensions":["ivp"]},"application/vnd.immervision-ivu":{"source":"iana","extensions":["ivu"]},"application/vnd.ims.imsccv1p1":{"source":"iana"},"application/vnd.ims.imsccv1p2":{"source":"iana"},"application/vnd.ims.imsccv1p3":{"source":"iana"},"application/vnd.ims.lis.v2.result+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolconsumerprofile+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolproxy+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolproxy.id+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolsettings+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolsettings.simple+json":{"source":"iana","compressible":true},"application/vnd.informedcontrol.rms+xml":{"source":"iana","compressible":true},"application/vnd.informix-visionary":{"source":"iana"},"application/vnd.infotech.project":{"source":"iana"},"application/vnd.infotech.project+xml":{"source":"iana","compressible":true},"application/vnd.innopath.wamp.notification":{"source":"iana"},"application/vnd.insors.igm":{"source":"iana","extensions":["igm"]},"application/vnd.intercon.formnet":{"source":"iana","extensions":["xpw","xpx"]},"application/vnd.intergeo":{"source":"iana","extensions":["i2g"]},"application/vnd.intertrust.digibox":{"source":"iana"},"application/vnd.intertrust.nncp":{"source":"iana"},"application/vnd.intu.qbo":{"source":"iana","extensions":["qbo"]},"application/vnd.intu.qfx":{"source":"iana","extensions":["qfx"]},"application/vnd.iptc.g2.catalogitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.conceptitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.knowledgeitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.newsitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.newsmessage+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.packageitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.planningitem+xml":{"source":"iana","compressible":true},"application/vnd.ipunplugged.rcprofile":{"source":"iana","extensions":["rcprofile"]},"application/vnd.irepository.package+xml":{"source":"iana","compressible":true,"extensions":["irp"]},"application/vnd.is-xpr":{"source":"iana","extensions":["xpr"]},"application/vnd.isac.fcs":{"source":"iana","extensions":["fcs"]},"application/vnd.iso11783-10+zip":{"source":"iana","compressible":false},"application/vnd.jam":{"source":"iana","extensions":["jam"]},"application/vnd.japannet-directory-service":{"source":"iana"},"application/vnd.japannet-jpnstore-wakeup":{"source":"iana"},"application/vnd.japannet-payment-wakeup":{"source":"iana"},"application/vnd.japannet-registration":{"source":"iana"},"application/vnd.japannet-registration-wakeup":{"source":"iana"},"application/vnd.japannet-setstore-wakeup":{"source":"iana"},"application/vnd.japannet-verification":{"source":"iana"},"application/vnd.japannet-verification-wakeup":{"source":"iana"},"application/vnd.jcp.javame.midlet-rms":{"source":"iana","extensions":["rms"]},"application/vnd.jisp":{"source":"iana","extensions":["jisp"]},"application/vnd.joost.joda-archive":{"source":"iana","extensions":["joda"]},"application/vnd.jsk.isdn-ngn":{"source":"iana"},"application/vnd.kahootz":{"source":"iana","extensions":["ktz","ktr"]},"application/vnd.kde.karbon":{"source":"iana","extensions":["karbon"]},"application/vnd.kde.kchart":{"source":"iana","extensions":["chrt"]},"application/vnd.kde.kformula":{"source":"iana","extensions":["kfo"]},"application/vnd.kde.kivio":{"source":"iana","extensions":["flw"]},"application/vnd.kde.kontour":{"source":"iana","extensions":["kon"]},"application/vnd.kde.kpresenter":{"source":"iana","extensions":["kpr","kpt"]},"application/vnd.kde.kspread":{"source":"iana","extensions":["ksp"]},"application/vnd.kde.kword":{"source":"iana","extensions":["kwd","kwt"]},"application/vnd.kenameaapp":{"source":"iana","extensions":["htke"]},"application/vnd.kidspiration":{"source":"iana","extensions":["kia"]},"application/vnd.kinar":{"source":"iana","extensions":["kne","knp"]},"application/vnd.koan":{"source":"iana","extensions":["skp","skd","skt","skm"]},"application/vnd.kodak-descriptor":{"source":"iana","extensions":["sse"]},"application/vnd.las":{"source":"iana"},"application/vnd.las.las+json":{"source":"iana","compressible":true},"application/vnd.las.las+xml":{"source":"iana","compressible":true,"extensions":["lasxml"]},"application/vnd.laszip":{"source":"iana"},"application/vnd.leap+json":{"source":"iana","compressible":true},"application/vnd.liberty-request+xml":{"source":"iana","compressible":true},"application/vnd.llamagraphics.life-balance.desktop":{"source":"iana","extensions":["lbd"]},"application/vnd.llamagraphics.life-balance.exchange+xml":{"source":"iana","compressible":true,"extensions":["lbe"]},"application/vnd.logipipe.circuit+zip":{"source":"iana","compressible":false},"application/vnd.loom":{"source":"iana"},"application/vnd.lotus-1-2-3":{"source":"iana","extensions":["123"]},"application/vnd.lotus-approach":{"source":"iana","extensions":["apr"]},"application/vnd.lotus-freelance":{"source":"iana","extensions":["pre"]},"application/vnd.lotus-notes":{"source":"iana","extensions":["nsf"]},"application/vnd.lotus-organizer":{"source":"iana","extensions":["org"]},"application/vnd.lotus-screencam":{"source":"iana","extensions":["scm"]},"application/vnd.lotus-wordpro":{"source":"iana","extensions":["lwp"]},"application/vnd.macports.portpkg":{"source":"iana","extensions":["portpkg"]},"application/vnd.mapbox-vector-tile":{"source":"iana","extensions":["mvt"]},"application/vnd.marlin.drm.actiontoken+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.conftoken+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.license+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.mdcf":{"source":"iana"},"application/vnd.mason+json":{"source":"iana","compressible":true},"application/vnd.maxar.archive.3tz+zip":{"source":"iana","compressible":false},"application/vnd.maxmind.maxmind-db":{"source":"iana"},"application/vnd.mcd":{"source":"iana","extensions":["mcd"]},"application/vnd.medcalcdata":{"source":"iana","extensions":["mc1"]},"application/vnd.mediastation.cdkey":{"source":"iana","extensions":["cdkey"]},"application/vnd.meridian-slingshot":{"source":"iana"},"application/vnd.mfer":{"source":"iana","extensions":["mwf"]},"application/vnd.mfmp":{"source":"iana","extensions":["mfm"]},"application/vnd.micro+json":{"source":"iana","compressible":true},"application/vnd.micrografx.flo":{"source":"iana","extensions":["flo"]},"application/vnd.micrografx.igx":{"source":"iana","extensions":["igx"]},"application/vnd.microsoft.portable-executable":{"source":"iana"},"application/vnd.microsoft.windows.thumbnail-cache":{"source":"iana"},"application/vnd.miele+json":{"source":"iana","compressible":true},"application/vnd.mif":{"source":"iana","extensions":["mif"]},"application/vnd.minisoft-hp3000-save":{"source":"iana"},"application/vnd.mitsubishi.misty-guard.trustweb":{"source":"iana"},"application/vnd.mobius.daf":{"source":"iana","extensions":["daf"]},"application/vnd.mobius.dis":{"source":"iana","extensions":["dis"]},"application/vnd.mobius.mbk":{"source":"iana","extensions":["mbk"]},"application/vnd.mobius.mqy":{"source":"iana","extensions":["mqy"]},"application/vnd.mobius.msl":{"source":"iana","extensions":["msl"]},"application/vnd.mobius.plc":{"source":"iana","extensions":["plc"]},"application/vnd.mobius.txf":{"source":"iana","extensions":["txf"]},"application/vnd.mophun.application":{"source":"iana","extensions":["mpn"]},"application/vnd.mophun.certificate":{"source":"iana","extensions":["mpc"]},"application/vnd.motorola.flexsuite":{"source":"iana"},"application/vnd.motorola.flexsuite.adsi":{"source":"iana"},"application/vnd.motorola.flexsuite.fis":{"source":"iana"},"application/vnd.motorola.flexsuite.gotap":{"source":"iana"},"application/vnd.motorola.flexsuite.kmr":{"source":"iana"},"application/vnd.motorola.flexsuite.ttc":{"source":"iana"},"application/vnd.motorola.flexsuite.wem":{"source":"iana"},"application/vnd.motorola.iprm":{"source":"iana"},"application/vnd.mozilla.xul+xml":{"source":"iana","compressible":true,"extensions":["xul"]},"application/vnd.ms-3mfdocument":{"source":"iana"},"application/vnd.ms-artgalry":{"source":"iana","extensions":["cil"]},"application/vnd.ms-asf":{"source":"iana"},"application/vnd.ms-cab-compressed":{"source":"iana","extensions":["cab"]},"application/vnd.ms-color.iccprofile":{"source":"apache"},"application/vnd.ms-excel":{"source":"iana","compressible":false,"extensions":["xls","xlm","xla","xlc","xlt","xlw"]},"application/vnd.ms-excel.addin.macroenabled.12":{"source":"iana","extensions":["xlam"]},"application/vnd.ms-excel.sheet.binary.macroenabled.12":{"source":"iana","extensions":["xlsb"]},"application/vnd.ms-excel.sheet.macroenabled.12":{"source":"iana","extensions":["xlsm"]},"application/vnd.ms-excel.template.macroenabled.12":{"source":"iana","extensions":["xltm"]},"application/vnd.ms-fontobject":{"source":"iana","compressible":true,"extensions":["eot"]},"application/vnd.ms-htmlhelp":{"source":"iana","extensions":["chm"]},"application/vnd.ms-ims":{"source":"iana","extensions":["ims"]},"application/vnd.ms-lrm":{"source":"iana","extensions":["lrm"]},"application/vnd.ms-office.activex+xml":{"source":"iana","compressible":true},"application/vnd.ms-officetheme":{"source":"iana","extensions":["thmx"]},"application/vnd.ms-opentype":{"source":"apache","compressible":true},"application/vnd.ms-outlook":{"compressible":false,"extensions":["msg"]},"application/vnd.ms-package.obfuscated-opentype":{"source":"apache"},"application/vnd.ms-pki.seccat":{"source":"apache","extensions":["cat"]},"application/vnd.ms-pki.stl":{"source":"apache","extensions":["stl"]},"application/vnd.ms-playready.initiator+xml":{"source":"iana","compressible":true},"application/vnd.ms-powerpoint":{"source":"iana","compressible":false,"extensions":["ppt","pps","pot"]},"application/vnd.ms-powerpoint.addin.macroenabled.12":{"source":"iana","extensions":["ppam"]},"application/vnd.ms-powerpoint.presentation.macroenabled.12":{"source":"iana","extensions":["pptm"]},"application/vnd.ms-powerpoint.slide.macroenabled.12":{"source":"iana","extensions":["sldm"]},"application/vnd.ms-powerpoint.slideshow.macroenabled.12":{"source":"iana","extensions":["ppsm"]},"application/vnd.ms-powerpoint.template.macroenabled.12":{"source":"iana","extensions":["potm"]},"application/vnd.ms-printdevicecapabilities+xml":{"source":"iana","compressible":true},"application/vnd.ms-printing.printticket+xml":{"source":"apache","compressible":true},"application/vnd.ms-printschematicket+xml":{"source":"iana","compressible":true},"application/vnd.ms-project":{"source":"iana","extensions":["mpp","mpt"]},"application/vnd.ms-tnef":{"source":"iana"},"application/vnd.ms-windows.devicepairing":{"source":"iana"},"application/vnd.ms-windows.nwprinting.oob":{"source":"iana"},"application/vnd.ms-windows.printerpairing":{"source":"iana"},"application/vnd.ms-windows.wsd.oob":{"source":"iana"},"application/vnd.ms-wmdrm.lic-chlg-req":{"source":"iana"},"application/vnd.ms-wmdrm.lic-resp":{"source":"iana"},"application/vnd.ms-wmdrm.meter-chlg-req":{"source":"iana"},"application/vnd.ms-wmdrm.meter-resp":{"source":"iana"},"application/vnd.ms-word.document.macroenabled.12":{"source":"iana","extensions":["docm"]},"application/vnd.ms-word.template.macroenabled.12":{"source":"iana","extensions":["dotm"]},"application/vnd.ms-works":{"source":"iana","extensions":["wps","wks","wcm","wdb"]},"application/vnd.ms-wpl":{"source":"iana","extensions":["wpl"]},"application/vnd.ms-xpsdocument":{"source":"iana","compressible":false,"extensions":["xps"]},"application/vnd.msa-disk-image":{"source":"iana"},"application/vnd.mseq":{"source":"iana","extensions":["mseq"]},"application/vnd.msign":{"source":"iana"},"application/vnd.multiad.creator":{"source":"iana"},"application/vnd.multiad.creator.cif":{"source":"iana"},"application/vnd.music-niff":{"source":"iana"},"application/vnd.musician":{"source":"iana","extensions":["mus"]},"application/vnd.muvee.style":{"source":"iana","extensions":["msty"]},"application/vnd.mynfc":{"source":"iana","extensions":["taglet"]},"application/vnd.nacamar.ybrid+json":{"source":"iana","compressible":true},"application/vnd.ncd.control":{"source":"iana"},"application/vnd.ncd.reference":{"source":"iana"},"application/vnd.nearst.inv+json":{"source":"iana","compressible":true},"application/vnd.nebumind.line":{"source":"iana"},"application/vnd.nervana":{"source":"iana"},"application/vnd.netfpx":{"source":"iana"},"application/vnd.neurolanguage.nlu":{"source":"iana","extensions":["nlu"]},"application/vnd.nimn":{"source":"iana"},"application/vnd.nintendo.nitro.rom":{"source":"iana"},"application/vnd.nintendo.snes.rom":{"source":"iana"},"application/vnd.nitf":{"source":"iana","extensions":["ntf","nitf"]},"application/vnd.noblenet-directory":{"source":"iana","extensions":["nnd"]},"application/vnd.noblenet-sealer":{"source":"iana","extensions":["nns"]},"application/vnd.noblenet-web":{"source":"iana","extensions":["nnw"]},"application/vnd.nokia.catalogs":{"source":"iana"},"application/vnd.nokia.conml+wbxml":{"source":"iana"},"application/vnd.nokia.conml+xml":{"source":"iana","compressible":true},"application/vnd.nokia.iptv.config+xml":{"source":"iana","compressible":true},"application/vnd.nokia.isds-radio-presets":{"source":"iana"},"application/vnd.nokia.landmark+wbxml":{"source":"iana"},"application/vnd.nokia.landmark+xml":{"source":"iana","compressible":true},"application/vnd.nokia.landmarkcollection+xml":{"source":"iana","compressible":true},"application/vnd.nokia.n-gage.ac+xml":{"source":"iana","compressible":true,"extensions":["ac"]},"application/vnd.nokia.n-gage.data":{"source":"iana","extensions":["ngdat"]},"application/vnd.nokia.n-gage.symbian.install":{"source":"iana","extensions":["n-gage"]},"application/vnd.nokia.ncd":{"source":"iana"},"application/vnd.nokia.pcd+wbxml":{"source":"iana"},"application/vnd.nokia.pcd+xml":{"source":"iana","compressible":true},"application/vnd.nokia.radio-preset":{"source":"iana","extensions":["rpst"]},"application/vnd.nokia.radio-presets":{"source":"iana","extensions":["rpss"]},"application/vnd.novadigm.edm":{"source":"iana","extensions":["edm"]},"application/vnd.novadigm.edx":{"source":"iana","extensions":["edx"]},"application/vnd.novadigm.ext":{"source":"iana","extensions":["ext"]},"application/vnd.ntt-local.content-share":{"source":"iana"},"application/vnd.ntt-local.file-transfer":{"source":"iana"},"application/vnd.ntt-local.ogw_remote-access":{"source":"iana"},"application/vnd.ntt-local.sip-ta_remote":{"source":"iana"},"application/vnd.ntt-local.sip-ta_tcp_stream":{"source":"iana"},"application/vnd.oasis.opendocument.chart":{"source":"iana","extensions":["odc"]},"application/vnd.oasis.opendocument.chart-template":{"source":"iana","extensions":["otc"]},"application/vnd.oasis.opendocument.database":{"source":"iana","extensions":["odb"]},"application/vnd.oasis.opendocument.formula":{"source":"iana","extensions":["odf"]},"application/vnd.oasis.opendocument.formula-template":{"source":"iana","extensions":["odft"]},"application/vnd.oasis.opendocument.graphics":{"source":"iana","compressible":false,"extensions":["odg"]},"application/vnd.oasis.opendocument.graphics-template":{"source":"iana","extensions":["otg"]},"application/vnd.oasis.opendocument.image":{"source":"iana","extensions":["odi"]},"application/vnd.oasis.opendocument.image-template":{"source":"iana","extensions":["oti"]},"application/vnd.oasis.opendocument.presentation":{"source":"iana","compressible":false,"extensions":["odp"]},"application/vnd.oasis.opendocument.presentation-template":{"source":"iana","extensions":["otp"]},"application/vnd.oasis.opendocument.spreadsheet":{"source":"iana","compressible":false,"extensions":["ods"]},"application/vnd.oasis.opendocument.spreadsheet-template":{"source":"iana","extensions":["ots"]},"application/vnd.oasis.opendocument.text":{"source":"iana","compressible":false,"extensions":["odt"]},"application/vnd.oasis.opendocument.text-master":{"source":"iana","extensions":["odm"]},"application/vnd.oasis.opendocument.text-template":{"source":"iana","extensions":["ott"]},"application/vnd.oasis.opendocument.text-web":{"source":"iana","extensions":["oth"]},"application/vnd.obn":{"source":"iana"},"application/vnd.ocf+cbor":{"source":"iana"},"application/vnd.oci.image.manifest.v1+json":{"source":"iana","compressible":true},"application/vnd.oftn.l10n+json":{"source":"iana","compressible":true},"application/vnd.oipf.contentaccessdownload+xml":{"source":"iana","compressible":true},"application/vnd.oipf.contentaccessstreaming+xml":{"source":"iana","compressible":true},"application/vnd.oipf.cspg-hexbinary":{"source":"iana"},"application/vnd.oipf.dae.svg+xml":{"source":"iana","compressible":true},"application/vnd.oipf.dae.xhtml+xml":{"source":"iana","compressible":true},"application/vnd.oipf.mippvcontrolmessage+xml":{"source":"iana","compressible":true},"application/vnd.oipf.pae.gem":{"source":"iana"},"application/vnd.oipf.spdiscovery+xml":{"source":"iana","compressible":true},"application/vnd.oipf.spdlist+xml":{"source":"iana","compressible":true},"application/vnd.oipf.ueprofile+xml":{"source":"iana","compressible":true},"application/vnd.oipf.userprofile+xml":{"source":"iana","compressible":true},"application/vnd.olpc-sugar":{"source":"iana","extensions":["xo"]},"application/vnd.oma-scws-config":{"source":"iana"},"application/vnd.oma-scws-http-request":{"source":"iana"},"application/vnd.oma-scws-http-response":{"source":"iana"},"application/vnd.oma.bcast.associated-procedure-parameter+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.drm-trigger+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.imd+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.ltkm":{"source":"iana"},"application/vnd.oma.bcast.notification+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.provisioningtrigger":{"source":"iana"},"application/vnd.oma.bcast.sgboot":{"source":"iana"},"application/vnd.oma.bcast.sgdd+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.sgdu":{"source":"iana"},"application/vnd.oma.bcast.simple-symbol-container":{"source":"iana"},"application/vnd.oma.bcast.smartcard-trigger+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.sprov+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.stkm":{"source":"iana"},"application/vnd.oma.cab-address-book+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-feature-handler+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-pcc+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-subs-invite+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-user-prefs+xml":{"source":"iana","compressible":true},"application/vnd.oma.dcd":{"source":"iana"},"application/vnd.oma.dcdc":{"source":"iana"},"application/vnd.oma.dd2+xml":{"source":"iana","compressible":true,"extensions":["dd2"]},"application/vnd.oma.drm.risd+xml":{"source":"iana","compressible":true},"application/vnd.oma.group-usage-list+xml":{"source":"iana","compressible":true},"application/vnd.oma.lwm2m+cbor":{"source":"iana"},"application/vnd.oma.lwm2m+json":{"source":"iana","compressible":true},"application/vnd.oma.lwm2m+tlv":{"source":"iana"},"application/vnd.oma.pal+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.detailed-progress-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.final-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.groups+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.invocation-descriptor+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.optimized-progress-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.push":{"source":"iana"},"application/vnd.oma.scidm.messages+xml":{"source":"iana","compressible":true},"application/vnd.oma.xcap-directory+xml":{"source":"iana","compressible":true},"application/vnd.omads-email+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omads-file+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omads-folder+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omaloc-supl-init":{"source":"iana"},"application/vnd.onepager":{"source":"iana"},"application/vnd.onepagertamp":{"source":"iana"},"application/vnd.onepagertamx":{"source":"iana"},"application/vnd.onepagertat":{"source":"iana"},"application/vnd.onepagertatp":{"source":"iana"},"application/vnd.onepagertatx":{"source":"iana"},"application/vnd.openblox.game+xml":{"source":"iana","compressible":true,"extensions":["obgx"]},"application/vnd.openblox.game-binary":{"source":"iana"},"application/vnd.openeye.oeb":{"source":"iana"},"application/vnd.openofficeorg.extension":{"source":"apache","extensions":["oxt"]},"application/vnd.openstreetmap.data+xml":{"source":"iana","compressible":true,"extensions":["osm"]},"application/vnd.opentimestamps.ots":{"source":"iana"},"application/vnd.openxmlformats-officedocument.custom-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.customxmlproperties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawing+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.chart+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramcolors+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramdata+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramlayout+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramstyle+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.extended-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.commentauthors+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.handoutmaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.notesmaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.notesslide+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.presentation":{"source":"iana","compressible":false,"extensions":["pptx"]},"application/vnd.openxmlformats-officedocument.presentationml.presentation.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.presprops+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slide":{"source":"iana","extensions":["sldx"]},"application/vnd.openxmlformats-officedocument.presentationml.slide+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slidelayout+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slidemaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slideshow":{"source":"iana","extensions":["ppsx"]},"application/vnd.openxmlformats-officedocument.presentationml.slideshow.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slideupdateinfo+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.tablestyles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.tags+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.template":{"source":"iana","extensions":["potx"]},"application/vnd.openxmlformats-officedocument.presentationml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.viewprops+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.calcchain+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.externallink+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcachedefinition+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcacherecords+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivottable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.querytable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionheaders+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionlog+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedstrings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":{"source":"iana","compressible":false,"extensions":["xlsx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetmetadata+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.tablesinglecells+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.template":{"source":"iana","extensions":["xltx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.usernames+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.volatiledependencies+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.theme+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.themeoverride+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.vmldrawing":{"source":"iana"},"application/vnd.openxmlformats-officedocument.wordprocessingml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.document":{"source":"iana","compressible":false,"extensions":["docx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.glossary+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.endnotes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.fonttable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.footer+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.footnotes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.numbering+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.settings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.styles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.template":{"source":"iana","extensions":["dotx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.websettings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.core-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.digital-signature-xmlsignature+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.relationships+xml":{"source":"iana","compressible":true},"application/vnd.oracle.resource+json":{"source":"iana","compressible":true},"application/vnd.orange.indata":{"source":"iana"},"application/vnd.osa.netdeploy":{"source":"iana"},"application/vnd.osgeo.mapguide.package":{"source":"iana","extensions":["mgp"]},"application/vnd.osgi.bundle":{"source":"iana"},"application/vnd.osgi.dp":{"source":"iana","extensions":["dp"]},"application/vnd.osgi.subsystem":{"source":"iana","extensions":["esa"]},"application/vnd.otps.ct-kip+xml":{"source":"iana","compressible":true},"application/vnd.oxli.countgraph":{"source":"iana"},"application/vnd.pagerduty+json":{"source":"iana","compressible":true},"application/vnd.palm":{"source":"iana","extensions":["pdb","pqa","oprc"]},"application/vnd.panoply":{"source":"iana"},"application/vnd.paos.xml":{"source":"iana"},"application/vnd.patentdive":{"source":"iana"},"application/vnd.patientecommsdoc":{"source":"iana"},"application/vnd.pawaafile":{"source":"iana","extensions":["paw"]},"application/vnd.pcos":{"source":"iana"},"application/vnd.pg.format":{"source":"iana","extensions":["str"]},"application/vnd.pg.osasli":{"source":"iana","extensions":["ei6"]},"application/vnd.piaccess.application-licence":{"source":"iana"},"application/vnd.picsel":{"source":"iana","extensions":["efif"]},"application/vnd.pmi.widget":{"source":"iana","extensions":["wg"]},"application/vnd.poc.group-advertisement+xml":{"source":"iana","compressible":true},"application/vnd.pocketlearn":{"source":"iana","extensions":["plf"]},"application/vnd.powerbuilder6":{"source":"iana","extensions":["pbd"]},"application/vnd.powerbuilder6-s":{"source":"iana"},"application/vnd.powerbuilder7":{"source":"iana"},"application/vnd.powerbuilder7-s":{"source":"iana"},"application/vnd.powerbuilder75":{"source":"iana"},"application/vnd.powerbuilder75-s":{"source":"iana"},"application/vnd.preminet":{"source":"iana"},"application/vnd.previewsystems.box":{"source":"iana","extensions":["box"]},"application/vnd.proteus.magazine":{"source":"iana","extensions":["mgz"]},"application/vnd.psfs":{"source":"iana"},"application/vnd.publishare-delta-tree":{"source":"iana","extensions":["qps"]},"application/vnd.pvi.ptid1":{"source":"iana","extensions":["ptid"]},"application/vnd.pwg-multiplexed":{"source":"iana"},"application/vnd.pwg-xhtml-print+xml":{"source":"iana","compressible":true},"application/vnd.qualcomm.brew-app-res":{"source":"iana"},"application/vnd.quarantainenet":{"source":"iana"},"application/vnd.quark.quarkxpress":{"source":"iana","extensions":["qxd","qxt","qwd","qwt","qxl","qxb"]},"application/vnd.quobject-quoxdocument":{"source":"iana"},"application/vnd.radisys.moml+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-conf+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-conn+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-dialog+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-stream+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-conf+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-base+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-fax-detect+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-fax-sendrecv+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-group+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-speech+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-transform+xml":{"source":"iana","compressible":true},"application/vnd.rainstor.data":{"source":"iana"},"application/vnd.rapid":{"source":"iana"},"application/vnd.rar":{"source":"iana","extensions":["rar"]},"application/vnd.realvnc.bed":{"source":"iana","extensions":["bed"]},"application/vnd.recordare.musicxml":{"source":"iana","extensions":["mxl"]},"application/vnd.recordare.musicxml+xml":{"source":"iana","compressible":true,"extensions":["musicxml"]},"application/vnd.renlearn.rlprint":{"source":"iana"},"application/vnd.resilient.logic":{"source":"iana"},"application/vnd.restful+json":{"source":"iana","compressible":true},"application/vnd.rig.cryptonote":{"source":"iana","extensions":["cryptonote"]},"application/vnd.rim.cod":{"source":"apache","extensions":["cod"]},"application/vnd.rn-realmedia":{"source":"apache","extensions":["rm"]},"application/vnd.rn-realmedia-vbr":{"source":"apache","extensions":["rmvb"]},"application/vnd.route66.link66+xml":{"source":"iana","compressible":true,"extensions":["link66"]},"application/vnd.rs-274x":{"source":"iana"},"application/vnd.ruckus.download":{"source":"iana"},"application/vnd.s3sms":{"source":"iana"},"application/vnd.sailingtracker.track":{"source":"iana","extensions":["st"]},"application/vnd.sar":{"source":"iana"},"application/vnd.sbm.cid":{"source":"iana"},"application/vnd.sbm.mid2":{"source":"iana"},"application/vnd.scribus":{"source":"iana"},"application/vnd.sealed.3df":{"source":"iana"},"application/vnd.sealed.csf":{"source":"iana"},"application/vnd.sealed.doc":{"source":"iana"},"application/vnd.sealed.eml":{"source":"iana"},"application/vnd.sealed.mht":{"source":"iana"},"application/vnd.sealed.net":{"source":"iana"},"application/vnd.sealed.ppt":{"source":"iana"},"application/vnd.sealed.tiff":{"source":"iana"},"application/vnd.sealed.xls":{"source":"iana"},"application/vnd.sealedmedia.softseal.html":{"source":"iana"},"application/vnd.sealedmedia.softseal.pdf":{"source":"iana"},"application/vnd.seemail":{"source":"iana","extensions":["see"]},"application/vnd.seis+json":{"source":"iana","compressible":true},"application/vnd.sema":{"source":"iana","extensions":["sema"]},"application/vnd.semd":{"source":"iana","extensions":["semd"]},"application/vnd.semf":{"source":"iana","extensions":["semf"]},"application/vnd.shade-save-file":{"source":"iana"},"application/vnd.shana.informed.formdata":{"source":"iana","extensions":["ifm"]},"application/vnd.shana.informed.formtemplate":{"source":"iana","extensions":["itp"]},"application/vnd.shana.informed.interchange":{"source":"iana","extensions":["iif"]},"application/vnd.shana.informed.package":{"source":"iana","extensions":["ipk"]},"application/vnd.shootproof+json":{"source":"iana","compressible":true},"application/vnd.shopkick+json":{"source":"iana","compressible":true},"application/vnd.shp":{"source":"iana"},"application/vnd.shx":{"source":"iana"},"application/vnd.sigrok.session":{"source":"iana"},"application/vnd.simtech-mindmapper":{"source":"iana","extensions":["twd","twds"]},"application/vnd.siren+json":{"source":"iana","compressible":true},"application/vnd.smaf":{"source":"iana","extensions":["mmf"]},"application/vnd.smart.notebook":{"source":"iana"},"application/vnd.smart.teacher":{"source":"iana","extensions":["teacher"]},"application/vnd.snesdev-page-table":{"source":"iana"},"application/vnd.software602.filler.form+xml":{"source":"iana","compressible":true,"extensions":["fo"]},"application/vnd.software602.filler.form-xml-zip":{"source":"iana"},"application/vnd.solent.sdkm+xml":{"source":"iana","compressible":true,"extensions":["sdkm","sdkd"]},"application/vnd.spotfire.dxp":{"source":"iana","extensions":["dxp"]},"application/vnd.spotfire.sfs":{"source":"iana","extensions":["sfs"]},"application/vnd.sqlite3":{"source":"iana"},"application/vnd.sss-cod":{"source":"iana"},"application/vnd.sss-dtf":{"source":"iana"},"application/vnd.sss-ntf":{"source":"iana"},"application/vnd.stardivision.calc":{"source":"apache","extensions":["sdc"]},"application/vnd.stardivision.draw":{"source":"apache","extensions":["sda"]},"application/vnd.stardivision.impress":{"source":"apache","extensions":["sdd"]},"application/vnd.stardivision.math":{"source":"apache","extensions":["smf"]},"application/vnd.stardivision.writer":{"source":"apache","extensions":["sdw","vor"]},"application/vnd.stardivision.writer-global":{"source":"apache","extensions":["sgl"]},"application/vnd.stepmania.package":{"source":"iana","extensions":["smzip"]},"application/vnd.stepmania.stepchart":{"source":"iana","extensions":["sm"]},"application/vnd.street-stream":{"source":"iana"},"application/vnd.sun.wadl+xml":{"source":"iana","compressible":true,"extensions":["wadl"]},"application/vnd.sun.xml.calc":{"source":"apache","extensions":["sxc"]},"application/vnd.sun.xml.calc.template":{"source":"apache","extensions":["stc"]},"application/vnd.sun.xml.draw":{"source":"apache","extensions":["sxd"]},"application/vnd.sun.xml.draw.template":{"source":"apache","extensions":["std"]},"application/vnd.sun.xml.impress":{"source":"apache","extensions":["sxi"]},"application/vnd.sun.xml.impress.template":{"source":"apache","extensions":["sti"]},"application/vnd.sun.xml.math":{"source":"apache","extensions":["sxm"]},"application/vnd.sun.xml.writer":{"source":"apache","extensions":["sxw"]},"application/vnd.sun.xml.writer.global":{"source":"apache","extensions":["sxg"]},"application/vnd.sun.xml.writer.template":{"source":"apache","extensions":["stw"]},"application/vnd.sus-calendar":{"source":"iana","extensions":["sus","susp"]},"application/vnd.svd":{"source":"iana","extensions":["svd"]},"application/vnd.swiftview-ics":{"source":"iana"},"application/vnd.sycle+xml":{"source":"iana","compressible":true},"application/vnd.syft+json":{"source":"iana","compressible":true},"application/vnd.symbian.install":{"source":"apache","extensions":["sis","sisx"]},"application/vnd.syncml+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["xsm"]},"application/vnd.syncml.dm+wbxml":{"source":"iana","charset":"UTF-8","extensions":["bdm"]},"application/vnd.syncml.dm+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["xdm"]},"application/vnd.syncml.dm.notification":{"source":"iana"},"application/vnd.syncml.dmddf+wbxml":{"source":"iana"},"application/vnd.syncml.dmddf+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["ddf"]},"application/vnd.syncml.dmtnds+wbxml":{"source":"iana"},"application/vnd.syncml.dmtnds+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.syncml.ds.notification":{"source":"iana"},"application/vnd.tableschema+json":{"source":"iana","compressible":true},"application/vnd.tao.intent-module-archive":{"source":"iana","extensions":["tao"]},"application/vnd.tcpdump.pcap":{"source":"iana","extensions":["pcap","cap","dmp"]},"application/vnd.think-cell.ppttc+json":{"source":"iana","compressible":true},"application/vnd.tmd.mediaflex.api+xml":{"source":"iana","compressible":true},"application/vnd.tml":{"source":"iana"},"application/vnd.tmobile-livetv":{"source":"iana","extensions":["tmo"]},"application/vnd.tri.onesource":{"source":"iana"},"application/vnd.trid.tpt":{"source":"iana","extensions":["tpt"]},"application/vnd.triscape.mxs":{"source":"iana","extensions":["mxs"]},"application/vnd.trueapp":{"source":"iana","extensions":["tra"]},"application/vnd.truedoc":{"source":"iana"},"application/vnd.ubisoft.webplayer":{"source":"iana"},"application/vnd.ufdl":{"source":"iana","extensions":["ufd","ufdl"]},"application/vnd.uiq.theme":{"source":"iana","extensions":["utz"]},"application/vnd.umajin":{"source":"iana","extensions":["umj"]},"application/vnd.unity":{"source":"iana","extensions":["unityweb"]},"application/vnd.uoml+xml":{"source":"iana","compressible":true,"extensions":["uoml"]},"application/vnd.uplanet.alert":{"source":"iana"},"application/vnd.uplanet.alert-wbxml":{"source":"iana"},"application/vnd.uplanet.bearer-choice":{"source":"iana"},"application/vnd.uplanet.bearer-choice-wbxml":{"source":"iana"},"application/vnd.uplanet.cacheop":{"source":"iana"},"application/vnd.uplanet.cacheop-wbxml":{"source":"iana"},"application/vnd.uplanet.channel":{"source":"iana"},"application/vnd.uplanet.channel-wbxml":{"source":"iana"},"application/vnd.uplanet.list":{"source":"iana"},"application/vnd.uplanet.list-wbxml":{"source":"iana"},"application/vnd.uplanet.listcmd":{"source":"iana"},"application/vnd.uplanet.listcmd-wbxml":{"source":"iana"},"application/vnd.uplanet.signal":{"source":"iana"},"application/vnd.uri-map":{"source":"iana"},"application/vnd.valve.source.material":{"source":"iana"},"application/vnd.vcx":{"source":"iana","extensions":["vcx"]},"application/vnd.vd-study":{"source":"iana"},"application/vnd.vectorworks":{"source":"iana"},"application/vnd.vel+json":{"source":"iana","compressible":true},"application/vnd.verimatrix.vcas":{"source":"iana"},"application/vnd.veritone.aion+json":{"source":"iana","compressible":true},"application/vnd.veryant.thin":{"source":"iana"},"application/vnd.ves.encrypted":{"source":"iana"},"application/vnd.vidsoft.vidconference":{"source":"iana"},"application/vnd.visio":{"source":"iana","extensions":["vsd","vst","vss","vsw"]},"application/vnd.visionary":{"source":"iana","extensions":["vis"]},"application/vnd.vividence.scriptfile":{"source":"iana"},"application/vnd.vsf":{"source":"iana","extensions":["vsf"]},"application/vnd.wap.sic":{"source":"iana"},"application/vnd.wap.slc":{"source":"iana"},"application/vnd.wap.wbxml":{"source":"iana","charset":"UTF-8","extensions":["wbxml"]},"application/vnd.wap.wmlc":{"source":"iana","extensions":["wmlc"]},"application/vnd.wap.wmlscriptc":{"source":"iana","extensions":["wmlsc"]},"application/vnd.webturbo":{"source":"iana","extensions":["wtb"]},"application/vnd.wfa.dpp":{"source":"iana"},"application/vnd.wfa.p2p":{"source":"iana"},"application/vnd.wfa.wsc":{"source":"iana"},"application/vnd.windows.devicepairing":{"source":"iana"},"application/vnd.wmc":{"source":"iana"},"application/vnd.wmf.bootstrap":{"source":"iana"},"application/vnd.wolfram.mathematica":{"source":"iana"},"application/vnd.wolfram.mathematica.package":{"source":"iana"},"application/vnd.wolfram.player":{"source":"iana","extensions":["nbp"]},"application/vnd.wordperfect":{"source":"iana","extensions":["wpd"]},"application/vnd.wqd":{"source":"iana","extensions":["wqd"]},"application/vnd.wrq-hp3000-labelled":{"source":"iana"},"application/vnd.wt.stf":{"source":"iana","extensions":["stf"]},"application/vnd.wv.csp+wbxml":{"source":"iana"},"application/vnd.wv.csp+xml":{"source":"iana","compressible":true},"application/vnd.wv.ssp+xml":{"source":"iana","compressible":true},"application/vnd.xacml+json":{"source":"iana","compressible":true},"application/vnd.xara":{"source":"iana","extensions":["xar"]},"application/vnd.xfdl":{"source":"iana","extensions":["xfdl"]},"application/vnd.xfdl.webform":{"source":"iana"},"application/vnd.xmi+xml":{"source":"iana","compressible":true},"application/vnd.xmpie.cpkg":{"source":"iana"},"application/vnd.xmpie.dpkg":{"source":"iana"},"application/vnd.xmpie.plan":{"source":"iana"},"application/vnd.xmpie.ppkg":{"source":"iana"},"application/vnd.xmpie.xlim":{"source":"iana"},"application/vnd.yamaha.hv-dic":{"source":"iana","extensions":["hvd"]},"application/vnd.yamaha.hv-script":{"source":"iana","extensions":["hvs"]},"application/vnd.yamaha.hv-voice":{"source":"iana","extensions":["hvp"]},"application/vnd.yamaha.openscoreformat":{"source":"iana","extensions":["osf"]},"application/vnd.yamaha.openscoreformat.osfpvg+xml":{"source":"iana","compressible":true,"extensions":["osfpvg"]},"application/vnd.yamaha.remote-setup":{"source":"iana"},"application/vnd.yamaha.smaf-audio":{"source":"iana","extensions":["saf"]},"application/vnd.yamaha.smaf-phrase":{"source":"iana","extensions":["spf"]},"application/vnd.yamaha.through-ngn":{"source":"iana"},"application/vnd.yamaha.tunnel-udpencap":{"source":"iana"},"application/vnd.yaoweme":{"source":"iana"},"application/vnd.yellowriver-custom-menu":{"source":"iana","extensions":["cmp"]},"application/vnd.youtube.yt":{"source":"iana"},"application/vnd.zul":{"source":"iana","extensions":["zir","zirz"]},"application/vnd.zzazz.deck+xml":{"source":"iana","compressible":true,"extensions":["zaz"]},"application/voicexml+xml":{"source":"iana","compressible":true,"extensions":["vxml"]},"application/voucher-cms+json":{"source":"iana","compressible":true},"application/vq-rtcpxr":{"source":"iana"},"application/wasm":{"source":"iana","compressible":true,"extensions":["wasm"]},"application/watcherinfo+xml":{"source":"iana","compressible":true,"extensions":["wif"]},"application/webpush-options+json":{"source":"iana","compressible":true},"application/whoispp-query":{"source":"iana"},"application/whoispp-response":{"source":"iana"},"application/widget":{"source":"iana","extensions":["wgt"]},"application/winhlp":{"source":"apache","extensions":["hlp"]},"application/wita":{"source":"iana"},"application/wordperfect5.1":{"source":"iana"},"application/wsdl+xml":{"source":"iana","compressible":true,"extensions":["wsdl"]},"application/wspolicy+xml":{"source":"iana","compressible":true,"extensions":["wspolicy"]},"application/x-7z-compressed":{"source":"apache","compressible":false,"extensions":["7z"]},"application/x-abiword":{"source":"apache","extensions":["abw"]},"application/x-ace-compressed":{"source":"apache","extensions":["ace"]},"application/x-amf":{"source":"apache"},"application/x-apple-diskimage":{"source":"apache","extensions":["dmg"]},"application/x-arj":{"compressible":false,"extensions":["arj"]},"application/x-authorware-bin":{"source":"apache","extensions":["aab","x32","u32","vox"]},"application/x-authorware-map":{"source":"apache","extensions":["aam"]},"application/x-authorware-seg":{"source":"apache","extensions":["aas"]},"application/x-bcpio":{"source":"apache","extensions":["bcpio"]},"application/x-bdoc":{"compressible":false,"extensions":["bdoc"]},"application/x-bittorrent":{"source":"apache","extensions":["torrent"]},"application/x-blorb":{"source":"apache","extensions":["blb","blorb"]},"application/x-bzip":{"source":"apache","compressible":false,"extensions":["bz"]},"application/x-bzip2":{"source":"apache","compressible":false,"extensions":["bz2","boz"]},"application/x-cbr":{"source":"apache","extensions":["cbr","cba","cbt","cbz","cb7"]},"application/x-cdlink":{"source":"apache","extensions":["vcd"]},"application/x-cfs-compressed":{"source":"apache","extensions":["cfs"]},"application/x-chat":{"source":"apache","extensions":["chat"]},"application/x-chess-pgn":{"source":"apache","extensions":["pgn"]},"application/x-chrome-extension":{"extensions":["crx"]},"application/x-cocoa":{"source":"nginx","extensions":["cco"]},"application/x-compress":{"source":"apache"},"application/x-conference":{"source":"apache","extensions":["nsc"]},"application/x-cpio":{"source":"apache","extensions":["cpio"]},"application/x-csh":{"source":"apache","extensions":["csh"]},"application/x-deb":{"compressible":false},"application/x-debian-package":{"source":"apache","extensions":["deb","udeb"]},"application/x-dgc-compressed":{"source":"apache","extensions":["dgc"]},"application/x-director":{"source":"apache","extensions":["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"]},"application/x-doom":{"source":"apache","extensions":["wad"]},"application/x-dtbncx+xml":{"source":"apache","compressible":true,"extensions":["ncx"]},"application/x-dtbook+xml":{"source":"apache","compressible":true,"extensions":["dtb"]},"application/x-dtbresource+xml":{"source":"apache","compressible":true,"extensions":["res"]},"application/x-dvi":{"source":"apache","compressible":false,"extensions":["dvi"]},"application/x-envoy":{"source":"apache","extensions":["evy"]},"application/x-eva":{"source":"apache","extensions":["eva"]},"application/x-font-bdf":{"source":"apache","extensions":["bdf"]},"application/x-font-dos":{"source":"apache"},"application/x-font-framemaker":{"source":"apache"},"application/x-font-ghostscript":{"source":"apache","extensions":["gsf"]},"application/x-font-libgrx":{"source":"apache"},"application/x-font-linux-psf":{"source":"apache","extensions":["psf"]},"application/x-font-pcf":{"source":"apache","extensions":["pcf"]},"application/x-font-snf":{"source":"apache","extensions":["snf"]},"application/x-font-speedo":{"source":"apache"},"application/x-font-sunos-news":{"source":"apache"},"application/x-font-type1":{"source":"apache","extensions":["pfa","pfb","pfm","afm"]},"application/x-font-vfont":{"source":"apache"},"application/x-freearc":{"source":"apache","extensions":["arc"]},"application/x-futuresplash":{"source":"apache","extensions":["spl"]},"application/x-gca-compressed":{"source":"apache","extensions":["gca"]},"application/x-glulx":{"source":"apache","extensions":["ulx"]},"application/x-gnumeric":{"source":"apache","extensions":["gnumeric"]},"application/x-gramps-xml":{"source":"apache","extensions":["gramps"]},"application/x-gtar":{"source":"apache","extensions":["gtar"]},"application/x-gzip":{"source":"apache"},"application/x-hdf":{"source":"apache","extensions":["hdf"]},"application/x-httpd-php":{"compressible":true,"extensions":["php"]},"application/x-install-instructions":{"source":"apache","extensions":["install"]},"application/x-iso9660-image":{"source":"apache","extensions":["iso"]},"application/x-iwork-keynote-sffkey":{"extensions":["key"]},"application/x-iwork-numbers-sffnumbers":{"extensions":["numbers"]},"application/x-iwork-pages-sffpages":{"extensions":["pages"]},"application/x-java-archive-diff":{"source":"nginx","extensions":["jardiff"]},"application/x-java-jnlp-file":{"source":"apache","compressible":false,"extensions":["jnlp"]},"application/x-javascript":{"compressible":true},"application/x-keepass2":{"extensions":["kdbx"]},"application/x-latex":{"source":"apache","compressible":false,"extensions":["latex"]},"application/x-lua-bytecode":{"extensions":["luac"]},"application/x-lzh-compressed":{"source":"apache","extensions":["lzh","lha"]},"application/x-makeself":{"source":"nginx","extensions":["run"]},"application/x-mie":{"source":"apache","extensions":["mie"]},"application/x-mobipocket-ebook":{"source":"apache","extensions":["prc","mobi"]},"application/x-mpegurl":{"compressible":false},"application/x-ms-application":{"source":"apache","extensions":["application"]},"application/x-ms-shortcut":{"source":"apache","extensions":["lnk"]},"application/x-ms-wmd":{"source":"apache","extensions":["wmd"]},"application/x-ms-wmz":{"source":"apache","extensions":["wmz"]},"application/x-ms-xbap":{"source":"apache","extensions":["xbap"]},"application/x-msaccess":{"source":"apache","extensions":["mdb"]},"application/x-msbinder":{"source":"apache","extensions":["obd"]},"application/x-mscardfile":{"source":"apache","extensions":["crd"]},"application/x-msclip":{"source":"apache","extensions":["clp"]},"application/x-msdos-program":{"extensions":["exe"]},"application/x-msdownload":{"source":"apache","extensions":["exe","dll","com","bat","msi"]},"application/x-msmediaview":{"source":"apache","extensions":["mvb","m13","m14"]},"application/x-msmetafile":{"source":"apache","extensions":["wmf","wmz","emf","emz"]},"application/x-msmoney":{"source":"apache","extensions":["mny"]},"application/x-mspublisher":{"source":"apache","extensions":["pub"]},"application/x-msschedule":{"source":"apache","extensions":["scd"]},"application/x-msterminal":{"source":"apache","extensions":["trm"]},"application/x-mswrite":{"source":"apache","extensions":["wri"]},"application/x-netcdf":{"source":"apache","extensions":["nc","cdf"]},"application/x-ns-proxy-autoconfig":{"compressible":true,"extensions":["pac"]},"application/x-nzb":{"source":"apache","extensions":["nzb"]},"application/x-perl":{"source":"nginx","extensions":["pl","pm"]},"application/x-pilot":{"source":"nginx","extensions":["prc","pdb"]},"application/x-pkcs12":{"source":"apache","compressible":false,"extensions":["p12","pfx"]},"application/x-pkcs7-certificates":{"source":"apache","extensions":["p7b","spc"]},"application/x-pkcs7-certreqresp":{"source":"apache","extensions":["p7r"]},"application/x-pki-message":{"source":"iana"},"application/x-rar-compressed":{"source":"apache","compressible":false,"extensions":["rar"]},"application/x-redhat-package-manager":{"source":"nginx","extensions":["rpm"]},"application/x-research-info-systems":{"source":"apache","extensions":["ris"]},"application/x-sea":{"source":"nginx","extensions":["sea"]},"application/x-sh":{"source":"apache","compressible":true,"extensions":["sh"]},"application/x-shar":{"source":"apache","extensions":["shar"]},"application/x-shockwave-flash":{"source":"apache","compressible":false,"extensions":["swf"]},"application/x-silverlight-app":{"source":"apache","extensions":["xap"]},"application/x-sql":{"source":"apache","extensions":["sql"]},"application/x-stuffit":{"source":"apache","compressible":false,"extensions":["sit"]},"application/x-stuffitx":{"source":"apache","extensions":["sitx"]},"application/x-subrip":{"source":"apache","extensions":["srt"]},"application/x-sv4cpio":{"source":"apache","extensions":["sv4cpio"]},"application/x-sv4crc":{"source":"apache","extensions":["sv4crc"]},"application/x-t3vm-image":{"source":"apache","extensions":["t3"]},"application/x-tads":{"source":"apache","extensions":["gam"]},"application/x-tar":{"source":"apache","compressible":true,"extensions":["tar"]},"application/x-tcl":{"source":"apache","extensions":["tcl","tk"]},"application/x-tex":{"source":"apache","extensions":["tex"]},"application/x-tex-tfm":{"source":"apache","extensions":["tfm"]},"application/x-texinfo":{"source":"apache","extensions":["texinfo","texi"]},"application/x-tgif":{"source":"apache","extensions":["obj"]},"application/x-ustar":{"source":"apache","extensions":["ustar"]},"application/x-virtualbox-hdd":{"compressible":true,"extensions":["hdd"]},"application/x-virtualbox-ova":{"compressible":true,"extensions":["ova"]},"application/x-virtualbox-ovf":{"compressible":true,"extensions":["ovf"]},"application/x-virtualbox-vbox":{"compressible":true,"extensions":["vbox"]},"application/x-virtualbox-vbox-extpack":{"compressible":false,"extensions":["vbox-extpack"]},"application/x-virtualbox-vdi":{"compressible":true,"extensions":["vdi"]},"application/x-virtualbox-vhd":{"compressible":true,"extensions":["vhd"]},"application/x-virtualbox-vmdk":{"compressible":true,"extensions":["vmdk"]},"application/x-wais-source":{"source":"apache","extensions":["src"]},"application/x-web-app-manifest+json":{"compressible":true,"extensions":["webapp"]},"application/x-www-form-urlencoded":{"source":"iana","compressible":true},"application/x-x509-ca-cert":{"source":"iana","extensions":["der","crt","pem"]},"application/x-x509-ca-ra-cert":{"source":"iana"},"application/x-x509-next-ca-cert":{"source":"iana"},"application/x-xfig":{"source":"apache","extensions":["fig"]},"application/x-xliff+xml":{"source":"apache","compressible":true,"extensions":["xlf"]},"application/x-xpinstall":{"source":"apache","compressible":false,"extensions":["xpi"]},"application/x-xz":{"source":"apache","extensions":["xz"]},"application/x-zmachine":{"source":"apache","extensions":["z1","z2","z3","z4","z5","z6","z7","z8"]},"application/x400-bp":{"source":"iana"},"application/xacml+xml":{"source":"iana","compressible":true},"application/xaml+xml":{"source":"apache","compressible":true,"extensions":["xaml"]},"application/xcap-att+xml":{"source":"iana","compressible":true,"extensions":["xav"]},"application/xcap-caps+xml":{"source":"iana","compressible":true,"extensions":["xca"]},"application/xcap-diff+xml":{"source":"iana","compressible":true,"extensions":["xdf"]},"application/xcap-el+xml":{"source":"iana","compressible":true,"extensions":["xel"]},"application/xcap-error+xml":{"source":"iana","compressible":true},"application/xcap-ns+xml":{"source":"iana","compressible":true,"extensions":["xns"]},"application/xcon-conference-info+xml":{"source":"iana","compressible":true},"application/xcon-conference-info-diff+xml":{"source":"iana","compressible":true},"application/xenc+xml":{"source":"iana","compressible":true,"extensions":["xenc"]},"application/xhtml+xml":{"source":"iana","compressible":true,"extensions":["xhtml","xht"]},"application/xhtml-voice+xml":{"source":"apache","compressible":true},"application/xliff+xml":{"source":"iana","compressible":true,"extensions":["xlf"]},"application/xml":{"source":"iana","compressible":true,"extensions":["xml","xsl","xsd","rng"]},"application/xml-dtd":{"source":"iana","compressible":true,"extensions":["dtd"]},"application/xml-external-parsed-entity":{"source":"iana"},"application/xml-patch+xml":{"source":"iana","compressible":true},"application/xmpp+xml":{"source":"iana","compressible":true},"application/xop+xml":{"source":"iana","compressible":true,"extensions":["xop"]},"application/xproc+xml":{"source":"apache","compressible":true,"extensions":["xpl"]},"application/xslt+xml":{"source":"iana","compressible":true,"extensions":["xsl","xslt"]},"application/xspf+xml":{"source":"apache","compressible":true,"extensions":["xspf"]},"application/xv+xml":{"source":"iana","compressible":true,"extensions":["mxml","xhvml","xvml","xvm"]},"application/yang":{"source":"iana","extensions":["yang"]},"application/yang-data+json":{"source":"iana","compressible":true},"application/yang-data+xml":{"source":"iana","compressible":true},"application/yang-patch+json":{"source":"iana","compressible":true},"application/yang-patch+xml":{"source":"iana","compressible":true},"application/yin+xml":{"source":"iana","compressible":true,"extensions":["yin"]},"application/zip":{"source":"iana","compressible":false,"extensions":["zip"]},"application/zlib":{"source":"iana"},"application/zstd":{"source":"iana"},"audio/1d-interleaved-parityfec":{"source":"iana"},"audio/32kadpcm":{"source":"iana"},"audio/3gpp":{"source":"iana","compressible":false,"extensions":["3gpp"]},"audio/3gpp2":{"source":"iana"},"audio/aac":{"source":"iana"},"audio/ac3":{"source":"iana"},"audio/adpcm":{"source":"apache","extensions":["adp"]},"audio/amr":{"source":"iana","extensions":["amr"]},"audio/amr-wb":{"source":"iana"},"audio/amr-wb+":{"source":"iana"},"audio/aptx":{"source":"iana"},"audio/asc":{"source":"iana"},"audio/atrac-advanced-lossless":{"source":"iana"},"audio/atrac-x":{"source":"iana"},"audio/atrac3":{"source":"iana"},"audio/basic":{"source":"iana","compressible":false,"extensions":["au","snd"]},"audio/bv16":{"source":"iana"},"audio/bv32":{"source":"iana"},"audio/clearmode":{"source":"iana"},"audio/cn":{"source":"iana"},"audio/dat12":{"source":"iana"},"audio/dls":{"source":"iana"},"audio/dsr-es201108":{"source":"iana"},"audio/dsr-es202050":{"source":"iana"},"audio/dsr-es202211":{"source":"iana"},"audio/dsr-es202212":{"source":"iana"},"audio/dv":{"source":"iana"},"audio/dvi4":{"source":"iana"},"audio/eac3":{"source":"iana"},"audio/encaprtp":{"source":"iana"},"audio/evrc":{"source":"iana"},"audio/evrc-qcp":{"source":"iana"},"audio/evrc0":{"source":"iana"},"audio/evrc1":{"source":"iana"},"audio/evrcb":{"source":"iana"},"audio/evrcb0":{"source":"iana"},"audio/evrcb1":{"source":"iana"},"audio/evrcnw":{"source":"iana"},"audio/evrcnw0":{"source":"iana"},"audio/evrcnw1":{"source":"iana"},"audio/evrcwb":{"source":"iana"},"audio/evrcwb0":{"source":"iana"},"audio/evrcwb1":{"source":"iana"},"audio/evs":{"source":"iana"},"audio/flexfec":{"source":"iana"},"audio/fwdred":{"source":"iana"},"audio/g711-0":{"source":"iana"},"audio/g719":{"source":"iana"},"audio/g722":{"source":"iana"},"audio/g7221":{"source":"iana"},"audio/g723":{"source":"iana"},"audio/g726-16":{"source":"iana"},"audio/g726-24":{"source":"iana"},"audio/g726-32":{"source":"iana"},"audio/g726-40":{"source":"iana"},"audio/g728":{"source":"iana"},"audio/g729":{"source":"iana"},"audio/g7291":{"source":"iana"},"audio/g729d":{"source":"iana"},"audio/g729e":{"source":"iana"},"audio/gsm":{"source":"iana"},"audio/gsm-efr":{"source":"iana"},"audio/gsm-hr-08":{"source":"iana"},"audio/ilbc":{"source":"iana"},"audio/ip-mr_v2.5":{"source":"iana"},"audio/isac":{"source":"apache"},"audio/l16":{"source":"iana"},"audio/l20":{"source":"iana"},"audio/l24":{"source":"iana","compressible":false},"audio/l8":{"source":"iana"},"audio/lpc":{"source":"iana"},"audio/melp":{"source":"iana"},"audio/melp1200":{"source":"iana"},"audio/melp2400":{"source":"iana"},"audio/melp600":{"source":"iana"},"audio/mhas":{"source":"iana"},"audio/midi":{"source":"apache","extensions":["mid","midi","kar","rmi"]},"audio/mobile-xmf":{"source":"iana","extensions":["mxmf"]},"audio/mp3":{"compressible":false,"extensions":["mp3"]},"audio/mp4":{"source":"iana","compressible":false,"extensions":["m4a","mp4a"]},"audio/mp4a-latm":{"source":"iana"},"audio/mpa":{"source":"iana"},"audio/mpa-robust":{"source":"iana"},"audio/mpeg":{"source":"iana","compressible":false,"extensions":["mpga","mp2","mp2a","mp3","m2a","m3a"]},"audio/mpeg4-generic":{"source":"iana"},"audio/musepack":{"source":"apache"},"audio/ogg":{"source":"iana","compressible":false,"extensions":["oga","ogg","spx","opus"]},"audio/opus":{"source":"iana"},"audio/parityfec":{"source":"iana"},"audio/pcma":{"source":"iana"},"audio/pcma-wb":{"source":"iana"},"audio/pcmu":{"source":"iana"},"audio/pcmu-wb":{"source":"iana"},"audio/prs.sid":{"source":"iana"},"audio/qcelp":{"source":"iana"},"audio/raptorfec":{"source":"iana"},"audio/red":{"source":"iana"},"audio/rtp-enc-aescm128":{"source":"iana"},"audio/rtp-midi":{"source":"iana"},"audio/rtploopback":{"source":"iana"},"audio/rtx":{"source":"iana"},"audio/s3m":{"source":"apache","extensions":["s3m"]},"audio/scip":{"source":"iana"},"audio/silk":{"source":"apache","extensions":["sil"]},"audio/smv":{"source":"iana"},"audio/smv-qcp":{"source":"iana"},"audio/smv0":{"source":"iana"},"audio/sofa":{"source":"iana"},"audio/sp-midi":{"source":"iana"},"audio/speex":{"source":"iana"},"audio/t140c":{"source":"iana"},"audio/t38":{"source":"iana"},"audio/telephone-event":{"source":"iana"},"audio/tetra_acelp":{"source":"iana"},"audio/tetra_acelp_bb":{"source":"iana"},"audio/tone":{"source":"iana"},"audio/tsvcis":{"source":"iana"},"audio/uemclip":{"source":"iana"},"audio/ulpfec":{"source":"iana"},"audio/usac":{"source":"iana"},"audio/vdvi":{"source":"iana"},"audio/vmr-wb":{"source":"iana"},"audio/vnd.3gpp.iufp":{"source":"iana"},"audio/vnd.4sb":{"source":"iana"},"audio/vnd.audiokoz":{"source":"iana"},"audio/vnd.celp":{"source":"iana"},"audio/vnd.cisco.nse":{"source":"iana"},"audio/vnd.cmles.radio-events":{"source":"iana"},"audio/vnd.cns.anp1":{"source":"iana"},"audio/vnd.cns.inf1":{"source":"iana"},"audio/vnd.dece.audio":{"source":"iana","extensions":["uva","uvva"]},"audio/vnd.digital-winds":{"source":"iana","extensions":["eol"]},"audio/vnd.dlna.adts":{"source":"iana"},"audio/vnd.dolby.heaac.1":{"source":"iana"},"audio/vnd.dolby.heaac.2":{"source":"iana"},"audio/vnd.dolby.mlp":{"source":"iana"},"audio/vnd.dolby.mps":{"source":"iana"},"audio/vnd.dolby.pl2":{"source":"iana"},"audio/vnd.dolby.pl2x":{"source":"iana"},"audio/vnd.dolby.pl2z":{"source":"iana"},"audio/vnd.dolby.pulse.1":{"source":"iana"},"audio/vnd.dra":{"source":"iana","extensions":["dra"]},"audio/vnd.dts":{"source":"iana","extensions":["dts"]},"audio/vnd.dts.hd":{"source":"iana","extensions":["dtshd"]},"audio/vnd.dts.uhd":{"source":"iana"},"audio/vnd.dvb.file":{"source":"iana"},"audio/vnd.everad.plj":{"source":"iana"},"audio/vnd.hns.audio":{"source":"iana"},"audio/vnd.lucent.voice":{"source":"iana","extensions":["lvp"]},"audio/vnd.ms-playready.media.pya":{"source":"iana","extensions":["pya"]},"audio/vnd.nokia.mobile-xmf":{"source":"iana"},"audio/vnd.nortel.vbk":{"source":"iana"},"audio/vnd.nuera.ecelp4800":{"source":"iana","extensions":["ecelp4800"]},"audio/vnd.nuera.ecelp7470":{"source":"iana","extensions":["ecelp7470"]},"audio/vnd.nuera.ecelp9600":{"source":"iana","extensions":["ecelp9600"]},"audio/vnd.octel.sbc":{"source":"iana"},"audio/vnd.presonus.multitrack":{"source":"iana"},"audio/vnd.qcelp":{"source":"iana"},"audio/vnd.rhetorex.32kadpcm":{"source":"iana"},"audio/vnd.rip":{"source":"iana","extensions":["rip"]},"audio/vnd.rn-realaudio":{"compressible":false},"audio/vnd.sealedmedia.softseal.mpeg":{"source":"iana"},"audio/vnd.vmx.cvsd":{"source":"iana"},"audio/vnd.wave":{"compressible":false},"audio/vorbis":{"source":"iana","compressible":false},"audio/vorbis-config":{"source":"iana"},"audio/wav":{"compressible":false,"extensions":["wav"]},"audio/wave":{"compressible":false,"extensions":["wav"]},"audio/webm":{"source":"apache","compressible":false,"extensions":["weba"]},"audio/x-aac":{"source":"apache","compressible":false,"extensions":["aac"]},"audio/x-aiff":{"source":"apache","extensions":["aif","aiff","aifc"]},"audio/x-caf":{"source":"apache","compressible":false,"extensions":["caf"]},"audio/x-flac":{"source":"apache","extensions":["flac"]},"audio/x-m4a":{"source":"nginx","extensions":["m4a"]},"audio/x-matroska":{"source":"apache","extensions":["mka"]},"audio/x-mpegurl":{"source":"apache","extensions":["m3u"]},"audio/x-ms-wax":{"source":"apache","extensions":["wax"]},"audio/x-ms-wma":{"source":"apache","extensions":["wma"]},"audio/x-pn-realaudio":{"source":"apache","extensions":["ram","ra"]},"audio/x-pn-realaudio-plugin":{"source":"apache","extensions":["rmp"]},"audio/x-realaudio":{"source":"nginx","extensions":["ra"]},"audio/x-tta":{"source":"apache"},"audio/x-wav":{"source":"apache","extensions":["wav"]},"audio/xm":{"source":"apache","extensions":["xm"]},"chemical/x-cdx":{"source":"apache","extensions":["cdx"]},"chemical/x-cif":{"source":"apache","extensions":["cif"]},"chemical/x-cmdf":{"source":"apache","extensions":["cmdf"]},"chemical/x-cml":{"source":"apache","extensions":["cml"]},"chemical/x-csml":{"source":"apache","extensions":["csml"]},"chemical/x-pdb":{"source":"apache"},"chemical/x-xyz":{"source":"apache","extensions":["xyz"]},"font/collection":{"source":"iana","extensions":["ttc"]},"font/otf":{"source":"iana","compressible":true,"extensions":["otf"]},"font/sfnt":{"source":"iana"},"font/ttf":{"source":"iana","compressible":true,"extensions":["ttf"]},"font/woff":{"source":"iana","extensions":["woff"]},"font/woff2":{"source":"iana","extensions":["woff2"]},"image/aces":{"source":"iana","extensions":["exr"]},"image/apng":{"compressible":false,"extensions":["apng"]},"image/avci":{"source":"iana","extensions":["avci"]},"image/avcs":{"source":"iana","extensions":["avcs"]},"image/avif":{"source":"iana","compressible":false,"extensions":["avif"]},"image/bmp":{"source":"iana","compressible":true,"extensions":["bmp"]},"image/cgm":{"source":"iana","extensions":["cgm"]},"image/dicom-rle":{"source":"iana","extensions":["drle"]},"image/emf":{"source":"iana","extensions":["emf"]},"image/fits":{"source":"iana","extensions":["fits"]},"image/g3fax":{"source":"iana","extensions":["g3"]},"image/gif":{"source":"iana","compressible":false,"extensions":["gif"]},"image/heic":{"source":"iana","extensions":["heic"]},"image/heic-sequence":{"source":"iana","extensions":["heics"]},"image/heif":{"source":"iana","extensions":["heif"]},"image/heif-sequence":{"source":"iana","extensions":["heifs"]},"image/hej2k":{"source":"iana","extensions":["hej2"]},"image/hsj2":{"source":"iana","extensions":["hsj2"]},"image/ief":{"source":"iana","extensions":["ief"]},"image/jls":{"source":"iana","extensions":["jls"]},"image/jp2":{"source":"iana","compressible":false,"extensions":["jp2","jpg2"]},"image/jpeg":{"source":"iana","compressible":false,"extensions":["jpeg","jpg","jpe"]},"image/jph":{"source":"iana","extensions":["jph"]},"image/jphc":{"source":"iana","extensions":["jhc"]},"image/jpm":{"source":"iana","compressible":false,"extensions":["jpm"]},"image/jpx":{"source":"iana","compressible":false,"extensions":["jpx","jpf"]},"image/jxr":{"source":"iana","extensions":["jxr"]},"image/jxra":{"source":"iana","extensions":["jxra"]},"image/jxrs":{"source":"iana","extensions":["jxrs"]},"image/jxs":{"source":"iana","extensions":["jxs"]},"image/jxsc":{"source":"iana","extensions":["jxsc"]},"image/jxsi":{"source":"iana","extensions":["jxsi"]},"image/jxss":{"source":"iana","extensions":["jxss"]},"image/ktx":{"source":"iana","extensions":["ktx"]},"image/ktx2":{"source":"iana","extensions":["ktx2"]},"image/naplps":{"source":"iana"},"image/pjpeg":{"compressible":false},"image/png":{"source":"iana","compressible":false,"extensions":["png"]},"image/prs.btif":{"source":"iana","extensions":["btif"]},"image/prs.pti":{"source":"iana","extensions":["pti"]},"image/pwg-raster":{"source":"iana"},"image/sgi":{"source":"apache","extensions":["sgi"]},"image/svg+xml":{"source":"iana","compressible":true,"extensions":["svg","svgz"]},"image/t38":{"source":"iana","extensions":["t38"]},"image/tiff":{"source":"iana","compressible":false,"extensions":["tif","tiff"]},"image/tiff-fx":{"source":"iana","extensions":["tfx"]},"image/vnd.adobe.photoshop":{"source":"iana","compressible":true,"extensions":["psd"]},"image/vnd.airzip.accelerator.azv":{"source":"iana","extensions":["azv"]},"image/vnd.cns.inf2":{"source":"iana"},"image/vnd.dece.graphic":{"source":"iana","extensions":["uvi","uvvi","uvg","uvvg"]},"image/vnd.djvu":{"source":"iana","extensions":["djvu","djv"]},"image/vnd.dvb.subtitle":{"source":"iana","extensions":["sub"]},"image/vnd.dwg":{"source":"iana","extensions":["dwg"]},"image/vnd.dxf":{"source":"iana","extensions":["dxf"]},"image/vnd.fastbidsheet":{"source":"iana","extensions":["fbs"]},"image/vnd.fpx":{"source":"iana","extensions":["fpx"]},"image/vnd.fst":{"source":"iana","extensions":["fst"]},"image/vnd.fujixerox.edmics-mmr":{"source":"iana","extensions":["mmr"]},"image/vnd.fujixerox.edmics-rlc":{"source":"iana","extensions":["rlc"]},"image/vnd.globalgraphics.pgb":{"source":"iana"},"image/vnd.microsoft.icon":{"source":"iana","compressible":true,"extensions":["ico"]},"image/vnd.mix":{"source":"iana"},"image/vnd.mozilla.apng":{"source":"iana"},"image/vnd.ms-dds":{"compressible":true,"extensions":["dds"]},"image/vnd.ms-modi":{"source":"iana","extensions":["mdi"]},"image/vnd.ms-photo":{"source":"apache","extensions":["wdp"]},"image/vnd.net-fpx":{"source":"iana","extensions":["npx"]},"image/vnd.pco.b16":{"source":"iana","extensions":["b16"]},"image/vnd.radiance":{"source":"iana"},"image/vnd.sealed.png":{"source":"iana"},"image/vnd.sealedmedia.softseal.gif":{"source":"iana"},"image/vnd.sealedmedia.softseal.jpg":{"source":"iana"},"image/vnd.svf":{"source":"iana"},"image/vnd.tencent.tap":{"source":"iana","extensions":["tap"]},"image/vnd.valve.source.texture":{"source":"iana","extensions":["vtf"]},"image/vnd.wap.wbmp":{"source":"iana","extensions":["wbmp"]},"image/vnd.xiff":{"source":"iana","extensions":["xif"]},"image/vnd.zbrush.pcx":{"source":"iana","extensions":["pcx"]},"image/webp":{"source":"apache","extensions":["webp"]},"image/wmf":{"source":"iana","extensions":["wmf"]},"image/x-3ds":{"source":"apache","extensions":["3ds"]},"image/x-cmu-raster":{"source":"apache","extensions":["ras"]},"image/x-cmx":{"source":"apache","extensions":["cmx"]},"image/x-freehand":{"source":"apache","extensions":["fh","fhc","fh4","fh5","fh7"]},"image/x-icon":{"source":"apache","compressible":true,"extensions":["ico"]},"image/x-jng":{"source":"nginx","extensions":["jng"]},"image/x-mrsid-image":{"source":"apache","extensions":["sid"]},"image/x-ms-bmp":{"source":"nginx","compressible":true,"extensions":["bmp"]},"image/x-pcx":{"source":"apache","extensions":["pcx"]},"image/x-pict":{"source":"apache","extensions":["pic","pct"]},"image/x-portable-anymap":{"source":"apache","extensions":["pnm"]},"image/x-portable-bitmap":{"source":"apache","extensions":["pbm"]},"image/x-portable-graymap":{"source":"apache","extensions":["pgm"]},"image/x-portable-pixmap":{"source":"apache","extensions":["ppm"]},"image/x-rgb":{"source":"apache","extensions":["rgb"]},"image/x-tga":{"source":"apache","extensions":["tga"]},"image/x-xbitmap":{"source":"apache","extensions":["xbm"]},"image/x-xcf":{"compressible":false},"image/x-xpixmap":{"source":"apache","extensions":["xpm"]},"image/x-xwindowdump":{"source":"apache","extensions":["xwd"]},"message/cpim":{"source":"iana"},"message/delivery-status":{"source":"iana"},"message/disposition-notification":{"source":"iana","extensions":["disposition-notification"]},"message/external-body":{"source":"iana"},"message/feedback-report":{"source":"iana"},"message/global":{"source":"iana","extensions":["u8msg"]},"message/global-delivery-status":{"source":"iana","extensions":["u8dsn"]},"message/global-disposition-notification":{"source":"iana","extensions":["u8mdn"]},"message/global-headers":{"source":"iana","extensions":["u8hdr"]},"message/http":{"source":"iana","compressible":false},"message/imdn+xml":{"source":"iana","compressible":true},"message/news":{"source":"iana"},"message/partial":{"source":"iana","compressible":false},"message/rfc822":{"source":"iana","compressible":true,"extensions":["eml","mime"]},"message/s-http":{"source":"iana"},"message/sip":{"source":"iana"},"message/sipfrag":{"source":"iana"},"message/tracking-status":{"source":"iana"},"message/vnd.si.simp":{"source":"iana"},"message/vnd.wfa.wsc":{"source":"iana","extensions":["wsc"]},"model/3mf":{"source":"iana","extensions":["3mf"]},"model/e57":{"source":"iana"},"model/gltf+json":{"source":"iana","compressible":true,"extensions":["gltf"]},"model/gltf-binary":{"source":"iana","compressible":true,"extensions":["glb"]},"model/iges":{"source":"iana","compressible":false,"extensions":["igs","iges"]},"model/mesh":{"source":"iana","compressible":false,"extensions":["msh","mesh","silo"]},"model/mtl":{"source":"iana","extensions":["mtl"]},"model/obj":{"source":"iana","extensions":["obj"]},"model/step":{"source":"iana"},"model/step+xml":{"source":"iana","compressible":true,"extensions":["stpx"]},"model/step+zip":{"source":"iana","compressible":false,"extensions":["stpz"]},"model/step-xml+zip":{"source":"iana","compressible":false,"extensions":["stpxz"]},"model/stl":{"source":"iana","extensions":["stl"]},"model/vnd.collada+xml":{"source":"iana","compressible":true,"extensions":["dae"]},"model/vnd.dwf":{"source":"iana","extensions":["dwf"]},"model/vnd.flatland.3dml":{"source":"iana"},"model/vnd.gdl":{"source":"iana","extensions":["gdl"]},"model/vnd.gs-gdl":{"source":"apache"},"model/vnd.gs.gdl":{"source":"iana"},"model/vnd.gtw":{"source":"iana","extensions":["gtw"]},"model/vnd.moml+xml":{"source":"iana","compressible":true},"model/vnd.mts":{"source":"iana","extensions":["mts"]},"model/vnd.opengex":{"source":"iana","extensions":["ogex"]},"model/vnd.parasolid.transmit.binary":{"source":"iana","extensions":["x_b"]},"model/vnd.parasolid.transmit.text":{"source":"iana","extensions":["x_t"]},"model/vnd.pytha.pyox":{"source":"iana"},"model/vnd.rosette.annotated-data-model":{"source":"iana"},"model/vnd.sap.vds":{"source":"iana","extensions":["vds"]},"model/vnd.usdz+zip":{"source":"iana","compressible":false,"extensions":["usdz"]},"model/vnd.valve.source.compiled-map":{"source":"iana","extensions":["bsp"]},"model/vnd.vtu":{"source":"iana","extensions":["vtu"]},"model/vrml":{"source":"iana","compressible":false,"extensions":["wrl","vrml"]},"model/x3d+binary":{"source":"apache","compressible":false,"extensions":["x3db","x3dbz"]},"model/x3d+fastinfoset":{"source":"iana","extensions":["x3db"]},"model/x3d+vrml":{"source":"apache","compressible":false,"extensions":["x3dv","x3dvz"]},"model/x3d+xml":{"source":"iana","compressible":true,"extensions":["x3d","x3dz"]},"model/x3d-vrml":{"source":"iana","extensions":["x3dv"]},"multipart/alternative":{"source":"iana","compressible":false},"multipart/appledouble":{"source":"iana"},"multipart/byteranges":{"source":"iana"},"multipart/digest":{"source":"iana"},"multipart/encrypted":{"source":"iana","compressible":false},"multipart/form-data":{"source":"iana","compressible":false},"multipart/header-set":{"source":"iana"},"multipart/mixed":{"source":"iana"},"multipart/multilingual":{"source":"iana"},"multipart/parallel":{"source":"iana"},"multipart/related":{"source":"iana","compressible":false},"multipart/report":{"source":"iana"},"multipart/signed":{"source":"iana","compressible":false},"multipart/vnd.bint.med-plus":{"source":"iana"},"multipart/voice-message":{"source":"iana"},"multipart/x-mixed-replace":{"source":"iana"},"text/1d-interleaved-parityfec":{"source":"iana"},"text/cache-manifest":{"source":"iana","compressible":true,"extensions":["appcache","manifest"]},"text/calendar":{"source":"iana","extensions":["ics","ifb"]},"text/calender":{"compressible":true},"text/cmd":{"compressible":true},"text/coffeescript":{"extensions":["coffee","litcoffee"]},"text/cql":{"source":"iana"},"text/cql-expression":{"source":"iana"},"text/cql-identifier":{"source":"iana"},"text/css":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["css"]},"text/csv":{"source":"iana","compressible":true,"extensions":["csv"]},"text/csv-schema":{"source":"iana"},"text/directory":{"source":"iana"},"text/dns":{"source":"iana"},"text/ecmascript":{"source":"iana"},"text/encaprtp":{"source":"iana"},"text/enriched":{"source":"iana"},"text/fhirpath":{"source":"iana"},"text/flexfec":{"source":"iana"},"text/fwdred":{"source":"iana"},"text/gff3":{"source":"iana"},"text/grammar-ref-list":{"source":"iana"},"text/html":{"source":"iana","compressible":true,"extensions":["html","htm","shtml"]},"text/jade":{"extensions":["jade"]},"text/javascript":{"source":"iana","compressible":true},"text/jcr-cnd":{"source":"iana"},"text/jsx":{"compressible":true,"extensions":["jsx"]},"text/less":{"compressible":true,"extensions":["less"]},"text/markdown":{"source":"iana","compressible":true,"extensions":["markdown","md"]},"text/mathml":{"source":"nginx","extensions":["mml"]},"text/mdx":{"compressible":true,"extensions":["mdx"]},"text/mizar":{"source":"iana"},"text/n3":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["n3"]},"text/parameters":{"source":"iana","charset":"UTF-8"},"text/parityfec":{"source":"iana"},"text/plain":{"source":"iana","compressible":true,"extensions":["txt","text","conf","def","list","log","in","ini"]},"text/provenance-notation":{"source":"iana","charset":"UTF-8"},"text/prs.fallenstein.rst":{"source":"iana"},"text/prs.lines.tag":{"source":"iana","extensions":["dsc"]},"text/prs.prop.logic":{"source":"iana"},"text/raptorfec":{"source":"iana"},"text/red":{"source":"iana"},"text/rfc822-headers":{"source":"iana"},"text/richtext":{"source":"iana","compressible":true,"extensions":["rtx"]},"text/rtf":{"source":"iana","compressible":true,"extensions":["rtf"]},"text/rtp-enc-aescm128":{"source":"iana"},"text/rtploopback":{"source":"iana"},"text/rtx":{"source":"iana"},"text/sgml":{"source":"iana","extensions":["sgml","sgm"]},"text/shaclc":{"source":"iana"},"text/shex":{"source":"iana","extensions":["shex"]},"text/slim":{"extensions":["slim","slm"]},"text/spdx":{"source":"iana","extensions":["spdx"]},"text/strings":{"source":"iana"},"text/stylus":{"extensions":["stylus","styl"]},"text/t140":{"source":"iana"},"text/tab-separated-values":{"source":"iana","compressible":true,"extensions":["tsv"]},"text/troff":{"source":"iana","extensions":["t","tr","roff","man","me","ms"]},"text/turtle":{"source":"iana","charset":"UTF-8","extensions":["ttl"]},"text/ulpfec":{"source":"iana"},"text/uri-list":{"source":"iana","compressible":true,"extensions":["uri","uris","urls"]},"text/vcard":{"source":"iana","compressible":true,"extensions":["vcard"]},"text/vnd.a":{"source":"iana"},"text/vnd.abc":{"source":"iana"},"text/vnd.ascii-art":{"source":"iana"},"text/vnd.curl":{"source":"iana","extensions":["curl"]},"text/vnd.curl.dcurl":{"source":"apache","extensions":["dcurl"]},"text/vnd.curl.mcurl":{"source":"apache","extensions":["mcurl"]},"text/vnd.curl.scurl":{"source":"apache","extensions":["scurl"]},"text/vnd.debian.copyright":{"source":"iana","charset":"UTF-8"},"text/vnd.dmclientscript":{"source":"iana"},"text/vnd.dvb.subtitle":{"source":"iana","extensions":["sub"]},"text/vnd.esmertec.theme-descriptor":{"source":"iana","charset":"UTF-8"},"text/vnd.familysearch.gedcom":{"source":"iana","extensions":["ged"]},"text/vnd.ficlab.flt":{"source":"iana"},"text/vnd.fly":{"source":"iana","extensions":["fly"]},"text/vnd.fmi.flexstor":{"source":"iana","extensions":["flx"]},"text/vnd.gml":{"source":"iana"},"text/vnd.graphviz":{"source":"iana","extensions":["gv"]},"text/vnd.hans":{"source":"iana"},"text/vnd.hgl":{"source":"iana"},"text/vnd.in3d.3dml":{"source":"iana","extensions":["3dml"]},"text/vnd.in3d.spot":{"source":"iana","extensions":["spot"]},"text/vnd.iptc.newsml":{"source":"iana"},"text/vnd.iptc.nitf":{"source":"iana"},"text/vnd.latex-z":{"source":"iana"},"text/vnd.motorola.reflex":{"source":"iana"},"text/vnd.ms-mediapackage":{"source":"iana"},"text/vnd.net2phone.commcenter.command":{"source":"iana"},"text/vnd.radisys.msml-basic-layout":{"source":"iana"},"text/vnd.senx.warpscript":{"source":"iana"},"text/vnd.si.uricatalogue":{"source":"iana"},"text/vnd.sosi":{"source":"iana"},"text/vnd.sun.j2me.app-descriptor":{"source":"iana","charset":"UTF-8","extensions":["jad"]},"text/vnd.trolltech.linguist":{"source":"iana","charset":"UTF-8"},"text/vnd.wap.si":{"source":"iana"},"text/vnd.wap.sl":{"source":"iana"},"text/vnd.wap.wml":{"source":"iana","extensions":["wml"]},"text/vnd.wap.wmlscript":{"source":"iana","extensions":["wmls"]},"text/vtt":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["vtt"]},"text/x-asm":{"source":"apache","extensions":["s","asm"]},"text/x-c":{"source":"apache","extensions":["c","cc","cxx","cpp","h","hh","dic"]},"text/x-component":{"source":"nginx","extensions":["htc"]},"text/x-fortran":{"source":"apache","extensions":["f","for","f77","f90"]},"text/x-gwt-rpc":{"compressible":true},"text/x-handlebars-template":{"extensions":["hbs"]},"text/x-java-source":{"source":"apache","extensions":["java"]},"text/x-jquery-tmpl":{"compressible":true},"text/x-lua":{"extensions":["lua"]},"text/x-markdown":{"compressible":true,"extensions":["mkd"]},"text/x-nfo":{"source":"apache","extensions":["nfo"]},"text/x-opml":{"source":"apache","extensions":["opml"]},"text/x-org":{"compressible":true,"extensions":["org"]},"text/x-pascal":{"source":"apache","extensions":["p","pas"]},"text/x-processing":{"compressible":true,"extensions":["pde"]},"text/x-sass":{"extensions":["sass"]},"text/x-scss":{"extensions":["scss"]},"text/x-setext":{"source":"apache","extensions":["etx"]},"text/x-sfv":{"source":"apache","extensions":["sfv"]},"text/x-suse-ymp":{"compressible":true,"extensions":["ymp"]},"text/x-uuencode":{"source":"apache","extensions":["uu"]},"text/x-vcalendar":{"source":"apache","extensions":["vcs"]},"text/x-vcard":{"source":"apache","extensions":["vcf"]},"text/xml":{"source":"iana","compressible":true,"extensions":["xml"]},"text/xml-external-parsed-entity":{"source":"iana"},"text/yaml":{"compressible":true,"extensions":["yaml","yml"]},"video/1d-interleaved-parityfec":{"source":"iana"},"video/3gpp":{"source":"iana","extensions":["3gp","3gpp"]},"video/3gpp-tt":{"source":"iana"},"video/3gpp2":{"source":"iana","extensions":["3g2"]},"video/av1":{"source":"iana"},"video/bmpeg":{"source":"iana"},"video/bt656":{"source":"iana"},"video/celb":{"source":"iana"},"video/dv":{"source":"iana"},"video/encaprtp":{"source":"iana"},"video/ffv1":{"source":"iana"},"video/flexfec":{"source":"iana"},"video/h261":{"source":"iana","extensions":["h261"]},"video/h263":{"source":"iana","extensions":["h263"]},"video/h263-1998":{"source":"iana"},"video/h263-2000":{"source":"iana"},"video/h264":{"source":"iana","extensions":["h264"]},"video/h264-rcdo":{"source":"iana"},"video/h264-svc":{"source":"iana"},"video/h265":{"source":"iana"},"video/iso.segment":{"source":"iana","extensions":["m4s"]},"video/jpeg":{"source":"iana","extensions":["jpgv"]},"video/jpeg2000":{"source":"iana"},"video/jpm":{"source":"apache","extensions":["jpm","jpgm"]},"video/jxsv":{"source":"iana"},"video/mj2":{"source":"iana","extensions":["mj2","mjp2"]},"video/mp1s":{"source":"iana"},"video/mp2p":{"source":"iana"},"video/mp2t":{"source":"iana","extensions":["ts"]},"video/mp4":{"source":"iana","compressible":false,"extensions":["mp4","mp4v","mpg4"]},"video/mp4v-es":{"source":"iana"},"video/mpeg":{"source":"iana","compressible":false,"extensions":["mpeg","mpg","mpe","m1v","m2v"]},"video/mpeg4-generic":{"source":"iana"},"video/mpv":{"source":"iana"},"video/nv":{"source":"iana"},"video/ogg":{"source":"iana","compressible":false,"extensions":["ogv"]},"video/parityfec":{"source":"iana"},"video/pointer":{"source":"iana"},"video/quicktime":{"source":"iana","compressible":false,"extensions":["qt","mov"]},"video/raptorfec":{"source":"iana"},"video/raw":{"source":"iana"},"video/rtp-enc-aescm128":{"source":"iana"},"video/rtploopback":{"source":"iana"},"video/rtx":{"source":"iana"},"video/scip":{"source":"iana"},"video/smpte291":{"source":"iana"},"video/smpte292m":{"source":"iana"},"video/ulpfec":{"source":"iana"},"video/vc1":{"source":"iana"},"video/vc2":{"source":"iana"},"video/vnd.cctv":{"source":"iana"},"video/vnd.dece.hd":{"source":"iana","extensions":["uvh","uvvh"]},"video/vnd.dece.mobile":{"source":"iana","extensions":["uvm","uvvm"]},"video/vnd.dece.mp4":{"source":"iana"},"video/vnd.dece.pd":{"source":"iana","extensions":["uvp","uvvp"]},"video/vnd.dece.sd":{"source":"iana","extensions":["uvs","uvvs"]},"video/vnd.dece.video":{"source":"iana","extensions":["uvv","uvvv"]},"video/vnd.directv.mpeg":{"source":"iana"},"video/vnd.directv.mpeg-tts":{"source":"iana"},"video/vnd.dlna.mpeg-tts":{"source":"iana"},"video/vnd.dvb.file":{"source":"iana","extensions":["dvb"]},"video/vnd.fvt":{"source":"iana","extensions":["fvt"]},"video/vnd.hns.video":{"source":"iana"},"video/vnd.iptvforum.1dparityfec-1010":{"source":"iana"},"video/vnd.iptvforum.1dparityfec-2005":{"source":"iana"},"video/vnd.iptvforum.2dparityfec-1010":{"source":"iana"},"video/vnd.iptvforum.2dparityfec-2005":{"source":"iana"},"video/vnd.iptvforum.ttsavc":{"source":"iana"},"video/vnd.iptvforum.ttsmpeg2":{"source":"iana"},"video/vnd.motorola.video":{"source":"iana"},"video/vnd.motorola.videop":{"source":"iana"},"video/vnd.mpegurl":{"source":"iana","extensions":["mxu","m4u"]},"video/vnd.ms-playready.media.pyv":{"source":"iana","extensions":["pyv"]},"video/vnd.nokia.interleaved-multimedia":{"source":"iana"},"video/vnd.nokia.mp4vr":{"source":"iana"},"video/vnd.nokia.videovoip":{"source":"iana"},"video/vnd.objectvideo":{"source":"iana"},"video/vnd.radgamettools.bink":{"source":"iana"},"video/vnd.radgamettools.smacker":{"source":"iana"},"video/vnd.sealed.mpeg1":{"source":"iana"},"video/vnd.sealed.mpeg4":{"source":"iana"},"video/vnd.sealed.swf":{"source":"iana"},"video/vnd.sealedmedia.softseal.mov":{"source":"iana"},"video/vnd.uvvu.mp4":{"source":"iana","extensions":["uvu","uvvu"]},"video/vnd.vivo":{"source":"iana","extensions":["viv"]},"video/vnd.youtube.yt":{"source":"iana"},"video/vp8":{"source":"iana"},"video/vp9":{"source":"iana"},"video/webm":{"source":"apache","compressible":false,"extensions":["webm"]},"video/x-f4v":{"source":"apache","extensions":["f4v"]},"video/x-fli":{"source":"apache","extensions":["fli"]},"video/x-flv":{"source":"apache","compressible":false,"extensions":["flv"]},"video/x-m4v":{"source":"apache","extensions":["m4v"]},"video/x-matroska":{"source":"apache","compressible":false,"extensions":["mkv","mk3d","mks"]},"video/x-mng":{"source":"apache","extensions":["mng"]},"video/x-ms-asf":{"source":"apache","extensions":["asf","asx"]},"video/x-ms-vob":{"source":"apache","extensions":["vob"]},"video/x-ms-wm":{"source":"apache","extensions":["wm"]},"video/x-ms-wmv":{"source":"apache","compressible":false,"extensions":["wmv"]},"video/x-ms-wmx":{"source":"apache","extensions":["wmx"]},"video/x-ms-wvx":{"source":"apache","extensions":["wvx"]},"video/x-msvideo":{"source":"apache","extensions":["avi"]},"video/x-sgi-movie":{"source":"apache","extensions":["movie"]},"video/x-smv":{"source":"apache","extensions":["smv"]},"x-conference/x-cooltalk":{"source":"apache","extensions":["ice"]},"x-shader/x-fragment":{"compressible":true},"x-shader/x-vertex":{"compressible":true}}')}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),n=t.X(0,[638,70,241],()=>a(77826));module.exports=n})();