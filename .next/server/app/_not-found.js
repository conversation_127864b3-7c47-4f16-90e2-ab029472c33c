(()=>{var e={};e.id=165,e.ids=[165],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},72254:e=>{"use strict";e.exports=require("node:buffer")},6005:e=>{"use strict";e.exports=require("node:crypto")},87561:e=>{"use strict";e.exports=require("node:fs")},88849:e=>{"use strict";e.exports=require("node:http")},22286:e=>{"use strict";e.exports=require("node:https")},87503:e=>{"use strict";e.exports=require("node:net")},49411:e=>{"use strict";e.exports=require("node:path")},97742:e=>{"use strict";e.exports=require("node:process")},84492:e=>{"use strict";e.exports=require("node:stream")},72477:e=>{"use strict";e.exports=require("node:stream/web")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},65628:e=>{"use strict";e.exports=require("node:zlib")},77282:e=>{"use strict";e.exports=require("process")},71267:e=>{"use strict";e.exports=require("worker_threads")},83635:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>c,pages:()=>d,routeModule:()=>x,tree:()=>l});var s=t(50482),o=t(69108),n=t(62563),i=t.n(n),a=t(68300),u={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>a[e]);t.d(r,u);let l=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,21342)),"/Users/<USER>/Downloads/Coding/IBC/Group Creator/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],d=[],c="/_not-found",p={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/_not-found",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},59579:(e,r,t)=>{Promise.resolve().then(t.bind(t,1024)),Promise.resolve().then(t.bind(t,65102)),Promise.resolve().then(t.bind(t,22315)),Promise.resolve().then(t.bind(t,34910)),Promise.resolve().then(t.bind(t,34755))},70786:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,2583,23)),Promise.resolve().then(t.t.bind(t,26840,23)),Promise.resolve().then(t.t.bind(t,38771,23)),Promise.resolve().then(t.t.bind(t,13225,23)),Promise.resolve().then(t.t.bind(t,9295,23)),Promise.resolve().then(t.t.bind(t,43982,23))},21342:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d,metadata:()=>l});var s=t(25036),o=t(42195),n=t.n(o),i=t(14677),a=t(91296);t(5023);var u=t(27171);let l={title:"Telegram Group Creator",description:"Create Telegram groups easily"};function d({children:e}){return s.jsx(i.El,{children:s.jsx("html",{lang:"en",children:(0,s.jsxs)("body",{className:n().className,children:[s.jsx(i.CH,{children:s.jsx("header",{className:"border-b",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 py-3 flex items-center justify-between",children:[s.jsx("h1",{className:"text-xl font-semibold",children:"Telegram Group Creator"}),s.jsx(a.l8,{afterSignOutUrl:"/",appearance:{elements:{avatarBox:"w-10 h-10"}}})]})})}),e,s.jsx(u.x,{})]})})})}},5023:()=>{}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,70,241],()=>t(83635));module.exports=s})();