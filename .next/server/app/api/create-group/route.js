"use strict";(()=>{var e={};e.id=560,e.ids=[560],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},10367:e=>{e.exports=require("websocket")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},22057:e=>{e.exports=require("constants")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},41808:e=>{e.exports=require("net")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},12781:e=>{e.exports=require("stream")},73837:e=>{e.exports=require("util")},78844:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>_,originalPathname:()=>T,patchFetch:()=>C,requestAsyncStorage:()=>A,routeModule:()=>y,serverHooks:()=>b,staticGenerationAsyncStorage:()=>I,staticGenerationBailout:()=>v});var o={};t.r(o),t.d(o,{POST:()=>S});var n=t(95419),a=t(69108),s=t(99678),i=t(78070),l=t(43588),c=t(47218),u=t(88302);let m=u.z.string().min(2,"Username must be at least 2 characters").max(32,"Username must be at most 32 characters").regex(/^@[a-zA-Z0-9_]+$/,"Username must start with @ and contain only letters, numbers, and underscores"),d=u.z.string().min(1,"Project name is required").max(100,"Project name must be at most 100 characters").trim(),g=u.z.string().url("Must be a valid URL").regex(/^https:\/\/(calendly\.com|cal\.com)/,"Must be a Calendly or Cal.com link").optional().or(u.z.literal("")),p=u.z.string().email("Must be a valid email address").optional(),h=(u.z.object({projectName:d,projectLeads:u.z.array(m).min(1,"At least one project lead is required").max(5,"Maximum 5 project leads allowed"),outreachMemberName:u.z.string().min(1,"Outreach member is required").optional(),outreachMemberUsername:m.optional(),customOutreachUsername:m.optional(),useCustomOutreachUsername:u.z.boolean(),salesperson:u.z.string().optional(),customSalesRepUsername:m.optional(),useCustomSalesRepUsername:u.z.boolean(),enterSalesRepManually:u.z.boolean(),customCalendlyLink:g,includeCalendly:u.z.boolean()}).refine(e=>(!e.useCustomOutreachUsername||!!e.customOutreachUsername)&&(!!e.useCustomOutreachUsername||!!e.outreachMemberName&&!!e.outreachMemberUsername)&&(!e.enterSalesRepManually||!!e.customSalesRepUsername)&&(!!e.enterSalesRepManually||!!e.salesperson)&&(!e.useCustomSalesRepUsername||!!e.customSalesRepUsername),{message:"Please fill in all required fields correctly"}),u.z.object({projectName:d,projectLeads:u.z.array(m).min(1).max(5),outreachTeamMember:m,outreachMemberEmail:p,salesperson:u.z.string().min(1,"Salesperson is required"),salesRepUsername:m,paUsername:m.optional(),calendlyLink:g,includeCalendly:u.z.boolean(),welcomeMessage:u.z.string().min(1,"Welcome message is required"),inviteSalesRep:u.z.boolean(),salesRepEmoji:u.z.string().optional(),salesRepPAUsername:m.optional(),salesRepCalendlyLink:g,salesRepCategory:u.z.string().optional(),outreachMemberUsernames:u.z.array(m).optional(),outreachMemberEmoji:u.z.string().optional()}));function E(e){if(!e)return null;let r=e.trim();return"none"===r.toLowerCase()?null:r.startsWith("@")?r:"@"+r}u.z.object({clientEmail:u.z.string().email("Invalid client email"),privateKey:u.z.string().min(1,"Private key is required"),spreadsheetId:u.z.string().min(1,"Spreadsheet ID is required")});var f=t(75315);async function w(e){let r;console.log("\uD83D\uDE80 Starting group creation process...");let t=await e.json();console.log("\uD83D\uDCDD Raw request received:",{projectName:t.projectName,salesRepUsername:t.salesRepUsername,outreachMemberUsernames:t.outreachMemberUsernames});try{r=h.parse(t)}catch(e){throw console.error("❌ Request validation failed:",e.errors),new f.p8(`Invalid request data: ${e.errors?.[0]?.message||"Validation failed"}`)}let o=await (0,f.JN)(()=>(0,c.Bu)(),2,1e3),{projectName:n,projectLeads:a,salesRepUsername:s,salesRepEmoji:u="",salesRepPAUsername:m,salesRepCalendlyLink:d,outreachMemberUsernames:g=[],outreachMemberEmoji:p="",includeCalendly:w=!1,welcomeMessage:S}=r,y=function(e,r){if(!e)return"";let t=r?.emoji||"";return`${t} ${e} <> IBC Group`}(n,{emoji:u});console.log(`📝 Creating group: ${y}`);try{console.log(`📝 Creating supergroup: ${y}`);let e=await o.invoke(new l.Api.channels.CreateChannel({title:y,about:"IBC Group Discussion",megagroup:!0}));if(!e.chats||0===e.chats.length)throw new f.jH("Failed to create the group - no chat returned");let r=e.chats[0];console.log(`✅ Group created with ID: ${r.id}`);let t=E(s),n=E(m||""),c=g.map(e=>E(e)).filter(Boolean),u=a.map(e=>E(e)).filter(Boolean);u[0],console.log("\uD83D\uDC65 Formatted usernames:",{salesRep:t,salesRepPA:n,outreachMembers:c,projectLeads:u});let p=["@IbcAdmin_Bot",t,n,...c].filter(Boolean);console.log("\uD83D\uDC65 Adding members to group...");let h=[];for(let e of p)try{console.log(`👤 Attempting to add admin member: ${e}`);let t=await o.getEntity(e);await (0,f.JN)(async()=>{await o.invoke(new l.Api.channels.InviteToChannel({channel:r,users:[t]}))},2,500),console.log(`✅ Successfully added member: ${e}`);try{await o.invoke(new l.Api.channels.EditAdmin({channel:r,userId:t,adminRights:new l.Api.ChatAdminRights({changeInfo:!0,postMessages:!0,editMessages:!0,deleteMessages:!0,banUsers:!0,inviteUsers:!0,pinMessages:!0,addAdmins:!1,anonymous:!1,manageCall:!0,other:!0}),rank:"@IbcAdmin_Bot"===e?"Bot Admin":c.includes(e)?"Outreach Admin":e===n?"PA Admin":"Admin"})),console.log(`👑 Successfully made ${e} an admin`)}catch(r){console.error(`⚠️ Error making ${e} an admin:`,r.message)}}catch(t){console.error(`❌ Error adding member ${e}:`,t.message);let r=f.jH.fromTelegramApiError(t);h.push({username:e,reason:r.message});continue}for(let e of u)try{console.log(`👤 Attempting to add regular member: ${e}`);let t=await o.getEntity(e);await (0,f.JN)(async()=>{await o.invoke(new l.Api.channels.InviteToChannel({channel:r,users:[t]}))},2,500),console.log(`✅ Successfully added regular member: ${e}`)}catch(t){console.error(`❌ Error adding regular member ${e}:`,t.message);let r=f.jH.fromTelegramApiError(t);h.push({username:e,reason:r.message});continue}try{console.log("\uD83D\uDCE8 Sending welcome message..."),await o.sendMessage(r,{message:S}),console.log("✅ Welcome message sent successfully");let e=`IBC VENTURES:

Led by Mario Nawfal, founder of IBC (established in 2017, latest valuation at $400m), as well as industry veterans, and over 200 employees, we are proud to host the largest shows on X and actively invest, incubate, and accelerate Web3 and AI projects, boasting a venture portfolio of over 300 companies, including multiple unicorns.

Our incubator has leaped to over 20 incubatees in less than 5 months, with already 3 INCUBATED unicorns under our belt.

🏛 X Shows
With 2-4 billion monthly impressions and millions of weekly listeners across all formats, we consistently rank in the global top 10 accounts on ALL social media platforms (several times reaching #4). This includes TikTok, Youtube, Instagram and X.

Our shows have featured iconic guests, including:
Elon, RFK, Hunter Biden, Mark Cuban, CZ, Prince Alwaleed, Marc Andreessen, Bill Ackman, Vivek, David Sacks, and many world leaders and politicians.

🏛 Our Growth
🟢 Marketed, launched, and accelerated over 300 projects to date, with nearly 200 in 2024 alone.
🟢 Expanded our incubator to include over 20 startups in less than six months, including three unicorns.
🟢 Collaborated with over 50 memecoin projects, with our latest being the top-performing memecoin at the end of 2024.

🏛 AI Innovation
At IBC, our obsession with AI is driving new ventures. Having worked with the best in the industry, we are:
✅ Launching our own AI initiatives, in partnerships with the biggest project in web3 crypto.
✅ Introducing a new AI video show next month featuring the biggest names in the industry. This is on top of our existing DAILY web3 AI show.
✅Actively seeking innovative AI projects to incubate, invest in, and expose to the masses.

🏛 Media Assets
As one of the largest media companies on X, we leverage multiple assets, including:
🚀Mario Nawfal
🚀The Crypto Town Hall (A daily show with Mario Nawfal, Scott Melker , and Ran aka CryptoBanter)
🚀The Roundtable Show
and are actively acquiring more media assets.

🏛 Web3 Consulting, Advisory, and Marketing
Since 2017, we've been at the forefront of Web3 marketing, delivering proven results for Binance-launched projects, established protocols, and leading memecoins. Our Go-To-Market expertise is unmatched.This includes Go To Market, KOL marketing, fundraising, market making, listing, and community building. We're confident enough to now say we are the best in the space, or at least one of the best, at what we do.

🏛 IBC Group Accelerator
The ONLY media-led accelerator, launchpad, and spot exchange in the world. We provide startups with a clear path from ideation to token launch, leveraging one of the most experienced teams in Web3.

🏛 Memecoin/AI agent Incubation

We've worked with some of the biggest memecoins in the space, including 2 top 10 memes

Some of the memes that we can share are Maneki, Ben the Dog, Doger, Brett on Base, Maga ($700m), Shiba Saga, Biao, Luigi Mangioni, Occupy Mars, Dragon, and many more to list

And many other AI agents including Gekko, DegentAI, Holozone, Chai, and a few large ones we cannot share

We not only have the most reach, but work with some of the largest alpha groups to generate buying power for projects, and have a close relationship with each and every major exchange.

For more information, message us or visit our website:
👉🏻 https://www.ibcgroup.io/`;if(await o.sendMessage(r,{message:e}),w&&d){let e=`We'd be glad to connect for a brief introductory call to discuss things further and explore potential opportunities for collaboration at your convenience.

Please let me know if this timing works for you to schedule a call with our Partnership Executive at IBC:
${d}`;await o.sendMessage(r,{message:e}),console.log("\uD83D\uDCC5 Calendly message sent successfully")}console.log("✅ IBC Ventures info message sent successfully")}catch(e){console.error("❌ Error sending messages:",e.message)}try{await o.sendMessage(r,{message:"/optin"}),console.log("\uD83E\uDD16 Optin command sent")}catch(e){console.error("❌ Error sending optin command:",e.message)}if(n)try{console.log(`👑 Setting up PA admin: ${n}`);let e=await o.getEntity(n);e&&(await o.sendMessage(r,{message:`/setadmin ${e.id}`}),console.log(`✅ Setadmin command sent for PA: ${n}`))}catch(r){console.error(`❌ Error executing setadmin command for PA: ${r.message}`);let e=f.jH.fromTelegramApiError(r);h.push({username:n,reason:e.message})}if(t)try{console.log(`📨 Inviting sales rep: ${t}`);let e=await o.getEntity(t);e&&(await o.sendMessage(r,{message:`/invite_new_group ${e.id}`}),console.log(`✅ Invite_new_group command sent for Sales Rep: ${t}`))}catch(r){console.error(`❌ Error executing invite_new_group command for Sales Rep: ${r.message}`);let e=f.jH.fromTelegramApiError(r);h.push({username:t,reason:e.message})}return console.log("\uD83C\uDF89 Group creation completed successfully!"),i.Z.json({success:!0,group_id:String(r.id),group_name:y,message:"Group created successfully",added_members:{admins:p,regular:u},failed_invites:h})}catch(e){throw f.jH.fromTelegramApiError(e)}}let S=(0,f.bi)(async e=>{try{return await w(e)}catch(r){console.error("❌ Error in group creation:",r);let e=(0,f.jl)(r,{operation:"create_telegram_group",timestamp:new Date().toISOString()});return i.Z.json(e,{status:r.statusCode||500})}}),y=new n.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/create-group/route",pathname:"/api/create-group",filename:"route",bundlePath:"app/api/create-group/route"},resolvedPagePath:"/Users/<USER>/Downloads/Coding/IBC/Group Creator/src/app/api/create-group/route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:A,staticGenerationAsyncStorage:I,serverHooks:b,headerHooks:_,staticGenerationBailout:v}=y,T="/api/create-group/route";function C(){return(0,s.patchFetch)({serverHooks:b,staticGenerationAsyncStorage:I})}},48350:(e,r,t)=>{t.d(r,{a6:()=>c,jw:()=>l,k5:()=>i});var o=t(88302);let n=o.z.object({NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY:o.z.string().min(1,"Clerk publishable key is required"),CLERK_SECRET_KEY:o.z.string().min(1,"Clerk secret key is required"),TELEGRAM_API_ID:o.z.string().min(1,"Telegram API ID is required").transform(Number),TELEGRAM_API_HASH:o.z.string().min(1,"Telegram API Hash is required"),TELEGRAM_SESSION_STRING:o.z.string().min(1,"Telegram session string is required"),GOOGLE_SHEETS_CLIENT_EMAIL:o.z.string().email("Invalid Google Sheets client email").optional(),GOOGLE_SHEETS_PRIVATE_KEY:o.z.string().optional(),GOOGLE_SHEETS_SPREADSHEET_ID:o.z.string().optional(),NODE_ENV:o.z.enum(["development","production","test"]).default("development")}),a=o.z.object({NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY:o.z.string().min(1,"Clerk publishable key is required")}),s=function(){try{let e=n.parse(process.env);return console.log("✅ Environment variables validated successfully"),e}catch(e){throw e instanceof o.z.ZodError&&(console.error("❌ Environment validation failed:"),e.errors.forEach(e=>{console.error(`  - ${e.path.join(".")}: ${e.message}`)}),console.error("\n\uD83D\uDCDD Required environment variables:"),console.error("  - NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY"),console.error("  - CLERK_SECRET_KEY"),console.error("  - TELEGRAM_API_ID"),console.error("  - TELEGRAM_API_HASH"),console.error("  - TELEGRAM_SESSION_STRING"),console.error("\n\uD83D\uDCDD Optional environment variables:"),console.error("  - GOOGLE_SHEETS_CLIENT_EMAIL"),console.error("  - GOOGLE_SHEETS_PRIVATE_KEY"),console.error("  - GOOGLE_SHEETS_SPREADSHEET_ID"),process.exit(1)),e}}();(function(){try{a.parse({NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY:"pk_test_aGFybWxlc3Mtam9leS0zMi5jbGVyay5hY2NvdW50cy5kZXYk"})}catch(e){throw e instanceof o.z.ZodError&&(console.error("❌ Client environment validation failed:"),e.errors.forEach(e=>{console.error(`  - ${e.path.join(".")}: ${e.message}`)})),e}})(),s.NODE_ENV,s.NODE_ENV;let i=!!(s.GOOGLE_SHEETS_CLIENT_EMAIL&&s.GOOGLE_SHEETS_PRIVATE_KEY&&s.GOOGLE_SHEETS_SPREADSHEET_ID),l=()=>({apiId:s.TELEGRAM_API_ID,apiHash:s.TELEGRAM_API_HASH,sessionString:s.TELEGRAM_SESSION_STRING}),c=()=>{if(!i)throw Error("Google Sheets is not configured. Please set the required environment variables.");return{clientEmail:s.GOOGLE_SHEETS_CLIENT_EMAIL,privateKey:s.GOOGLE_SHEETS_PRIVATE_KEY,spreadsheetId:s.GOOGLE_SHEETS_SPREADSHEET_ID}}},75315:(e,r,t)=>{t.d(r,{JN:()=>d,bi:()=>m,jH:()=>o,jl:()=>u,jq:()=>n,p8:()=>a});class o extends Error{constructor(e,r,t=500){super(e),this.name="TelegramError",this.code=r,this.statusCode=t}static fromTelegramApiError(e){let r=e.message||"Unknown Telegram API error";return r.toLowerCase().includes("flood")?new o("Too many requests. Please wait before trying again.","FLOOD_WAIT",429):r.toLowerCase().includes("not enough rights")?new o("Insufficient permissions. Please check bot admin rights.","INSUFFICIENT_RIGHTS",403):r.toLowerCase().includes("session")?new o("Telegram session expired. Please regenerate session string.","SESSION_EXPIRED",401):r.toLowerCase().includes("user not found")?new o("User not found. Please check the username.","USER_NOT_FOUND",404):r.toLowerCase().includes("chat not found")?new o("Chat not found or bot was removed from chat.","CHAT_NOT_FOUND",404):new o(r,"UNKNOWN_ERROR",500)}}class n extends Error{constructor(e,r,t=500){super(e),this.name="GoogleSheetsError",this.sheetName=r,this.statusCode=t}static fromGoogleApiError(e,r){let t=e.message||"Unknown Google Sheets API error";return 403===e.code?new n("Access denied to Google Sheets. Please check service account permissions.",r,403):404===e.code?new n(`Spreadsheet or sheet "${r}" not found. Please check the spreadsheet ID and sheet name.`,r,404):400===e.code?new n(`Invalid request to Google Sheets API: ${t}`,r,400):new n(`Google Sheets API error: ${t}`,r,500)}}class a extends Error{constructor(e,r){super(e),this.statusCode=400,this.name="ValidationError",this.field=r}}class s extends Error{constructor(e="Authentication required"){super(e),this.statusCode=401,this.name="AuthenticationError"}}class i extends Error{constructor(e="Insufficient permissions"){super(e),this.statusCode=403,this.name="AuthorizationError"}}class l extends Error{constructor(e="Rate limit exceeded",r){super(e),this.statusCode=429,this.name="RateLimitError",this.retryAfter=r}}function c(e){return e instanceof o||e instanceof n||e instanceof a||e instanceof s||e instanceof i||e instanceof l}function u(e,r){return function(e,r){let t={name:e.name,message:e.message,stack:e.stack,context:r,timestamp:new Date().toISOString()};c(e)?console.warn("⚠️ Operational error:",t):console.error("❌ Unexpected error:",t)}(e,r),{success:!1,error:c(e)?e.message:"An unexpected error occurred. Please try again later.",code:"code"in e?e.code:void 0,timestamp:new Date().toISOString()}}function m(e){return async(...r)=>{try{return await e(...r)}catch(e){if(e instanceof Error)throw e;throw Error(String(e))}}}async function d(e,r=3,t=1e3){let o;for(let n=0;n<=r;n++)try{return await e()}catch(l){if((o=l instanceof Error?l:Error(String(l)))instanceof a||o instanceof s||o instanceof i)throw o;if(n===r)break;let e=t*Math.pow(2,n);console.log(`⏳ Retrying in ${e}ms (attempt ${n+1}/${r+1})`),await new Promise(r=>setTimeout(r,e))}throw o}},47218:(e,r,t)=>{t.d(r,{Bu:()=>m});var o=t(43588),n=t(9123),a=t(48350);let s=null,i=null,l=Date.now(),c=null;function u(){l=Date.now(),c&&clearTimeout(c),c=setTimeout(async()=>{Date.now()-l>=3e5&&s&&(console.log("\uD83E\uDDF9 Auto-disconnecting inactive Telegram client"),await g())},3e5)}async function m(){if(i)return console.log("⏳ Waiting for existing connection..."),await i;if(s&&s.connected)return u(),s;i=d();try{let e=await i;return u(),e}finally{i=null}}async function d(){try{let{apiId:e,apiHash:r,sessionString:t}=(0,a.jw)(),i=new n.StringSession(t);console.log("\uD83D\uDD17 Initializing Telegram client...");let l=new o.TelegramClient(i,e,r,{connectionRetries:5,timeout:3e4,autoReconnect:!0});if(await l.connect(),!l.connected)throw Error("Failed to connect to Telegram - client not connected");let c=await l.getMe();return console.log(`✅ Connected to Telegram as: ${c.firstName} ${c.lastName||""} (@${c.username||"no username"})`),l.addEventHandler(e=>{"UpdateConnectionState"===e.className&&-1===e.state&&(console.log("\uD83D\uDD0C Telegram client disconnected"),s=null)}),s=l,l}catch(e){if(console.error("❌ Failed to initialize Telegram client:",e.message),s){try{await s.disconnect()}catch(e){console.error("Error disconnecting failed client:",e)}s=null}throw Error(`Telegram client initialization failed: ${e.message}`)}}async function g(){if(c&&(clearTimeout(c),c=null),i=null,s)try{s.connected&&(await s.disconnect(),console.log("\uD83D\uDD0C Telegram client disconnected"))}catch(e){console.error("❌ Error disconnecting Telegram client:",e.message)}finally{s=null}}async function p(){console.log("\uD83D\uDED1 Shutting down Telegram client..."),await g()}"undefined"!=typeof process&&(process.on("SIGINT",p),process.on("SIGTERM",p),process.on("beforeExit",p))}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[638,70,19,475],()=>t(78844));module.exports=o})();