"use strict";(()=>{var e={};e.id=983,e.ids=[983],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6113:e=>{e.exports=require("crypto")},85611:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>f,originalPathname:()=>m,patchFetch:()=>x,requestAsyncStorage:()=>d,routeModule:()=>p,serverHooks:()=>l,staticGenerationAsyncStorage:()=>h,staticGenerationBailout:()=>k});var o={};r.r(o),r.d(o,{GET:()=>c});var s=r(95419),a=r(69108),n=r(99678),i=r(78070),u=r(6113);let c=async e=>{let t=(0,u.randomBytes)(32).toString("hex"),r=i.Z.json({success:!0,token:t});return function(e,t){e.cookies.set("csrf-token",(0,u.createHash)("sha256").update(t).digest("hex"),{httpOnly:!0,secure:!0,sameSite:"strict",maxAge:86400,path:"/"})}(r,t),r},p=new s.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/csrf-token/route",pathname:"/api/csrf-token",filename:"route",bundlePath:"app/api/csrf-token/route"},resolvedPagePath:"/Users/<USER>/Downloads/Coding/IBC/Group Creator/src/app/api/csrf-token/route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:d,staticGenerationAsyncStorage:h,serverHooks:l,headerHooks:f,staticGenerationBailout:k}=p,m="/api/csrf-token/route";function x(){return(0,n.patchFetch)({serverHooks:l,staticGenerationAsyncStorage:h})}},95419:(e,t,r)=>{e.exports=r(30517)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[638,70],()=>r(85611));module.exports=o})();