"use strict";(()=>{var e={};e.id=829,e.ids=[829],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},10367:e=>{e.exports=require("websocket")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},22057:e=>{e.exports=require("constants")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},41808:e=>{e.exports=require("net")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},12781:e=>{e.exports=require("stream")},73837:e=>{e.exports=require("util")},59495:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>S,originalPathname:()=>T,patchFetch:()=>g,requestAsyncStorage:()=>u,routeModule:()=>E,serverHooks:()=>d,staticGenerationAsyncStorage:()=>_,staticGenerationBailout:()=>p});var n={};t.r(n),t.d(n,{GET:()=>c});var o=t(95419),i=t(69108),a=t(99678),s=t(78070),l=t(47218);async function c(){try{let e=await (0,l.Bu)(),r=await e.getMe();return s.Z.json({status:"healthy",user_info:JSON.stringify(r)})}catch(e){return s.Z.json({error:e.message},{status:500})}}let E=new o.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/health/route",pathname:"/api/health",filename:"route",bundlePath:"app/api/health/route"},resolvedPagePath:"/Users/<USER>/Downloads/Coding/IBC/Group Creator/src/app/api/health/route.ts",nextConfigOutput:"",userland:n}),{requestAsyncStorage:u,staticGenerationAsyncStorage:_,serverHooks:d,headerHooks:S,staticGenerationBailout:p}=E,T="/api/health/route";function g(){return(0,a.patchFetch)({serverHooks:d,staticGenerationAsyncStorage:_})}},48350:(e,r,t)=>{t.d(r,{a6:()=>c,jw:()=>l,k5:()=>s});var n=t(88302);let o=n.z.object({NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY:n.z.string().min(1,"Clerk publishable key is required"),CLERK_SECRET_KEY:n.z.string().min(1,"Clerk secret key is required"),TELEGRAM_API_ID:n.z.string().min(1,"Telegram API ID is required").transform(Number),TELEGRAM_API_HASH:n.z.string().min(1,"Telegram API Hash is required"),TELEGRAM_SESSION_STRING:n.z.string().min(1,"Telegram session string is required"),GOOGLE_SHEETS_CLIENT_EMAIL:n.z.string().email("Invalid Google Sheets client email").optional(),GOOGLE_SHEETS_PRIVATE_KEY:n.z.string().optional(),GOOGLE_SHEETS_SPREADSHEET_ID:n.z.string().optional(),NODE_ENV:n.z.enum(["development","production","test"]).default("development")}),i=n.z.object({NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY:n.z.string().min(1,"Clerk publishable key is required")}),a=function(){try{let e=o.parse(process.env);return console.log("✅ Environment variables validated successfully"),e}catch(e){throw e instanceof n.z.ZodError&&(console.error("❌ Environment validation failed:"),e.errors.forEach(e=>{console.error(`  - ${e.path.join(".")}: ${e.message}`)}),console.error("\n\uD83D\uDCDD Required environment variables:"),console.error("  - NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY"),console.error("  - CLERK_SECRET_KEY"),console.error("  - TELEGRAM_API_ID"),console.error("  - TELEGRAM_API_HASH"),console.error("  - TELEGRAM_SESSION_STRING"),console.error("\n\uD83D\uDCDD Optional environment variables:"),console.error("  - GOOGLE_SHEETS_CLIENT_EMAIL"),console.error("  - GOOGLE_SHEETS_PRIVATE_KEY"),console.error("  - GOOGLE_SHEETS_SPREADSHEET_ID"),process.exit(1)),e}}();(function(){try{i.parse({NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY:"pk_test_aGFybWxlc3Mtam9leS0zMi5jbGVyay5hY2NvdW50cy5kZXYk"})}catch(e){throw e instanceof n.z.ZodError&&(console.error("❌ Client environment validation failed:"),e.errors.forEach(e=>{console.error(`  - ${e.path.join(".")}: ${e.message}`)})),e}})(),a.NODE_ENV,a.NODE_ENV;let s=!!(a.GOOGLE_SHEETS_CLIENT_EMAIL&&a.GOOGLE_SHEETS_PRIVATE_KEY&&a.GOOGLE_SHEETS_SPREADSHEET_ID),l=()=>({apiId:a.TELEGRAM_API_ID,apiHash:a.TELEGRAM_API_HASH,sessionString:a.TELEGRAM_SESSION_STRING}),c=()=>{if(!s)throw Error("Google Sheets is not configured. Please set the required environment variables.");return{clientEmail:a.GOOGLE_SHEETS_CLIENT_EMAIL,privateKey:a.GOOGLE_SHEETS_PRIVATE_KEY,spreadsheetId:a.GOOGLE_SHEETS_SPREADSHEET_ID}}},47218:(e,r,t)=>{t.d(r,{Bu:()=>u});var n=t(43588),o=t(9123),i=t(48350);let a=null,s=null,l=Date.now(),c=null;function E(){l=Date.now(),c&&clearTimeout(c),c=setTimeout(async()=>{Date.now()-l>=3e5&&a&&(console.log("\uD83E\uDDF9 Auto-disconnecting inactive Telegram client"),await d())},3e5)}async function u(){if(s)return console.log("⏳ Waiting for existing connection..."),await s;if(a&&a.connected)return E(),a;s=_();try{let e=await s;return E(),e}finally{s=null}}async function _(){try{let{apiId:e,apiHash:r,sessionString:t}=(0,i.jw)(),s=new o.StringSession(t);console.log("\uD83D\uDD17 Initializing Telegram client...");let l=new n.TelegramClient(s,e,r,{connectionRetries:5,timeout:3e4,autoReconnect:!0});if(await l.connect(),!l.connected)throw Error("Failed to connect to Telegram - client not connected");let c=await l.getMe();return console.log(`✅ Connected to Telegram as: ${c.firstName} ${c.lastName||""} (@${c.username||"no username"})`),l.addEventHandler(e=>{"UpdateConnectionState"===e.className&&-1===e.state&&(console.log("\uD83D\uDD0C Telegram client disconnected"),a=null)}),a=l,l}catch(e){if(console.error("❌ Failed to initialize Telegram client:",e.message),a){try{await a.disconnect()}catch(e){console.error("Error disconnecting failed client:",e)}a=null}throw Error(`Telegram client initialization failed: ${e.message}`)}}async function d(){if(c&&(clearTimeout(c),c=null),s=null,a)try{a.connected&&(await a.disconnect(),console.log("\uD83D\uDD0C Telegram client disconnected"))}catch(e){console.error("❌ Error disconnecting Telegram client:",e.message)}finally{a=null}}async function S(){console.log("\uD83D\uDED1 Shutting down Telegram client..."),await d()}"undefined"!=typeof process&&(process.on("SIGINT",S),process.on("SIGTERM",S),process.on("beforeExit",S))}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[638,70,19,475],()=>t(59495));module.exports=n})();