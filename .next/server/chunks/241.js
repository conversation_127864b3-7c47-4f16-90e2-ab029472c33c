exports.id=241,exports.ids=[241],exports.modules={42195:e=>{e.exports={style:{fontFamily:"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'",fontStyle:"normal"},className:"__className_e8ce0c"}},30290:(e,t,r)=>{"use strict";r.d(t,{vn:()=>ru,a7:()=>rt,qI:()=>rr,El:()=>tB,Gp:()=>t2,_L:()=>L,X1:()=>M,KM:()=>rc,Bg:()=>t3,A:()=>t1,Li:()=>t8,Cv:()=>rn,gM:()=>rl,yB:()=>rs,N1:()=>ri,C2:()=>ro,sO:()=>ra,cL:()=>tW,$d:()=>rb,qu:()=>rS,AM:()=>rw,Mo:()=>tG,gX:()=>rv,CH:()=>t9,tj:()=>re,l8:()=>tQ,Iw:()=>tX,_E:()=>tq,CJ:()=>rp,Gi:()=>rh,kD:()=>T,G1:()=>I,sZ:()=>R,V9:()=>N,ZC:()=>B,aC:()=>t7,ll:()=>rE,E2:()=>rO,jS:()=>rx,o8:()=>tm,eW:()=>tw,qi:()=>tS,kP:()=>rk,xo:()=>rC,zq:()=>rA,QS:()=>rP,aF:()=>r_,r0:()=>t$,NA:()=>rf,ns:()=>rd});var n,i,o,a,s,l,u,c,d,h={};r.r(h),r.d(h,{SWRConfig:()=>e3,default:()=>e4,mutate:()=>eq,preload:()=>eQ,unstable_serialize:()=>e1,useSWRConfig:()=>eX}),r(36324);var f=e=>"undefined"!=typeof atob&&"function"==typeof atob?atob(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e,"base64").toString():e,p="pk_live_";function g(e){if(!y(e=e||""))return null;let t=e.startsWith(p)?"production":"development",r=f(e.split("_")[2]);return r.endsWith("$")?{instanceType:t,frontendApi:r=r.slice(0,-1)}:null}function y(e){let t=(e=e||"").startsWith(p)||e.startsWith("pk_test_"),r=f(e.split("_")[2]||"").endsWith("$");return t&&r}var m=Object.defineProperty,b=Object.getOwnPropertyDescriptor,v=Object.getOwnPropertyNames,w=Object.prototype.hasOwnProperty,S=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let i of v(t))w.call(e,i)||i===r||m(e,i,{get:()=>t[i],enumerable:!(n=b(t,i))||n.enumerable});return e},_=r(3729),k=r.n(_),E=()=>!1,A=()=>!1,P=()=>{try{return!0}catch(e){}return!1},C=new Set,x=(e,t,r)=>{let n=A()||P(),i=r??e;C.has(i)||n||(C.add(i),console.warn(`Clerk - DEPRECATION WARNING: "${e}" is deprecated and will be removed in the next major release.
${t}`))},O=(e,t,r,n)=>{let i=e[t];Object.defineProperty(e,t,{get:()=>(x(t,r,n),i),set(e){i=e}})};function R(e){return T(e)||B(e)||"clerkRuntimeError"in e}function T(e){return"clerkError"in e}function B(e){return"code"in e&&[4001,32602,32603].includes(e.code)&&"message"in e}var j=class e extends Error{constructor(t){super(t),this.code=t,Object.setPrototypeOf(this,e.prototype),x("MagicLinkError","Use `EmailLinkError` instead.")}},U=class e extends Error{constructor(t){super(t),this.code=t,Object.setPrototypeOf(this,e.prototype)}};function N(e){return x("isMagicLinkError","Use `isEmailLinkError` instead."),e instanceof j}function I(e){return e instanceof U}var M=new Proxy({Expired:"expired",Failed:"failed"},{get:(e,t,r)=>(x("MagicLinkErrorCode","Use `EmailLinkErrorCode` instead."),Reflect.get(e,t,r))}),L={Expired:"expired",Failed:"failed"},K=Object.freeze({InvalidFrontendApiErrorMessage:"The frontendApi passed to Clerk is invalid. You can get your Frontend API key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})",InvalidProxyUrlErrorMessage:"The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})",InvalidPublishableKeyErrorMessage:"The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})",MissingPublishableKeyErrorMessage:"Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys."});let D="Clerk: Child of WithClerk must be a function.",z=e=>`Clerk: You've passed multiple children components to <${e}/>. You can only pass a single child component or text.`,H="Clerk: Unsupported usage of isSatellite, domain or proxyUrl. The usage of isSatellite, domain or proxyUrl as function is not supported in non-browser environments.",F=e=>`Clerk: <${e} /> can only accept <${e}.Page /> and <${e}.Link /> as its children. Any other provided component will be ignored.`,$=e=>`Clerk: Missing props. <${e}.Page /> component requires the following props: url, label, labelIcon, alongside with children to be rendered inside the page.`,q=e=>`Clerk: Missing props. <${e}.Link /> component requires the following props: url, label and labelIcon.`,V=function({packageName:e,customMessages:t}){let r=e,n={...K,...t};function i(e,t){if(!t)return`${r}: ${e}`;let n=e;for(let r of e.matchAll(/{{([a-zA-Z0-9-_]+)}}/g)){let e=(t[r[1]]||"").toString();n=n.replace(`{{${r[1]}}}`,e)}return`${r}: ${n}`}return{setPackageName({packageName:e}){return"string"==typeof e&&(r=e),this},setMessages({customMessages:e}){return Object.assign(n,e||{}),this},throwInvalidPublishableKeyError(e){throw Error(i(n.InvalidPublishableKeyErrorMessage,e))},throwInvalidFrontendApiError(e){throw Error(i(n.InvalidFrontendApiErrorMessage,e))},throwInvalidProxyUrl(e){throw Error(i(n.InvalidProxyUrlErrorMessage,e))},throwMissingPublishableKeyError(){throw Error(i(n.MissingPublishableKeyErrorMessage))}}}({packageName:"@clerk/react"}),W=new Map;var G=(e,t,r)=>{if(!t.has(e))throw TypeError("Cannot "+r)},J=(e,t,r)=>(G(e,t,"read from private field"),r?r.call(e):t.get(e)),Y=(e,t,r)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,r)},X=(e,t,r,n)=>(G(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r);async function Q(e="",t){let{async:r,defer:n,beforeLoad:i,crossOrigin:o}=t||{};return new Promise((t,a)=>{e||a("loadScript cannot be called without a src"),document&&document.body||a("loadScript cannot be called when document does not exist");let s=document.createElement("script");o&&s.setAttribute("crossorigin",o),s.async=r||!1,s.defer=n||!1,s.addEventListener("load",()=>{s.remove(),t(s)}),s.addEventListener("error",()=>{s.remove(),a()}),s.src=e,i?.(s),document.body.appendChild(s)})}function Z(e){return e.startsWith("/")}RegExp("bot|spider|crawl|APIs-Google|AdsBot|Googlebot|mediapartners|Google Favicon|FeedFetcher|Google-Read-Aloud|DuplexWeb-Google|googleweblight|bing|yandex|baidu|duckduck|yahoo|ecosia|ia_archiver|facebook|instagram|pinterest|reddit|slack|twitter|whatsapp|youtube|semrush","i");let{isDevOrStagingUrl:ee}=function(){let e=[".lcl.dev",".stg.dev",".lclstage.dev",".stgstage.dev",".dev.lclclerk.com",".stg.lclclerk.com",".accounts.lclclerk.com","accountsstage.dev","accounts.dev"],t=new Map;return{isDevOrStagingUrl:r=>{if(!r)return!1;let n="string"==typeof r?r:r.hostname,i=t.get(n);return void 0===i&&(i=e.some(e=>n.endsWith(e)),t.set(n,i)),i}}}(),et=e=>{if(e)return e;let t=er("4.30.5");return t?"snapshot"===t?"4.70.0":t:en("4.30.5")},er=e=>e.match(/-(.*)\./)?.[1],en=e=>e.split(".")[0],ei=e=>{let{frontendApi:t,publishableKey:r}=e;return r||t||V.throwMissingPublishableKeyError(),Q(eo(e),{async:!0,crossOrigin:"anonymous",beforeLoad:ea(e)}).catch(()=>{throw Error("Clerk: Failed to load Clerk")})},eo=e=>{let{clerkJSUrl:t,clerkJSVariant:r,clerkJSVersion:n,proxyUrl:i,domain:o,publishableKey:a,frontendApi:s}=e;if(t)return t;let l="";l=i&&function(e){return!e||/^http(s)?:\/\//.test(e||"")||Z(e)}(i)?(function(e){return e?Z(e)?new URL(e,window.location.origin).toString():e:""})(i).replace(/http(s)?:\/\//,""):o&&!ee(g(a)?.frontendApi||s||"")?function(e){let t;if(!e)return"";if(e.match(/^(clerk\.)+\w*$/))t=/(clerk\.)*(?=clerk\.)/;else{if(e.match(/\.clerk.accounts/))return e;t=/^(clerk\.)*/gi}let r=e.replace(t,"");return`clerk.${r}`}(o):g(a)?.frontendApi||s||"";let u=r?`${r.replace(/\.+$/,"")}.`:"",c=et(n);return`https://${l}/npm/@clerk/clerk-js@${c}/dist/clerk.${u}browser.js`},ea=e=>t=>{let{publishableKey:r,frontendApi:n,proxyUrl:i,domain:o}=e;r?t.setAttribute("data-clerk-publishable-key",r):n&&t.setAttribute("data-clerk-frontend-api",n),i&&t.setAttribute("data-clerk-proxy-url",i),o&&t.setAttribute("data-clerk-domain",o)},es=class e{constructor(e){this.clerkjs=null,this.preopenSignIn=null,this.preopenSignUp=null,this.preopenUserProfile=null,this.preopenOrganizationProfile=null,this.preopenCreateOrganization=null,this.premountSignInNodes=new Map,this.premountSignUpNodes=new Map,this.premountUserProfileNodes=new Map,this.premountUserButtonNodes=new Map,this.premountOrganizationProfileNodes=new Map,this.premountCreateOrganizationNodes=new Map,this.premountOrganizationSwitcherNodes=new Map,this.premountOrganizationListNodes=new Map,this.premountMethodCalls=new Map,this.loadedListeners=[],Y(this,a,!1),Y(this,s,void 0),Y(this,l,void 0),Y(this,u,void 0),Y(this,c,void 0),this.isReady=()=>!!this.clerkjs?.isReady(),this.buildSignInUrl=e=>{let t=()=>this.clerkjs?.buildSignInUrl(e)||"";if(this.clerkjs&&J(this,a))return t();this.premountMethodCalls.set("buildSignInUrl",t)},this.buildSignUpUrl=e=>{let t=()=>this.clerkjs?.buildSignUpUrl(e)||"";if(this.clerkjs&&J(this,a))return t();this.premountMethodCalls.set("buildSignUpUrl",t)},this.buildUserProfileUrl=()=>{let e=()=>this.clerkjs?.buildUserProfileUrl()||"";if(this.clerkjs&&J(this,a))return e();this.premountMethodCalls.set("buildUserProfileUrl",e)},this.buildCreateOrganizationUrl=()=>{let e=()=>this.clerkjs?.buildCreateOrganizationUrl()||"";if(this.clerkjs&&J(this,a))return e();this.premountMethodCalls.set("buildCreateOrganizationUrl",e)},this.buildOrganizationProfileUrl=()=>{let e=()=>this.clerkjs?.buildOrganizationProfileUrl()||"";if(this.clerkjs&&J(this,a))return e();this.premountMethodCalls.set("buildOrganizationProfileUrl",e)},this.buildHomeUrl=()=>{let e=()=>this.clerkjs?.buildHomeUrl()||"";if(this.clerkjs&&J(this,a))return e();this.premountMethodCalls.set("buildHomeUrl",e)},this.buildUrlWithAuth=(e,t)=>{let r=()=>this.clerkjs?.buildUrlWithAuth(e,t)||"";if(this.clerkjs&&J(this,a))return r();this.premountMethodCalls.set("buildUrlWithAuth",r)},this.handleUnauthenticated=()=>{let e=()=>this.clerkjs?.handleUnauthenticated();this.clerkjs&&J(this,a)?e():this.premountMethodCalls.set("handleUnauthenticated",e)},this.addOnLoaded=e=>{this.loadedListeners.push(e),this.loaded&&this.emitLoaded()},this.emitLoaded=()=>{this.loadedListeners.forEach(e=>e()),this.loadedListeners=[]},this.hydrateClerkJS=e=>{if(!e)throw Error("Failed to hydrate latest Clerk JS");return this.clerkjs=e,this.premountMethodCalls.forEach(e=>e()),null!==this.preopenSignIn&&e.openSignIn(this.preopenSignIn),null!==this.preopenSignUp&&e.openSignUp(this.preopenSignUp),null!==this.preopenUserProfile&&e.openUserProfile(this.preopenUserProfile),null!==this.preopenOrganizationProfile&&e.openOrganizationProfile(this.preopenOrganizationProfile),null!==this.preopenCreateOrganization&&e.openCreateOrganization(this.preopenCreateOrganization),this.premountSignInNodes.forEach((t,r)=>{e.mountSignIn(r,t)}),this.premountSignUpNodes.forEach((t,r)=>{e.mountSignUp(r,t)}),this.premountUserProfileNodes.forEach((t,r)=>{e.mountUserProfile(r,t)}),this.premountUserButtonNodes.forEach((t,r)=>{e.mountUserButton(r,t)}),this.premountOrganizationListNodes.forEach((t,r)=>{e.mountOrganizationList(r,t)}),X(this,a,!0),this.emitLoaded(),this.clerkjs},this.__unstable__updateProps=e=>{this.clerkjs&&"__unstable__updateProps"in this.clerkjs&&this.clerkjs.__unstable__updateProps(e)},this.setActive=({session:e,organization:t,beforeEmit:r})=>this.clerkjs?this.clerkjs.setActive({session:e,organization:t,beforeEmit:r}):Promise.reject(),this.setSession=(e,t)=>(x("setSession","Use `Clerk.setActive` instead"),this.setActive({session:e,beforeEmit:t})),this.openSignIn=e=>{this.clerkjs&&J(this,a)?this.clerkjs.openSignIn(e):this.preopenSignIn=e},this.closeSignIn=()=>{this.clerkjs&&J(this,a)?this.clerkjs.closeSignIn():this.preopenSignIn=null},this.openUserProfile=e=>{this.clerkjs&&J(this,a)?this.clerkjs.openUserProfile(e):this.preopenUserProfile=e},this.closeUserProfile=()=>{this.clerkjs&&J(this,a)?this.clerkjs.closeUserProfile():this.preopenUserProfile=null},this.openOrganizationProfile=e=>{this.clerkjs&&J(this,a)?this.clerkjs.openOrganizationProfile(e):this.preopenOrganizationProfile=e},this.closeOrganizationProfile=()=>{this.clerkjs&&J(this,a)?this.clerkjs.closeOrganizationProfile():this.preopenOrganizationProfile=null},this.openCreateOrganization=e=>{this.clerkjs&&J(this,a)?this.clerkjs.openCreateOrganization(e):this.preopenCreateOrganization=e},this.closeCreateOrganization=()=>{this.clerkjs&&J(this,a)?this.clerkjs.closeCreateOrganization():this.preopenCreateOrganization=null},this.openSignUp=e=>{this.clerkjs&&J(this,a)?this.clerkjs.openSignUp(e):this.preopenSignUp=e},this.closeSignUp=()=>{this.clerkjs&&J(this,a)?this.clerkjs.closeSignUp():this.preopenSignUp=null},this.mountSignIn=(e,t)=>{this.clerkjs&&J(this,a)?this.clerkjs.mountSignIn(e,t):this.premountSignInNodes.set(e,t)},this.unmountSignIn=e=>{this.clerkjs&&J(this,a)?this.clerkjs.unmountSignIn(e):this.premountSignInNodes.delete(e)},this.mountSignUp=(e,t)=>{this.clerkjs&&J(this,a)?this.clerkjs.mountSignUp(e,t):this.premountSignUpNodes.set(e,t)},this.unmountSignUp=e=>{this.clerkjs&&J(this,a)?this.clerkjs.unmountSignUp(e):this.premountSignUpNodes.delete(e)},this.mountUserProfile=(e,t)=>{this.clerkjs&&J(this,a)?this.clerkjs.mountUserProfile(e,t):this.premountUserProfileNodes.set(e,t)},this.unmountUserProfile=e=>{this.clerkjs&&J(this,a)?this.clerkjs.unmountUserProfile(e):this.premountUserProfileNodes.delete(e)},this.mountOrganizationProfile=(e,t)=>{this.clerkjs&&J(this,a)?this.clerkjs.mountOrganizationProfile(e,t):this.premountOrganizationProfileNodes.set(e,t)},this.unmountOrganizationProfile=e=>{this.clerkjs&&J(this,a)?this.clerkjs.unmountOrganizationProfile(e):this.premountOrganizationProfileNodes.delete(e)},this.mountCreateOrganization=(e,t)=>{this.clerkjs&&J(this,a)?this.clerkjs.mountCreateOrganization(e,t):this.premountCreateOrganizationNodes.set(e,t)},this.unmountCreateOrganization=e=>{this.clerkjs&&J(this,a)?this.clerkjs.unmountCreateOrganization(e):this.premountCreateOrganizationNodes.delete(e)},this.mountOrganizationSwitcher=(e,t)=>{this.clerkjs&&J(this,a)?this.clerkjs.mountOrganizationSwitcher(e,t):this.premountOrganizationSwitcherNodes.set(e,t)},this.unmountOrganizationSwitcher=e=>{this.clerkjs&&J(this,a)?this.clerkjs.unmountOrganizationSwitcher(e):this.premountOrganizationSwitcherNodes.delete(e)},this.mountOrganizationList=(e,t)=>{this.clerkjs&&J(this,a)?this.clerkjs.mountOrganizationList(e,t):this.premountOrganizationListNodes.set(e,t)},this.unmountOrganizationList=e=>{this.clerkjs&&J(this,a)?this.clerkjs.unmountOrganizationList(e):this.premountOrganizationListNodes.delete(e)},this.mountUserButton=(e,t)=>{this.clerkjs&&J(this,a)?this.clerkjs.mountUserButton(e,t):this.premountUserButtonNodes.set(e,t)},this.unmountUserButton=e=>{this.clerkjs&&J(this,a)?this.clerkjs.unmountUserButton(e):this.premountUserButtonNodes.delete(e)},this.addListener=e=>{let t=()=>this.clerkjs?.addListener(e);return this.clerkjs?t():(this.premountMethodCalls.set("addListener",t),()=>this.premountMethodCalls.delete("addListener"))},this.navigate=e=>{let t=()=>this.clerkjs?.navigate(e);this.clerkjs&&J(this,a)?t():this.premountMethodCalls.set("navigate",t)},this.redirectWithAuth=(...e)=>{let t=()=>this.clerkjs?.redirectWithAuth(...e);this.clerkjs&&J(this,a)?t():this.premountMethodCalls.set("redirectWithAuth",t)},this.redirectToSignIn=e=>{let t=()=>this.clerkjs?.redirectToSignIn(e);this.clerkjs&&J(this,a)?t():this.premountMethodCalls.set("redirectToSignIn",t)},this.redirectToSignUp=e=>{let t=()=>this.clerkjs?.redirectToSignUp(e);this.clerkjs&&J(this,a)?t():this.premountMethodCalls.set("redirectToSignUp",t)},this.redirectToUserProfile=()=>{let e=()=>this.clerkjs?.redirectToUserProfile();this.clerkjs&&J(this,a)?e():this.premountMethodCalls.set("redirectToUserProfile",e)},this.redirectToHome=()=>{let e=()=>this.clerkjs?.redirectToHome();this.clerkjs&&J(this,a)?e():this.premountMethodCalls.set("redirectToHome",e)},this.redirectToOrganizationProfile=()=>{let e=()=>this.clerkjs?.redirectToOrganizationProfile();this.clerkjs&&J(this,a)?e():this.premountMethodCalls.set("redirectToOrganizationProfile",e)},this.redirectToCreateOrganization=()=>{let e=()=>this.clerkjs?.redirectToCreateOrganization();this.clerkjs&&J(this,a)?e():this.premountMethodCalls.set("redirectToCreateOrganization",e)},this.handleRedirectCallback=e=>{let t=()=>this.clerkjs?.handleRedirectCallback(e);this.clerkjs&&J(this,a)?t()?.catch(()=>{}):this.premountMethodCalls.set("handleRedirectCallback",t)},this.handleMagicLinkVerification=async e=>{x("handleMagicLinkVerification","Use `handleEmailLinkVerification` instead.");let t=()=>this.clerkjs?.handleMagicLinkVerification(e);if(this.clerkjs&&J(this,a))return t();this.premountMethodCalls.set("handleMagicLinkVerification",t)},this.handleEmailLinkVerification=async e=>{let t=()=>this.clerkjs?.handleEmailLinkVerification(e);if(this.clerkjs&&J(this,a))return t();this.premountMethodCalls.set("handleEmailLinkVerification",t)},this.authenticateWithMetamask=async e=>{let t=()=>this.clerkjs?.authenticateWithMetamask(e);if(this.clerkjs&&J(this,a))return t();this.premountMethodCalls.set("authenticateWithMetamask",t)},this.createOrganization=async e=>{let t=()=>this.clerkjs?.createOrganization(e);if(this.clerkjs&&J(this,a))return t();this.premountMethodCalls.set("createOrganization",t)},this.getOrganizationMemberships=async()=>{let e=()=>this.clerkjs?.getOrganizationMemberships();if(this.clerkjs&&J(this,a))return e();this.premountMethodCalls.set("getOrganizationMemberships",e)},this.getOrganization=async e=>{let t=()=>this.clerkjs?.getOrganization(e);if(this.clerkjs&&J(this,a))return t();this.premountMethodCalls.set("getOrganization",t)},this.signOut=async(e,t)=>{let r=()=>this.clerkjs?.signOut(e,t);if(this.clerkjs&&J(this,a))return r();this.premountMethodCalls.set("signOut",r)};let{Clerk:t=null,frontendApi:r,publishableKey:n}=e||{};X(this,u,r),X(this,c,n),X(this,l,e?.proxyUrl),X(this,s,e?.domain),this.options=e,this.Clerk=t,this.mode="server",this.loadClerkJS()}get publishableKey(){return J(this,c)}get loaded(){return J(this,a)}static getOrCreateInstance(t){return X(this,d,new e(t)),J(this,d)}static clearInstance(){X(this,d,null)}get domain(){if("function"==typeof J(this,s))throw Error(H);return J(this,s)||""}get proxyUrl(){if("function"==typeof J(this,l))throw Error(H);return J(this,l)||""}get sdkMetadata(){return this.clerkjs?.sdkMetadata||this.options.sdkMetadata||void 0}get instanceType(){return this.clerkjs?.instanceType}get frontendApi(){return this.clerkjs?.frontendApi||J(this,u)||""}get isStandardBrowser(){return this.clerkjs?.isStandardBrowser||this.options.standardBrowser||!1}get isSatellite(){if("function"==typeof this.options.isSatellite)throw Error(H);return!1}async loadClerkJS(){if(!("browser"!==this.mode||J(this,a)))try{if(this.Clerk){var e;let t;(e=this.Clerk,"function"==typeof e)?(t=new this.Clerk(this.publishableKey||this.frontendApi||"",{proxyUrl:this.proxyUrl,domain:this.domain}),await t.load(this.options)):(t=this.Clerk).isReady()||await t.load(this.options),global.Clerk=t}else{if(global.Clerk||await ei({...this.options,frontendApi:this.frontendApi,publishableKey:this.publishableKey,proxyUrl:this.proxyUrl,domain:this.domain}),!global.Clerk)throw Error("Failed to download latest ClerkJS. Contact <EMAIL>.");await global.Clerk.load(this.options)}if(global.Clerk.sdkMetadata=this.options.sdkMetadata??{name:"@clerk/clerk-react",version:"4.30.5"},global.Clerk?.loaded||global.Clerk?.isReady())return this.hydrateClerkJS(global.Clerk);return}catch(e){console.error(e.stack||e.message||e);return}}get version(){return this.clerkjs?.version}get client(){return this.clerkjs?this.clerkjs.client:void 0}get session(){return this.clerkjs?this.clerkjs.session:void 0}get user(){return this.clerkjs?this.clerkjs.user:void 0}get organization(){return this.clerkjs?this.clerkjs.organization:void 0}get __unstable__environment(){return this.clerkjs?this.clerkjs.__unstable__environment:void 0}__unstable__setEnvironment(...e){this.clerkjs&&"__unstable__setEnvironment"in this.clerkjs&&this.clerkjs.__unstable__setEnvironment(e)}};a=new WeakMap,s=new WeakMap,l=new WeakMap,u=new WeakMap,c=new WeakMap,Y(es,d=new WeakMap,void 0);let el=(e,t,r)=>!e&&r?eu(r):ec(t),eu=e=>{let t=e.userId,r=e.user,n=e.sessionId,i=e.session,o=e.organization,a=e.orgId,s=e.orgRole;return{userId:t,user:r,sessionId:n,session:i,organization:o,orgId:a,orgRole:s,orgPermissions:e.orgPermissions,orgSlug:e.orgSlug,actor:e.actor,lastOrganizationInvitation:null,lastOrganizationMember:null}},ec=e=>{let t=e.user?e.user.id:e.user,r=e.user,n=e.session?e.session.id:e.session,i=e.session,o=i?.actor,a=e.organization,s=e.organization?e.organization.id:e.organization,l=a?.slug,u=a?r?.organizationMemberships?.find(e=>e.organization.id===s):a,c=u?u.permissions:u;return{userId:t,user:r,sessionId:n,session:i,organization:a,orgId:s,orgRole:u?u.role:u,orgSlug:l,orgPermissions:c,actor:o,lastOrganizationInvitation:e.lastOrganizationInvitation,lastOrganizationMember:e.lastOrganizationMember}};var ed=r(8145);let eh=()=>{},ef=eh(),ep=Object,eg=e=>e===ef,ey=e=>"function"==typeof e,em=(e,t)=>({...e,...t}),eb=e=>ey(e.then),ev=new WeakMap,ew=0,eS=e=>{let t,r;let n=typeof e,i=e&&e.constructor,o=i==Date;if(ep(e)!==e||o||i==RegExp)t=o?e.toJSON():"symbol"==n?e.toString():"string"==n?JSON.stringify(e):""+e;else{if(t=ev.get(e))return t;if(t=++ew+"~",ev.set(e,t),i==Array){for(r=0,t="@";r<e.length;r++)t+=eS(e[r])+",";ev.set(e,t)}if(i==ep){t="#";let n=ep.keys(e).sort();for(;!eg(r=n.pop());)eg(e[r])||(t+=r+":"+eS(e[r])+",");ev.set(e,t)}}return t},e_=new WeakMap,ek={},eE={},eA="undefined"!=typeof document,eP=()=>!1,eC=(e,t)=>{let r=e_.get(e);return[()=>!eg(t)&&e.get(t)||ek,n=>{if(!eg(t)){let i=e.get(t);t in eE||(eE[t]=i),r[5](t,em(i,n),i||ek)}},r[6],()=>!eg(t)&&t in eE?eE[t]:!eg(t)&&e.get(t)||ek]},ex=!0,[eO,eR]=[eh,eh],eT={initFocus:e=>(eA&&document.addEventListener("visibilitychange",e),eO("focus",e),()=>{eA&&document.removeEventListener("visibilitychange",e),eR("focus",e)}),initReconnect:e=>{let t=()=>{ex=!0,e()},r=()=>{ex=!1};return eO("online",t),eO("offline",r),()=>{eR("online",t),eR("offline",r)}}},eB=!_.useId,ej=e=>eP()?window.requestAnimationFrame(e):setTimeout(e,1),eU=(0,_.useEffect),eN="undefined"!=typeof navigator&&navigator.connection,eI=false,eM=e=>{if(ey(e))try{e=e()}catch(t){e=""}let t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?eS(e):"",t]},eL=0,eK=()=>++eL;var eD={ERROR_REVALIDATE_EVENT:3,FOCUS_EVENT:0,MUTATE_EVENT:2,RECONNECT_EVENT:1};async function ez(...e){let[t,r,n,i]=e,o=em({populateCache:!0,throwOnError:!0},"boolean"==typeof i?{revalidate:i}:i||{}),a=o.populateCache,s=o.rollbackOnError,l=o.optimisticData,u=!1!==o.revalidate,c=e=>"function"==typeof s?s(e):!1!==s,d=o.throwOnError;if(ey(r)){let e=[];for(let n of t.keys())!/^\$(inf|sub)\$/.test(n)&&r(t.get(n)._k)&&e.push(n);return Promise.all(e.map(h))}return h(r);async function h(r){let i;let[o]=eM(r);if(!o)return;let[s,h]=eC(t,o),[f,p,g,y]=e_.get(t),m=f[o],b=()=>u&&(delete g[o],delete y[o],m&&m[0])?m[0](2).then(()=>s().data):s().data;if(e.length<3)return b();let v=n,w=eK();p[o]=[w,0];let S=!eg(l),_=s(),k=_.data,E=_._c,A=eg(E)?k:E;if(S&&h({data:l=ey(l)?l(A,k):l,_c:A}),ey(v))try{v=v(A)}catch(e){i=e}if(v&&eb(v)){if(v=await v.catch(e=>{i=e}),w!==p[o][0]){if(i)throw i;return v}i&&S&&c(i)&&(a=!0,h({data:v=A,_c:ef}))}a&&!i&&(ey(a)&&(v=a(v,A)),h({data:v,error:ef,_c:ef})),p[o][1]=eK();let P=await b();if(h({_c:ef}),i){if(d)throw i;return}return a?P:v}}let eH=(e,t)=>{for(let r in e)e[r][0]&&e[r][0](t)},eF=(e,t)=>{if(!e_.has(e)){let r=em(eT,t),n={},i=ez.bind(ef,e),o=eh,a={},s=(e,t)=>{let r=a[e]||[];return a[e]=r,r.push(t),()=>r.splice(r.indexOf(t),1)},l=(t,r,n)=>{e.set(t,r);let i=a[t];if(i)for(let e of i)e(r,n)},u=()=>{!e_.has(e)&&e_.set(e,[n,{},{},{},i,l,s])};return u(),[e,i,u,o]}return[e,e_.get(e)[4]]},[e$,eq]=eF(new Map),eV=em({onLoadingSlow:eh,onSuccess:eh,onError:eh,onErrorRetry:(e,t,r,n,i)=>{let o=r.errorRetryCount,a=i.retryCount,s=~~((Math.random()+.5)*(1<<(a<8?a:8)))*r.errorRetryInterval;(eg(o)||!(a>o))&&setTimeout(n,s,i)},onDiscarded:eh,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:eI?1e4:5e3,focusThrottleInterval:5e3,dedupingInterval:2e3,loadingTimeout:eI?5e3:3e3,compare:(e,t)=>eS(e)==eS(t),isPaused:()=>!1,cache:e$,mutate:eq,fallback:{}},{isOnline:()=>ex,isVisible:()=>{let e=eA&&document.visibilityState;return eg(e)||"hidden"!==e}}),eW=(e,t)=>{let r=em(e,t);if(t){let{use:n,fallback:i}=e,{use:o,fallback:a}=t;n&&o&&(r.use=n.concat(o)),i&&a&&(r.fallback=em(i,a))}return r},eG=(0,_.createContext)({}),eJ=[],eY=e=>ey(e[1])?[e[0],e[1],e[2]||{}]:[e[0],null,(null===e[1]?e[2]:e[1])||{}],eX=()=>em(eV,(0,_.useContext)(eG)),eQ=(e,t)=>{let[r,n]=eM(e),[,,,i]=e_.get(e$);if(i[r])return i[r];let o=t(n);return i[r]=o,o},eZ=eJ.concat(e=>(t,r,n)=>{let i=r&&((...e)=>{let[n]=eM(t),[,,,i]=e_.get(e$),o=i[n];return eg(o)?r(...e):(delete i[n],o)});return e(t,i,n)}),e0=(e,t,r)=>{let n=t[e]||(t[e]=[]);return n.push(r),()=>{let e=n.indexOf(r);e>=0&&(n[e]=n[n.length-1],n.pop())}},e1=e=>eM(e)[0],e2=_.use||(e=>{if("pending"===e.status)throw e;if("fulfilled"===e.status)return e.value;if("rejected"===e.status)throw e.reason;throw e.status="pending",e.then(t=>{e.status="fulfilled",e.value=t},t=>{e.status="rejected",e.reason=t}),e}),e8={dedupe:!0},e3=ep.defineProperty(e=>{let{value:t}=e,r=(0,_.useContext)(eG),n=ey(t),i=(0,_.useMemo)(()=>n?t(r):t,[n,r,t]),o=(0,_.useMemo)(()=>n?i:eW(r,i),[n,r,i]),a=i&&i.provider,s=(0,_.useRef)(ef);a&&!s.current&&(s.current=eF(a(o.cache||e$),i));let l=s.current;return l&&(o.cache=l[0],o.mutate=l[1]),eU(()=>{if(l)return l[2]&&l[2](),l[3]},[]),(0,_.createElement)(eG.Provider,em(e,{value:o}))},"defaultValue",{value:eV}),e4=(n=(e,t,r)=>{let{cache:n,compare:i,suspense:o,fallbackData:a,revalidateOnMount:s,revalidateIfStale:l,refreshInterval:u,refreshWhenHidden:c,refreshWhenOffline:d,keepPreviousData:h}=r,[f,p,g,y]=e_.get(n),[m,b]=eM(e),v=(0,_.useRef)(!1),w=(0,_.useRef)(!1),S=(0,_.useRef)(m),k=(0,_.useRef)(t),E=(0,_.useRef)(r),A=()=>E.current,P=()=>A().isVisible()&&A().isOnline(),[C,x,O,R]=eC(n,m),T=(0,_.useRef)({}).current,B=eg(a)?r.fallback[m]:a,j=(e,t)=>{for(let r in T)if("data"===r){if(!i(e[r],t[r])&&(!eg(e[r])||!i(H,t[r])))return!1}else if(t[r]!==e[r])return!1;return!0},U=(0,_.useMemo)(()=>{let e=!!m&&!!t&&(eg(s)?!A().isPaused()&&!o&&(!!eg(l)||l):s),r=t=>{let r=em(t);return(delete r._k,e)?{isValidating:!0,isLoading:!0,...r}:r},n=C(),i=R(),a=r(n),u=n===i?a:r(i),c=a;return[()=>{let e=r(C());return j(e,c)?(c.data=e.data,c.isLoading=e.isLoading,c.isValidating=e.isValidating,c.error=e.error,c):(c=e,e)},()=>u]},[n,m]),N=(0,ed.useSyncExternalStore)((0,_.useCallback)(e=>O(m,(t,r)=>{j(r,t)||e()}),[n,m]),U[0],U[1]),I=!v.current,M=f[m]&&f[m].length>0,L=N.data,K=eg(L)?B:L,D=N.error,z=(0,_.useRef)(K),H=h?eg(L)?z.current:L:K,F=(!M||!!eg(D))&&(I&&!eg(s)?s:!A().isPaused()&&(o?!eg(K)&&l:eg(K)||l)),$=!!(m&&t&&I&&F),q=eg(N.isValidating)?$:N.isValidating,V=eg(N.isLoading)?$:N.isLoading,W=(0,_.useCallback)(async e=>{let t,n;let o=k.current;if(!m||!o||w.current||A().isPaused())return!1;let a=!0,s=e||{},l=!g[m]||!s.dedupe,u=()=>eB?!w.current&&m===S.current&&v.current:m===S.current,c={isValidating:!1,isLoading:!1},d=()=>{x(c)},h=()=>{let e=g[m];e&&e[1]===n&&delete g[m]},y={isValidating:!0};eg(C().data)&&(y.isLoading=!0);try{if(l&&(x(y),r.loadingTimeout&&eg(C().data)&&setTimeout(()=>{a&&u()&&A().onLoadingSlow(m,r)},r.loadingTimeout),g[m]=[o(b),eK()]),[t,n]=g[m],t=await t,l&&setTimeout(h,r.dedupingInterval),!g[m]||g[m][1]!==n)return l&&u()&&A().onDiscarded(m),!1;c.error=ef;let e=p[m];if(!eg(e)&&(n<=e[0]||n<=e[1]||0===e[1]))return d(),l&&u()&&A().onDiscarded(m),!1;let s=C().data;c.data=i(s,t)?s:t,l&&u()&&A().onSuccess(t,m,r)}catch(r){h();let e=A(),{shouldRetryOnError:t}=e;!e.isPaused()&&(c.error=r,l&&u()&&(e.onError(r,m,e),(!0===t||ey(t)&&t(r))&&P()&&e.onErrorRetry(r,m,e,e=>{let t=f[m];t&&t[0]&&t[0](eD.ERROR_REVALIDATE_EVENT,e)},{retryCount:(s.retryCount||0)+1,dedupe:!0})))}return a=!1,d(),!0},[m,n]),G=(0,_.useCallback)((...e)=>ez(n,S.current,...e),[]);if(eU(()=>{k.current=t,E.current=r,eg(L)||(z.current=L)}),eU(()=>{if(!m)return;let e=W.bind(ef,e8),t=0,r=e0(m,f,(r,n={})=>{if(r==eD.FOCUS_EVENT){let r=Date.now();A().revalidateOnFocus&&r>t&&P()&&(t=r+A().focusThrottleInterval,e())}else if(r==eD.RECONNECT_EVENT)A().revalidateOnReconnect&&P()&&e();else if(r==eD.MUTATE_EVENT)return W();else if(r==eD.ERROR_REVALIDATE_EVENT)return W(n)});return w.current=!1,S.current=m,v.current=!0,x({_k:b}),F&&(eg(K),e()),()=>{w.current=!0,r()}},[m]),eU(()=>{let e;function t(){let t=ey(u)?u(C().data):u;t&&-1!==e&&(e=setTimeout(r,t))}function r(){!C().error&&(c||A().isVisible())&&(d||A().isOnline())?W(e8).then(t):t()}return t(),()=>{e&&(clearTimeout(e),e=-1)}},[u,c,d,m]),(0,_.useDebugValue)(H),o&&eg(K)&&m){if(!eB)throw Error("Fallback data is required when using suspense in SSR.");k.current=t,E.current=r,w.current=!1;let e=y[m];if(eg(e)||e2(G(e)),eg(D)){let e=W(e8);eg(H)||(e.status="fulfilled",e.value=!0),e2(e)}else throw D}return{mutate:G,get data(){return T.data=!0,H},get error(){return T.error=!0,D},get isValidating(){return T.isValidating=!0,q},get isLoading(){return T.isLoading=!0,V}}},function(...e){let t=eX(),[r,i,o]=eY(e),a=eW(t,o),s=n,{use:l}=a,u=(l||[]).concat(eZ);for(let e=u.length;e--;)s=u[e](s);return s(r,i||a.fetcher||null,a)}),e5=e=>eM(e?e(0,null):null)[0],e6=Promise.resolve(),e7=(i=e=>(t,r,n)=>{let i;let o=(0,_.useRef)(!1),{cache:a,initialSize:s=1,revalidateAll:l=!1,persistSize:u=!1,revalidateFirstPage:c=!0,revalidateOnMount:d=!1,parallel:h=!1}=n;try{(i=e5(t))&&(i="$inf$"+i)}catch(e){}let[f,p,g]=eC(a,i),y=(0,_.useCallback)(()=>eg(f()._l)?s:f()._l,[a,i,s]);(0,ed.useSyncExternalStore)((0,_.useCallback)(e=>i?g(i,()=>{e()}):()=>{},[a,i]),y,y);let m=(0,_.useCallback)(()=>{let e=f()._l;return eg(e)?s:e},[i,s]),b=(0,_.useRef)(m());eU(()=>{if(!o.current){o.current=!0;return}i&&p({_l:u?b.current:m()})},[i,a]);let v=d&&!o.current,w=e(i,async e=>{let i=f()._i,o=[],s=m(),[u]=eC(a,e),d=u().data,g=[],y=null;for(let e=0;e<s;++e){let[s,u]=eM(t(e,h?null:y));if(!s)break;let[f,p]=eC(a,s),m=f().data,b=l||i||eg(m)||c&&!e&&!eg(d)||v||d&&!eg(d[e])&&!n.compare(d[e],m);if(r&&b){let t=async()=>{p({data:m=await r(u),_k:u}),o[e]=m};h?g.push(t):await t()}else o[e]=m;h||(y=m)}return h&&await Promise.all(g.map(e=>e())),p({_i:ef}),o},n),S=(0,_.useCallback)(function(e,t){let r="boolean"==typeof t?{revalidate:t}:t||{},n=!1!==r.revalidate;return i?(n&&(eg(e)?p({_i:!0}):p({_i:!1})),arguments.length?w.mutate(e,{...r,revalidate:n}):w.mutate()):e6},[i,a]),k=(0,_.useCallback)(e=>{let r;if(!i)return e6;let[,n]=eC(a,i);if(ey(e)?r=e(m()):"number"==typeof e&&(r=e),"number"!=typeof r)return e6;n({_l:r}),b.current=r;let o=[],[s]=eC(a,i),l=null;for(let e=0;e<r;++e){let[r]=eM(t(e,l)),[n]=eC(a,r),i=r?n().data:ef;if(eg(i))return S(s().data);o.push(i),l=i}return S(o)},[i,a,S,m]);return{size:m(),setSize:k,mutate:S,get data(){return w.data},get error(){return w.error},get isValidating(){return w.isValidating},get isLoading(){return w.isLoading}}},(...e)=>{let[t,r,n]=eY(e),o=(n.use||[]).concat(i);return e4(t,r,{...n,use:o})});function e9(e,t){if(!e)throw"string"==typeof t?Error(t):Error(`${t.displayName} not found`)}var te=(e,t)=>{let{assertCtxFn:r=e9}=t||{},n=_.createContext(void 0);return n.displayName=e,[n,()=>{let t=_.useContext(n);return r(t,`${e} not found`),t.value},()=>{let e=_.useContext(n);return e?e.value:{}}]},tt={};((e,t)=>{for(var r in t)m(e,r,{get:t[r],enumerable:!0})})(tt,{SWRConfig:()=>e3,useSWR:()=>e4,useSWRInfinite:()=>e7}),S(tt,h,"default"),o&&S(o,h,"default");var[tr,tn]=te("ClerkInstanceContext"),[ti,to]=te("UserContext"),[ta,ts]=te("ClientContext"),[tl,tu]=te("SessionContext"),[tc,td]=te("OrganizationContext"),th=({children:e,organization:t,lastOrganizationMember:r,lastOrganizationInvitation:n,swrConfig:i})=>_.createElement(e3,{value:i},_.createElement(tc.Provider,{value:{value:{organization:t,lastOrganizationMember:r,lastOrganizationInvitation:n}}},e));function tf(e,t){let r=new Set(Object.keys(t)),n={};for(let t of Object.keys(e))r.has(t)||(n[t]=e[t]);return n}var tp=(e,t)=>{let r="boolean"==typeof e&&e,n=(0,_.useRef)(r?t.initialPage:e?.initialPage??t.initialPage),i=(0,_.useRef)(r?t.pageSize:e?.pageSize??t.pageSize),o={};for(let n of Object.keys(t))o[n]=r?t[n]:e?.[n]??t[n];return{...o,initialPage:n.current,pageSize:i.current}},tg=(e,t,r,n)=>{let[i,o]=(0,_.useState)(e.initialPage??1),a=(0,_.useRef)(e.initialPage??1),s=(0,_.useRef)(e.pageSize??10),l=r.enabled??!0,u=r.infinite??!1,c=r.keepPreviousData??!1,d={...n,...e,initialPage:i,pageSize:s.current},{data:h,isValidating:f,isLoading:p,error:g,mutate:y}=e4(!u&&t&&l?d:null,e=>{let r=tf(e,n);return t?.(r)},{keepPreviousData:c}),{data:m,isLoading:b,isValidating:v,error:w,size:S,setSize:k,mutate:E}=e7(t=>u&&l?{...e,...n,initialPage:a.current+t,pageSize:s.current}:null,e=>{let r=tf(e,n);return t?.(r)}),A=(0,_.useMemo)(()=>u?S:i,[u,S,i]),P=(0,_.useCallback)(e=>{if(u){k(e);return}return o(e)},[k]),C=(0,_.useMemo)(()=>u?m?.map(e=>e?.data).flat()??[]:h?.data??[],[u,h,m]),x=(0,_.useMemo)(()=>u?m?.[m?.length-1]?.total_count||0:h?.total_count??0,[u,h,m]),O=u?b:p,R=u?v:f,T=!!(u?w:g),B=(0,_.useCallback)(()=>{P(e=>Math.max(0,e+1))},[P]),j=(0,_.useCallback)(()=>{P(e=>Math.max(0,e-1))},[P]),U=(a.current-1)*s.current,N=Math.ceil((x-U)/s.current),I=x-U*s.current>A*s.current,M=(A-1)*s.current>U*s.current,L=u?e=>E(e,{revalidate:!1}):e=>y(e,{revalidate:!1});return{data:C,count:x,isLoading:O,isFetching:R,isError:T,page:A,pageCount:N,fetchPage:P,fetchNext:B,fetchPrevious:j,hasNextPage:I,hasPreviousPage:M,revalidate:u?()=>E():()=>y(),setData:L}},ty={data:void 0,count:void 0,isLoading:!1,isFetching:!1,isError:!1,page:void 0,pageCount:void 0,fetchPage:void 0,fetchNext:void 0,fetchPrevious:void 0,hasNextPage:!1,hasPreviousPage:!1,revalidate:void 0,setData:void 0},tm=e=>{var t,r;let{invitationList:n,membershipList:i,domains:o,membershipRequests:a,memberships:s,invitations:l}=e||{},{organization:u,lastOrganizationMember:c,lastOrganizationInvitation:d}=td(),h=tu(),f=tp(o,{initialPage:1,pageSize:10,keepPreviousData:!1,infinite:!1,enrollmentMode:void 0}),p=tp(a,{initialPage:1,pageSize:10,status:"pending",keepPreviousData:!1,infinite:!1}),g=tp(s,{initialPage:1,pageSize:10,role:void 0,keepPreviousData:!1,infinite:!1}),y=tp(l,{initialPage:1,pageSize:10,status:["pending"],keepPreviousData:!1,infinite:!1}),m=tn(),b=!!(m.loaded&&h&&u),v=void 0===o?void 0:{initialPage:f.initialPage,pageSize:f.pageSize,enrollmentMode:f.enrollmentMode},w=void 0===a?void 0:{initialPage:p.initialPage,pageSize:p.pageSize,status:p.status},S=void 0===s?void 0:{initialPage:g.initialPage,pageSize:g.pageSize,role:g.role},_=void 0===l?void 0:{initialPage:y.initialPage,pageSize:y.pageSize,status:y.status},k=tg({...v},u?.getDomains,{keepPreviousData:f.keepPreviousData,infinite:f.infinite,enabled:!!v},{type:"domains",organizationId:u?.id}),E=tg({...w},u?.getMembershipRequests,{keepPreviousData:p.keepPreviousData,infinite:p.infinite,enabled:!!w},{type:"membershipRequests",organizationId:u?.id}),A=tg({...S,paginated:!0},u?.getMemberships,{keepPreviousData:g.keepPreviousData,infinite:g.infinite,enabled:!!S},{type:"members",organizationId:u?.id}),P=tg({..._},u?.getInvitations,{keepPreviousData:y.keepPreviousData,infinite:y.infinite,enabled:!!_},{type:"invitations",organizationId:u?.id}),C=m.loaded?()=>m.organization?.getPendingInvitations(n):()=>[],O=m.loaded?()=>m.organization?.getMemberships(i):()=>[];n&&x("invitationList in useOrganization","Use the `invitations` property and return value instead.");let{data:R,isValidating:T,mutate:B}=e4(b&&n?tb("invites",u,d,n):null,C);i&&x("membershipList in useOrganization","Use the `memberships` property and return value instead.");let{data:j,isValidating:U,mutate:N}=e4(b&&i?tb("memberships",u,c,i):null,O);return void 0===u?{isLoaded:!1,organization:void 0,invitationList:void 0,membershipList:void 0,membership:void 0,domains:ty,membershipRequests:ty,memberships:ty,invitations:ty}:null===u?{isLoaded:!0,organization:null,invitationList:null,membershipList:null,membership:null,domains:null,membershipRequests:null,memberships:null,invitations:null}:!m.loaded&&u?{isLoaded:!0,organization:u,invitationList:void 0,membershipList:void 0,membership:void 0,domains:ty,membershipRequests:ty,memberships:ty,invitations:ty}:{isLoaded:!U&&!T,organization:u,membershipList:j,membership:(t=h.user.organizationMemberships,r=u.id,t.find(e=>e.organization.id===r)),invitationList:R,unstable__mutate:()=>{N(),B()},domains:k,membershipRequests:E,memberships:A,invitations:P}};function tb(e,t,r,n){return[e,t.id,r?.id,r?.updatedAt,n.offset,n.limit].filter(Boolean).join("-")}var tv={data:void 0,count:void 0,isLoading:!1,isFetching:!1,isError:!1,page:void 0,pageCount:void 0,fetchPage:void 0,fetchNext:void 0,fetchPrevious:void 0,hasNextPage:!1,hasPreviousPage:!1,revalidate:void 0,setData:void 0},tw=e=>{let{userMemberships:t,userInvitations:r,userSuggestions:n}=e||{},i=tp(t,{initialPage:1,pageSize:10,keepPreviousData:!1,infinite:!1}),o=tp(r,{initialPage:1,pageSize:10,status:"pending",keepPreviousData:!1,infinite:!1}),a=tp(n,{initialPage:1,pageSize:10,status:"pending",keepPreviousData:!1,infinite:!1}),s=tn(),l=to(),u=void 0===t?void 0:{initialPage:i.initialPage,pageSize:i.pageSize},c=void 0===r?void 0:{initialPage:o.initialPage,pageSize:o.pageSize,status:o.status},d=void 0===n?void 0:{initialPage:a.initialPage,pageSize:a.pageSize,status:a.status},h=!!(s.loaded&&l),f=tg({...u,paginated:!0},l?.getOrganizationMemberships,{keepPreviousData:i.keepPreviousData,infinite:i.infinite,enabled:!!u},{type:"userMemberships",userId:l?.id}),p=tg({...c},l?.getOrganizationInvitations,{keepPreviousData:o.keepPreviousData,infinite:o.infinite,enabled:!!c},{type:"userInvitations",userId:l?.id}),g=tg({...d},l?.getOrganizationSuggestions,{keepPreviousData:a.keepPreviousData,infinite:a.infinite,enabled:!!d},{type:"userSuggestions",userId:l?.id});if(!h)return{isLoaded:!1,organizationList:void 0,createOrganization:void 0,setActive:void 0,userMemberships:tv,userInvitations:tv,userSuggestions:tv};let y={isLoaded:h,organizationList:l.organizationMemberships.map(e=>({membership:e,organization:e.organization})),setActive:s.setActive,createOrganization:s.createOrganization,userMemberships:f,userInvitations:p,userSuggestions:g};return O(y,"organizationList","Use `userMemberships` instead."),y},tS=()=>{x("useOrganizations","Use useOrganizationList, useOrganization, or useClerk instead.");let e=tn();return e.loaded?{isLoaded:!0,createOrganization:e.createOrganization,getOrganizationMemberships:e.getOrganizationMemberships,getOrganization:e.getOrganization}:{isLoaded:!1,createOrganization:void 0,getOrganizationMemberships:void 0,getOrganization:void 0}};_.useEffect;let[t_,tk]=te("AuthContext"),[tE,tA]=[tr,tn];function tP(e){let{isomorphicClerkOptions:t,initialState:r,children:n}=e,{isomorphicClerk:i,loaded:o}=tC(t);t.frontendApi&&x("frontendApi","Use `publishableKey` instead.");let[a,s]=k().useState({client:i.client,session:i.session,user:i.user,organization:i.organization,lastOrganizationInvitation:null,lastOrganizationMember:null});k().useEffect(()=>i.addListener(e=>s({...e})),[]);let l=el(o,a,r),u=k().useMemo(()=>({value:i}),[o]),c=k().useMemo(()=>({value:a.client}),[a.client]),{sessionId:d,session:h,userId:f,user:p,orgId:g,actor:y,lastOrganizationInvitation:m,lastOrganizationMember:b,organization:v,orgRole:w,orgSlug:S,orgPermissions:_}=l,E=k().useMemo(()=>({value:{sessionId:d,userId:f,actor:y,orgId:g,orgRole:w,orgSlug:S,orgPermissions:_}}),[d,f,y,g,w,S]),A=k().useMemo(()=>({value:p}),[f,p]),P=k().useMemo(()=>({value:h}),[d,h]),C=k().useMemo(()=>({value:{organization:v,lastOrganizationInvitation:m,lastOrganizationMember:b}}),[g,v,m,b]);return k().createElement(tE.Provider,{value:u},k().createElement(ta.Provider,{value:c},k().createElement(tl.Provider,{value:P},k().createElement(th,{...C.value},k().createElement(t_.Provider,{value:E},k().createElement(ti.Provider,{value:A},n))))))}let tC=e=>{let[t,r]=k().useState(!1),n=k().useMemo(()=>es.getOrCreateInstance(e),[]);return k().useEffect(()=>{n.__unstable__updateProps({appearance:e.appearance})},[e.appearance]),k().useEffect(()=>{n.__unstable__updateProps({options:e})},[e.localization]),k().useEffect(()=>{n.addOnLoaded(()=>r(!0))},[]),k().useEffect(()=>()=>{es.clearInstance()},[]),{isomorphicClerk:n,loaded:t}},tx=Object.freeze({noGuarantees:Object.freeze({guaranteedLoaded:!1}),guaranteedLoaded:Object.freeze({guaranteedLoaded:!0})}),tO=k().createContext(void 0);tO.displayName="StructureContext";let tR=()=>{let e=k().useContext(tO);return function(e){if(!e)throw Error("Clerk: You must wrap your application in a <ClerkProvider> component.")}(e),e},tT=({children:e})=>tR().guaranteedLoaded?k().createElement(k().Fragment,null,e):k().createElement(tO.Provider,{value:tx.guaranteedLoaded},e);!function(e){V.setMessages(e).setPackageName(e)}({packageName:"@clerk/clerk-react"});let tB=function(e,t,r){let n=e.displayName||e.name||t||"Component",i=n=>((function(e,t,r=1){k().useEffect(()=>{let n=W.get(e)||0;if(n==r)throw Error(t);return W.set(e,n+1),()=>{W.set(e,(W.get(e)||1)-1)}},[])})(t,r),k().createElement(e,{...n}));return i.displayName=`withMaxAllowedInstancesGuard(${n})`,i}(function(e){let{initialState:t,children:r,...n}=e,{frontendApi:i="",publishableKey:o="",Clerk:a}=n;return!a&&(o||i?o&&!y(o)?V.throwInvalidPublishableKeyError({key:o}):o||!i||(i||"").startsWith("clerk.")||V.throwInvalidFrontendApiError({key:i}):V.throwMissingPublishableKeyError()),k().createElement(tO.Provider,{value:tx.noGuarantees},k().createElement(tP,{initialState:t,isomorphicClerkOptions:n},r))},"ClerkProvider","Clerk: You've added multiple <ClerkProvider> components in your React component tree. Wrap your components in a single <ClerkProvider>.");tB.displayName="ClerkProvider";var tj=e=>{let t=r=>{if(!r)return r;if(Array.isArray(r))return r.map(e=>"object"==typeof e||Array.isArray(e)?t(e):e);let n={...r};for(let r of Object.keys(n)){let i=e(r.toString());i!==r&&(n[i]=n[r],delete n[r]),"object"==typeof n[i]&&(n[i]=t(n[i]))}return n};return t};tj(function(e){return e?e.replace(/[A-Z]/g,e=>`_${e.toLowerCase()}`):""}),tj(function(e){return e?e.replace(/([-_][a-z])/g,e=>e.toUpperCase().replace(/-|_/,"")):""}),Object.freeze({"image/png":"png","image/jpeg":"jpg","image/gif":"gif","image/webp":"webp","image/x-icon":"ico","image/vnd.microsoft.icon":"ico"});var tU=e=>{E()&&console.error(e)},tN=r(81202);let tI=e=>{let t=Array(e.length).fill(null),[r,n]=(0,_.useState)(t);return e.map((e,t)=>({id:e.id,mount:e=>n(r=>r.map((r,n)=>n===t?e:r)),unmount:()=>n(e=>e.map((e,r)=>r===t?null:e)),portal:()=>k().createElement(k().Fragment,null,r[t]?(0,tN.createPortal)(e.component,r[t]):null)}))},tM=(e,t)=>!!e&&k().isValidElement(e)&&e?.type===t,tL=e=>tD({children:e,reorderItemsLabels:["account","security"],LinkComponent:tY,PageComponent:tJ,componentName:"UserProfile"}),tK=e=>tD({children:e,reorderItemsLabels:["members","settings"],LinkComponent:t0,PageComponent:tZ,componentName:"OrganizationProfile"}),tD=({children:e,LinkComponent:t,PageComponent:r,reorderItemsLabels:n,componentName:i})=>{let o=[];k().Children.forEach(e,e=>{if(!tM(e,r)&&!tM(e,t)){e&&tU(F(i));return}let{props:a}=e,{children:s,label:l,url:u,labelIcon:c}=a;if(tM(e,r)){if(tz(a,n))o.push({label:l});else if(tH(a))o.push({label:l,labelIcon:c,children:s,url:u});else{tU($(i));return}}if(tM(e,t)){if(tF(a))o.push({label:l,labelIcon:c,url:u});else{tU(q(i));return}}});let a=[],s=[],l=[];o.forEach((e,t)=>{if(tH(e)){a.push({component:e.children,id:t}),s.push({component:e.labelIcon,id:t});return}tF(e)&&l.push({component:e.labelIcon,id:t})});let u=tI(a),c=tI(s),d=tI(l),h=[],f=[];return o.forEach((e,t)=>{if(tz(e,n)){h.push({label:e.label});return}if(tH(e)){let{portal:r,mount:n,unmount:i}=u.find(e=>e.id===t),{portal:o,mount:a,unmount:s}=c.find(e=>e.id===t);h.push({label:e.label,url:e.url,mount:n,unmount:i,mountIcon:a,unmountIcon:s}),f.push(r),f.push(o);return}if(tF(e)){let{portal:r,mount:n,unmount:i}=d.find(e=>e.id===t);h.push({label:e.label,url:e.url,mountIcon:n,unmountIcon:i}),f.push(r);return}}),{customPages:h,customPagesPortals:f}},tz=(e,t)=>{let{children:r,label:n,url:i,labelIcon:o}=e;return!r&&!i&&!o&&t.some(e=>e===n)},tH=e=>{let{children:t,label:r,url:n,labelIcon:i}=e;return!!t&&!!n&&!!i&&!!r},tF=e=>{let{children:t,label:r,url:n,labelIcon:i}=e;return!t&&!!n&&!!i&&!!r},t$=(e,t)=>{t=t||e.displayName||e.name||"Component",e.displayName=t;let r=t=>{let r=tA();return r.loaded?k().createElement(tT,null,k().createElement(e,{...t,clerk:r})):null};return r.displayName=`withClerk(${t})`,r},tq=({children:e})=>{let t=tA();if("function"!=typeof e)throw Error(D);return t.loaded?k().createElement(tT,null,e(t)):null};class tV extends k().PureComponent{constructor(){super(...arguments),this.portalRef=k().createRef()}componentDidUpdate(e){(e.props.appearance!==this.props.props.appearance||e.props?.customPages?.length!==this.props.props?.customPages?.length)&&this.props.updateProps({node:this.portalRef.current,props:this.props.props})}componentDidMount(){this.portalRef.current&&this.props.mount(this.portalRef.current,this.props.props)}componentWillUnmount(){this.portalRef.current&&this.props.unmount(this.portalRef.current)}render(){return k().createElement(k().Fragment,null,k().createElement("div",{ref:this.portalRef}),this.props?.customPagesPortals?.map((e,t)=>_.createElement(e,{key:t})))}}let tW=t$(({clerk:e,...t})=>k().createElement(tV,{mount:e.mountSignIn,unmount:e.unmountSignIn,updateProps:e.__unstable__updateProps,props:t}),"SignIn"),tG=t$(({clerk:e,...t})=>k().createElement(tV,{mount:e.mountSignUp,unmount:e.unmountSignUp,updateProps:e.__unstable__updateProps,props:t}),"SignUp");function tJ({children:e}){return tU("Clerk: <UserProfile.Page /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`."),k().createElement(k().Fragment,null,e)}function tY({children:e}){return tU("Clerk: <UserProfile.Link /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`."),k().createElement(k().Fragment,null,e)}let tX=Object.assign(t$(({clerk:e,...t})=>{let{customPages:r,customPagesPortals:n}=tL(t.children);return k().createElement(tV,{mount:e.mountUserProfile,unmount:e.unmountUserProfile,updateProps:e.__unstable__updateProps,props:{...t,customPages:r},customPagesPortals:n})},"UserProfile"),{Page:tJ,Link:tY}),tQ=Object.assign(t$(({clerk:e,...t})=>{let{customPages:r,customPagesPortals:n}=tL(t.children),i=Object.assign(t.userProfileProps||{},{customPages:r});return k().createElement(tV,{mount:e.mountUserButton,unmount:e.unmountUserButton,updateProps:e.__unstable__updateProps,props:{...t,userProfileProps:i},customPagesPortals:n})},"UserButton"),{UserProfilePage:tJ,UserProfileLink:tY});function tZ({children:e}){return tU("Clerk: <OrganizationProfile.Page /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`."),k().createElement(k().Fragment,null,e)}function t0({children:e}){return tU("Clerk: <OrganizationProfile.Link /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`."),k().createElement(k().Fragment,null,e)}let t1=Object.assign(t$(({clerk:e,...t})=>{let{customPages:r,customPagesPortals:n}=tK(t.children);return k().createElement(tV,{mount:e.mountOrganizationProfile,unmount:e.unmountOrganizationProfile,updateProps:e.__unstable__updateProps,props:{...t,customPages:r},customPagesPortals:n})},"OrganizationProfile"),{Page:tZ,Link:t0}),t2=t$(({clerk:e,...t})=>k().createElement(tV,{mount:e.mountCreateOrganization,unmount:e.unmountCreateOrganization,updateProps:e.__unstable__updateProps,props:t}),"CreateOrganization"),t8=Object.assign(t$(({clerk:e,...t})=>{let{customPages:r,customPagesPortals:n}=tK(t.children),i=Object.assign(t.organizationProfileProps||{},{customPages:r});return k().createElement(tV,{mount:e.mountOrganizationSwitcher,unmount:e.unmountOrganizationSwitcher,updateProps:e.__unstable__updateProps,props:{...t,organizationProfileProps:i},customPagesPortals:n})},"OrganizationSwitcher"),{OrganizationProfilePage:tZ,OrganizationProfileLink:t0}),t3=t$(({clerk:e,...t})=>k().createElement(tV,{mount:e.mountOrganizationList,unmount:e.unmountOrganizationList,updateProps:e.__unstable__updateProps,props:t}),"OrganizationList"),t4=e=>new Promise(t=>{e.loaded&&t(),e.addOnLoaded(t)}),t5=e=>async t=>(await t4(e),e.session)?e.session.getToken(t):null,t6=e=>async(...t)=>(await t4(e),e.signOut(...t)),t7=()=>{let{sessionId:e,userId:t,actor:r,orgId:n,orgRole:i,orgSlug:o,orgPermissions:a}=tk(),s=tA(),l=(0,_.useCallback)(t5(s),[s]),u=(0,_.useCallback)(t6(s),[s]),c=(0,_.useCallback)(e=>{if(!e?.permission&&!e?.role)throw Error('Clerk: Missing parameters. `has` from `useAuth` requires a permission or role key to be passed. Example usage: `has({permission: "org:posts:edit"`');return!!n&&!!t&&!!i&&!!a&&(e.permission?a.includes(e.permission):!!e.role&&i===e.role)},[n,i,t,a]);if(void 0===e&&void 0===t)return{isLoaded:!1,isSignedIn:void 0,sessionId:e,userId:t,actor:void 0,orgId:void 0,orgRole:void 0,orgSlug:void 0,has:void 0,signOut:u,getToken:l};if(null===e&&null===t)return{isLoaded:!0,isSignedIn:!1,sessionId:e,userId:t,actor:null,orgId:null,orgRole:null,orgSlug:null,has:()=>!1,signOut:u,getToken:l};if(e&&t&&n&&i)return{isLoaded:!0,isSignedIn:!0,sessionId:e,userId:t,actor:r||null,orgId:n,orgRole:i,orgSlug:o||null,has:c,signOut:u,getToken:l};if(e&&t&&!n)return{isLoaded:!0,isSignedIn:!0,sessionId:e,userId:t,actor:r||null,orgId:null,orgRole:null,orgSlug:null,has:()=>!1,signOut:u,getToken:l};throw Error("Clerk: Invalid state. Feel free to submit a bug or reach out to support here: https://clerk.com/support")},t9=({children:e})=>{let{userId:t}=tk();return t?k().createElement(k().Fragment,null,e):null},re=({children:e})=>{let{userId:t}=tk();return null===t?k().createElement(k().Fragment,null,e):null},rt=({children:e})=>tA().loaded?k().createElement(tT,null,e):null,rr=({children:e})=>tA().loaded?null:k().createElement(k().Fragment,null,e),rn=({children:e,fallback:t,...r})=>{let{isLoaded:n,has:i,userId:o}=t7();if(!n)return null;let a=k().createElement(k().Fragment,null,t??null),s=k().createElement(k().Fragment,null,e);return o?"function"==typeof r.condition?r.condition(i)?s:a:r.role||r.permission?i(r)?s:a:s:a},ri=t$(({clerk:e,...t})=>{let{client:r,session:n}=e,{__unstable__environment:i}=e,o=r.activeSessions&&r.activeSessions.length>0;return k().useEffect(()=>{if(null===n&&o&&i){let{afterSignOutOneUrl:t}=i.displayConfig;e.navigate(t)}else e.redirectToSignIn(t)},[]),null},"RedirectToSignIn"),ro=t$(({clerk:e,...t})=>(k().useEffect(()=>{e.redirectToSignUp(t)},[]),null),"RedirectToSignUp"),ra=t$(({clerk:e})=>(k().useEffect(()=>{e.redirectToUserProfile()},[]),null),"RedirectToUserProfile"),rs=t$(({clerk:e})=>(k().useEffect(()=>{e.redirectToOrganizationProfile()},[]),null),"RedirectToOrganizationProfile"),rl=t$(({clerk:e})=>(k().useEffect(()=>{e.redirectToCreateOrganization()},[]),null),"RedirectToCreateOrganization"),ru=t$(({clerk:e,...t})=>(k().useEffect(()=>{e.handleRedirectCallback(t)},[]),null),"AuthenticateWithRedirectCallback"),rc=({children:e})=>{let t=tu();return k().createElement(k().Fragment,{key:t?t.id:"no-users"},e)},rd=(e,t)=>{t=t||e.displayName||e.name||"Component",e.displayName=t;let r=t=>{let r=to();return r?k().createElement(e,{...t,user:r}):null};return r.displayName=`withUser(${t})`,r},rh=({children:e})=>{let t=to();if("function"!=typeof e)throw Error(D);return t?k().createElement(k().Fragment,null,e(t)):null},rf=(e,t)=>{t=t||e.displayName||e.name||"Component",e.displayName=t;let r=t=>{let r=tu();return r?k().createElement(e,{...t,session:r}):null};return r.displayName=`withSession(${t})`,r},rp=({children:e})=>{let t=tu();if("function"!=typeof e)throw Error(D);return t?k().createElement(k().Fragment,null,e(t)):null},rg=e=>t=>{try{return k().Children.only(e)}catch(e){throw Error(z(t))}},ry=(e,t)=>(e||(e=t),"string"==typeof e&&(e=k().createElement("button",null,e)),e),rm=e=>(...t)=>{if(e&&"function"==typeof e)return e(...t)},rb=t$(({clerk:e,children:t,...r})=>{let{afterSignInUrl:n,afterSignUpUrl:i,redirectUrl:o,mode:a,...s}=r,l=rg(t=ry(t,"Sign in"))("SignInButton"),u=()=>{let t={afterSignInUrl:n,afterSignUpUrl:i,redirectUrl:o};return"modal"===a?e.openSignIn(t):e.redirectToSignIn(t)},c=async e=>(await rm(l.props.onClick)(e),u()),d={...s,onClick:c};return k().cloneElement(l,d)},"SignInButton"),rv=t$(({clerk:e,children:t,...r})=>{let{afterSignInUrl:n,afterSignUpUrl:i,redirectUrl:o,mode:a,unsafeMetadata:s,...l}=r,u=rg(t=ry(t,"Sign up"))("SignUpButton"),c=()=>{let t={afterSignInUrl:n,afterSignUpUrl:i,redirectUrl:o,unsafeMetadata:s};return"modal"===a?e.openSignUp(t):e.redirectToSignUp(t)},d=async e=>(await rm(u.props.onClick)(e),c()),h={...l,onClick:d};return k().cloneElement(u,h)},"SignUpButton"),rw=t$(({clerk:e,children:t,...r})=>{let{signOutCallback:n,signOutOptions:i,...o}=r,a=rg(t=ry(t,"Sign out"))("SignOutButton"),s=()=>e.signOut(n,i),l=async e=>(await rm(a.props.onClick)(e),s()),u={...o,onClick:l};return k().cloneElement(a,u)},"SignOutButton"),rS=t$(({clerk:e,children:t,...r})=>{let{redirectUrl:n,...i}=r,o=rg(t=ry(t,"Sign in with Metamask"))("SignInWithMetamaskButton"),a=async()=>{(async function(){await e.authenticateWithMetamask({redirectUrl:n})})()},s=async e=>(await rm(o.props.onClick)(e),a()),l={...i,onClick:s};return k().cloneElement(o,l)},"SignInWithMetamask");function r_(){let e=to();return void 0===e?{isLoaded:!1,isSignedIn:void 0,user:void 0}:null===e?{isLoaded:!0,isSignedIn:!1,user:null}:{isLoaded:!0,isSignedIn:!0,user:e}}let rk=()=>{let e=tu();return void 0===e?{isLoaded:!1,isSignedIn:void 0,session:void 0}:null===e?{isLoaded:!0,isSignedIn:!1,session:null}:{isLoaded:!0,isSignedIn:!0,session:e}},rE=()=>tA(),rA=()=>{let e=tA(),t=ts();return t?{isLoaded:!0,signIn:t.signIn,setSession:e.setSession,setActive:e.setActive}:{isLoaded:!1,signIn:void 0,setSession:void 0,setActive:void 0}},rP=()=>{let e=tA(),t=ts();return t?{isLoaded:!0,signUp:t.signUp,setSession:e.setSession,setActive:e.setActive}:{isLoaded:!1,signUp:void 0,setSession:void 0,setActive:void 0}},rC=()=>{let e=tA(),t=ts();return t?{isLoaded:!0,sessions:t.sessions,setSession:e.setSession,setActive:e.setActive}:{isLoaded:!1,sessions:void 0,setSession:void 0,setActive:void 0}};function rx(e){x("useMagicLink","Use `useEmailLink` instead.");let{startMagicLinkFlow:t,cancelMagicLinkFlow:r}=k().useMemo(()=>e.createMagicLinkFlow(),[e]);return k().useEffect(()=>r,[]),{startMagicLinkFlow:t,cancelMagicLinkFlow:r}}function rO(e){let{startEmailLinkFlow:t,cancelEmailLinkFlow:r}=k().useMemo(()=>e.createEmailLinkFlow(),[e]);return k().useEffect(()=>r,[]),{startEmailLinkFlow:t,cancelEmailLinkFlow:r}}},36324:()=>{},1024:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ClientClerkProvider:()=>d});var n=r(30290),i=r(14767),o=r(3729),a=r.n(o),s=r(80145);let l=a().useEffect,u=e=>({...e,frontendApi:e.frontendApi||process.env.NEXT_PUBLIC_CLERK_FRONTEND_API||"",publishableKey:e.publishableKey||"pk_test_aGFybWxlc3Mtam9leS0zMi5jbGVyay5hY2NvdW50cy5kZXYk",clerkJSUrl:e.clerkJSUrl||process.env.NEXT_PUBLIC_CLERK_JS,clerkJSVersion:e.clerkJSVersion||process.env.NEXT_PUBLIC_CLERK_JS_VERSION,proxyUrl:e.proxyUrl||process.env.NEXT_PUBLIC_CLERK_PROXY_URL||"",domain:e.domain||process.env.NEXT_PUBLIC_CLERK_DOMAIN||"",isSatellite:e.isSatellite||"true"===process.env.NEXT_PUBLIC_CLERK_IS_SATELLITE,signInUrl:e.signInUrl||process.env.NEXT_PUBLIC_CLERK_SIGN_IN_URL||"",signUpUrl:e.signUpUrl||process.env.NEXT_PUBLIC_CLERK_SIGN_UP_URL||"",afterSignInUrl:e.afterSignInUrl||process.env.NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL||"",afterSignUpUrl:e.afterSignUpUrl||process.env.NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL||"",sdkMetadata:{name:"@clerk/nextjs",version:"4.29.7"}}),c=()=>{let{push:e}=(0,i.useRouter)(),[t,r]=(0,o.useTransition)(),n=(0,o.useRef)(),a=(0,o.useRef)([]);return n.current||(n.current=(t,n)=>new Promise(i=>{a.current.push(i),r(()=>{e(t,n)})})),(0,o.useEffect)(()=>{t||(a?.current?.length&&a.current.forEach(e=>e()),a.current=[])},[t]),n.current},d=e=>{let{__unstable_invokeMiddlewareOnAuthStateChange:t=!0}=e,r=(0,i.useRouter)(),o=c();l(()=>{window.__unstable__onBeforeSetActive=()=>{t&&(r.refresh(),r.push(window.location.href))},window.__unstable__onAfterSetActive=()=>{r.refresh()}},[]);let d=u({...e,navigate:o});return a().createElement(s.f,{options:d},a().createElement(n.El,{...d}))}},80145:(e,t,r)=>{"use strict";r.d(t,{f:()=>s,k:()=>a});var n=r(3729),i=r.n(n);let o=i().createContext(void 0);o.displayName="ClerkNextOptionsCtx";let a=()=>i().useContext(o).value,s=e=>{let{children:t,options:r}=e;return i().createElement(o.Provider,{value:{value:r}},t)}},65102:(e,t,r)=>{"use strict";r.r(t),r.d(t,{AuthenticateWithRedirectCallback:()=>n.vn,ClerkLoaded:()=>n.a7,ClerkLoading:()=>n.qI,MultisessionAppSupport:()=>n.KM,Protect:()=>n.Cv,RedirectToCreateOrganization:()=>n.gM,RedirectToOrganizationProfile:()=>n.yB,RedirectToSignIn:()=>n.N1,RedirectToSignUp:()=>n.C2,RedirectToUserProfile:()=>n.sO,SignedIn:()=>n.CH,SignedOut:()=>n.tj});var n=r(30290)},22315:(e,t,r)=>{"use strict";r.r(t),r.d(t,{EmailLinkErrorCode:()=>n._L,MagicLinkErrorCode:()=>n.X1,WithClerk:()=>n._E,WithSession:()=>n.CJ,WithUser:()=>n.Gi,isClerkAPIResponseError:()=>n.kD,isEmailLinkError:()=>n.G1,isKnownError:()=>n.sZ,isMagicLinkError:()=>n.V9,isMetamaskError:()=>n.ZC,useAuth:()=>n.aC,useClerk:()=>n.ll,useEmailLink:()=>n.E2,useMagicLink:()=>n.jS,useOrganization:()=>n.o8,useOrganizationList:()=>n.eW,useOrganizations:()=>n.qi,useSession:()=>n.kP,useSessionList:()=>n.xo,useSignIn:()=>n.zq,useSignUp:()=>n.QS,useUser:()=>n.aF,withClerk:()=>n.r0,withSession:()=>n.NA,withUser:()=>n.ns});var n=r(30290)},34910:(e,t,r)=>{"use strict";r.r(t),r.d(t,{CreateOrganization:()=>n.Gp,OrganizationList:()=>n.Bg,OrganizationProfile:()=>n.A,OrganizationSwitcher:()=>n.Li,SignIn:()=>s,SignInButton:()=>n.$d,SignInWithMetamaskButton:()=>n.qu,SignOutButton:()=>n.AM,SignUp:()=>l,SignUpButton:()=>n.gX,UserButton:()=>n.l8,UserProfile:()=>n.Iw});var n=r(30290),i=r(3729),o=r.n(i),a=r(80145);let s=e=>{let{signInUrl:t}=(0,a.k)();return t?o().createElement(n.cL,{routing:"path",path:t,...e}):o().createElement(n.cL,{...e})},l=e=>{let{signUpUrl:t}=(0,a.k)();return t?o().createElement(n.Mo,{routing:"path",path:t,...e}):o().createElement(n.Mo,{...e})}},88928:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return o}});let n=r(71870),i=r(19847);function o(e,t){return(0,i.normalizePathTrailingSlash)((0,n.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},13664:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"callServer",{enumerable:!0,get:function(){return i}});let n=r(2583);async function i(e,t){let r=(0,n.getServerActionDispatcher)();if(!r)throw Error("Invariant: missing action dispatcher.");return new Promise((n,i)=>{r({actionId:e,actionArgs:t,resolve:n,reject:i})})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},23371:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return a}});let n=r(3729),i=r(81202),o="next-route-announcer";function a(e){let{tree:t}=e,[r,a]=(0,n.useState)(null);(0,n.useEffect)(()=>(a(function(){var e;let t=document.getElementsByName(o)[0];if(null==t?void 0:null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(o);e.style.cssText="position:absolute";let t=document.createElement("div");return t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",e.attachShadow({mode:"open"}).appendChild(t),document.body.appendChild(e),t}}()),()=>{let e=document.getElementsByTagName(o)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}),[]);let[s,l]=(0,n.useState)(""),u=(0,n.useRef)();return(0,n.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==u.current&&u.current!==e&&l(e),u.current=e},[t]),r?(0,i.createPortal)(s,r):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},15048:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RSC_HEADER:function(){return r},ACTION:function(){return n},NEXT_ROUTER_STATE_TREE:function(){return i},NEXT_ROUTER_PREFETCH_HEADER:function(){return o},NEXT_URL:function(){return a},RSC_CONTENT_TYPE_HEADER:function(){return s},RSC_VARY_HEADER:function(){return l},FLIGHT_PARAMETERS:function(){return u},NEXT_RSC_UNION_QUERY:function(){return c},NEXT_DID_POSTPONE_HEADER:function(){return d}});let r="RSC",n="Next-Action",i="Next-Router-State-Tree",o="Next-Router-Prefetch",a="Next-Url",s="text/x-component",l=r+", "+i+", "+o+", "+a,u=[[r],[i],[o]],c="_rsc",d="x-nextjs-postponed";("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2583:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getServerActionDispatcher:function(){return E},urlToUrlWithoutFlightMarker:function(){return P},createEmptyCacheNode:function(){return O},default:function(){return j}});let n=r(17824),i=r(95344),o=n._(r(3729)),a=r(46860),s=r(8085),l=r(47475),u=r(78486),c=r(14954),d=r(26840),h=r(87995),f=r(56338),p=r(88928),g=r(23371),y=r(87046),m=r(7550),b=r(63664),v=r(15048),w=r(22874),S=r(96411),_=null,k=null;function E(){return k}let A={};function P(e){let t=new URL(e,location.origin);return t.searchParams.delete(v.NEXT_RSC_UNION_QUERY),t}function C(e){return e.origin!==window.location.origin}function x(e){let{appRouterState:t,sync:r}=e;return(0,o.useInsertionEffect)(()=>{let{tree:e,pushRef:n,canonicalUrl:i}=t,o={...n.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:e};n.pendingPush&&(0,l.createHrefFromUrl)(new URL(window.location.href))!==i?(n.pendingPush=!1,window.history.pushState(o,"",i)):window.history.replaceState(o,"",i),r(t)},[t,r]),null}function O(){return{lazyData:null,rsc:null,prefetchRsc:null,parallelRoutes:new Map}}function R(e){null==e&&(e={});let t=window.history.state,r=null==t?void 0:t.__NA;r&&(e.__NA=r);let n=null==t?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;return n&&(e.__PRIVATE_NEXTJS_INTERNALS_TREE=n),e}function T(e){let{headCacheNode:t}=e,r=null!==t?t.head:null,n=null!==t?t.prefetchHead:null,i=null!==n?n:r;return(0,o.useDeferredValue)(r,i)}function B(e){let t,{buildId:r,initialHead:n,initialTree:l,initialCanonicalUrl:d,initialSeedData:v,assetPrefix:E,missingSlots:P}=e,O=(0,o.useMemo)(()=>(0,h.createInitialRouterState)({buildId:r,initialSeedData:v,initialCanonicalUrl:d,initialTree:l,initialParallelRoutes:_,isServer:!0,location:null,initialHead:n}),[r,v,d,l,n]),[B,j,U]=(0,c.useReducerWithReduxDevtools)(O);(0,o.useEffect)(()=>{_=null},[]);let{canonicalUrl:N}=(0,c.useUnwrapState)(B),{searchParams:I,pathname:M}=(0,o.useMemo)(()=>{let e=new URL(N,"http://n");return{searchParams:e.searchParams,pathname:(0,S.hasBasePath)(e.pathname)?(0,w.removeBasePath)(e.pathname):e.pathname}},[N]),L=(0,o.useCallback)((e,t,r)=>{(0,o.startTransition)(()=>{j({type:s.ACTION_SERVER_PATCH,flightData:t,previousTree:e,overrideCanonicalUrl:r})})},[j]),K=(0,o.useCallback)((e,t,r)=>{let n=new URL((0,p.addBasePath)(e),location.href);return j({type:s.ACTION_NAVIGATE,url:n,isExternalUrl:C(n),locationSearch:location.search,shouldScroll:null==r||r,navigateType:t})},[j]);k=(0,o.useCallback)(e=>{(0,o.startTransition)(()=>{j({...e,type:s.ACTION_SERVER_ACTION})})},[j]);let D=(0,o.useMemo)(()=>({back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{if((0,f.isBot)(window.navigator.userAgent))return;let r=new URL((0,p.addBasePath)(e),window.location.href);C(r)||(0,o.startTransition)(()=>{var e;j({type:s.ACTION_PREFETCH,url:r,kind:null!=(e=null==t?void 0:t.kind)?e:s.PrefetchKind.FULL})})},replace:(e,t)=>{void 0===t&&(t={}),(0,o.startTransition)(()=>{var r;K(e,"replace",null==(r=t.scroll)||r)})},push:(e,t)=>{void 0===t&&(t={}),(0,o.startTransition)(()=>{var r;K(e,"push",null==(r=t.scroll)||r)})},refresh:()=>{(0,o.startTransition)(()=>{j({type:s.ACTION_REFRESH,origin:window.location.origin})})},fastRefresh:()=>{throw Error("fastRefresh can only be used in development mode. Please use refresh instead.")}}),[j,K]);(0,o.useEffect)(()=>{window.next&&(window.next.router=D)},[D]),(0,o.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE)&&j({type:s.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE})}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[j]);let{pushRef:z}=(0,c.useUnwrapState)(B);if(z.mpaNavigation){if(A.pendingMpaPath!==N){let e=window.location;z.pendingPush?e.assign(N):e.replace(N),A.pendingMpaPath=N}(0,o.use)((0,b.createInfinitePromise)())}(0,o.useEffect)(()=>{let e=window.history.pushState.bind(window.history),t=window.history.replaceState.bind(window.history),r=e=>{let t=window.location.href;(0,o.startTransition)(()=>{j({type:s.ACTION_RESTORE,url:new URL(null!=e?e:t,t),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE})})};window.history.pushState=function(t,n,i){return(null==t?void 0:t.__NA)||(null==t?void 0:t._N)||(t=R(t),i&&r(i)),e(t,n,i)},window.history.replaceState=function(e,n,i){return(null==e?void 0:e.__NA)||(null==e?void 0:e._N)||(e=R(e),i&&r(i)),t(e,n,i)};let n=e=>{let{state:t}=e;if(t){if(!t.__NA){window.location.reload();return}(0,o.startTransition)(()=>{j({type:s.ACTION_RESTORE,url:new URL(window.location.href),tree:t.__PRIVATE_NEXTJS_INTERNALS_TREE})})}};return window.addEventListener("popstate",n),()=>{window.history.pushState=e,window.history.replaceState=t,window.removeEventListener("popstate",n)}},[j]);let{cache:H,tree:F,nextUrl:$,focusAndScrollRef:q}=(0,c.useUnwrapState)(B),V=(0,o.useMemo)(()=>(0,m.findHeadInCache)(H,F[1]),[H,F]);if(null!==V){let[e,r]=V;t=(0,i.jsx)(T,{headCacheNode:e},r)}else t=null;let W=(0,i.jsxs)(y.RedirectBoundary,{children:[t,H.rsc,(0,i.jsx)(g.AppRouterAnnouncer,{tree:F})]});return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(x,{appRouterState:(0,c.useUnwrapState)(B),sync:U}),(0,i.jsx)(u.PathnameContext.Provider,{value:M,children:(0,i.jsx)(u.SearchParamsContext.Provider,{value:I,children:(0,i.jsx)(a.GlobalLayoutRouterContext.Provider,{value:{buildId:r,changeByServerResponse:L,tree:F,focusAndScrollRef:q,nextUrl:$},children:(0,i.jsx)(a.AppRouterContext.Provider,{value:D,children:(0,i.jsx)(a.LayoutRouterContext.Provider,{value:{childNodes:H.parallelRoutes,tree:F,url:N},children:W})})})})})]})}function j(e){let{globalErrorComponent:t,...r}=e;return(0,i.jsx)(d.ErrorBoundary,{errorComponent:t,children:(0,i.jsx)(B,{...r})})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},64586:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"bailoutToClientRendering",{enumerable:!0,get:function(){return o}});let n=r(63689),i=r(94749);function o(e){let t=i.staticGenerationAsyncStorage.getStore();if((null==t||!t.forceStatic)&&(null==t?void 0:t.isStaticGeneration))throw new n.BailoutToCSRError(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},18446:(e,t,r)=>{"use strict";function n(e){}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clientHookInServerComponentError",{enumerable:!0,get:function(){return n}}),r(39694),r(3729),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},26840:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ErrorBoundaryHandler:function(){return c},GlobalError:function(){return d},default:function(){return h},ErrorBoundary:function(){return f}});let n=r(39694),i=r(95344),o=n._(r(3729)),a=r(14767),s=r(47796),l={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},text:{fontSize:"14px",fontWeight:400,lineHeight:"28px",margin:"0 8px"}};function u(e){let{error:t}=e;if("function"==typeof fetch.__nextGetStaticStore){var r;let e=null==(r=fetch.__nextGetStaticStore())?void 0:r.getStore();if((null==e?void 0:e.isRevalidate)||(null==e?void 0:e.isStaticGeneration))throw console.error(t),t}return null}class c extends o.default.Component{static getDerivedStateFromError(e){if((0,s.isNextRouterError)(e))throw e;return{error:e}}static getDerivedStateFromProps(e,t){return e.pathname!==t.previousPathname&&t.error?{error:null,previousPathname:e.pathname}:{error:t.error,previousPathname:e.pathname}}render(){return this.state.error?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(u,{error:this.state.error}),this.props.errorStyles,this.props.errorScripts,(0,i.jsx)(this.props.errorComponent,{error:this.state.error,reset:this.reset})]}):this.props.children}constructor(e){super(e),this.reset=()=>{this.setState({error:null})},this.state={error:null,previousPathname:this.props.pathname}}}function d(e){let{error:t}=e,r=null==t?void 0:t.digest;return(0,i.jsxs)("html",{id:"__next_error__",children:[(0,i.jsx)("head",{}),(0,i.jsxs)("body",{children:[(0,i.jsx)(u,{error:t}),(0,i.jsx)("div",{style:l.error,children:(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{style:l.text,children:"Application error: a "+(r?"server":"client")+"-side exception has occurred (see the "+(r?"server logs":"browser console")+" for more information)."}),r?(0,i.jsx)("p",{style:l.text,children:"Digest: "+r}):null]})})]})]})}let h=d;function f(e){let{errorComponent:t,errorStyles:r,errorScripts:n,children:o}=e,s=(0,a.usePathname)();return t?(0,i.jsx)(c,{pathname:s,errorComponent:t,errorStyles:r,errorScripts:n,children:o}):(0,i.jsx)(i.Fragment,{children:o})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3082:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DynamicServerError:function(){return n},isDynamicServerError:function(){return i}});let r="DYNAMIC_SERVER_USAGE";class n extends Error{constructor(e){super("Dynamic server usage: "+e),this.description=e,this.digest=r}}function i(e){return"object"==typeof e&&null!==e&&"digest"in e&&"string"==typeof e.digest&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},63664:(e,t)=>{"use strict";let r;function n(){return r||(r=new Promise(()=>{})),r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createInfinitePromise",{enumerable:!0,get:function(){return n}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},47796:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return o}});let n=r(70226),i=r(72792);function o(e){return e&&e.digest&&((0,i.isRedirectError)(e)||(0,n.isNotFoundError)(e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},38771:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return _}}),r(39694);let n=r(17824),i=r(95344),o=n._(r(3729));r(81202);let a=r(46860),s=r(47013),l=r(63664),u=r(26840),c=r(24287),d=r(51586),h=r(87046),f=r(13225),p=r(13717),g=r(75325),y=["bottom","height","left","right","top","width","x","y"];function m(e,t){let r=e.getBoundingClientRect();return r.top>=0&&r.top<=t}class b extends o.default.Component{componentDidMount(){this.handlePotentialScroll()}componentDidUpdate(){this.props.focusAndScrollRef.apply&&this.handlePotentialScroll()}render(){return this.props.children}constructor(...e){super(...e),this.handlePotentialScroll=()=>{let{focusAndScrollRef:e,segmentPath:t}=this.props;if(e.apply){if(0!==e.segmentPaths.length&&!e.segmentPaths.some(e=>t.every((t,r)=>(0,c.matchSegment)(t,e[r]))))return;let r=null,n=e.hashFragment;if(n&&(r=function(e){var t;return"top"===e?document.body:null!=(t=document.getElementById(e))?t:document.getElementsByName(e)[0]}(n)),!r&&(r=null),!(r instanceof Element))return;for(;!(r instanceof HTMLElement)||function(e){if(["sticky","fixed"].includes(getComputedStyle(e).position))return!0;let t=e.getBoundingClientRect();return y.every(e=>0===t[e])}(r);){if(null===r.nextElementSibling)return;r=r.nextElementSibling}e.apply=!1,e.hashFragment=null,e.segmentPaths=[],(0,d.handleSmoothScroll)(()=>{if(n){r.scrollIntoView();return}let e=document.documentElement,t=e.clientHeight;!m(r,t)&&(e.scrollTop=0,m(r,t)||r.scrollIntoView())},{dontForceLayout:!0,onlyHashChange:e.onlyHashChange}),e.onlyHashChange=!1,r.focus()}}}}function v(e){let{segmentPath:t,children:r}=e,n=(0,o.useContext)(a.GlobalLayoutRouterContext);if(!n)throw Error("invariant global layout router not mounted");return(0,i.jsx)(b,{segmentPath:t,focusAndScrollRef:n.focusAndScrollRef,children:r})}function w(e){let{parallelRouterKey:t,url:r,childNodes:n,segmentPath:u,tree:d,cacheKey:h}=e,f=(0,o.useContext)(a.GlobalLayoutRouterContext);if(!f)throw Error("invariant global layout router not mounted");let{buildId:p,changeByServerResponse:g,tree:y}=f,m=n.get(h);if(void 0===m){let e={lazyData:null,rsc:null,prefetchRsc:null,head:null,parallelRoutes:new Map};m=e,n.set(h,e)}let b=null!==m.prefetchRsc?m.prefetchRsc:m.rsc,v=(0,o.useDeferredValue)(m.rsc,b),w="object"==typeof v&&null!==v&&"function"==typeof v.then?(0,o.use)(v):v;if(!w){let e=m.lazyData;if(null===e){let t=function e(t,r){if(t){let[n,i]=t,o=2===t.length;if((0,c.matchSegment)(r[0],n)&&r[1].hasOwnProperty(i)){if(o){let t=e(void 0,r[1][i]);return[r[0],{...r[1],[i]:[t[0],t[1],t[2],"refetch"]}]}return[r[0],{...r[1],[i]:e(t.slice(2),r[1][i])}]}}return r}(["",...u],y);m.lazyData=e=(0,s.fetchServerResponse)(new URL(r,location.origin),t,f.nextUrl,p)}let[t,n]=(0,o.use)(e);m.lazyData=null,setTimeout(()=>{(0,o.startTransition)(()=>{g(y,t,n)})}),(0,o.use)((0,l.createInfinitePromise)())}return(0,i.jsx)(a.LayoutRouterContext.Provider,{value:{tree:d[1][t],childNodes:m.parallelRoutes,url:r},children:w})}function S(e){let{children:t,loading:r,loadingStyles:n,loadingScripts:a,hasLoading:s}=e;return s?(0,i.jsx)(o.Suspense,{fallback:(0,i.jsxs)(i.Fragment,{children:[n,a,r]}),children:t}):(0,i.jsx)(i.Fragment,{children:t})}function _(e){let{parallelRouterKey:t,segmentPath:r,error:n,errorStyles:s,errorScripts:l,templateStyles:c,templateScripts:d,loading:y,loadingStyles:m,loadingScripts:b,hasLoading:_,template:k,notFound:E,notFoundStyles:A,styles:P}=e,C=(0,o.useContext)(a.LayoutRouterContext);if(!C)throw Error("invariant expected layout router to be mounted");let{childNodes:x,tree:O,url:R}=C,T=x.get(t);T||(T=new Map,x.set(t,T));let B=O[1][t][0],j=(0,p.getSegmentValue)(B),U=[B];return(0,i.jsxs)(i.Fragment,{children:[P,U.map(e=>{let o=(0,p.getSegmentValue)(e),P=(0,g.createRouterCacheKey)(e);return(0,i.jsxs)(a.TemplateContext.Provider,{value:(0,i.jsx)(v,{segmentPath:r,children:(0,i.jsx)(u.ErrorBoundary,{errorComponent:n,errorStyles:s,errorScripts:l,children:(0,i.jsx)(S,{hasLoading:_,loading:y,loadingStyles:m,loadingScripts:b,children:(0,i.jsx)(f.NotFoundBoundary,{notFound:E,notFoundStyles:A,children:(0,i.jsx)(h.RedirectBoundary,{children:(0,i.jsx)(w,{parallelRouterKey:t,url:R,tree:O,childNodes:T,segmentPath:r,cacheKey:P,isActive:j===o})})})})})}),children:[c,d,k]},(0,g.createRouterCacheKey)(e,!0))})]})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},24287:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{matchSegment:function(){return i},canSegmentBeOverridden:function(){return o}});let n=r(54269),i=(e,t)=>"string"==typeof e?"string"==typeof t&&e===t:"string"!=typeof t&&e[0]===t[0]&&e[1]===t[1],o=(e,t)=>{var r;return!Array.isArray(e)&&!!Array.isArray(t)&&(null==(r=(0,n.getSegmentParam)(e))?void 0:r.param)===t[0]};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},14767:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return p},useSearchParams:function(){return g},usePathname:function(){return y},ServerInsertedHTMLContext:function(){return u.ServerInsertedHTMLContext},useServerInsertedHTML:function(){return u.useServerInsertedHTML},useRouter:function(){return m},useParams:function(){return b},useSelectedLayoutSegments:function(){return v},useSelectedLayoutSegment:function(){return w},redirect:function(){return c.redirect},permanentRedirect:function(){return c.permanentRedirect},RedirectType:function(){return c.RedirectType},notFound:function(){return d.notFound}});let n=r(3729),i=r(46860),o=r(78486),a=r(18446),s=r(13717),l=r(19457),u=r(69505),c=r(72792),d=r(70226),h=Symbol("internal for urlsearchparams readonly");function f(){return Error("ReadonlyURLSearchParams cannot be modified")}class p{[Symbol.iterator](){return this[h][Symbol.iterator]()}append(){throw f()}delete(){throw f()}set(){throw f()}sort(){throw f()}constructor(e){this[h]=e,this.entries=e.entries.bind(e),this.forEach=e.forEach.bind(e),this.get=e.get.bind(e),this.getAll=e.getAll.bind(e),this.has=e.has.bind(e),this.keys=e.keys.bind(e),this.values=e.values.bind(e),this.toString=e.toString.bind(e),this.size=e.size}}function g(){(0,a.clientHookInServerComponentError)("useSearchParams");let e=(0,n.useContext)(o.SearchParamsContext),t=(0,n.useMemo)(()=>e?new p(e):null,[e]);{let{bailoutToClientRendering:e}=r(64586);e("useSearchParams()")}return t}function y(){return(0,a.clientHookInServerComponentError)("usePathname"),(0,n.useContext)(o.PathnameContext)}function m(){(0,a.clientHookInServerComponentError)("useRouter");let e=(0,n.useContext)(i.AppRouterContext);if(null===e)throw Error("invariant expected app router to be mounted");return e}function b(){(0,a.clientHookInServerComponentError)("useParams");let e=(0,n.useContext)(i.GlobalLayoutRouterContext),t=(0,n.useContext)(o.PathParamsContext);return(0,n.useMemo)(()=>(null==e?void 0:e.tree)?function e(t,r){for(let n of(void 0===r&&(r={}),Object.values(t[1]))){let t=n[0],i=Array.isArray(t),o=i?t[1]:t;!o||o.startsWith(l.PAGE_SEGMENT_KEY)||(i&&("c"===t[2]||"oc"===t[2])?r[t[0]]=t[1].split("/"):i&&(r[t[0]]=t[1]),r=e(n,r))}return r}(e.tree):t,[null==e?void 0:e.tree,t])}function v(e){void 0===e&&(e="children"),(0,a.clientHookInServerComponentError)("useSelectedLayoutSegments");let{tree:t}=(0,n.useContext)(i.LayoutRouterContext);return function e(t,r,n,i){let o;if(void 0===n&&(n=!0),void 0===i&&(i=[]),n)o=t[1][r];else{var a;let e=t[1];o=null!=(a=e.children)?a:Object.values(e)[0]}if(!o)return i;let u=o[0],c=(0,s.getSegmentValue)(u);return!c||c.startsWith(l.PAGE_SEGMENT_KEY)?i:(i.push(c),e(o,r,!1,i))}(t,e)}function w(e){void 0===e&&(e="children"),(0,a.clientHookInServerComponentError)("useSelectedLayoutSegment");let t=v(e);return 0===t.length?null:t[0]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},13225:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"NotFoundBoundary",{enumerable:!0,get:function(){return c}});let n=r(17824),i=r(95344),o=n._(r(3729)),a=r(14767),s=r(70226);r(70837);let l=r(46860);class u extends o.default.Component{componentDidCatch(){}static getDerivedStateFromError(e){if((0,s.isNotFoundError)(e))return{notFoundTriggered:!0};throw e}static getDerivedStateFromProps(e,t){return e.pathname!==t.previousPathname&&t.notFoundTriggered?{notFoundTriggered:!1,previousPathname:e.pathname}:{notFoundTriggered:t.notFoundTriggered,previousPathname:e.pathname}}render(){return this.state.notFoundTriggered?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("meta",{name:"robots",content:"noindex"}),!1,this.props.notFoundStyles,this.props.notFound]}):this.props.children}constructor(e){super(e),this.state={notFoundTriggered:!!e.asNotFound,previousPathname:e.pathname}}}function c(e){let{notFound:t,notFoundStyles:r,asNotFound:n,children:s}=e,c=(0,a.usePathname)(),d=(0,o.useContext)(l.MissingSlotContext);return t?(0,i.jsx)(u,{pathname:c,notFound:t,notFoundStyles:r,asNotFound:n,missingSlots:d,children:s}):(0,i.jsx)(i.Fragment,{children:s})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},70226:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{notFound:function(){return n},isNotFoundError:function(){return i}});let r="NEXT_NOT_FOUND";function n(){let e=Error(r);throw e.digest=r,e}function i(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},92051:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PromiseQueue",{enumerable:!0,get:function(){return u}});let n=r(69996),i=r(67074);var o=i._("_maxConcurrency"),a=i._("_runningCount"),s=i._("_queue"),l=i._("_processNext");class u{enqueue(e){let t,r;let i=new Promise((e,n)=>{t=e,r=n}),o=async()=>{try{n._(this,a)[a]++;let r=await e();t(r)}catch(e){r(e)}finally{n._(this,a)[a]--,n._(this,l)[l]()}};return n._(this,s)[s].push({promiseFn:i,task:o}),n._(this,l)[l](),i}bump(e){let t=n._(this,s)[s].findIndex(t=>t.promiseFn===e);if(t>-1){let e=n._(this,s)[s].splice(t,1)[0];n._(this,s)[s].unshift(e),n._(this,l)[l](!0)}}constructor(e=5){Object.defineProperty(this,l,{value:c}),Object.defineProperty(this,o,{writable:!0,value:void 0}),Object.defineProperty(this,a,{writable:!0,value:void 0}),Object.defineProperty(this,s,{writable:!0,value:void 0}),n._(this,o)[o]=e,n._(this,a)[a]=0,n._(this,s)[s]=[]}}function c(e){if(void 0===e&&(e=!1),(n._(this,a)[a]<n._(this,o)[o]||e)&&n._(this,s)[s].length>0){var t;null==(t=n._(this,s)[s].shift())||t.task()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},87046:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectErrorBoundary:function(){return u},RedirectBoundary:function(){return c}});let n=r(17824),i=r(95344),o=n._(r(3729)),a=r(14767),s=r(72792);function l(e){let{redirect:t,reset:r,redirectType:n}=e,i=(0,a.useRouter)();return(0,o.useEffect)(()=>{o.default.startTransition(()=>{n===s.RedirectType.push?i.push(t,{}):i.replace(t,{}),r()})},[t,n,r,i]),null}class u extends o.default.Component{static getDerivedStateFromError(e){if((0,s.isRedirectError)(e))return{redirect:(0,s.getURLFromRedirectError)(e),redirectType:(0,s.getRedirectTypeFromError)(e)};throw e}render(){let{redirect:e,redirectType:t}=this.state;return null!==e&&null!==t?(0,i.jsx)(l,{redirect:e,redirectType:t,reset:()=>this.setState({redirect:null})}):this.props.children}constructor(e){super(e),this.state={redirect:null,redirectType:null}}}function c(e){let{children:t}=e,r=(0,a.useRouter)();return(0,i.jsx)(u,{router:r,children:t})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},17761:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},72792:(e,t,r)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectType:function(){return n},getRedirectError:function(){return l},redirect:function(){return u},permanentRedirect:function(){return c},isRedirectError:function(){return d},getURLFromRedirectError:function(){return h},getRedirectTypeFromError:function(){return f},getRedirectStatusCodeFromError:function(){return p}});let i=r(55403),o=r(47849),a=r(17761),s="NEXT_REDIRECT";function l(e,t,r){void 0===r&&(r=a.RedirectStatusCode.TemporaryRedirect);let n=Error(s);n.digest=s+";"+t+";"+e+";"+r+";";let o=i.requestAsyncStorage.getStore();return o&&(n.mutableCookies=o.mutableCookies),n}function u(e,t){void 0===t&&(t="replace");let r=o.actionAsyncStorage.getStore();throw l(e,t,(null==r?void 0:r.isAction)?a.RedirectStatusCode.SeeOther:a.RedirectStatusCode.TemporaryRedirect)}function c(e,t){void 0===t&&(t="replace");let r=o.actionAsyncStorage.getStore();throw l(e,t,(null==r?void 0:r.isAction)?a.RedirectStatusCode.SeeOther:a.RedirectStatusCode.PermanentRedirect)}function d(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r,n,i]=e.digest.split(";",4),o=Number(i);return t===s&&("replace"===r||"push"===r)&&"string"==typeof n&&!isNaN(o)&&o in a.RedirectStatusCode}function h(e){return d(e)?e.digest.split(";",3)[2]:null}function f(e){if(!d(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function p(e){if(!d(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(n||(n={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9295:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}});let n=r(17824),i=r(95344),o=n._(r(3729)),a=r(46860);function s(){let e=(0,o.useContext)(a.TemplateContext);return(0,i.jsx)(i.Fragment,{children:e})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},69543:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyFlightData",{enumerable:!0,get:function(){return o}});let n=r(67234),i=r(56408);function o(e,t,r,o){void 0===o&&(o=!1);let[a,s,l]=r.slice(-3);if(null===s)return!1;if(3===r.length){let r=s[2];t.rsc=r,t.prefetchRsc=null,(0,n.fillLazyItemsTillLeafWithHead)(t,e,a,s,l,o)}else t.rsc=e.rsc,t.prefetchRsc=e.prefetchRsc,t.parallelRoutes=new Map(e.parallelRoutes),(0,i.fillCacheWithNewSubTreeData)(t,e,r,o);return!0}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},71697:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{applyRouterStatePatchToFullTree:function(){return s},applyRouterStatePatchToTreeSkipDefault:function(){return l}});let n=r(19457),i=r(24287);function o(e,t,r){void 0===r&&(r=!1);let[a,s]=e,[l,u]=t;if(!r&&l===n.DEFAULT_SEGMENT_KEY&&a!==n.DEFAULT_SEGMENT_KEY)return e;if((0,i.matchSegment)(a,l)){let t={};for(let e in s)void 0!==u[e]?t[e]=o(s[e],u[e],r):t[e]=s[e];for(let e in u)t[e]||(t[e]=u[e]);let n=[a,t];return e[2]&&(n[2]=e[2]),e[3]&&(n[3]=e[3]),e[4]&&(n[4]=e[4]),n}return t}function a(e,t,r,n){let s;void 0===n&&(n=!1);let[l,u,,,c]=t;if(1===e.length)return o(t,r,n);let[d,h]=e;if(!(0,i.matchSegment)(d,l))return null;if(2===e.length)s=o(u[h],r,n);else if(null===(s=a(e.slice(2),u[h],r,n)))return null;let f=[e[0],{...u,[h]:s}];return c&&(f[4]=!0),f}function s(e,t,r){return a(e,t,r,!0)}function l(e,t,r){return a(e,t,r,!1)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},95684:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{extractPathFromFlightRouterState:function(){return u},computeChangedPath:function(){return c}});let n=r(45767),i=r(19457),o=r(24287),a=e=>"/"===e[0]?e.slice(1):e,s=e=>"string"==typeof e?e:e[1];function l(e){return e.reduce((e,t)=>""===(t=a(t))||(0,i.isGroupSegment)(t)?e:e+"/"+t,"")||"/"}function u(e){var t;let r=Array.isArray(e[0])?e[0][1]:e[0];if(r===i.DEFAULT_SEGMENT_KEY||n.INTERCEPTION_ROUTE_MARKERS.some(e=>r.startsWith(e)))return;if(r.startsWith(i.PAGE_SEGMENT_KEY))return"";let o=[r],a=null!=(t=e[1])?t:{},s=a.children?u(a.children):void 0;if(void 0!==s)o.push(s);else for(let[e,t]of Object.entries(a)){if("children"===e)continue;let r=u(t);void 0!==r&&o.push(r)}return l(o)}function c(e,t){let r=function e(t,r){let[i,a]=t,[l,c]=r,d=s(i),h=s(l);if(n.INTERCEPTION_ROUTE_MARKERS.some(e=>d.startsWith(e)||h.startsWith(e)))return"";if(!(0,o.matchSegment)(i,l)){var f;return null!=(f=u(r))?f:""}for(let t in a)if(c[t]){let r=e(a[t],c[t]);if(null!==r)return s(l)+"/"+r}return null}(e,t);return null==r||"/"===r?r:l(r.split("/"))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},47475:(e,t)=>{"use strict";function r(e,t){return void 0===t&&(t=!0),e.pathname+e.search+(t?e.hash:"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createHrefFromUrl",{enumerable:!0,get:function(){return r}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},87995:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createInitialRouterState",{enumerable:!0,get:function(){return a}});let n=r(47475),i=r(67234),o=r(95684);function a(e){var t;let{buildId:r,initialTree:a,initialSeedData:s,initialCanonicalUrl:l,initialParallelRoutes:u,isServer:c,location:d,initialHead:h}=e,f={lazyData:null,rsc:s[2],prefetchRsc:null,parallelRoutes:c?new Map:u};return(null===u||0===u.size)&&(0,i.fillLazyItemsTillLeafWithHead)(f,void 0,a,s,h),{buildId:r,tree:a,cache:f,prefetchCache:new Map,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:{apply:!1,onlyHashChange:!1,hashFragment:null,segmentPaths:[]},canonicalUrl:d?(0,n.createHrefFromUrl)(d):l,nextUrl:null!=(t=(0,o.extractPathFromFlightRouterState)(a)||(null==d?void 0:d.pathname))?t:null}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},75325:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createRouterCacheKey",{enumerable:!0,get:function(){return i}});let n=r(19457);function i(e,t){return(void 0===t&&(t=!1),Array.isArray(e))?(e[0]+"|"+e[1]+"|"+e[2]).toLowerCase():t&&e.startsWith(n.PAGE_SEGMENT_KEY)?n.PAGE_SEGMENT_KEY:e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},47013:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fetchServerResponse",{enumerable:!0,get:function(){return c}});let n=r(15048),i=r(2583),o=r(13664),a=r(8085),s=r(65344),{createFromFetch:l}=r(82228);function u(e){return[(0,i.urlToUrlWithoutFlightMarker)(e).toString(),void 0]}async function c(e,t,r,c,d){let h={[n.RSC_HEADER]:"1",[n.NEXT_ROUTER_STATE_TREE]:encodeURIComponent(JSON.stringify(t))};d===a.PrefetchKind.AUTO&&(h[n.NEXT_ROUTER_PREFETCH_HEADER]="1"),r&&(h[n.NEXT_URL]=r);let f=(0,s.hexHash)([h[n.NEXT_ROUTER_PREFETCH_HEADER]||"0",h[n.NEXT_ROUTER_STATE_TREE],h[n.NEXT_URL]].join(","));try{let t=new URL(e);t.searchParams.set(n.NEXT_RSC_UNION_QUERY,f);let r=await fetch(t,{credentials:"same-origin",headers:h}),a=(0,i.urlToUrlWithoutFlightMarker)(r.url),s=r.redirected?a:void 0,d=r.headers.get("content-type")||"",p=!!r.headers.get(n.NEXT_DID_POSTPONE_HEADER);if(d!==n.RSC_CONTENT_TYPE_HEADER||!r.ok)return e.hash&&(a.hash=e.hash),u(a.toString());let[g,y]=await l(Promise.resolve(r),{callServer:o.callServer});if(c!==g)return u(r.url);return[y,s,p]}catch(t){return console.error("Failed to fetch RSC payload for "+e+". Falling back to browser navigation.",t),[e.toString(),void 0]}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},77676:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillCacheWithDataProperty",{enumerable:!0,get:function(){return function e(t,r,i,o){let a=i.length<=2,[s,l]=i,u=(0,n.createRouterCacheKey)(l),c=r.parallelRoutes.get(s),d=t.parallelRoutes.get(s);d&&d!==c||(d=new Map(c),t.parallelRoutes.set(s,d));let h=null==c?void 0:c.get(u),f=d.get(u);if(a){f&&f.lazyData&&f!==h||d.set(u,{lazyData:o(),rsc:null,prefetchRsc:null,parallelRoutes:new Map});return}if(!f||!h){f||d.set(u,{lazyData:o(),rsc:null,prefetchRsc:null,parallelRoutes:new Map});return}return f===h&&(f={lazyData:f.lazyData,rsc:f.rsc,prefetchRsc:f.prefetchRsc,parallelRoutes:new Map(f.parallelRoutes)},d.set(u,f)),e(f,h,i.slice(2),o)}}});let n=r(75325);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},56408:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillCacheWithNewSubTreeData",{enumerable:!0,get:function(){return function e(t,r,a,s){let l=a.length<=5,[u,c]=a,d=(0,o.createRouterCacheKey)(c),h=r.parallelRoutes.get(u);if(!h)return;let f=t.parallelRoutes.get(u);f&&f!==h||(f=new Map(h),t.parallelRoutes.set(u,f));let p=h.get(d),g=f.get(d);if(l){if(!g||!g.lazyData||g===p){let e=a[3];g={lazyData:null,rsc:e[2],prefetchRsc:null,parallelRoutes:p?new Map(p.parallelRoutes):new Map},p&&(0,n.invalidateCacheByRouterState)(g,p,a[2]),(0,i.fillLazyItemsTillLeafWithHead)(g,p,a[2],e,a[4],s),f.set(d,g)}return}g&&p&&(g===p&&(g={lazyData:g.lazyData,rsc:g.rsc,prefetchRsc:g.prefetchRsc,parallelRoutes:new Map(g.parallelRoutes)},f.set(d,g)),e(g,p,a.slice(2),s))}}});let n=r(20250),i=r(67234),o=r(75325);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},67234:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function e(t,r,i,o,a,s){if(0===Object.keys(i[1]).length){t.head=a;return}for(let l in i[1]){let u;let c=i[1][l],d=c[0],h=(0,n.createRouterCacheKey)(d),f=null!==o&&void 0!==o[1][l]?o[1][l]:null;if(r){let n=r.parallelRoutes.get(l);if(n){let r,i=new Map(n),o=i.get(h);r=null!==f?{lazyData:null,rsc:f[2],prefetchRsc:null,parallelRoutes:new Map(null==o?void 0:o.parallelRoutes)}:s&&o?{lazyData:o.lazyData,rsc:o.rsc,prefetchRsc:o.prefetchRsc,parallelRoutes:new Map(o.parallelRoutes)}:{lazyData:null,rsc:null,prefetchRsc:null,parallelRoutes:new Map(null==o?void 0:o.parallelRoutes)},i.set(h,r),e(r,o,c,f||null,a,s),t.parallelRoutes.set(l,i);continue}}u=null!==f?{lazyData:null,rsc:f[2],prefetchRsc:null,parallelRoutes:new Map}:{lazyData:null,rsc:null,prefetchRsc:null,parallelRoutes:new Map};let p=t.parallelRoutes.get(l);p?p.set(h,u):t.parallelRoutes.set(l,new Map([[h,u]])),e(u,void 0,c,f,a,s)}}}});let n=r(75325);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},80696:(e,t)=>{"use strict";var r;function n(e){let{kind:t,prefetchTime:r,lastUsedTime:n}=e;return Date.now()<(null!=n?n:r)+3e4?n?"reusable":"fresh":"auto"===t&&Date.now()<r+3e5?"stale":"full"===t&&Date.now()<r+3e5?"reusable":"expired"}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{PrefetchCacheEntryStatus:function(){return r},getPrefetchEntryCacheStatus:function(){return n}}),function(e){e.fresh="fresh",e.reusable="reusable",e.expired="expired",e.stale="stale"}(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},44080:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleMutable",{enumerable:!0,get:function(){return o}});let n=r(95684);function i(e){return void 0!==e}function o(e,t){var r,o,a;let s=null==(o=t.shouldScroll)||o,l=e.nextUrl;if(i(t.patchedTree)){let r=(0,n.computeChangedPath)(e.tree,t.patchedTree);r?l=r:l||(l=e.canonicalUrl)}return{buildId:e.buildId,canonicalUrl:i(t.canonicalUrl)?t.canonicalUrl===e.canonicalUrl?e.canonicalUrl:t.canonicalUrl:e.canonicalUrl,pushRef:{pendingPush:i(t.pendingPush)?t.pendingPush:e.pushRef.pendingPush,mpaNavigation:i(t.mpaNavigation)?t.mpaNavigation:e.pushRef.mpaNavigation,preserveCustomHistoryState:i(t.preserveCustomHistoryState)?t.preserveCustomHistoryState:e.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!s&&(!!i(null==t?void 0:t.scrollableSegments)||e.focusAndScrollRef.apply),onlyHashChange:!!t.hashFragment&&e.canonicalUrl.split("#",1)[0]===(null==(r=t.canonicalUrl)?void 0:r.split("#",1)[0]),hashFragment:s?t.hashFragment&&""!==t.hashFragment?decodeURIComponent(t.hashFragment.slice(1)):e.focusAndScrollRef.hashFragment:null,segmentPaths:s?null!=(a=null==t?void 0:t.scrollableSegments)?a:e.focusAndScrollRef.segmentPaths:[]},cache:t.cache?t.cache:e.cache,prefetchCache:t.prefetchCache?t.prefetchCache:e.prefetchCache,tree:i(t.patchedTree)?t.patchedTree:e.tree,nextUrl:l}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},71418:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSegmentMismatch",{enumerable:!0,get:function(){return i}});let n=r(69643);function i(e,t,r){return(0,n.handleExternalUrl)(e,{},e.canonicalUrl,!0)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},32293:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function e(t,r,i){let o=i.length<=2,[a,s]=i,l=(0,n.createRouterCacheKey)(s),u=r.parallelRoutes.get(a);if(!u)return;let c=t.parallelRoutes.get(a);if(c&&c!==u||(c=new Map(u),t.parallelRoutes.set(a,c)),o){c.delete(l);return}let d=u.get(l),h=c.get(l);h&&d&&(h===d&&(h={lazyData:h.lazyData,rsc:h.rsc,prefetchRsc:h.prefetchRsc,parallelRoutes:new Map(h.parallelRoutes)},c.set(l,h)),e(h,d,i.slice(2)))}}});let n=r(75325);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},20250:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return i}});let n=r(75325);function i(e,t,r){for(let i in r[1]){let o=r[1][i][0],a=(0,n.createRouterCacheKey)(o),s=t.parallelRoutes.get(i);if(s){let t=new Map(s);t.delete(a),e.parallelRoutes.set(i,t)}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},53694:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function e(t,r){let n=t[0],i=r[0];if(Array.isArray(n)&&Array.isArray(i)){if(n[0]!==i[0]||n[2]!==i[2])return!0}else if(n!==i)return!0;if(t[4])return!r[4];if(r[4])return!0;let o=Object.values(t[1])[0],a=Object.values(r[1])[0];return!o||!a||e(o,a)}}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},13026:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{updateCacheNodeOnNavigation:function(){return function e(t,r,s,u,c,d){let h=r[1],f=s[1],p=u[1],g=t.parallelRoutes,y=new Map(g),m={},b=null;for(let t in f){let r;let s=f[t],u=h[t],v=g.get(t),w=p[t],S=s[0],_=(0,o.createRouterCacheKey)(S),k=void 0!==u?u[0]:void 0,E=void 0!==v?v.get(_):void 0;if(null!==(r=S===n.PAGE_SEGMENT_KEY?a(s,void 0!==w?w:null,c,d):S===n.DEFAULT_SEGMENT_KEY?void 0!==u?{route:u,node:null,children:null}:a(s,void 0!==w?w:null,c,d):void 0!==k&&(0,i.matchSegment)(S,k)&&void 0!==E&&void 0!==u?null!=w?e(E,u,s,w,c,d):function(e){let t=l(e,null,null,!1);return{route:e,node:t,children:null}}(s):a(s,void 0!==w?w:null,c,d))){null===b&&(b=new Map),b.set(t,r);let e=r.node;if(null!==e){let r=new Map(v);r.set(_,e),y.set(t,r)}m[t]=r.route}else m[t]=s}if(null===b)return null;let v={lazyData:null,rsc:t.rsc,prefetchRsc:t.prefetchRsc,head:t.head,prefetchHead:t.prefetchHead,parallelRoutes:y};return{route:function(e,t){let r=[e[0],t];return 2 in e&&(r[2]=e[2]),3 in e&&(r[3]=e[3]),4 in e&&(r[4]=e[4]),r}(s,m),node:v,children:b}}},listenForDynamicRequest:function(){return s},abortTask:function(){return u},updateCacheNodeOnPopstateRestoration:function(){return function e(t,r){let n=r[1],i=t.parallelRoutes,a=new Map(i);for(let t in n){let r=n[t],s=r[0],l=(0,o.createRouterCacheKey)(s),u=i.get(t);if(void 0!==u){let n=u.get(l);if(void 0!==n){let i=e(n,r),o=new Map(u);o.set(l,i),a.set(t,o)}}}let s=t.rsc,l=h(s)&&"pending"===s.status;return{lazyData:null,rsc:s,head:t.head,prefetchHead:l?t.prefetchHead:null,prefetchRsc:l?t.prefetchRsc:null,parallelRoutes:a}}}});let n=r(19457),i=r(24287),o=r(75325);function a(e,t,r,n){let i=l(e,t,r,n);return{route:e,node:i,children:null}}function s(e,t){t.then(t=>{for(let r of t[0]){let t=r.slice(0,-3),n=r[r.length-3],a=r[r.length-2],s=r[r.length-1];"string"!=typeof t&&function(e,t,r,n,a){let s=e;for(let e=0;e<t.length;e+=2){let r=t[e],n=t[e+1],o=s.children;if(null!==o){let e=o.get(r);if(void 0!==e){let t=e.route[0];if((0,i.matchSegment)(n,t)){s=e;continue}}}return}(function e(t,r,n,a){let s=t.children,l=t.node;if(null===s){null!==l&&(function e(t,r,n,a,s){let l=r[1],u=n[1],d=a[1],f=t.parallelRoutes;for(let t in l){let r=l[t],n=u[t],a=d[t],h=f.get(t),p=r[0],g=(0,o.createRouterCacheKey)(p),y=void 0!==h?h.get(g):void 0;void 0!==y&&(void 0!==n&&(0,i.matchSegment)(p,n[0])&&null!=a?e(y,r,n,a,s):c(r,y,null))}let p=t.rsc,g=a[2];null===p?t.rsc=g:h(p)&&p.resolve(g);let y=t.head;h(y)&&y.resolve(s)}(l,t.route,r,n,a),t.node=null);return}let u=r[1],d=n[1];for(let t in r){let r=u[t],n=d[t],o=s.get(t);if(void 0!==o){let t=o.route[0];if((0,i.matchSegment)(r[0],t)&&null!=n)return e(o,r,n,a)}}})(s,r,n,a)}(e,t,n,a,s)}u(e,null)},t=>{u(e,t)})}function l(e,t,r,n){let i=e[1],a=null!==t?t[1]:null,s=new Map;for(let e in i){let t=i[e],u=null!==a?a[e]:null,c=t[0],d=(0,o.createRouterCacheKey)(c),h=l(t,void 0===u?null:u,r,n),f=new Map;f.set(d,h),s.set(e,f)}let u=0===s.size,c=null!==t?t[2]:null;return{lazyData:null,parallelRoutes:s,prefetchRsc:n||void 0===c?null:c,prefetchHead:!n&&u?r:null,rsc:f(),head:u?f():null}}function u(e,t){let r=e.node;if(null===r)return;let n=e.children;if(null===n)c(e.route,r,t);else for(let e of n.values())u(e,t);e.node=null}function c(e,t,r){let n=e[1],i=t.parallelRoutes;for(let e in n){let t=n[e],a=i.get(e);if(void 0===a)continue;let s=t[0],l=(0,o.createRouterCacheKey)(s),u=a.get(l);void 0!==u&&c(t,u,r)}let a=t.rsc;h(a)&&(null===r?a.resolve(null):a.reject(r));let s=t.head;h(s)&&s.resolve(null)}let d=Symbol();function h(e){return e&&e.tag===d}function f(){let e,t;let r=new Promise((r,n)=>{e=r,t=n});return r.status="pending",r.resolve=t=>{"pending"===r.status&&(r.status="fulfilled",r.value=t,e(t))},r.reject=e=>{"pending"===r.status&&(r.status="rejected",r.reason=e,t(e))},r.tag=d,r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},94813:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createPrefetchCacheKey",{enumerable:!0,get:function(){return a}});let n=r(71870),i=r(86050),o=r(47475);function a(e,t){let r=(0,o.createHrefFromUrl)(e,!1);return t&&!(0,i.pathHasPrefix)(r,t)?(0,n.addPathPrefix)(r,""+t+"%"):r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},52298:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fastRefreshReducer",{enumerable:!0,get:function(){return n}}),r(47013),r(47475),r(71697),r(53694),r(69643),r(44080),r(69543),r(2583),r(71418);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7550:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"findHeadInCache",{enumerable:!0,get:function(){return i}});let n=r(75325);function i(e,t){return function e(t,r,i){if(0===Object.keys(r).length)return[t,i];for(let o in r){let[a,s]=r[o],l=t.parallelRoutes.get(o);if(!l)continue;let u=(0,n.createRouterCacheKey)(a),c=l.get(u);if(!c)continue;let d=e(c,s,i+"/"+u);if(d)return d}return null}(e,t,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},13717:(e,t)=>{"use strict";function r(e){return Array.isArray(e)?e[1]:e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getSegmentValue",{enumerable:!0,get:function(){return r}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},69643:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleExternalUrl:function(){return v},navigateReducer:function(){return S}});let n=r(47013),i=r(47475),o=r(32293),a=r(77676),s=r(71697),l=r(37528),u=r(53694),c=r(8085),d=r(44080),h=r(69543),f=r(80696),p=r(22574),g=r(7772),y=r(2583),m=r(19457),b=(r(13026),r(94813));function v(e,t,r,n){return t.mpaNavigation=!0,t.canonicalUrl=r,t.pendingPush=n,t.scrollableSegments=void 0,(0,d.handleMutable)(e,t)}function w(e){let t=[],[r,n]=e;if(0===Object.keys(n).length)return[[r]];for(let[e,i]of Object.entries(n))for(let n of w(i))""===r?t.push([e,...n]):t.push([r,e,...n]);return t}let S=function(e,t){let{url:r,isExternalUrl:S,navigateType:_,shouldScroll:k}=t,E={},{hash:A}=r,P=(0,i.createHrefFromUrl)(r),C="push"===_;if((0,p.prunePrefetchCache)(e.prefetchCache),E.preserveCustomHistoryState=!1,S)return v(e,E,r.toString(),C);let x=(0,b.createPrefetchCacheKey)(r,e.nextUrl),O=e.prefetchCache.get(x);if(!O){let t={data:(0,n.fetchServerResponse)(r,e.tree,e.nextUrl,e.buildId,void 0),kind:c.PrefetchKind.TEMPORARY,prefetchTime:Date.now(),treeAtTimeOfPrefetch:e.tree,lastUsedTime:null};e.prefetchCache.set(x,t),O=t}let R=(0,f.getPrefetchEntryCacheStatus)(O),{treeAtTimeOfPrefetch:T,data:B}=O;return g.prefetchQueue.bump(B),B.then(t=>{let[c,p,g]=t;if(O&&!O.lastUsedTime&&(O.lastUsedTime=Date.now()),"string"==typeof c)return v(e,E,c,C);let b=e.tree,S=e.cache,_=[];for(let t of c){let i=t.slice(0,-4),c=t.slice(-3)[0],d=["",...i],p=(0,s.applyRouterStatePatchToTreeSkipDefault)(d,b,c);if(null===p&&(p=(0,s.applyRouterStatePatchToTreeSkipDefault)(d,T,c)),null!==p){if((0,u.isNavigatingToNewRootLayout)(b,p))return v(e,E,P,C);let s=(0,y.createEmptyCacheNode)(),k=(0,h.applyFlightData)(S,s,t,(null==O?void 0:O.kind)==="auto"&&R===f.PrefetchCacheEntryStatus.reusable);for(let t of((!k&&R===f.PrefetchCacheEntryStatus.stale||g)&&(k=function(e,t,r,n,i){let o=!1;for(let s of(e.rsc=t.rsc,e.prefetchRsc=t.prefetchRsc,e.parallelRoutes=new Map(t.parallelRoutes),w(n).map(e=>[...r,...e])))(0,a.fillCacheWithDataProperty)(e,t,s,i),o=!0;return o}(s,S,i,c,()=>(0,n.fetchServerResponse)(r,b,e.nextUrl,e.buildId))),(0,l.shouldHardNavigate)(d,b)?(s.rsc=S.rsc,s.prefetchRsc=S.prefetchRsc,(0,o.invalidateCacheBelowFlightSegmentPath)(s,S,i),E.cache=s):k&&(E.cache=s),S=s,b=p,w(c))){let e=[...i,...t];e[e.length-1]!==m.DEFAULT_SEGMENT_KEY&&_.push(e)}}}return E.patchedTree=b,E.canonicalUrl=p?(0,i.createHrefFromUrl)(p):P,E.pendingPush=C,E.scrollableSegments=_,E.hashFragment=A,E.shouldScroll=k,(0,d.handleMutable)(e,E)},()=>e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7772:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{prefetchQueue:function(){return u},prefetchReducer:function(){return c}});let n=r(47013),i=r(8085),o=r(22574),a=r(15048),s=r(92051),l=r(94813),u=new s.PromiseQueue(5);function c(e,t){(0,o.prunePrefetchCache)(e.prefetchCache);let{url:r}=t;r.searchParams.delete(a.NEXT_RSC_UNION_QUERY);let s=(0,l.createPrefetchCacheKey)(r,e.nextUrl),c=e.prefetchCache.get(s);if(c&&(c.kind===i.PrefetchKind.TEMPORARY&&e.prefetchCache.set(s,{...c,kind:t.kind}),!(c.kind===i.PrefetchKind.AUTO&&t.kind===i.PrefetchKind.FULL)))return e;let d=u.enqueue(()=>(0,n.fetchServerResponse)(r,e.tree,e.nextUrl,e.buildId,t.kind));return e.prefetchCache.set(s,{treeAtTimeOfPrefetch:e.tree,data:d,kind:t.kind,prefetchTime:Date.now(),lastUsedTime:null}),e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},22574:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"prunePrefetchCache",{enumerable:!0,get:function(){return i}});let n=r(80696);function i(e){for(let[t,r]of e)(0,n.getPrefetchEntryCacheStatus)(r)===n.PrefetchCacheEntryStatus.expired&&e.delete(t)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},17787:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"refreshReducer",{enumerable:!0,get:function(){return h}});let n=r(47013),i=r(47475),o=r(71697),a=r(53694),s=r(69643),l=r(44080),u=r(67234),c=r(2583),d=r(71418);function h(e,t){let{origin:r}=t,h={},f=e.canonicalUrl,p=e.tree;h.preserveCustomHistoryState=!1;let g=(0,c.createEmptyCacheNode)();return g.lazyData=(0,n.fetchServerResponse)(new URL(f,r),[p[0],p[1],p[2],"refetch"],e.nextUrl,e.buildId),g.lazyData.then(r=>{let[n,c]=r;if("string"==typeof n)return(0,s.handleExternalUrl)(e,h,n,e.pushRef.pendingPush);for(let r of(g.lazyData=null,n)){if(3!==r.length)return console.log("REFRESH FAILED"),e;let[n]=r,l=(0,o.applyRouterStatePatchToFullTree)([""],p,n);if(null===l)return(0,d.handleSegmentMismatch)(e,t,n);if((0,a.isNavigatingToNewRootLayout)(p,l))return(0,s.handleExternalUrl)(e,h,f,e.pushRef.pendingPush);let y=c?(0,i.createHrefFromUrl)(c):void 0;c&&(h.canonicalUrl=y);let[m,b]=r.slice(-2);if(null!==m){let e=m[2];g.rsc=e,g.prefetchRsc=null,(0,u.fillLazyItemsTillLeafWithHead)(g,void 0,n,m,b),h.cache=g,h.prefetchCache=new Map}h.patchedTree=l,h.canonicalUrl=f,p=l}return(0,l.handleMutable)(e,h)},()=>e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},25206:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"restoreReducer",{enumerable:!0,get:function(){return o}});let n=r(47475),i=r(95684);function o(e,t){var r;let{url:o,tree:a}=t,s=(0,n.createHrefFromUrl)(o),l=e.cache;return{buildId:e.buildId,canonicalUrl:s,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:e.focusAndScrollRef,cache:l,prefetchCache:e.prefetchCache,tree:a,nextUrl:null!=(r=(0,i.extractPathFromFlightRouterState)(a))?r:o.pathname}}r(13026),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9501:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverActionReducer",{enumerable:!0,get:function(){return b}});let n=r(13664),i=r(15048),o=r(88928),a=r(47475),s=r(69643),l=r(71697),u=r(53694),c=r(44080),d=r(67234),h=r(2583),f=r(95684),p=r(71418),{createFromFetch:g,encodeReply:y}=r(82228);async function m(e,t){let r,{actionId:a,actionArgs:s}=t,l=await y(s),u=(0,f.extractPathFromFlightRouterState)(e.tree),c=e.nextUrl&&e.nextUrl!==u,d=await fetch("",{method:"POST",headers:{Accept:i.RSC_CONTENT_TYPE_HEADER,[i.ACTION]:a,[i.NEXT_ROUTER_STATE_TREE]:encodeURIComponent(JSON.stringify(e.tree)),...c?{[i.NEXT_URL]:e.nextUrl}:{}},body:l}),h=d.headers.get("x-action-redirect");try{let e=JSON.parse(d.headers.get("x-action-revalidated")||"[[],0,0]");r={paths:e[0]||[],tag:!!e[1],cookie:e[2]}}catch(e){r={paths:[],tag:!1,cookie:!1}}let p=h?new URL((0,o.addBasePath)(h),new URL(e.canonicalUrl,window.location.href)):void 0;if(d.headers.get("content-type")===i.RSC_CONTENT_TYPE_HEADER){let e=await g(Promise.resolve(d),{callServer:n.callServer});if(h){let[,t]=null!=e?e:[];return{actionFlightData:t,redirectLocation:p,revalidatedParts:r}}let[t,[,i]]=null!=e?e:[];return{actionResult:t,actionFlightData:i,redirectLocation:p,revalidatedParts:r}}return{redirectLocation:p,revalidatedParts:r}}function b(e,t){let{resolve:r,reject:n}=t,i={},o=e.canonicalUrl,f=e.tree;return i.preserveCustomHistoryState=!1,i.inFlightServerAction=m(e,t),i.inFlightServerAction.then(n=>{let{actionResult:g,actionFlightData:y,redirectLocation:m}=n;if(m&&(e.pushRef.pendingPush=!0,i.pendingPush=!0),!y)return(i.actionResultResolved||(r(g),i.actionResultResolved=!0),m)?(0,s.handleExternalUrl)(e,i,m.href,e.pushRef.pendingPush):e;if("string"==typeof y)return(0,s.handleExternalUrl)(e,i,y,e.pushRef.pendingPush);for(let r of(i.inFlightServerAction=null,y)){if(3!==r.length)return console.log("SERVER ACTION APPLY FAILED"),e;let[n]=r,a=(0,l.applyRouterStatePatchToFullTree)([""],f,n);if(null===a)return(0,p.handleSegmentMismatch)(e,t,n);if((0,u.isNavigatingToNewRootLayout)(f,a))return(0,s.handleExternalUrl)(e,i,o,e.pushRef.pendingPush);let[c,g]=r.slice(-2),y=null!==c?c[2]:null;if(null!==y){let e=(0,h.createEmptyCacheNode)();e.rsc=y,e.prefetchRsc=null,(0,d.fillLazyItemsTillLeafWithHead)(e,void 0,n,c,g),i.cache=e,i.prefetchCache=new Map}i.patchedTree=a,i.canonicalUrl=o,f=a}if(m){let e=(0,a.createHrefFromUrl)(m,!1);i.canonicalUrl=e}return i.actionResultResolved||(r(g),i.actionResultResolved=!0),(0,c.handleMutable)(e,i)},t=>{if("rejected"===t.status)return i.actionResultResolved||(n(t.reason),i.actionResultResolved=!0),e;throw t})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},57910:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverPatchReducer",{enumerable:!0,get:function(){return d}});let n=r(47475),i=r(71697),o=r(53694),a=r(69643),s=r(69543),l=r(44080),u=r(2583),c=r(71418);function d(e,t){let{flightData:r,overrideCanonicalUrl:d}=t,h={};if(h.preserveCustomHistoryState=!1,"string"==typeof r)return(0,a.handleExternalUrl)(e,h,r,e.pushRef.pendingPush);let f=e.tree,p=e.cache;for(let l of r){let r=l.slice(0,-4),[g]=l.slice(-3,-2),y=(0,i.applyRouterStatePatchToTreeSkipDefault)(["",...r],f,g);if(null===y)return(0,c.handleSegmentMismatch)(e,t,g);if((0,o.isNavigatingToNewRootLayout)(f,y))return(0,a.handleExternalUrl)(e,h,e.canonicalUrl,e.pushRef.pendingPush);let m=d?(0,n.createHrefFromUrl)(d):void 0;m&&(h.canonicalUrl=m);let b=(0,u.createEmptyCacheNode)();(0,s.applyFlightData)(p,b,l),h.patchedTree=y,h.cache=b,p=b,f=y}return(0,l.handleMutable)(e,h)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8085:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{PrefetchKind:function(){return r},ACTION_REFRESH:function(){return n},ACTION_NAVIGATE:function(){return i},ACTION_RESTORE:function(){return o},ACTION_SERVER_PATCH:function(){return a},ACTION_PREFETCH:function(){return s},ACTION_FAST_REFRESH:function(){return l},ACTION_SERVER_ACTION:function(){return u},isThenable:function(){return c}});let n="refresh",i="navigate",o="restore",a="server-patch",s="prefetch",l="fast-refresh",u="server-action";function c(e){return e&&("object"==typeof e||"function"==typeof e)&&"function"==typeof e.then}(function(e){e.AUTO="auto",e.FULL="full",e.TEMPORARY="temporary"})(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},73479:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reducer",{enumerable:!0,get:function(){return n}}),r(8085),r(69643),r(57910),r(25206),r(17787),r(7772),r(52298),r(9501);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},37528:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"shouldHardNavigate",{enumerable:!0,get:function(){return function e(t,r){let[i,o]=r,[a,s]=t;return(0,n.matchSegment)(a,i)?!(t.length<=2)&&e(t.slice(2),o[s]):!!Array.isArray(a)}}});let n=r(24287);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},25517:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createSearchParamsBailoutProxy",{enumerable:!0,get:function(){return i}});let n=r(1396);function i(){return new Proxy({},{get(e,t){"string"==typeof t&&(0,n.staticGenerationBailout)("searchParams."+t)}})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1396:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isStaticGenBailoutError:function(){return s},staticGenerationBailout:function(){return u}});let n=r(3082),i=r(94749),o="NEXT_STATIC_GEN_BAILOUT";class a extends Error{constructor(...e){super(...e),this.code=o}}function s(e){return"object"==typeof e&&null!==e&&"code"in e&&e.code===o}function l(e,t){let{dynamic:r,link:n}=t||{};return"Page"+(r?' with `dynamic = "'+r+'"`':"")+" couldn't be rendered statically because it used `"+e+"`."+(n?" See more info here: "+n:"")}let u=(e,t)=>{let{dynamic:r,link:o}=void 0===t?{}:t,s=i.staticGenerationAsyncStorage.getStore();if(!s)return!1;if(s.forceStatic)return!0;if(s.dynamicShouldError)throw new a(l(e,{link:o,dynamic:null!=r?r:"error"}));let u=l(e,{dynamic:r,link:"https://nextjs.org/docs/messages/dynamic-server-error"});if(null==s.postpone||s.postpone.call(s,e),s.revalidate=0,s.isStaticGeneration){let t=new n.DynamicServerError(u);throw s.dynamicUsageDescription=e,s.dynamicUsageStack=t.stack,t}return!1};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},43982:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}}),r(39694);let n=r(95344);r(3729);let i=r(25517);function o(e){let{Component:t,propsForComponent:r,isStaticGeneration:o}=e;if(o){let e=(0,i.createSearchParamsBailoutProxy)();return(0,n.jsx)(t,{searchParams:e,...r})}return(0,n.jsx)(t,{...r})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},14954:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{useUnwrapState:function(){return a},useReducerWithReduxDevtools:function(){return s}});let n=r(17824)._(r(3729)),i=r(8085);function o(e){if(e instanceof Map){let t={};for(let[r,n]of e.entries()){if("function"==typeof n){t[r]="fn()";continue}if("object"==typeof n&&null!==n){if(n.$$typeof){t[r]=n.$$typeof.toString();continue}if(n._bundlerConfig){t[r]="FlightData";continue}}t[r]=o(n)}return t}if("object"==typeof e&&null!==e){let t={};for(let r in e){let n=e[r];if("function"==typeof n){t[r]="fn()";continue}if("object"==typeof n&&null!==n){if(n.$$typeof){t[r]=n.$$typeof.toString();continue}if(n.hasOwnProperty("_bundlerConfig")){t[r]="FlightData";continue}}t[r]=o(n)}return t}return Array.isArray(e)?e.map(o):e}function a(e){return(0,i.isThenable)(e)?(0,n.use)(e):e}r(34087);let s=function(e){return[e,()=>{},()=>{}]};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},96411:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return i}});let n=r(86050);function i(e){return(0,n.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},19847:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return o}});let n=r(74310),i=r(12244),o=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:r,hash:o}=(0,i.parsePath)(e);return""+(0,n.removeTrailingSlash)(t)+r+o};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},22874:(e,t,r)=>{"use strict";function n(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return n}}),r(96411),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},54269:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getSegmentParam",{enumerable:!0,get:function(){return i}});let n=r(45767);function i(e){let t=n.INTERCEPTION_ROUTE_MARKERS.find(t=>e.startsWith(t));return(t&&(e=e.slice(t.length)),e.startsWith("[[...")&&e.endsWith("]]"))?{type:"optional-catchall",param:e.slice(5,-2)}:e.startsWith("[...")&&e.endsWith("]")?{type:"catchall",param:e.slice(4,-1)}:e.startsWith("[")&&e.endsWith("]")?{type:"dynamic",param:e.slice(1,-1)}:null}},45767:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return i},isInterceptionRouteAppPath:function(){return o},extractInterceptionRouteInformation:function(){return a}});let n=r(77655),i=["(..)(..)","(.)","(..)","(...)"];function o(e){return void 0!==e.split("/").find(e=>i.find(t=>e.startsWith(t)))}function a(e){let t,r,o;for(let n of e.split("/"))if(r=i.find(e=>n.startsWith(e))){[t,o]=e.split(r,2);break}if(!t||!r||!o)throw Error(`Invalid interception route: ${e}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`);switch(t=(0,n.normalizeAppPath)(t),r){case"(.)":o="/"===t?`/${o}`:t+"/"+o;break;case"(..)":if("/"===t)throw Error(`Invalid interception route: ${e}. Cannot use (..) marker at the root level, use (.) instead.`);o=t.split("/").slice(0,-1).concat(o).join("/");break;case"(...)":o="/"+o;break;case"(..)(..)":let a=t.split("/");if(a.length<=2)throw Error(`Invalid interception route: ${e}. Cannot use (..)(..) marker at the root level or one level up.`);o=a.slice(0,-2).concat(o).join("/");break;default:throw Error("Invariant: unexpected marker")}return{interceptingRoute:t,interceptedRoute:o}}},16372:(e,t,r)=>{"use strict";e.exports=r(20399)},46860:(e,t,r)=>{"use strict";e.exports=r(16372).vendored.contexts.AppRouterContext},78486:(e,t,r)=>{"use strict";e.exports=r(16372).vendored.contexts.HooksClientContext},69505:(e,t,r)=>{"use strict";e.exports=r(16372).vendored.contexts.ServerInsertedHtml},81202:(e,t,r)=>{"use strict";e.exports=r(16372).vendored["react-ssr"].ReactDOM},95344:(e,t,r)=>{"use strict";e.exports=r(16372).vendored["react-ssr"].ReactJsxRuntime},82228:(e,t,r)=>{"use strict";e.exports=r(16372).vendored["react-ssr"].ReactServerDOMWebpackClientEdge},3729:(e,t,r)=>{"use strict";e.exports=r(16372).vendored["react-ssr"].React},65344:(e,t)=>{"use strict";function r(e){let t=5381;for(let r=0;r<e.length;r++)t=(t<<5)+t+e.charCodeAt(r)&4294967295;return t>>>0}function n(e){return r(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{djb2Hash:function(){return r},hexHash:function(){return n}})},63689:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BailoutToCSRError:function(){return n},isBailoutToCSRError:function(){return i}});let r="BAILOUT_TO_CLIENT_SIDE_RENDERING";class n extends Error{constructor(e){super("Bail out to client-side rendering: "+e),this.reason=e,this.digest=r}}function i(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}},8092:(e,t)=>{"use strict";function r(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return r}})},34087:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ActionQueueContext:function(){return s},createMutableActionQueue:function(){return c}});let n=r(17824),i=r(8085),o=r(73479),a=n._(r(3729)),s=a.default.createContext(null);function l(e,t){null!==e.pending&&(e.pending=e.pending.next,null!==e.pending&&u({actionQueue:e,action:e.pending,setState:t}))}async function u(e){let{actionQueue:t,action:r,setState:n}=e,o=t.state;if(!o)throw Error("Invariant: Router state not initialized");t.pending=r;let a=r.payload,s=t.action(o,a);function u(e){if(r.discarded){t.needsRefresh&&null===t.pending&&(t.needsRefresh=!1,t.dispatch({type:i.ACTION_REFRESH,origin:window.location.origin},n));return}t.state=e,t.devToolsInstance&&t.devToolsInstance.send(a,e),l(t,n),r.resolve(e)}(0,i.isThenable)(s)?s.then(u,e=>{l(t,n),r.reject(e)}):u(s)}function c(){let e={state:null,dispatch:(t,r)=>(function(e,t,r){let n={resolve:r,reject:()=>{}};if(t.type!==i.ACTION_RESTORE){let e=new Promise((e,t)=>{n={resolve:e,reject:t}});(0,a.startTransition)(()=>{r(e)})}let o={payload:t,next:null,resolve:n.resolve,reject:n.reject};null===e.pending?(e.last=o,u({actionQueue:e,action:o,setState:r})):t.type===i.ACTION_NAVIGATE?(e.pending.discarded=!0,e.last=o,e.pending.payload.type===i.ACTION_SERVER_ACTION&&(e.needsRefresh=!0),u({actionQueue:e,action:o,setState:r})):(null!==e.last&&(e.last.next=o),e.last=o)})(e,t,r),action:async(e,t)=>{if(null===e)throw Error("Invariant: Router state not initialized");return(0,o.reducer)(e,t)},pending:null,last:null};return e}},71870:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return i}});let n=r(12244);function i(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:i,hash:o}=(0,n.parsePath)(e);return""+t+r+i+o}},77655:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeAppPath:function(){return o},normalizeRscURL:function(){return a}});let n=r(8092),i=r(19457);function o(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,r,n)=>!t||(0,i.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t,""))}function a(e){return e.replace(/\.rsc($|\?)/,"$1")}},51586:(e,t)=>{"use strict";function r(e,t){if(void 0===t&&(t={}),t.onlyHashChange){e();return}let r=document.documentElement,n=r.style.scrollBehavior;r.style.scrollBehavior="auto",t.dontForceLayout||r.getClientRects(),e(),r.style.scrollBehavior=n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSmoothScroll",{enumerable:!0,get:function(){return r}})},56338:(e,t)=>{"use strict";function r(e){return/Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(e)}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isBot",{enumerable:!0,get:function(){return r}})},12244:(e,t)=>{"use strict";function r(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return r}})},86050:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return i}});let n=r(12244);function i(e,t){if("string"!=typeof e)return!1;let{pathname:r}=(0,n.parsePath)(e);return r===t||r.startsWith(t+"/")}},74310:(e,t)=>{"use strict";function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})},19457:(e,t)=>{"use strict";function r(e){return"("===e[0]&&e.endsWith(")")}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isGroupSegment:function(){return r},PAGE_SEGMENT_KEY:function(){return n},DEFAULT_SEGMENT_KEY:function(){return i}});let n="__PAGE__",i="__DEFAULT__"},70837:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},30080:(e,t,r)=>{"use strict";/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var n=r(3729);"function"==typeof Object.is&&Object.is,n.useState,n.useEffect,n.useLayoutEffect,n.useDebugValue,t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:function(e,t){return t()}},8145:(e,t,r)=>{"use strict";e.exports=r(30080)},15259:(e,t,r)=>{"use strict";r.d(t,{tD:()=>te,gq:()=>t1,oF:()=>e2,_G:()=>ef,XL:()=>tX,t5:()=>tk,z3:()=>eG,uX:()=>t0,aR:()=>eW,B9:()=>e$,JH:()=>eq});var n,i,o=r(83614),a=(e,t)=>!t&&function(e){return e.endsWith(".lclstage.dev")||e.endsWith(".stgstage.dev")||e.endsWith(".clerkstage.dev")||e.endsWith(".accountsstage.dev")}(e)?"canary":t&&t.split(".")[0]||"latest",s=(e,{pkgVersion:t="4.68.2",clerkJSVersion:r})=>{let n=e.replace(/http(s)?:\/\//,""),i=a(e,t);return`https://${n}/npm/@clerk/clerk-js@${r||i}/dist/clerk.browser.js`};async function l(e,t=1,r=5){try{return await e()}catch(i){var n;if(t>=r)throw i;return await (n=2**t*100,new Promise(e=>setTimeout(e,n))),l(e,t+1,r)}}r(9124);var u=e=>"undefined"!=typeof atob&&"function"==typeof atob?atob(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e,"base64").toString():e,c="pk_live_";function d(e){if(!function(e){let t=(e=e||"").startsWith(c)||e.startsWith("pk_test_"),r=u(e.split("_")[2]||"").endsWith("$");return t&&r}(e=e||""))return null;let t=e.startsWith(c)?"production":"development",r=u(e.split("_")[2]);return r.endsWith("$")?{instanceType:t,frontendApi:r=r.slice(0,-1)}:null}function h(e){return e.startsWith("test_")||e.startsWith("sk_test_")}var f=r(17801);function p(e){return{code:e.code,message:e.message,longMessage:e.long_message,meta:{paramName:e?.meta?.param_name,sessionId:e?.meta?.session_id,emailAddresses:e?.meta?.email_addresses,identifiers:e?.meta?.identifiers,zxcvbn:e?.meta?.zxcvbn}}}var g=class e extends Error{constructor(t,{data:r,status:n,clerkTraceId:i}){super(t),this.toString=()=>{let e=`[${this.name}]
Message:${this.message}
Status:${this.status}
Serialized errors: ${this.errors.map(e=>JSON.stringify(e))}`;return this.clerkTraceId&&(e+=`
Clerk Trace ID: ${this.clerkTraceId}`),e},Object.setPrototypeOf(this,e.prototype),this.status=n,this.message=t,this.clerkTraceId=i,this.clerkError=!0,this.errors=function(e=[]){return e.length>0?e.map(p):[]}(r)}};new Proxy({Expired:"expired",Failed:"failed"},{get:(e,t,r)=>((0,f.x9)("MagicLinkErrorCode","Use `EmailLinkErrorCode` instead."),Reflect.get(e,t,r))});var y=Object.freeze({InvalidFrontendApiErrorMessage:"The frontendApi passed to Clerk is invalid. You can get your Frontend API key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})",InvalidProxyUrlErrorMessage:"The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})",InvalidPublishableKeyErrorMessage:"The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})",MissingPublishableKeyErrorMessage:"Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys."}),m=r(52743),b=r.n(m),v=r(27988),w=r(14438),S=r.n(w),_=r(78591),k=r.n(_),E=r(83191),A=class{constructor(e){this.request=e}requireId(e){if(!e)throw Error("A valid resource ID is required.")}},P=RegExp("(?<!:)/{1,}","g");function C(...e){return e.filter(e=>e).join("/").replace(P,"/")}var x="/allowlist_identifiers",O=class extends A{async getAllowlistIdentifierList(){return this.request({method:"GET",path:x})}async createAllowlistIdentifier(e){return this.request({method:"POST",path:x,bodyParams:e})}async deleteAllowlistIdentifier(e){return this.requireId(e),this.request({method:"DELETE",path:C(x,e)})}},R="/clients",T=class extends A{async getClientList(){return this.request({method:"GET",path:R})}async getClient(e){return this.requireId(e),this.request({method:"GET",path:C(R,e)})}verifyClient(e){return this.request({method:"POST",path:C(R,"verify"),bodyParams:{token:e}})}},B=class extends A{async deleteDomain(e){return this.request({method:"DELETE",path:C("/domains",e)})}},j="/email_addresses",U=class extends A{async getEmailAddress(e){return this.requireId(e),this.request({method:"GET",path:C(j,e)})}async createEmailAddress(e){return this.request({method:"POST",path:j,bodyParams:e})}async updateEmailAddress(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:C(j,e),bodyParams:t})}async deleteEmailAddress(e){return this.requireId(e),this.request({method:"DELETE",path:C(j,e)})}},N=class extends A{async createEmail(e){return(0,o.x9)("EmailAPI.createEmail","This endpoint is no longer available and the function will be removed in the next major version."),this.request({method:"POST",path:"/emails",bodyParams:e})}},I=function({packageName:e,customMessages:t}){let r=e,n={...y,...t};function i(e,t){if(!t)return`${r}: ${e}`;let n=e;for(let r of e.matchAll(/{{([a-zA-Z0-9-_]+)}}/g)){let e=(t[r[1]]||"").toString();n=n.replace(`{{${r[1]}}}`,e)}return`${r}: ${n}`}return{setPackageName({packageName:e}){return"string"==typeof e&&(r=e),this},setMessages({customMessages:e}){return Object.assign(n,e||{}),this},throwInvalidPublishableKeyError(e){throw Error(i(n.InvalidPublishableKeyErrorMessage,e))},throwInvalidFrontendApiError(e){throw Error(i(n.InvalidFrontendApiErrorMessage,e))},throwInvalidProxyUrl(e){throw Error(i(n.InvalidProxyUrlErrorMessage,e))},throwMissingPublishableKeyError(){throw Error(i(n.MissingPublishableKeyErrorMessage))}}}({packageName:"@clerk/backend"}),{isDevOrStagingUrl:M}=function(){let e=[".lcl.dev",".stg.dev",".lclstage.dev",".stgstage.dev",".dev.lclclerk.com",".stg.lclclerk.com",".accounts.lclclerk.com","accountsstage.dev","accounts.dev"],t=new Map;return{isDevOrStagingUrl:r=>{if(!r)return!1;let n="string"==typeof r?r:r.hostname,i=t.get(n);return void 0===i&&(i=e.some(e=>n.endsWith(e)),t.set(n,i)),i}}}(),L=class extends A{async getInterstitial(){return(0,o.x9)("getInterstitial()",'Switch to `Clerk(...).localInterstitial(...)` from `import { Clerk } from "@clerk/backend"`.'),this.request({path:"internal/interstitial",method:"GET",headerParams:{"Content-Type":"text/html"}})}},K="/invitations",D=class extends A{async getInvitationList(e={}){return this.request({method:"GET",path:K,queryParams:e})}async createInvitation(e){return this.request({method:"POST",path:K,bodyParams:e})}async revokeInvitation(e){return this.requireId(e),this.request({method:"POST",path:C(K,e,"revoke")})}},{RuntimeFetch:z,RuntimeAbortController:H,RuntimeBlob:F,RuntimeFormData:$,RuntimeHeaders:q,RuntimeRequest:V,RuntimeResponse:W}=v,G=z.bind(globalThis),J={crypto:b(),fetch:G,AbortController:H,Blob:F,FormData:$,Headers:q,Request:V,Response:W},Y="/organizations",X=class extends A{async getOrganizationList(e){return this.request({method:"GET",path:Y,queryParams:e})}async createOrganization(e){return this.request({method:"POST",path:Y,bodyParams:e})}async getOrganization(e){let t="organizationId"in e?e.organizationId:e.slug;return this.requireId(t),this.request({method:"GET",path:C(Y,t)})}async updateOrganization(e,t){return this.requireId(e),this.request({method:"PATCH",path:C(Y,e),bodyParams:t})}async updateOrganizationLogo(e,t){this.requireId(e);let r=new J.FormData;return r.append("file",t?.file),r.append("uploader_user_id",t?.uploaderUserId),this.request({method:"PUT",path:C(Y,e,"logo"),formData:r})}async deleteOrganizationLogo(e){return this.requireId(e),this.request({method:"DELETE",path:C(Y,e,"logo")})}async updateOrganizationMetadata(e,t){return this.requireId(e),this.request({method:"PATCH",path:C(Y,e,"metadata"),bodyParams:t})}async deleteOrganization(e){return this.request({method:"DELETE",path:C(Y,e)})}async getOrganizationMembershipList(e){let{organizationId:t,limit:r,offset:n}=e;return this.requireId(t),this.request({method:"GET",path:C(Y,t,"memberships"),queryParams:{limit:r,offset:n}})}async createOrganizationMembership(e){let{organizationId:t,userId:r,role:n}=e;return this.requireId(t),this.request({method:"POST",path:C(Y,t,"memberships"),bodyParams:{userId:r,role:n}})}async updateOrganizationMembership(e){let{organizationId:t,userId:r,role:n}=e;return this.requireId(t),this.request({method:"PATCH",path:C(Y,t,"memberships",r),bodyParams:{role:n}})}async updateOrganizationMembershipMetadata(e){let{organizationId:t,userId:r,publicMetadata:n,privateMetadata:i}=e;return this.request({method:"PATCH",path:C(Y,t,"memberships",r,"metadata"),bodyParams:{publicMetadata:n,privateMetadata:i}})}async deleteOrganizationMembership(e){let{organizationId:t,userId:r}=e;return this.requireId(t),this.request({method:"DELETE",path:C(Y,t,"memberships",r)})}async getOrganizationInvitationList(e){let{organizationId:t,status:r,limit:n,offset:i}=e;return this.requireId(t),this.request({method:"GET",path:C(Y,t,"invitations"),queryParams:{status:r,limit:n,offset:i}})}async getPendingOrganizationInvitationList(e){(0,o.x9)("getPendingOrganizationInvitationList","Use `getOrganizationInvitationList` instead.");let{organizationId:t,limit:r,offset:n}=e;return this.requireId(t),this.request({method:"GET",path:C(Y,t,"invitations","pending"),queryParams:{limit:r,offset:n}})}async createOrganizationInvitation(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:C(Y,t,"invitations"),bodyParams:{...r}})}async getOrganizationInvitation(e){let{organizationId:t,invitationId:r}=e;return this.requireId(t),this.requireId(r),this.request({method:"GET",path:C(Y,t,"invitations",r)})}async revokeOrganizationInvitation(e){let{organizationId:t,invitationId:r,requestingUserId:n}=e;return this.requireId(t),this.request({method:"POST",path:C(Y,t,"invitations",r,"revoke"),bodyParams:{requestingUserId:n}})}},Q="/phone_numbers",Z=class extends A{async getPhoneNumber(e){return this.requireId(e),this.request({method:"GET",path:C(Q,e)})}async createPhoneNumber(e){return this.request({method:"POST",path:Q,bodyParams:e})}async updatePhoneNumber(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:C(Q,e),bodyParams:t})}async deletePhoneNumber(e){return this.requireId(e),this.request({method:"DELETE",path:C(Q,e)})}},ee="/redirect_urls",et=class extends A{async getRedirectUrlList(){return this.request({method:"GET",path:ee})}async getRedirectUrl(e){return this.requireId(e),this.request({method:"GET",path:C(ee,e)})}async createRedirectUrl(e){return this.request({method:"POST",path:ee,bodyParams:e})}async deleteRedirectUrl(e){return this.requireId(e),this.request({method:"DELETE",path:C(ee,e)})}},er="/sessions",en=class extends A{async getSessionList(e){return this.request({method:"GET",path:er,queryParams:e})}async getSession(e){return this.requireId(e),this.request({method:"GET",path:C(er,e)})}async revokeSession(e){return this.requireId(e),this.request({method:"POST",path:C(er,e,"revoke")})}async verifySession(e,t){return this.requireId(e),this.request({method:"POST",path:C(er,e,"verify"),bodyParams:{token:t}})}async getToken(e,t){return this.requireId(e),(await this.request({method:"POST",path:C(er,e,"tokens",t||"")})).jwt}},ei="/sign_in_tokens",eo=class extends A{async createSignInToken(e){return this.request({method:"POST",path:ei,bodyParams:e})}async revokeSignInToken(e){return this.requireId(e),this.request({method:"POST",path:C(ei,e,"revoke")})}},ea=class extends A{async createSMSMessage(e){return(0,o.x9)("SMSMessageAPI.createSMSMessage","This endpoint is no longer available and the function will be removed in the next major version."),this.request({method:"POST",path:"/sms_messages",bodyParams:e})}},es="/users",el=class extends A{async getUserList(e={}){return this.request({method:"GET",path:es,queryParams:e})}async getUser(e){return this.requireId(e),this.request({method:"GET",path:C(es,e)})}async createUser(e){return this.request({method:"POST",path:es,bodyParams:e})}async updateUser(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:C(es,e),bodyParams:t})}async updateUserProfileImage(e,t){this.requireId(e);let r=new J.FormData;return r.append("file",t?.file),this.request({method:"POST",path:C(es,e,"profile_image"),formData:r})}async updateUserMetadata(e,t){return this.requireId(e),this.request({method:"PATCH",path:C(es,e,"metadata"),bodyParams:t})}async deleteUser(e){return this.requireId(e),this.request({method:"DELETE",path:C(es,e)})}async getCount(e={}){return this.request({method:"GET",path:C(es,"count"),queryParams:e})}async getUserOauthAccessToken(e,t){return this.requireId(e),this.request({method:"GET",path:C(es,e,"oauth_access_tokens",t)})}async disableUserMFA(e){return this.requireId(e),this.request({method:"DELETE",path:C(es,e,"mfa")})}async getOrganizationMembershipList(e){let{userId:t,limit:r,offset:n}=e;return this.requireId(t),this.request({method:"GET",path:C(es,t,"organization_memberships"),queryParams:{limit:r,offset:n}})}async verifyPassword(e){let{userId:t,password:r}=e;return this.requireId(t),this.request({method:"POST",path:C(es,t,"verify_password"),bodyParams:{password:r}})}async verifyTOTP(e){let{userId:t,code:r}=e;return this.requireId(t),this.request({method:"POST",path:C(es,t,"verify_totp"),bodyParams:{code:r}})}},eu="https://api.clerk.dev",ec="@clerk/backend@0.38.1",ed={AuthToken:"x-clerk-auth-token",AuthStatus:"x-clerk-auth-status",AuthReason:"x-clerk-auth-reason",AuthMessage:"x-clerk-auth-message",EnableDebug:"x-clerk-debug",ClerkRedirectTo:"x-clerk-redirect-to",CloudFrontForwardedProto:"cloudfront-forwarded-proto",Authorization:"authorization",ForwardedPort:"x-forwarded-port",ForwardedProto:"x-forwarded-proto",ForwardedHost:"x-forwarded-host",Referrer:"referer",UserAgent:"user-agent",Origin:"origin",Host:"host",ContentType:"content-type"},eh={AuthStatus:ed.AuthStatus,AuthToken:ed.AuthToken},ef={Attributes:{AuthToken:"__clerkAuthToken",AuthStatus:"__clerkAuthStatus",AuthReason:"__clerkAuthReason",AuthMessage:"__clerkAuthMessage"},Cookies:{Session:"__session",ClientUat:"__client_uat"},Headers:ed,SearchParams:eh,ContentTypes:{Json:"application/json"}};function ep(e){if(!e||"string"!=typeof e)throw Error("Missing Clerk Secret Key or API Key. Go to https://dashboard.clerk.com and get your key for your instance.")}var eg=class e{constructor(e,t,r,n,i){this.id=e,this.identifier=t,this.createdAt=r,this.updatedAt=n,this.invitationId=i}static fromJSON(t){return new e(t.id,t.identifier,t.created_at,t.updated_at,t.invitation_id)}},ey=class e{constructor(e,t,r,n,i,o,a,s,l){this.id=e,this.clientId=t,this.userId=r,this.status=n,this.lastActiveAt=i,this.expireAt=o,this.abandonAt=a,this.createdAt=s,this.updatedAt=l}static fromJSON(t){return new e(t.id,t.client_id,t.user_id,t.status,t.last_active_at,t.expire_at,t.abandon_at,t.created_at,t.updated_at)}},em=class e{constructor(e,t,r,n,i,o,a,s){this.id=e,this.sessionIds=t,this.sessions=r,this.signInId=n,this.signUpId=i,this.lastActiveSessionId=o,this.createdAt=a,this.updatedAt=s}static fromJSON(t){return new e(t.id,t.session_ids,t.sessions.map(e=>ey.fromJSON(e)),t.sign_in_id,t.sign_up_id,t.last_active_session_id,t.created_at,t.updated_at)}},eb=class e{constructor(e,t,r,n){this.object=e,this.id=t,this.slug=r,this.deleted=n}static fromJSON(t){return new e(t.object,t.id||null,t.slug||null,t.deleted)}},ev=class e{constructor(e,t,r,n,i,o,a,s,l,u,c){this.id=e,this.fromEmailName=t,this.emailAddressId=r,this.toEmailAddress=n,this.subject=i,this.body=o,this.bodyPlain=a,this.status=s,this.slug=l,this.data=u,this.deliveredByClerk=c}static fromJSON(t){return new e(t.id,t.from_email_name,t.email_address_id,t.to_email_address,t.subject,t.body,t.body_plain,t.status,t.slug,t.data,t.delivered_by_clerk)}},ew=class e{constructor(e,t){this.id=e,this.type=t}static fromJSON(t){return new e(t.id,t.type)}},eS=class e{constructor(e,t,r=null,n=null,i=null,o=null){this.status=e,this.strategy=t,this.externalVerificationRedirectURL=r,this.attempts=n,this.expireAt=i,this.nonce=o}static fromJSON(t){return new e(t.status,t.strategy,t.external_verification_redirect_url?new URL(t.external_verification_redirect_url):null,t.attempts,t.expire_at,t.nonce)}},e_=class e{constructor(e,t,r,n){this.id=e,this.emailAddress=t,this.verification=r,this.linkedTo=n}static fromJSON(t){return new e(t.id,t.email_address,t.verification&&eS.fromJSON(t.verification),t.linked_to.map(e=>ew.fromJSON(e)))}},ek=class e{constructor(e,t,r,n,i,o,a,s,l,u,c,d={},h,f){this.id=e,this.provider=t,this.identificationId=r,this.externalId=n,this.approvedScopes=i,this.emailAddress=o,this.firstName=a,this.lastName=s,this.picture=l,this.imageUrl=u,this.username=c,this.publicMetadata=d,this.label=h,this.verification=f}static fromJSON(t){return new e(t.id,t.provider,t.identification_id,t.provider_user_id,t.approved_scopes,t.email_address,t.first_name,t.last_name,t.avatar_url,t.image_url,t.username,t.public_metadata,t.label,t.verification&&eS.fromJSON(t.verification))}};(0,o.Mr)(ek,"picture","Use `imageUrl` instead.");var eE=class e{constructor(e,t,r,n,i,o,a){this.id=e,this.emailAddress=t,this.publicMetadata=r,this.createdAt=n,this.updatedAt=i,this.status=o,this.revoked=a}static fromJSON(t){return new e(t.id,t.email_address,t.public_metadata,t.created_at,t.updated_at,t.status,t.revoked)}},eA=((n=eA||{}).AllowlistIdentifier="allowlist_identifier",n.Client="client",n.Email="email",n.EmailAddress="email_address",n.ExternalAccount="external_account",n.FacebookAccount="facebook_account",n.GoogleAccount="google_account",n.Invitation="invitation",n.OauthAccessToken="oauth_access_token",n.Organization="organization",n.OrganizationInvitation="organization_invitation",n.OrganizationMembership="organization_membership",n.PhoneNumber="phone_number",n.RedirectUrl="redirect_url",n.Session="session",n.SignInAttempt="sign_in_attempt",n.SignInToken="sign_in_token",n.SignUpAttempt="sign_up_attempt",n.SmsMessage="sms_message",n.User="user",n.Web3Wallet="web3_wallet",n.Token="token",n.TotalCount="total_count",n),eP=class e{constructor(e,t,r={},n,i,o){this.provider=e,this.token=t,this.publicMetadata=r,this.label=n,this.scopes=i,this.tokenSecret=o}static fromJSON(t){return new e(t.provider,t.token,t.public_metadata,t.label,t.scopes,t.token_secret)}},eC=class e{constructor(e,t,r,n,i,o,a,s,l,u={},c={},d,h,f){this.id=e,this.name=t,this.slug=r,this.logoUrl=n,this.imageUrl=i,this.hasImage=o,this.createdBy=a,this.createdAt=s,this.updatedAt=l,this.publicMetadata=u,this.privateMetadata=c,this.maxAllowedMemberships=d,this.adminDeleteEnabled=h,this.members_count=f}static fromJSON(t){return new e(t.id,t.name,t.slug,t.logo_url,t.image_url,t.has_image,t.created_by,t.created_at,t.updated_at,t.public_metadata,t.private_metadata,t.max_allowed_memberships,t.admin_delete_enabled,t.members_count)}};(0,o.Mr)(eC,"logoUrl","Use `imageUrl` instead.");var ex=class e{constructor(e,t,r,n,i,o,a,s={},l={}){this.id=e,this.emailAddress=t,this.role=r,this.organizationId=n,this.createdAt=i,this.updatedAt=o,this.status=a,this.publicMetadata=s,this.privateMetadata=l}static fromJSON(t){return new e(t.id,t.email_address,t.role,t.organization_id,t.created_at,t.updated_at,t.status,t.public_metadata,t.private_metadata)}},eO=class e{constructor(e,t,r={},n={},i,o,a,s){this.id=e,this.role=t,this.publicMetadata=r,this.privateMetadata=n,this.createdAt=i,this.updatedAt=o,this.organization=a,this.publicUserData=s}static fromJSON(t){return new e(t.id,t.role,t.public_metadata,t.private_metadata,t.created_at,t.updated_at,eC.fromJSON(t.organization),eR.fromJSON(t.public_user_data))}},eR=class e{constructor(e,t,r,n,i,o,a){this.identifier=e,this.firstName=t,this.lastName=r,this.profileImageUrl=n,this.imageUrl=i,this.hasImage=o,this.userId=a}static fromJSON(t){return new e(t.identifier,t.first_name,t.last_name,t.profile_image_url,t.image_url,t.has_image,t.user_id)}};(0,o.Mr)(eR,"profileImageUrl","Use `imageUrl` instead.");var eT=class e{constructor(e,t,r,n,i,o){this.id=e,this.phoneNumber=t,this.reservedForSecondFactor=r,this.defaultSecondFactor=n,this.verification=i,this.linkedTo=o}static fromJSON(t){return new e(t.id,t.phone_number,t.reserved_for_second_factor,t.default_second_factor,t.verification&&eS.fromJSON(t.verification),t.linked_to.map(e=>ew.fromJSON(e)))}},eB=class e{constructor(e,t,r,n){this.id=e,this.url=t,this.createdAt=r,this.updatedAt=n}static fromJSON(t){return new e(t.id,t.url,t.created_at,t.updated_at)}},ej=class e{constructor(e,t,r,n,i,o,a){this.id=e,this.userId=t,this.token=r,this.status=n,this.url=i,this.createdAt=o,this.updatedAt=a}static fromJSON(t){return new e(t.id,t.user_id,t.token,t.status,t.url,t.created_at,t.updated_at)}},eU=class e{constructor(e,t,r,n,i,o,a){this.id=e,this.fromPhoneNumber=t,this.toPhoneNumber=r,this.message=n,this.status=i,this.phoneNumberId=o,this.data=a}static fromJSON(t){return new e(t.id,t.from_phone_number,t.to_phone_number,t.message,t.status,t.phone_number_id,t.data)}},eN=class e{constructor(e){this.jwt=e}static fromJSON(t){return new e(t.jwt)}},eI=class e{constructor(e,t,r){this.id=e,this.web3Wallet=t,this.verification=r}static fromJSON(t){return new e(t.id,t.web3_wallet,t.verification&&eS.fromJSON(t.verification))}},eM=class e{constructor(e,t,r,n,i,o,a,s,l,u,c,d,h,f,p,g,y,m,b,v,w,S={},_={},k={},E=[],A=[],P=[],C=[],x){this.id=e,this.passwordEnabled=t,this.totpEnabled=r,this.backupCodeEnabled=n,this.twoFactorEnabled=i,this.banned=o,this.createdAt=a,this.updatedAt=s,this.profileImageUrl=l,this.imageUrl=u,this.hasImage=c,this.gender=d,this.birthday=h,this.primaryEmailAddressId=f,this.primaryPhoneNumberId=p,this.primaryWeb3WalletId=g,this.lastSignInAt=y,this.externalId=m,this.username=b,this.firstName=v,this.lastName=w,this.publicMetadata=S,this.privateMetadata=_,this.unsafeMetadata=k,this.emailAddresses=E,this.phoneNumbers=A,this.web3Wallets=P,this.externalAccounts=C,this.createOrganizationEnabled=x}static fromJSON(t){return new e(t.id,t.password_enabled,t.totp_enabled,t.backup_code_enabled,t.two_factor_enabled,t.banned,t.created_at,t.updated_at,t.profile_image_url,t.image_url,t.has_image,t.gender,t.birthday,t.primary_email_address_id,t.primary_phone_number_id,t.primary_web3_wallet_id,t.last_sign_in_at,t.external_id,t.username,t.first_name,t.last_name,t.public_metadata,t.private_metadata,t.unsafe_metadata,(t.email_addresses||[]).map(e=>e_.fromJSON(e)),(t.phone_numbers||[]).map(e=>eT.fromJSON(e)),(t.web3_wallets||[]).map(e=>eI.fromJSON(e)),(t.external_accounts||[]).map(e=>ek.fromJSON(e)),t.create_organization_enabled)}};function eL(e){if("string"!=typeof e&&"object"in e&&"deleted"in e)return eb.fromJSON(e);switch(e.object){case"allowlist_identifier":return eg.fromJSON(e);case"client":return em.fromJSON(e);case"email_address":return e_.fromJSON(e);case"email":return ev.fromJSON(e);case"invitation":return eE.fromJSON(e);case"oauth_access_token":return eP.fromJSON(e);case"organization":return eC.fromJSON(e);case"organization_invitation":return ex.fromJSON(e);case"organization_membership":return eO.fromJSON(e);case"phone_number":return eT.fromJSON(e);case"redirect_url":return eB.fromJSON(e);case"sign_in_token":return ej.fromJSON(e);case"session":return ey.fromJSON(e);case"sms_message":return eU.fromJSON(e);case"token":return eN.fromJSON(e);case"total_count":return e.total_count;case"user":return eM.fromJSON(e);default:return e}}(0,o.Mr)(eM,"profileImageUrl","Use `imageUrl` instead.");var eK=e=>async(...t)=>{let r=await e(...t);if(null===r.errors)return r.data;{let{errors:e,clerkTraceId:t}=r,{status:n,statusText:i}=r,o=new g(i||"",{data:[],status:n||"",clerkTraceId:t});throw o.errors=e,o}};function eD(e,t){return e&&"object"==typeof e&&"clerk_trace_id"in e&&"string"==typeof e.clerk_trace_id?e.clerk_trace_id:t?.get("cf-ray")||""}function ez(e){return{code:e.code,message:e.message,longMessage:e.long_message,meta:{paramName:e?.meta?.param_name,sessionId:e?.meta?.session_id}}}function eH(e){let t=eK(async t=>{let r;let{apiKey:n,secretKey:i,httpOptions:a,apiUrl:s=eu,apiVersion:l="v1",userAgent:u=ec}=e;n&&(0,o.x9)("apiKey","Use `secretKey` instead."),a&&(0,o.x9)("httpOptions","This option has been deprecated and will be removed with the next major release.\nA RequestInit init object used by the `request` method.");let{path:c,method:d,queryParams:h,headerParams:f,bodyParams:p,formData:g}=t,y=i||n;ep(y);let m=C(s,l,c),b=new URL(m);if(h)for(let[e,t]of Object.entries(k()({...h})))t&&[t].flat().forEach(t=>b.searchParams.append(e,t));let v={Authorization:`Bearer ${y}`,"User-Agent":u,...f};try{if(g)r=await J.fetch(b.href,{...a,method:d,headers:v,body:g});else{v["Content-Type"]="application/json";let e="GET"!==d&&p&&Object.keys(p).length>0?{body:JSON.stringify(k()(p,{deep:!1}))}:null;r=await J.fetch(b.href,S()(a||{},{method:d,headers:v,...e}))}let e=r?.headers&&r.headers?.get(ef.Headers.ContentType)===ef.ContentTypes.Json,t=await (e?r.json():r.text());if(!r.ok)throw t;return{data:Array.isArray(t)?t.map(e=>eL(e)):Array.isArray(t.data)&&void 0!==t.data?t.data.map(e=>eL(e)):eL(t),errors:null}}catch(e){if(e instanceof Error)return{data:null,errors:[{code:"unexpected_error",message:e.message||"Unexpected error"}],clerkTraceId:eD(e,r?.headers)};return{data:null,errors:function(e){if(e&&"object"==typeof e&&"errors"in e){let t=e.errors;return t.length>0?t.map(ez):[]}return[]}(e),status:r?.status,statusText:r?.statusText,clerkTraceId:eD(e,r?.headers)}}});return{allowlistIdentifiers:new O(t),clients:new T(t),emailAddresses:new U(t),emails:new N(t),interstitial:new L(t),invitations:new D(t),organizations:new X(t),phoneNumbers:new Z(t),redirectUrls:new et(t),sessions:new en(t),signInTokens:new eo(t),smsMessages:new ea(t),users:new el(t),domains:new B(t)}}var eF=e=>()=>{let t={...e};return t.apiKey=(t.apiKey||"").substring(0,7),t.secretKey=(t.secretKey||"").substring(0,7),t.jwtKey=(t.jwtKey||"").substring(0,7),{...t}};function e$(e,t,r){let{act:n,sid:i,org_id:a,org_role:s,org_slug:l,org_permissions:u,sub:c}=e,{apiKey:d,secretKey:h,apiUrl:f,apiVersion:p,token:g,session:y,user:m,organization:b}=t;d&&(0,o.x9)("apiKey","Use `secretKey` instead.");let{sessions:v}=eH({apiKey:d,secretKey:h,apiUrl:f,apiVersion:p}),w=eJ({sessionId:i,sessionToken:g,fetcher:(...e)=>v.getToken(...e)});return{actor:n,sessionClaims:e,sessionId:i,session:y,userId:c,user:m,orgId:a,orgRole:s,orgSlug:l,orgPermissions:u,organization:b,getToken:w,has:eY({orgId:a,orgRole:s,orgPermissions:u,userId:c}),debug:eF({...t,...r})}}function eq(e){return e?.apiKey&&(0,o.x9)("apiKey","Use `secretKey` instead."),{sessionClaims:null,sessionId:null,session:null,userId:null,user:null,actor:null,orgId:null,orgRole:null,orgSlug:null,orgPermissions:null,organization:null,getToken:()=>Promise.resolve(null),has:()=>!1,debug:eF(e)}}function eV(e){return e&&(delete e.privateMetadata,delete e.private_metadata),e}function eW(e){let t=e.user?{...e.user}:e.user,r=e.organization?{...e.organization}:e.organization;return eV(t),eV(r),{...e,user:t,organization:r}}var eG=e=>{let{debug:t,getToken:r,has:n,...i}=e;return i},eJ=e=>{let{fetcher:t,sessionToken:r,sessionId:n}=e||{};return async(e={})=>n?e.template?t(n,e.template):r:null},eY=({orgId:e,orgRole:t,userId:r,orgPermissions:n})=>i=>{if(!i?.permission&&!i?.role)throw Error('Missing parameters. `has` from `auth` or `getAuth` requires a permission or role key to be passed. Example usage: `has({permission: "org:posts:edit"`');return!!e&&!!r&&!!t&&!!n&&(i.permission?n.includes(i.permission):!!i.role&&t===i.role)},eX=class e extends Error{constructor({action:t,message:r,reason:n}){super(r),Object.setPrototypeOf(this,e.prototype),this.reason=n,this.message=r,this.action=t}getFullMessage(){return`${[this.message,this.action].filter(e=>e).join(" ")} (reason=${this.reason}, token-carrier=${this.tokenCarrier})`}};async function eQ(e){e.frontendApi&&(0,o.x9)("frontendApi","Use `publishableKey` instead."),e.pkgVersion&&(0,o.x9)("pkgVersion","Use `clerkJSVersion` instead."),e.frontendApi=d(e.publishableKey)?.frontendApi||e.frontendApi||"";let t=eZ(e),r=await l(()=>J.fetch(eZ(e),{method:"GET",headers:{"Clerk-Backend-SDK":e.userAgent||ec}}));if(!r.ok)throw new eX({action:"Contact <EMAIL>",message:`Error loading Clerk Interstitial from ${t} with code=${r.status}`,reason:"interstitial-remote-failed-to-load"});return r.text()}function eZ(e){e.frontendApi&&(0,o.x9)("frontendApi","Use `publishableKey` instead."),e.frontendApi=d(e.publishableKey)?.frontendApi||e.frontendApi||"";let{apiUrl:t,frontendApi:r,pkgVersion:n,clerkJSVersion:i,publishableKey:s,proxyUrl:l,isSatellite:u,domain:c,signInUrl:h}=e,f=new URL(t);return f.pathname=C(f.pathname,"v1","/public/interstitial"),f.searchParams.append("clerk_js_version",i||a(r,n)),s?f.searchParams.append("publishable_key",s):f.searchParams.append("frontend_api",r),l&&f.searchParams.append("proxy_url",l),u&&f.searchParams.append("is_satellite","true"),f.searchParams.append("sign_in_url",h||""),M(e.frontendApi)||f.searchParams.append("use_domain_for_script","true"),c&&f.searchParams.append("domain",c),f.href}var e0=(e,t)=>e.headers.get(t),e1=e=>e?.split(",")[0],e2=(e,t)=>{let r=new URL(e.url),n=e0(e,ef.Headers.ForwardedProto),i=e0(e,ef.Headers.ForwardedHost),o=e0(e,ef.Headers.Host),a=e8({protocol:r.protocol,forwardedProto:n,forwardedHost:i,host:o||r.host});return new URL(t||r.pathname,a)},e8=({protocol:e,forwardedProto:t,forwardedHost:r,host:n})=>{let i=e1(r)??n,o=e1(t)??e?.replace(/[:/]/,"");return i&&o?`${o}://${i}`:""},e3=e=>e?{cookies:e5(e),headers:e6(e),searchParams:e7(e)}:{},e4=e=>e?e.replace(/(%[0-9A-Z]{2})+/g,decodeURIComponent):e,e5=e=>{let t=e.headers&&e.headers?.get("cookie")?(0,E.Q)(e.headers.get("cookie")):{};return e=>{let r=t?.[e];if(void 0!==r)return e4(r)}},e6=e=>t=>e?.headers?.get(t)||void 0,e7=e=>e?.url?new URL(e.url)?.searchParams:void 0,e9=e=>e?.replace("Bearer ",""),te=((i=te||{}).SignedIn="signed-in",i.SignedOut="signed-out",i.Interstitial="interstitial",i.Unknown="unknown",i);async function tt(e,t){let{apiKey:r,secretKey:n,apiUrl:i,apiVersion:o,cookieToken:a,frontendApi:s,proxyUrl:l,publishableKey:u,domain:c,isSatellite:d,headerToken:h,loadSession:f,loadUser:p,loadOrganization:g,signInUrl:y,signUpUrl:m,afterSignInUrl:b,afterSignUpUrl:v,token:w}=e,{sid:S,org_id:_,sub:k}=t,{sessions:E,users:A,organizations:P}=eH({apiKey:r,secretKey:n,apiUrl:i,apiVersion:o}),[C,x,O]=await Promise.all([f?E.getSession(S):Promise.resolve(void 0),p?A.getUser(k):Promise.resolve(void 0),g&&_?P.getOrganization({organizationId:_}):Promise.resolve(void 0)]),R=e$(t,{secretKey:n,apiKey:r,apiUrl:i,apiVersion:o,token:a||h||"",session:C,user:x,organization:O},{...e,status:"signed-in"});return{status:"signed-in",reason:null,message:null,frontendApi:s,proxyUrl:l,publishableKey:u,domain:c,isSatellite:d,signInUrl:y,signUpUrl:m,afterSignInUrl:b,afterSignUpUrl:v,isSignedIn:!0,isInterstitial:!1,isUnknown:!1,toAuth:()=>R,token:w}}function tr(e,t,r=""){let{frontendApi:n,publishableKey:i,proxyUrl:o,isSatellite:a,domain:s,signInUrl:l,signUpUrl:u,afterSignInUrl:c,afterSignUpUrl:d}=e;return{status:"signed-out",reason:t,message:r,frontendApi:n,proxyUrl:o,publishableKey:i,isSatellite:a,domain:s,signInUrl:l,signUpUrl:u,afterSignInUrl:c,afterSignUpUrl:d,isSignedIn:!1,isInterstitial:!1,isUnknown:!1,toAuth:()=>eq({...e,status:"signed-out",reason:t,message:r}),token:null}}function tn(e,t,r=""){let{frontendApi:n,publishableKey:i,proxyUrl:o,isSatellite:a,domain:s,signInUrl:l,signUpUrl:u,afterSignInUrl:c,afterSignUpUrl:d}=e;return{status:"interstitial",reason:t,message:r,frontendApi:n,publishableKey:i,isSatellite:a,domain:s,proxyUrl:o,signInUrl:l,signUpUrl:u,afterSignInUrl:c,afterSignUpUrl:d,isSignedIn:!1,isInterstitial:!0,isUnknown:!1,toAuth:()=>null,token:null}}function ti({originURL:e,host:t,forwardedHost:r,forwardedProto:n}){let i=e8({forwardedProto:n,forwardedHost:r,protocol:e.protocol,host:t});return i&&new URL(i).origin!==e.origin}var to=(e,t)=>e?e.find(e=>e.code===t):null,ta={parse:(e,t)=>(function(e,t,r={}){if(!t.codes){t.codes={};for(let e=0;e<t.chars.length;++e)t.codes[t.chars[e]]=e}if(!r.loose&&e.length*t.bits&7)throw SyntaxError("Invalid padding");let n=e.length;for(;"="===e[n-1];)if(--n,!r.loose&&!((e.length-n)*t.bits&7))throw SyntaxError("Invalid padding");let i=new(r.out??Uint8Array)(n*t.bits/8|0),o=0,a=0,s=0;for(let r=0;r<n;++r){let n=t.codes[e[r]];if(void 0===n)throw SyntaxError("Invalid character "+e[r]);a=a<<t.bits|n,(o+=t.bits)>=8&&(o-=8,i[s++]=255&a>>o)}if(o>=t.bits||255&a<<8-o)throw SyntaxError("Unexpected end of data");return i})(e,ts,t),stringify:(e,t)=>(function(e,t,r={}){let{pad:n=!0}=r,i=(1<<t.bits)-1,o="",a=0,s=0;for(let r=0;r<e.length;++r)for(s=s<<8|255&e[r],a+=8;a>t.bits;)a-=t.bits,o+=t.chars[i&s>>a];if(a&&(o+=t.chars[i&s<<t.bits-a]),n)for(;o.length*t.bits&7;)o+="=";return o})(e,ts,t)},ts={chars:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bits:6},tl={RS256:"SHA-256",RS384:"SHA-384",RS512:"SHA-512"},tu="RSASSA-PKCS1-v1_5",tc={RS256:tu,RS384:tu,RS512:tu},td=Object.keys(tl),th=e=>Array.isArray(e)&&e.length>0&&e.every(e=>"string"==typeof e),tf=(e,t)=>{let r=[t].flat().filter(e=>!!e),n=[e].flat().filter(e=>!!e);if(r.length>0&&n.length>0){if("string"==typeof e){if(!r.includes(e))throw new eX({action:"Make sure that this is a valid Clerk generate JWT.",reason:"token-verification-failed",message:`Invalid JWT audience claim (aud) ${JSON.stringify(e)}. Is not included in "${JSON.stringify(r)}".`})}else if(th(e)&&!e.some(e=>r.includes(e)))throw new eX({action:"Make sure that this is a valid Clerk generate JWT.",reason:"token-verification-failed",message:`Invalid JWT audience claim array (aud) ${JSON.stringify(e)}. Is not included in "${JSON.stringify(r)}".`})}},tp=e=>{if(void 0!==e&&"JWT"!==e)throw new eX({action:"Make sure that this is a valid Clerk generate JWT.",reason:"token-invalid",message:`Invalid JWT type ${JSON.stringify(e)}. Expected "JWT".`})},tg=e=>{if(!td.includes(e))throw new eX({action:"Make sure that this is a valid Clerk generate JWT.",reason:"token-invalid-algorithm",message:`Invalid JWT algorithm ${JSON.stringify(e)}. Supported: ${td}.`})},ty=e=>{if("string"!=typeof e)throw new eX({action:"Make sure that this is a valid Clerk generate JWT.",reason:"token-verification-failed",message:`Subject claim (sub) is required and must be a string. Received ${JSON.stringify(e)}.`})},tm=(e,t)=>{if(e&&t&&0!==t.length&&!t.includes(e))throw new eX({reason:"token-invalid-authorized-parties",message:`Invalid JWT Authorized party claim (azp) ${JSON.stringify(e)}. Expected "${t}".`})},tb=(e,t)=>{if("function"!=typeof t||t(e)){if("string"==typeof t&&e&&e!==t)throw new eX({reason:"token-invalid-issuer",message:`Invalid JWT issuer claim (iss) ${JSON.stringify(e)}. Expected "${t}".`})}else throw new eX({reason:"token-invalid-issuer",message:"Failed JWT issuer resolver. Make sure that the resolver returns a truthy value."})},tv=(e,t)=>{if("number"!=typeof e)throw new eX({action:"Make sure that this is a valid Clerk generate JWT.",reason:"token-verification-failed",message:`Invalid JWT expiry date claim (exp) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),n=new Date(0);if(n.setUTCSeconds(e),n.getTime()<=r.getTime()-t)throw new eX({reason:"token-expired",message:`JWT is expired. Expiry date: ${n.toUTCString()}, Current date: ${r.toUTCString()}.`})},tw=(e,t)=>{if(void 0===e)return;if("number"!=typeof e)throw new eX({action:"Make sure that this is a valid Clerk generate JWT.",reason:"token-verification-failed",message:`Invalid JWT not before date claim (nbf) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),n=new Date(0);if(n.setUTCSeconds(e),n.getTime()>r.getTime()+t)throw new eX({reason:"token-not-active-yet",message:`JWT cannot be used prior to not before date claim (nbf). Not before date: ${n.toUTCString()}; Current date: ${r.toUTCString()};`})},tS=(e,t)=>{if(void 0===e)return;if("number"!=typeof e)throw new eX({action:"Make sure that this is a valid Clerk generate JWT.",reason:"token-verification-failed",message:`Invalid JWT issued at date claim (iat) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),n=new Date(0);if(n.setUTCSeconds(e),n.getTime()>r.getTime()+t)throw new eX({reason:"token-not-active-yet",message:`JWT issued at date claim (iat) is in the future. Issued at date: ${n.toUTCString()}; Current date: ${r.toUTCString()};`})};async function t_(e,t){let{header:r,signature:n,raw:i}=e,o=new TextEncoder().encode([i.header,i.payload].join(".")),a=function(e){let t=tl[e],r=tc[e];if(!t||!r)throw Error(`Unsupported algorithm ${e}, expected one of ${td.join(",")}.`);return{hash:{name:tl[e]},name:tc[e]}}(r.alg),s=await function(e,t,r){if("object"==typeof e)return J.crypto.subtle.importKey("jwk",e,t,!1,[r]);let n=function(e){let t=u(e.replace(/-----BEGIN.*?-----/g,"").replace(/-----END.*?-----/g,"").replace(/\s/g,"")),r=new ArrayBuffer(t.length),n=new Uint8Array(r);for(let e=0,r=t.length;e<r;e++)n[e]=t.charCodeAt(e);return n}(e),i="sign"===r?"pkcs8":"spki";return J.crypto.subtle.importKey(i,n,t,!1,[r])}(t,a,"verify");return J.crypto.subtle.verify(a.name,s,n,o)}function tk(e){let t=(e||"").toString().split(".");if(3!==t.length)throw new eX({reason:"token-invalid",message:"Invalid JWT form. A JWT consists of three parts separated by dots."});let[r,n,i]=t,a=new TextDecoder,s=JSON.parse(a.decode(ta.parse(r,{loose:!0}))),l=JSON.parse(a.decode(ta.parse(n,{loose:!0}))),u=ta.parse(i,{loose:!0});return(0,o.x6)(l,"orgs",'Add orgs to your session token using the "user.organizations" shortcode in JWT Templates instead.',"decodeJwt:orgs"),{header:s,payload:l,signature:u,raw:{header:r,payload:n,signature:i,text:e}}}async function tE(e,{audience:t,authorizedParties:r,clockSkewInSeconds:n,clockSkewInMs:i,issuer:a,key:s}){let l;n&&(0,o.x9)("clockSkewInSeconds","Use `clockSkewInMs` instead.");let u=i||n||5e3,c=tk(e),{header:d,payload:h}=c,{typ:f,alg:p}=d;tp(f),tg(p);let{azp:g,sub:y,aud:m,iss:b,iat:v,exp:w,nbf:S}=h;ty(y),tf([m],[t]),tm(g,r),tb(b,a),tv(w,u),tw(S,u),tS(v,u);try{l=await t_(c,s)}catch(e){throw new eX({action:"Make sure that this is a valid Clerk generate JWT.",reason:"token-verification-failed",message:`Error verifying JWT signature. ${e}`})}if(!l)throw new eX({reason:"token-invalid-signature",message:"JWT signature is invalid."});return h}var tA={},tP=0;function tC(e,t=36e5){tA[e.kid]=e,tP=Date.now(),t>=0&&setTimeout(()=>{e?delete tA[e.kid]:tA={}},t)}var tx="local";async function tO({apiKey:e,secretKey:t,apiUrl:r=eu,apiVersion:n="v1",issuer:i,kid:o,jwksCacheTtlInMs:a=36e5,skipJwksCache:s}){let u=!tA[o]&&Date.now()-tP>=3e5;if(s||u){let o;let s=t||e;if(s)o=()=>tT(r,s,n);else if(i)o=()=>tR(i);else throw new eX({action:"Contact <EMAIL>",message:"Failed to load JWKS from Clerk Backend or Frontend API.",reason:"jwk-remote-failed-to-load"});let{keys:u}=await l(o);if(!u||!u.length)throw new eX({action:"Contact <EMAIL>",message:"The JWKS endpoint did not contain any signing keys. Contact <EMAIL>.",reason:"jwk-remote-failed-to-load"});u.forEach(e=>tC(e,a))}let c=tA[o];if(!c){let e=Object.values(tA).map(e=>e.kid).join(", ");throw new eX({action:"Contact <EMAIL>",message:`Unable to find a signing key in JWKS that matches the kid='${o}' of the provided session token. Please make sure that the __session cookie or the HTTP authorization header contain a Clerk-generated session JWT.${e?` The following kid are available: ${e}`:""}`,reason:"jwk-remote-missing"})}return c}async function tR(e){let t=new URL(e);t.pathname=C(t.pathname,".well-known/jwks.json");let r=await J.fetch(t.href);if(!r.ok)throw new eX({action:"Contact <EMAIL>",message:`Error loading Clerk JWKS from ${t.href} with code=${r.status}`,reason:"jwk-remote-failed-to-load"});return r.json()}async function tT(e,t,r){if(!t)throw new eX({action:"Set the CLERK_SECRET_KEY or CLERK_API_KEY environment variable.",message:"Missing Clerk Secret Key or API Key. Go to https://dashboard.clerk.com and get your key for your instance.",reason:"jwk-remote-failed-to-load"});let n=new URL(e);n.pathname=C(n.pathname,r,"/jwks");let i=await J.fetch(n.href,{headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"}});if(!i.ok){let e=await i.json(),t=to(e?.errors,"clerk_key_invalid");if(t)throw new eX({action:"Contact <EMAIL>",message:t.message,reason:"secret-key-invalid"});throw new eX({action:"Contact <EMAIL>",message:`Error loading Clerk JWKS from ${n.href} with code=${i.status}`,reason:"jwk-remote-failed-to-load"})}return i.json()}async function tB(e,t){let r;let{apiKey:n,secretKey:i,apiUrl:a,apiVersion:s,audience:l,authorizedParties:u,clockSkewInSeconds:c,clockSkewInMs:d,issuer:h,jwksCacheTtlInMs:f,jwtKey:p,skipJwksCache:g}=t;t.apiKey&&(0,o.x9)("apiKey","Use `secretKey` instead.");let{header:y}=tk(e),{kid:m}=y;if(p)r=function(e){if(!tA[tx]){if(!e)throw new eX({action:"Set the CLERK_JWT_KEY environment variable.",message:"Missing local JWK.",reason:"jwk-local-missing"});tC({kid:"local",kty:"RSA",alg:"RS256",n:e.replace(/(\r\n|\n|\r)/gm,"").replace("-----BEGIN PUBLIC KEY-----","").replace("-----END PUBLIC KEY-----","").replace("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA","").replace("IDAQAB","").replace(/\+/g,"-").replace(/\//g,"_"),e:"AQAB"},-1)}return tA[tx]}(p);else if("string"==typeof h)r=await tO({issuer:h,kid:m,jwksCacheTtlInMs:f,skipJwksCache:g});else if(n||i)r=await tO({apiKey:n,secretKey:i,apiUrl:a,apiVersion:s,kid:m,jwksCacheTtlInMs:f,skipJwksCache:g});else throw new eX({action:"Set the CLERK_JWT_KEY environment variable.",message:"Failed to resolve JWK during verification.",reason:"jwk-failed-to-resolve"});return await tE(e,{audience:l,authorizedParties:u,clockSkewInSeconds:c,clockSkewInMs:d,key:r,issuer:h})}var tj=e=>!!e?.get("__clerk_satellite_url"),tU=e=>e?.get("__clerk_synced")==="true",tN=/^Mozilla\/|(Amazon CloudFront)/,tI=e=>tN.test(e||""),tM=e=>{let{apiKey:t,secretKey:r,userAgent:n}=e;if(h(r||t||"")&&!tI(n))return tr(e,"header-missing-non-browser")},tL=e=>{let{origin:t,host:r,forwardedHost:n,forwardedProto:i}=e;if(t&&ti({originURL:new URL(t),host:r,forwardedHost:n,forwardedProto:i}))return tr(e,"header-missing-cors")},tK=e=>{let{apiKey:t,secretKey:r,isSatellite:n,searchParams:i}=e;if(h(r||t||"")&&!n&&tj(i))return tn(e,"primary-responds-to-syncing")},tD=e=>{let{apiKey:t,secretKey:r,clientUat:n}=e;if(h(r||t||"")&&!n)return tn(e,"uat-missing")},tz=e=>{let{apiKey:t,secretKey:r,referrer:n,host:i,forwardedHost:o,forwardedProto:a}=e,s=n&&ti({originURL:new URL(n),host:i,forwardedHost:o,forwardedProto:a});if(h(r||t||"")&&s)return tn(e,"cross-origin-referrer")},tH=e=>{let{apiKey:t,secretKey:r,clientUat:n,cookieToken:i}=e;if(function(e){return e.startsWith("live_")||e.startsWith("sk_live_")}(r||t||"")&&!n&&!i)return tr(e,"cookie-and-uat-missing")},tF=e=>{let{clientUat:t}=e;if("0"===t)return tr(e,"standard-signed-out")},t$=e=>{let{clientUat:t,cookieToken:r}=e;if(t&&Number.parseInt(t)>0&&!r)return tn(e,"cookie-missing")},tq=async e=>{let{headerToken:t}=e,r=await tG(e,t);return await tt({...e,token:t},r)},tV=async e=>{let{cookieToken:t,clientUat:r}=e,n=await tG(e,t),i=await tt({...e,token:t},n),o=i.toAuth().sessionClaims.iat<Number.parseInt(r);return!r||o?tn(e,"cookie-outdated"):i};async function tW(e,t){for(let r of t){let t=await r(e);if(t)return t}return tr(e,"unexpected-error")}async function tG(e,t){let{isSatellite:r,proxyUrl:n}=e;return tB(t,{...e,issuer:r?null:n||(e=>e.startsWith("https://clerk.")||e.includes(".clerk.accounts"))})}var tJ=e=>{let{clientUat:t,isSatellite:r,searchParams:n,userAgent:i}=e,o=!t||"0"===t;return r&&o&&!tI(i)?tr(e,"satellite-needs-syncing"):r&&o&&!tU(n)?tn(e,"satellite-needs-syncing"):void 0};async function tY(e){let{cookies:t,headers:r,searchParams:n}=e3(e?.request);async function i(){try{return await tW(e,[tq])}catch(e){return s(e,"header")}}async function a(){try{return await tW(e,[tL,tM,tJ,tK,tH,tD,tz,t$,tF,tV])}catch(e){return s(e,"cookie")}}function s(t,r){return t instanceof eX?(t.tokenCarrier=r,["token-expired","token-not-active-yet"].includes(t.reason))?"header"===r?function(e,t,r=""){let{frontendApi:n,publishableKey:i,isSatellite:o,domain:a,signInUrl:s,signUpUrl:l,afterSignInUrl:u,afterSignUpUrl:c}=e;return{status:"unknown",reason:t,message:r,frontendApi:n,publishableKey:i,isSatellite:o,domain:a,signInUrl:s,signUpUrl:l,afterSignInUrl:u,afterSignUpUrl:c,isSignedIn:!1,isInterstitial:!1,isUnknown:!0,toAuth:()=>null,token:null}}(e,t.reason,t.getFullMessage()):tn(e,t.reason,t.getFullMessage()):tr(e,t.reason,t.getFullMessage()):tr(e,"unexpected-error",t.message)}return(e.frontendApi&&(0,o.x9)("frontendApi","Use `publishableKey` instead."),e.apiKey&&(0,o.x9)("apiKey","Use `secretKey` instead."),ep((e={...e,...tQ(e,r),frontendApi:d(e.publishableKey)?.frontendApi||e.frontendApi,apiUrl:e.apiUrl||eu,apiVersion:e.apiVersion||"v1",cookieToken:e.cookieToken||t?.(ef.Cookies.Session),clientUat:e.clientUat||t?.(ef.Cookies.ClientUat),searchParams:e.searchParams||n||void 0}).secretKey||e.apiKey),e.isSatellite&&(function(e,t){if(!e&&h(t))throw Error("Missing signInUrl. Pass a signInUrl for dev instances if an app is satellite")}(e.signInUrl,e.secretKey||e.apiKey),e.signInUrl&&e.origin&&function(e,t){let r;try{r=new URL(e)}catch{throw Error("The signInUrl needs to have a absolute url format.")}if(r.origin===t)throw Error("The signInUrl needs to be on a different origin than your satellite application.")}(e.signInUrl,e.origin),function(e){if(!e)throw Error("Missing domain and proxyUrl. A satellite application needs to specify a domain or a proxyUrl")}(e.proxyUrl||e.domain)),e.headerToken)?i():a()}var tX=e=>{let{frontendApi:t,isSignedIn:r,proxyUrl:n,isInterstitial:i,reason:o,message:a,publishableKey:s,isSatellite:l,domain:u}=e;return{frontendApi:t,isSignedIn:r,proxyUrl:n,isInterstitial:i,reason:o,message:a,publishableKey:s,isSatellite:l,domain:u}},tQ=(e,t)=>t?{headerToken:e9(e.headerToken||t(ef.Headers.Authorization)),origin:e.origin||t(ef.Headers.Origin),host:e.host||t(ef.Headers.Host),forwardedHost:e.forwardedHost||t(ef.Headers.ForwardedHost),forwardedPort:e.forwardedPort||t(ef.Headers.ForwardedPort),forwardedProto:e.forwardedProto||t(ef.Headers.CloudFrontForwardedProto)||t(ef.Headers.ForwardedProto),referrer:e.referrer||t(ef.Headers.Referrer),userAgent:e.userAgent||t(ef.Headers.UserAgent)}:{},tZ=(e,t)=>{let r;if(e.startsWith("http"))r=new URL(e);else{if(!t||!t.startsWith("http"))throw Error("destination url or return back url should be an absolute path url!");let n=new URL(t);r=new URL(e,n.origin)}return t&&r.searchParams.set("redirect_url",t),r.toString()};function t0({redirectAdapter:e,signUpUrl:t,signInUrl:r,frontendApi:n,publishableKey:i}){n?(0,o.x9)("frontendApi","Use `publishableKey` instead."):n=d(i)?.frontendApi;let a=function(e){if(!e)return"";let t=e.replace(/(clerk\.accountsstage\.)/,"accountsstage.").replace(/(clerk\.accounts\.|clerk\.)/,"accounts.");return`https://${t}`}(n);return{redirectToSignUp:({returnBackUrl:r}={})=>{t||a||I.throwMissingPublishableKeyError();let n=`${a}/sign-up`;return e(tZ(t||n,r))},redirectToSignIn:({returnBackUrl:t}={})=>{r||a||I.throwMissingPublishableKeyError();let n=`${a}/sign-in`;return e(tZ(r||n,t))}}}function t1(e){let t={...e},r=eH(t),n=function(e){let{apiClient:t}=e,{apiKey:r="",secretKey:n="",jwtKey:i="",apiUrl:a=eu,apiVersion:l="v1",frontendApi:u="",proxyUrl:c="",publishableKey:h="",isSatellite:f=!1,domain:p="",audience:g="",userAgent:y}=e.options;return{authenticateRequest:({apiKey:e,secretKey:t,audience:o,frontendApi:s,proxyUrl:d,publishableKey:y,jwtKey:m,isSatellite:b,domain:v,searchParams:w,...S})=>tY({...S,apiKey:e||r,secretKey:t||n,audience:o||g,apiUrl:a,apiVersion:l,frontendApi:s||u,proxyUrl:d||c,publishableKey:y||h,isSatellite:b||f,domain:v||p,jwtKey:m||i,searchParams:w}),localInterstitial:({frontendApi:e,publishableKey:t,proxyUrl:r,isSatellite:n,domain:i,...a})=>(function(e){e.frontendApi&&(0,o.x9)("frontendApi","Use `publishableKey` instead."),e.pkgVersion&&(0,o.x9)("pkgVersion","Use `clerkJSVersion` instead."),e.frontendApi=d(e.publishableKey)?.frontendApi||e.frontendApi||"";let t=M(e.frontendApi)?"":function(e){let t;if(!e)return"";if(e.match(/^(clerk\.)+\w*$/))t=/(clerk\.)*(?=clerk\.)/;else{if(e.match(/\.clerk.accounts/))return e;t=/^(clerk\.)*/gi}let r=e.replace(t,"");return`clerk.${r}`}(e.domain),{debugData:r,frontendApi:n,pkgVersion:i,clerkJSUrl:a,clerkJSVersion:l,publishableKey:u,proxyUrl:c,isSatellite:h=!1,domain:f,signInUrl:p}=e;return`
    <head>
        <meta charset="UTF-8" />
        <style>
          @media (prefers-color-scheme: dark) {
            body {
              background-color: black;
            }
          }
        </style>
    </head>
    <body>
        <script>
            window.__clerk_frontend_api = '${n}';
            window.__clerk_debug = ${JSON.stringify(r||{})};
            ${c?`window.__clerk_proxy_url = '${c}'`:""}
            ${f?`window.__clerk_domain = '${f}'`:""}
            window.startClerk = async () => {
                function formRedirect(){
                    const form = '<form method="get" action="" name="redirect"></form>';
                    document.body.innerHTML = document.body.innerHTML + form;

                    const searchParams = new URLSearchParams(window.location.search);
                    for (let paramTuple of searchParams) {
                        const input = document.createElement("input");
                        input.type = "hidden";
                        input.name = paramTuple[0];
                        input.value = paramTuple[1];
                        document.forms.redirect.appendChild(input);
                    }
                    const url = new URL(window.location.origin + window.location.pathname + window.location.hash);
                    window.history.pushState({}, '', url);

                    document.forms.redirect.action = window.location.pathname + window.location.hash;
                    document.forms.redirect.submit();
                }

                const Clerk = window.Clerk;
                try {
                    await Clerk.load({
                        isSatellite: ${h},
                        isInterstitial: true,
                        signInUrl: ${p?`'${p}'`:void 0}
                    });
                    if(Clerk.loaded){
                      if(window.location.href.indexOf("#") === -1){
                        window.location.href = window.location.href;
                      } else if (window.navigator.userAgent.toLowerCase().includes("firefox/")){
                          formRedirect();
                      } else {
                          window.location.reload();
                      }
                    }
                } catch (err) {
                    console.error('Clerk: ', err);
                }
            };
            (() => {
                const script = document.createElement('script');
                ${u?`script.setAttribute('data-clerk-publishable-key', '${u}');`:`script.setAttribute('data-clerk-frontend-api', '${n}');`}

                ${f?`script.setAttribute('data-clerk-domain', '${f}');`:""}
                ${c?`script.setAttribute('data-clerk-proxy-url', '${c}')`:""};
                script.async = true;
                script.src = '${a||s(c||t||n,{pkgVersion:i,clerkJSVersion:l})}';
                script.crossOrigin = 'anonymous';
                script.addEventListener('load', startClerk);
                document.body.appendChild(script);
            })();
        </script>
    </body>
`})({...a,frontendApi:e||u,proxyUrl:r||c,publishableKey:t||h,isSatellite:n||f,domain:i||p}),remotePublicInterstitial:({frontendApi:e,publishableKey:t,proxyUrl:r,isSatellite:n,domain:i,userAgent:o,...s})=>eQ({...s,apiUrl:a,frontendApi:e||u,publishableKey:t||h,proxyUrl:r||c,isSatellite:n||f,domain:i||p,userAgent:o||y}),remotePrivateInterstitial:()=>t.interstitial.getInterstitial(),remotePublicInterstitialUrl:eZ,debugRequestState:tX}}({options:t,apiClient:r}),i={...r,...n,__unstable_options:t};return(0,o.x6)(i,"__unstable_options","Use `createClerkClient({...})` to create a new clerk instance instead."),i}},52743:(e,t,r)=>{"use strict";let n;try{(n=r(6005).webcrypto)||(n=new(r(34456)).w)}catch(e){n=new(r(34456)).w}e.exports=n},27988:(e,t,r)=>{"use strict";let{fetch:n,Blob:i,FormData:o,Headers:a,Request:s,Response:l,AbortController:u}=r(41434);e.exports=n,e.exports.RuntimeBlob=i,e.exports.RuntimeFormData=o,e.exports.RuntimeHeaders=a,e.exports.RuntimeRequest=s,e.exports.RuntimeResponse=l,e.exports.RuntimeAbortController=u,e.exports.RuntimeFetch=n},29600:(e,t,r)=>{"use strict";r.d(t,{I:()=>f,E:()=>p});var n=r(15259),i=r(55288),o=r(68671);let a=(e,t={})=>{let r;let a=(0,o.to)(e,"AuthToken"),s=(0,o.to)(e,"AuthStatus"),l=(0,o.to)(e,"AuthMessage"),u=(0,o.to)(e,"AuthReason"),c={apiKey:i.$h,secretKey:i.Cn,apiUrl:i.T5,apiVersion:i.Gn,authStatus:s,authMessage:l,authReason:u};if(s&&s===n.tD.SignedIn){let{payload:e,raw:t}=(0,n.t5)(a);r=(0,n.B9)(e,{...c,token:t.text})}else r=(0,n.JH)(c);let d=(0,n.z3)((0,n.aR)({...r,...t}));return(0,o.JV)({},d)};var s=r(83614),l=r(58330),u=r(68443);let c=({debugLoggerName:e,noAuthStatusMessage:t})=>(0,l.n)(e,e=>(r,a)=>{"true"===(0,o.Pg)(r,n._G.Headers.EnableDebug)&&e.enable();let l=(0,o.to)(r,"AuthToken"),u=(0,o.to)(r,"AuthStatus"),c=(0,o.to)(r,"AuthMessage"),d=(0,o.to)(r,"AuthReason");if(e.debug("Debug",{authReason:d,authMessage:c,authStatus:u,authToken:l}),!u)throw Error(t);let h={apiKey:a?.apiKey||i.$h,authStatus:u,authMessage:c,secretKey:a?.secretKey||i.Cn,authReason:d,authToken:l,apiUrl:i.T5,apiVersion:i.Gn};if(e.debug("Options debug",h),u!==n.tD.SignedIn)return(0,n.JH)(h);let f=(0,n.t5)(l);e.debug("JWT debug",f.raw.text);let p=(0,n.B9)(f.payload,{...h,token:f.raw.text});return p&&(p.user&&(0,s.x6)(p,"user","Use `clerkClient.users.getUser` instead."),p.organization&&(0,s.x6)(p,"organization","Use `clerkClient.organizations.getOrganization` instead."),p.session&&(0,s.x6)(p,"session","Use `clerkClient.sessions.getSession` instead.")),p});c({debugLoggerName:"getAuth()",noAuthStatusMessage:(0,u.PL)()});var d=r(80424);let h=()=>{try{let{headers:e}=r(7439);return new d.Z("https://placeholder.com",{headers:e()})}catch(e){if(e&&"message"in e&&"string"==typeof e.message&&e.message.toLowerCase().includes("dynamic server usage"))throw e;throw Error(`Clerk: auth() and currentUser() are only supported in App Router (/app directory).
If you're using /pages, try getAuth() instead.
Original error: ${e}`)}},f=()=>{let e=c({debugLoggerName:"auth()",noAuthStatusMessage:(0,u.It)()})(h()),{notFound:t,redirect:n}=r(867);return e.protect=(r,i)=>{let o=r?.redirectUrl?void 0:r,a=r?.redirectUrl||i?.redirectUrl,s=()=>{a&&n(a),t()};return e.userId?o?"function"==typeof o?o(e.has)?{...e}:s():e.has(o)?{...e}:s():{...e}:s()},e},p=()=>a(h())},91296:(e,t,r)=>{"use strict";r.d(t,{cL:()=>s,l8:()=>l});var n=r(86843);let i=(0,n.createProxy)(String.raw`/Users/<USER>/Downloads/Coding/IBC/Group Creator/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js`),{__esModule:o,$$typeof:a}=i;i.default,(0,n.createProxy)(String.raw`/Users/<USER>/Downloads/Coding/IBC/Group Creator/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js#CreateOrganization`),(0,n.createProxy)(String.raw`/Users/<USER>/Downloads/Coding/IBC/Group Creator/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js#OrganizationList`),(0,n.createProxy)(String.raw`/Users/<USER>/Downloads/Coding/IBC/Group Creator/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js#OrganizationProfile`),(0,n.createProxy)(String.raw`/Users/<USER>/Downloads/Coding/IBC/Group Creator/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js#OrganizationSwitcher`);let s=(0,n.createProxy)(String.raw`/Users/<USER>/Downloads/Coding/IBC/Group Creator/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js#SignIn`);(0,n.createProxy)(String.raw`/Users/<USER>/Downloads/Coding/IBC/Group Creator/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js#SignInButton`),(0,n.createProxy)(String.raw`/Users/<USER>/Downloads/Coding/IBC/Group Creator/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js#SignInWithMetamaskButton`),(0,n.createProxy)(String.raw`/Users/<USER>/Downloads/Coding/IBC/Group Creator/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js#SignOutButton`),(0,n.createProxy)(String.raw`/Users/<USER>/Downloads/Coding/IBC/Group Creator/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js#SignUp`),(0,n.createProxy)(String.raw`/Users/<USER>/Downloads/Coding/IBC/Group Creator/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js#SignUpButton`);let l=(0,n.createProxy)(String.raw`/Users/<USER>/Downloads/Coding/IBC/Group Creator/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js#UserButton`);(0,n.createProxy)(String.raw`/Users/<USER>/Downloads/Coding/IBC/Group Creator/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js#UserProfile`)},85183:(e,t,r)=>{"use strict";r.d(t,{_:()=>n});let n={Headers:{NextRewrite:"x-middleware-rewrite",NextResume:"x-middleware-next",NextRedirect:"Location"}}},14677:(e,t,r)=>{"use strict";r.d(t,{El:()=>b,CH:()=>v});var n=r(40002),i=r.n(n);let o=e=>({...e,frontendApi:e.frontendApi||process.env.NEXT_PUBLIC_CLERK_FRONTEND_API||"",publishableKey:e.publishableKey||"pk_test_aGFybWxlc3Mtam9leS0zMi5jbGVyay5hY2NvdW50cy5kZXYk",clerkJSUrl:e.clerkJSUrl||process.env.NEXT_PUBLIC_CLERK_JS,clerkJSVersion:e.clerkJSVersion||process.env.NEXT_PUBLIC_CLERK_JS_VERSION,proxyUrl:e.proxyUrl||process.env.NEXT_PUBLIC_CLERK_PROXY_URL||"",domain:e.domain||process.env.NEXT_PUBLIC_CLERK_DOMAIN||"",isSatellite:e.isSatellite||"true"===process.env.NEXT_PUBLIC_CLERK_IS_SATELLITE,signInUrl:e.signInUrl||process.env.NEXT_PUBLIC_CLERK_SIGN_IN_URL||"",signUpUrl:e.signUpUrl||process.env.NEXT_PUBLIC_CLERK_SIGN_UP_URL||"",afterSignInUrl:e.afterSignInUrl||process.env.NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL||"",afterSignUpUrl:e.afterSignUpUrl||process.env.NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL||"",sdkMetadata:{name:"@clerk/nextjs",version:"4.29.7"}});var a=r(86843);let s=(0,a.createProxy)(String.raw`/Users/<USER>/Downloads/Coding/IBC/Group Creator/node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js`),{__esModule:l,$$typeof:u}=s;s.default;let c=(0,a.createProxy)(String.raw`/Users/<USER>/Downloads/Coding/IBC/Group Creator/node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js#ClientClerkProvider`);var d=r(29600),h=r(15259),f=r(55288);(0,h.gq)({apiKey:f.$h,secretKey:f.Cn,apiUrl:f.T5,apiVersion:f.Gn,userAgent:"@clerk/nextjs@4.29.7",proxyUrl:f.NM,domain:f.yK,isSatellite:f.lo}),r(9124);var p=r(78070);r(85183);let g=(e,t,r)=>(e.headers.set(t,r),e);r(58330),r(68671),r(83614),r(68443);let{redirectToSignIn:y,redirectToSignUp:m}=(0,h.uX)({redirectAdapter:e=>g(p.Z.redirect(e),h._G.Headers.ClerkRedirectTo,"true"),signInUrl:f.O0,signUpUrl:f.Dt,publishableKey:f.Am,frontendApi:f.yM}),b=function(e){let{children:t,...r}=e,n=d.E()?.__clerk_ssr_state;return i().createElement(c,{...o(r),initialState:n},t)},v=function(e){let{children:t}=e,{userId:r}=(0,d.I)();return r?i().createElement(i().Fragment,null,t):null};d.I},55288:(e,t,r)=>{"use strict";r.d(t,{$h:()=>l,Am:()=>d,Cn:()=>u,Dt:()=>y,Gn:()=>s,NM:()=>f,O0:()=>g,T5:()=>a,YU:()=>o,cE:()=>i,lo:()=>p,yK:()=>h,yM:()=>c});var n=r(83614);process.env.CLERK_JS_VERSION&&(0,n.x9)("CLERK_JS_VERSION","Use `NEXT_PUBLIC_CLERK_JS_VERSION` environment variable instead.");let i=process.env.NEXT_PUBLIC_CLERK_JS_VERSION||"",o=process.env.NEXT_PUBLIC_CLERK_JS||"",a=process.env.CLERK_API_URL||"https://api.clerk.dev",s=process.env.CLERK_API_VERSION||"v1",l=process.env.CLERK_API_KEY||"";l&&(0,n.x9)("CLERK_API_KEY","Use `CLERK_SECRET_KEY` environment variable instead.");let u=process.env.CLERK_SECRET_KEY||"",c=process.env.NEXT_PUBLIC_CLERK_FRONTEND_API||"";c&&(0,n.x9)("NEXT_PUBLIC_CLERK_FRONTEND_API","Use `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY` environment variable instead.");let d="pk_test_aGFybWxlc3Mtam9leS0zMi5jbGVyay5hY2NvdW50cy5kZXYk",h=process.env.NEXT_PUBLIC_CLERK_DOMAIN||"",f=process.env.NEXT_PUBLIC_CLERK_PROXY_URL||"",p="true"===process.env.NEXT_PUBLIC_CLERK_IS_SATELLITE,g=process.env.NEXT_PUBLIC_CLERK_SIGN_IN_URL||"",y=process.env.NEXT_PUBLIC_CLERK_SIGN_UP_URL||""},68443:(e,t,r)=>{"use strict";r.d(t,{H$:()=>i,It:()=>s,MV:()=>c,PL:()=>a,PN:()=>u,Yf:()=>l,iP:()=>o,xr:()=>n});let n=`
Missing domain and proxyUrl. A satellite application needs to specify a domain or a proxyUrl.

1) With middleware
   e.g. export default withClerkMiddleware(req => {...}, {domain:'YOUR_DOMAIN',isSatellite:true});
2) With environment variables e.g.
   NEXT_PUBLIC_CLERK_DOMAIN='YOUR_DOMAIN'
   NEXT_PUBLIC_CLERK_IS_SATELLITE='true'
   `,i=`
Invalid signInUrl. A satellite application requires a signInUrl for development instances.
Check if signInUrl is missing from your configuration or if it is not an absolute URL

1) With middleware
   e.g. export default withClerkMiddleware(req => {...}, {signInUrl:'SOME_URL',isSatellite:true});
2) With environment variables e.g.
   NEXT_PUBLIC_CLERK_SIGN_IN_URL='SOME_URL'
   NEXT_PUBLIC_CLERK_IS_SATELLITE='true'`,o=(e,t)=>`Clerk: The middleware was skipped for this request URL: ${e}. For performance reasons, it's recommended to your middleware matcher to:
export const config = {
  matcher: ${t},
};

Alternatively, you can set your own ignoredRoutes. See https://clerk.com/docs/nextjs/middleware
(This log only appears in development mode)
`,a=()=>s("getAuth"),s=(e="auth")=>`Clerk: ${e}() was called but Clerk can't detect usage of authMiddleware(). Please ensure the following:
- authMiddleware() is used in your Next.js Middleware.
- Your Middleware matcher is configured to match this route or page.
- If you are using the src directory, make sure the Middleware file is inside of it.

For more details, see https://clerk.com/docs/quickstarts/nextjs
`,l=e=>`Clerk: Clock skew detected. This usually means that your system clock is inaccurate. Clerk will continuously try to issue new tokens, as the existing ones will be treated as "expired" due to clock skew.

To resolve this issue, make sure your system's clock is set to the correct time (e.g. turn off and on automatic time synchronization).

---

${e}`,u=()=>`Clerk: Infinite redirect loop detected. That usually means that we were not able to determine the auth state for this request. A list of common causes and solutions follows.

Reason 1:
Your Clerk instance keys are incorrect, or you recently changed keys (Publishable Key, Secret Key).
How to resolve:
-> Make sure you're using the correct keys from the Clerk Dashboard. If you changed keys recently, make sure to clear your browser application data and cookies.

Reason 2:
A bug that may have already been fixed in the latest version of Clerk NextJS package.
How to resolve:
-> Make sure you are using the latest version of '@clerk/nextjs' and 'next'.
`,c=(e,t,r,n,i)=>{let o=n?`INFO: Clerk: The request to ${e} is being protected (401) because there is no signed-in user, and the path is included in \`apiRoutes\`. To prevent this behavior, choose one of:`:`INFO: Clerk: The request to ${e} is being redirected because there is no signed-in user, and the path is not included in \`ignoredRoutes\` or \`publicRoutes\`. To prevent this behavior, choose one of:`,a=n?`To prevent Clerk authentication from protecting (401) the api route, remove the rule matching "${e}" from the \`apiRoutes\` array passed to authMiddleware`:void 0,s=t?`To make the route accessible to both signed in and signed out users, add "${e}" to the \`publicRoutes\` array passed to authMiddleware`:`To make the route accessible to both signed in and signed out users, pass \`publicRoutes: ["${e}"]\` to authMiddleware`,l=[...i,e].map(e=>`"${e}"`).join(", "),u=r?`To prevent Clerk authentication from running at all, add "${e}" to the \`ignoredRoutes\` array passed to authMiddleware`:`To prevent Clerk authentication from running at all, pass \`ignoredRoutes: [${l}]\` to authMiddleware`;return`${o}

${[a,s,u,"Pass a custom `afterAuth` to authMiddleware, and replace Clerk's default behavior of redirecting unless a route is included in publicRoutes"].filter(Boolean).map((e,t)=>`${t+1}. ${e}`).join("\n")}

For additional information about middleware, please visit https://clerk.com/docs/nextjs/middleware
(This log only appears in development mode, or if \`debug: true\` is passed to authMiddleware)`}},68671:(e,t,r)=>{"use strict";r.d(t,{AI:()=>S,oU:()=>w,to:()=>d,Pg:()=>h,cu:()=>k,JV:()=>b,xr:()=>_,RL:()=>v,u4:()=>c,Mp:()=>y});var n=r(15259);function i(e,t,r){return"function"==typeof e?e(t):void 0!==e?e:void 0!==r?r:void 0}function o(e){return/^http(s)?:\/\//.test(e||"")}r(9124),r(17801);var a=r(78070),s=r(85183),l=r(55288),u=r(68443);function c(e,t,r){Object.assign(e,{[t]:r})}function d(e,t){var r;return((r=n._G.Attributes[t])in e?e[r]:void 0)||h(e,n._G.Headers[t])||("AuthStatus"===t||"AuthToken"===t)&&function(e,t){let r;if(f(e))return e.nextUrl.searchParams.get(t);if("query"in e&&(r=e.query[t]),!r){let n=(e.url||"").split("?")[1];r=new URLSearchParams(n).get(t)}return r}(e,t)||void 0}function h(e,t){return f(e)?e.headers.get(t):e.headers[t]||e.headers[t.toLowerCase()]||e.socket?._httpMessage?.getHeader(t)}function f(e){try{let{headers:t,nextUrl:r,cookies:n}=e||{};return"function"==typeof t?.get&&"function"==typeof r?.searchParams.get&&"function"==typeof n?.get}catch(e){return!1}}let p="x-middleware-override-headers",g="x-middleware-request",y=(e,t,r)=>{e.headers.get(p)||(e.headers.set(p,[...t.headers.keys()]),t.headers.forEach((t,r)=>{e.headers.set(`${g}-${r}`,t)})),Object.entries(r).forEach(([t,r])=>{e.headers.set(p,`${e.headers.get(p)},${t}`),e.headers.set(`${g}-${t}`,r)})},m=()=>{try{let e="clerkTest",t=`${g}-${e}`;return a.Z.next({request:{headers:new Headers({[e]:"true"})}}).headers.has(t)}catch(e){return!1}},b=(e,t)=>{let r={...t};return{...e,__clerk_ssr_state:r}};function v(e){return e.startsWith("test_")||e.startsWith("sk_test_")}function w(e,t,r){let i;let{reason:o,message:l,status:u,token:c}=r;if(t||(t=a.Z.next()),t.headers.get(s._.Headers.NextRedirect))return t;"1"===t.headers.get(s._.Headers.NextResume)&&(t.headers.delete(s._.Headers.NextResume),i=new URL(e.url));let d=t.headers.get(s._.Headers.NextRewrite);if(d){let r=new URL(e.url);if((i=new URL(d)).origin!==r.origin)return t}return i&&(m()?y(t,e,{[n._G.Headers.AuthStatus]:u,[n._G.Headers.AuthToken]:c||"",[n._G.Headers.AuthMessage]:l||"",[n._G.Headers.AuthReason]:o||""}):(t.headers.set(n._G.Headers.AuthStatus,u),t.headers.set(n._G.Headers.AuthToken,c||""),t.headers.set(n._G.Headers.AuthMessage,l||""),t.headers.set(n._G.Headers.AuthReason,o||""),i.searchParams.set(n._G.SearchParams.AuthStatus,u),i.searchParams.set(n._G.SearchParams.AuthToken,c||""),i.searchParams.set(n._G.Headers.AuthMessage,l||""),i.searchParams.set(n._G.Headers.AuthReason,o||"")),t.headers.set(s._.Headers.NextRewrite,i.href)),t}let S=()=>a.Z.json(null,{status:401,statusText:"Unauthorized"}),_=(e,t)=>{let r=new URL(e),n=new URL(t);return r.origin!==n.origin},k=(e,t)=>{let r;let a=(0,n.oF)(e),s=i(t?.proxyUrl,a,l.NM);r=s&&!o(s)?new URL(s,a).toString():s;let c=i(t.isSatellite,new URL(e.url),l.lo),d=i(t.domain,new URL(e.url),l.yK),h=t?.signInUrl||l.O0;if(c&&!r&&!d)throw Error(u.xr);if(c&&!o(h)&&v(l.Cn||l.$h))throw Error(u.H$);return{proxyUrl:r,isSatellite:c,domain:d,signInUrl:h}}},58330:(e,t,r)=>{"use strict";r.d(t,{n:()=>a});let n=e=>{if(!e||"string"!=typeof e)return e;try{return(e||"").replace(/^(sk_(live|test)_)(.+?)(.{3})$/,"$1*********$4")}catch(e){return""}},i=e=>(Array.isArray(e)?e:[e]).map(e=>"string"==typeof e?n(e):JSON.stringify(Object.fromEntries(Object.entries(e).map(([e,t])=>[e,n(t)])),null,2)).join(", "),o=(e,t)=>()=>{let r=[],n=!1;return{enable:()=>{n=!0},debug:(...e)=>{n&&r.push(e.map(e=>"function"==typeof e?e():e))},commit:()=>{if(n){for(let n of(console.log(`[clerk debug start: ${e}]`),r)){let e=t(n);e=e.split("\n").map(e=>`  ${e}`).join("\n"),process.env.VERCEL&&(e=function(e,t){let r=new TextEncoder,n=new TextDecoder("utf-8"),i=r.encode(e).slice(0,4096);return n.decode(i).replace(/\uFFFD/g,"")}(e,0)),console.log(e)}console.log(`[clerk debug end: ${e}] (@clerk/nextjs=4.29.7,next=14.1.0)`)}}}},a=(e,t)=>(...r)=>{let n=("string"==typeof e?o(e,i):e)(),a=t(n);try{let e=a(...r);if("object"==typeof e&&"then"in e&&"function"==typeof e.then)return e.then(e=>(n.commit(),e)).catch(e=>{throw n.commit(),e});return n.commit(),e}catch(e){throw n.commit(),e}}},34456:(e,t,r)=>{"use strict";r.d(t,{w:()=>nZ});var n,i,o,a={};r.r(a),r.d(a,{Any:()=>eD,BaseBlock:()=>D,BaseStringBlock:()=>z,BitString:()=>er,BmpString:()=>eS,Boolean:()=>Q,CharacterString:()=>eB,Choice:()=>ez,Constructed:()=>W,DATE:()=>eN,DateTime:()=>eM,Duration:()=>eL,EndOfContent:()=>J,Enumerated:()=>ea,GeneralString:()=>eT,GeneralizedTime:()=>eU,GraphicString:()=>eO,HexBlock:()=>U,IA5String:()=>ex,Integer:()=>eo,Null:()=>Y,NumericString:()=>eE,ObjectIdentifier:()=>eu,OctetString:()=>ee,Primitive:()=>F,PrintableString:()=>eA,RawData:()=>eF,RelativeObjectIdentifier:()=>eh,Repeated:()=>eH,Sequence:()=>ef,Set:()=>ep,TIME:()=>eK,TeletexString:()=>eP,TimeOfDay:()=>eI,UTCTime:()=>ej,UniversalString:()=>ek,Utf8String:()=>ev,ValueBlock:()=>I,VideotexString:()=>eC,ViewWriter:()=>E,VisibleString:()=>eR,compareSchema:()=>e$,fromBER:()=>q,verifySchema:()=>eq});class s{static isArrayBuffer(e){return"[object ArrayBuffer]"===Object.prototype.toString.call(e)}static toArrayBuffer(e){return this.isArrayBuffer(e)?e:e.byteLength===e.buffer.byteLength||0===e.byteOffset&&e.byteLength===e.buffer.byteLength?e.buffer:this.toUint8Array(e.buffer).slice(e.byteOffset,e.byteOffset+e.byteLength).buffer}static toUint8Array(e){return this.toView(e,Uint8Array)}static toView(e,t){if(e.constructor===t)return e;if(this.isArrayBuffer(e))return new t(e);if(this.isArrayBufferView(e))return new t(e.buffer,e.byteOffset,e.byteLength);throw TypeError("The provided value is not of type '(ArrayBuffer or ArrayBufferView)'")}static isBufferSource(e){return this.isArrayBufferView(e)||this.isArrayBuffer(e)}static isArrayBufferView(e){return ArrayBuffer.isView(e)||e&&this.isArrayBuffer(e.buffer)}static isEqual(e,t){let r=s.toUint8Array(e),n=s.toUint8Array(t);if(r.length!==n.byteLength)return!1;for(let e=0;e<r.length;e++)if(r[e]!==n[e])return!1;return!0}static concat(...e){let t;t=!Array.isArray(e[0])||e[1]instanceof Function?Array.isArray(e[0])&&e[1]instanceof Function?e[0]:e[e.length-1]instanceof Function?e.slice(0,e.length-1):e:e[0];let r=0;for(let e of t)r+=e.byteLength;let n=new Uint8Array(r),i=0;for(let e of t){let t=this.toUint8Array(e);n.set(t,i),i+=t.length}return e[e.length-1]instanceof Function?this.toView(n,e[e.length-1]):n.buffer}}let l="string",u=/^[0-9a-f\s]+$/i,c=/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/,d=/^[a-zA-Z0-9-_]+$/;class h{static fromString(e){let t=unescape(encodeURIComponent(e)),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r.buffer}static toString(e){let t=s.toUint8Array(e),r="";for(let e=0;e<t.length;e++)r+=String.fromCharCode(t[e]);return decodeURIComponent(escape(r))}}class f{static toString(e,t=!1){let r=s.toArrayBuffer(e),n=new DataView(r),i="";for(let e=0;e<r.byteLength;e+=2)i+=String.fromCharCode(n.getUint16(e,t));return i}static fromString(e,t=!1){let r=new ArrayBuffer(2*e.length),n=new DataView(r);for(let r=0;r<e.length;r++)n.setUint16(2*r,e.charCodeAt(r),t);return r}}class p{static isHex(e){return typeof e===l&&u.test(e)}static isBase64(e){return typeof e===l&&c.test(e)}static isBase64Url(e){return typeof e===l&&d.test(e)}static ToString(e,t="utf8"){let r=s.toUint8Array(e);switch(t.toLowerCase()){case"utf8":return this.ToUtf8String(r);case"binary":return this.ToBinary(r);case"hex":return this.ToHex(r);case"base64":return this.ToBase64(r);case"base64url":return this.ToBase64Url(r);case"utf16le":return f.toString(r,!0);case"utf16":case"utf16be":return f.toString(r);default:throw Error(`Unknown type of encoding '${t}'`)}}static FromString(e,t="utf8"){if(!e)return new ArrayBuffer(0);switch(t.toLowerCase()){case"utf8":return this.FromUtf8String(e);case"binary":return this.FromBinary(e);case"hex":return this.FromHex(e);case"base64":return this.FromBase64(e);case"base64url":return this.FromBase64Url(e);case"utf16le":return f.fromString(e,!0);case"utf16":case"utf16be":return f.fromString(e);default:throw Error(`Unknown type of encoding '${t}'`)}}static ToBase64(e){let t=s.toUint8Array(e);return"undefined"!=typeof btoa?btoa(this.ToString(t,"binary")):Buffer.from(t).toString("base64")}static FromBase64(e){let t=this.formatString(e);if(!t)return new ArrayBuffer(0);if(!p.isBase64(t))throw TypeError("Argument 'base64Text' is not Base64 encoded");return"undefined"!=typeof atob?this.FromBinary(atob(t)):new Uint8Array(Buffer.from(t,"base64")).buffer}static FromBase64Url(e){let t=this.formatString(e);if(!t)return new ArrayBuffer(0);if(!p.isBase64Url(t))throw TypeError("Argument 'base64url' is not Base64Url encoded");return this.FromBase64(this.Base64Padding(t.replace(/\-/g,"+").replace(/\_/g,"/")))}static ToBase64Url(e){return this.ToBase64(e).replace(/\+/g,"-").replace(/\//g,"_").replace(/\=/g,"")}static FromUtf8String(e,t=p.DEFAULT_UTF8_ENCODING){switch(t){case"ascii":return this.FromBinary(e);case"utf8":return h.fromString(e);case"utf16":case"utf16be":return f.fromString(e);case"utf16le":case"usc2":return f.fromString(e,!0);default:throw Error(`Unknown type of encoding '${t}'`)}}static ToUtf8String(e,t=p.DEFAULT_UTF8_ENCODING){switch(t){case"ascii":return this.ToBinary(e);case"utf8":return h.toString(e);case"utf16":case"utf16be":return f.toString(e);case"utf16le":case"usc2":return f.toString(e,!0);default:throw Error(`Unknown type of encoding '${t}'`)}}static FromBinary(e){let t=e.length,r=new Uint8Array(t);for(let n=0;n<t;n++)r[n]=e.charCodeAt(n);return r.buffer}static ToBinary(e){let t=s.toUint8Array(e),r="";for(let e=0;e<t.length;e++)r+=String.fromCharCode(t[e]);return r}static ToHex(e){let t=s.toUint8Array(e),r="",n=t.length;for(let e=0;e<n;e++){let n=t[e];n<16&&(r+="0"),r+=n.toString(16)}return r}static FromHex(e){let t=this.formatString(e);if(!t)return new ArrayBuffer(0);if(!p.isHex(t))throw TypeError("Argument 'hexString' is not HEX encoded");t.length%2&&(t=`0${t}`);let r=new Uint8Array(t.length/2);for(let e=0;e<t.length;e+=2){let n=t.slice(e,e+2);r[e/2]=parseInt(n,16)}return r.buffer}static ToUtf16String(e,t=!1){return f.toString(e,t)}static FromUtf16String(e,t=!1){return f.fromString(e,t)}static Base64Padding(e){let t=4-e.length%4;if(t<4)for(let r=0;r<t;r++)e+="=";return e}static formatString(e){return(null==e?void 0:e.replace(/[\n\r\t ]/g,""))||""}}function g(...e){let t=e.map(e=>e.byteLength).reduce((e,t)=>e+t),r=new Uint8Array(t),n=0;return e.map(e=>new Uint8Array(e)).forEach(e=>{for(let t of e)r[n++]=t}),r.buffer}function y(e,t){let r=0;if(1===e.length)return e[0];for(let n=e.length-1;n>=0;n--)r+=e[e.length-1-n]*Math.pow(2,t*n);return r}function m(e,t,r=-1){let n=e,i=0,o=Math.pow(2,t);for(let a=1;a<8;a++){if(e<o){let e;if(r<0)e=new ArrayBuffer(a),i=a;else{if(r<a)return new ArrayBuffer(0);e=new ArrayBuffer(r),i=r}let o=new Uint8Array(e);for(let e=a-1;e>=0;e--){let r=Math.pow(2,e*t);o[i-e-1]=Math.floor(n/r),n-=o[i-e-1]*r}return e}o*=Math.pow(2,t)}return new ArrayBuffer(0)}function b(...e){let t=0,r=0;for(let r of e)t+=r.length;let n=new ArrayBuffer(t),i=new Uint8Array(n);for(let t of e)i.set(t,r),r+=t.length;return i}function v(){let e=new Uint8Array(this.valueHex);if(this.valueHex.byteLength>=2){let t=255===e[0]&&128&e[1],r=0===e[0]&&(128&e[1])==0;(t||r)&&this.warnings.push("Needlessly long format")}let t=new ArrayBuffer(this.valueHex.byteLength),r=new Uint8Array(t);for(let e=0;e<this.valueHex.byteLength;e++)r[e]=0;r[0]=128&e[0];let n=y(r,8),i=new ArrayBuffer(this.valueHex.byteLength),o=new Uint8Array(i);for(let t=0;t<this.valueHex.byteLength;t++)o[t]=e[t];return o[0]&=127,y(o,8)-n}function w(e,t){let r=e.toString(10);if(t<r.length)return"";let n=t-r.length,i=Array(n);for(let e=0;e<n;e++)i[e]="0";return i.join("").concat(r)}/*!
 * Copyright (c) 2014, GMO GlobalSign
 * Copyright (c) 2015-2022, Peculiar Ventures
 * All rights reserved.
 * 
 * Author 2014-2019, Yury Strozhevsky
 * 
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 * 
 * * Redistributions of source code must retain the above copyright notice, this
 *   list of conditions and the following disclaimer.
 * 
 * * Redistributions in binary form must reproduce the above copyright notice, this
 *   list of conditions and the following disclaimer in the documentation and/or
 *   other materials provided with the distribution.
 * 
 * * Neither the name of the copyright holder nor the names of its
 *   contributors may be used to endorse or promote products derived from
 *   this software without specific prior written permission.
 * 
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
 * ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 * 
 */function S(){if("undefined"==typeof BigInt)throw Error("BigInt is not defined. Your environment doesn't implement BigInt.")}function _(e){let t=0,r=0;for(let r=0;r<e.length;r++)t+=e[r].byteLength;let n=new Uint8Array(t);for(let t=0;t<e.length;t++){let i=e[t];n.set(new Uint8Array(i),r),r+=i.byteLength}return n.buffer}function k(e,t,r,n){return t instanceof Uint8Array?t.byteLength?r<0?(e.error="Wrong parameter: inputOffset less than zero",!1):n<0?(e.error="Wrong parameter: inputLength less than zero",!1):!(t.byteLength-r-n<0)||(e.error="End of input reached before message was fully decoded (inconsistent offset and length values)",!1):(e.error="Wrong parameter: inputBuffer has zero length",!1):(e.error="Wrong parameter: inputBuffer must be 'Uint8Array'",!1)}p.DEFAULT_UTF8_ENCODING="utf8";class E{constructor(){this.items=[]}write(e){this.items.push(e)}final(){return _(this.items)}}let A=[new Uint8Array([1])],P="0123456789",C="name",x="valueHexView",O=new ArrayBuffer(0),R=new Uint8Array(0),T="EndOfContent",B="OCTET STRING",j="BIT STRING";function U(e){var t;return(t=class extends e{constructor(...e){var t;super(...e);let r=e[0]||{};this.isHexOnly=null!==(t=r.isHexOnly)&&void 0!==t&&t,this.valueHexView=r.valueHex?s.toUint8Array(r.valueHex):R}get valueHex(){return this.valueHexView.slice().buffer}set valueHex(e){this.valueHexView=new Uint8Array(e)}fromBER(e,t,r){let n=e instanceof ArrayBuffer?new Uint8Array(e):e;if(!k(this,n,t,r))return -1;let i=t+r;return(this.valueHexView=n.subarray(t,i),this.valueHexView.length)?(this.blockLength=r,i):(this.warnings.push("Zero buffer length"),t)}toBER(e=!1){return this.isHexOnly?e?new ArrayBuffer(this.valueHexView.byteLength):this.valueHexView.byteLength===this.valueHexView.buffer.byteLength?this.valueHexView.buffer:this.valueHexView.slice().buffer:(this.error="Flag 'isHexOnly' is not set, abort",O)}toJSON(){return{...super.toJSON(),isHexOnly:this.isHexOnly,valueHex:p.ToHex(this.valueHexView)}}}).NAME="hexBlock",t}class N{constructor({blockLength:e=0,error:t="",warnings:r=[],valueBeforeDecode:n=R}={}){this.blockLength=e,this.error=t,this.warnings=r,this.valueBeforeDecodeView=s.toUint8Array(n)}static blockName(){return this.NAME}get valueBeforeDecode(){return this.valueBeforeDecodeView.slice().buffer}set valueBeforeDecode(e){this.valueBeforeDecodeView=new Uint8Array(e)}toJSON(){return{blockName:this.constructor.NAME,blockLength:this.blockLength,error:this.error,warnings:this.warnings,valueBeforeDecode:p.ToHex(this.valueBeforeDecodeView)}}}N.NAME="baseBlock";class I extends N{fromBER(e,t,r){throw TypeError("User need to make a specific function in a class which extends 'ValueBlock'")}toBER(e,t){throw TypeError("User need to make a specific function in a class which extends 'ValueBlock'")}}I.NAME="valueBlock";class M extends U(N){constructor({idBlock:e={}}={}){var t,r,n,i;super(),e?(this.isHexOnly=null!==(t=e.isHexOnly)&&void 0!==t&&t,this.valueHexView=e.valueHex?s.toUint8Array(e.valueHex):R,this.tagClass=null!==(r=e.tagClass)&&void 0!==r?r:-1,this.tagNumber=null!==(n=e.tagNumber)&&void 0!==n?n:-1,this.isConstructed=null!==(i=e.isConstructed)&&void 0!==i&&i):(this.tagClass=-1,this.tagNumber=-1,this.isConstructed=!1)}toBER(e=!1){let t=0;switch(this.tagClass){case 1:t|=0;break;case 2:t|=64;break;case 3:t|=128;break;case 4:t|=192;break;default:return this.error="Unknown tag class",O}if(this.isConstructed&&(t|=32),this.tagNumber<31&&!this.isHexOnly){let r=new Uint8Array(1);if(!e){let e=this.tagNumber;e&=31,t|=e,r[0]=t}return r.buffer}if(!this.isHexOnly){let r=m(this.tagNumber,7),n=new Uint8Array(r),i=r.byteLength,o=new Uint8Array(i+1);if(o[0]=31|t,!e){for(let e=0;e<i-1;e++)o[e+1]=128|n[e];o[i]=n[i-1]}return o.buffer}let r=new Uint8Array(this.valueHexView.byteLength+1);if(r[0]=31|t,!e){let e=this.valueHexView;for(let t=0;t<e.length-1;t++)r[t+1]=128|e[t];r[this.valueHexView.byteLength]=e[e.length-1]}return r.buffer}fromBER(e,t,r){let n=s.toUint8Array(e);if(!k(this,n,t,r))return -1;let i=n.subarray(t,t+r);if(0===i.length)return this.error="Zero buffer length",-1;switch(192&i[0]){case 0:this.tagClass=1;break;case 64:this.tagClass=2;break;case 128:this.tagClass=3;break;case 192:this.tagClass=4;break;default:return this.error="Unknown tag class",-1}this.isConstructed=(32&i[0])==32,this.isHexOnly=!1;let o=31&i[0];if(31!==o)this.tagNumber=o,this.blockLength=1;else{let e=1,t=this.valueHexView=new Uint8Array(255),r=255;for(;128&i[e];){if(t[e-1]=127&i[e],++e>=i.length)return this.error="End of input reached before message was fully decoded",-1;if(e===r){r+=255;let e=new Uint8Array(r);for(let r=0;r<t.length;r++)e[r]=t[r];t=this.valueHexView=new Uint8Array(r)}}this.blockLength=e+1,t[e-1]=127&i[e];let n=new Uint8Array(e);for(let r=0;r<e;r++)n[r]=t[r];(t=this.valueHexView=new Uint8Array(e)).set(n),this.blockLength<=9?this.tagNumber=y(t,7):(this.isHexOnly=!0,this.warnings.push("Tag too long, represented as hex-coded"))}if(1===this.tagClass&&this.isConstructed)switch(this.tagNumber){case 1:case 2:case 5:case 6:case 9:case 13:case 14:case 23:case 24:case 31:case 32:case 33:case 34:return this.error="Constructed encoding used for primitive type",-1}return t+this.blockLength}toJSON(){return{...super.toJSON(),tagClass:this.tagClass,tagNumber:this.tagNumber,isConstructed:this.isConstructed}}}M.NAME="identificationBlock";class L extends N{constructor({lenBlock:e={}}={}){var t,r,n;super(),this.isIndefiniteForm=null!==(t=e.isIndefiniteForm)&&void 0!==t&&t,this.longFormUsed=null!==(r=e.longFormUsed)&&void 0!==r&&r,this.length=null!==(n=e.length)&&void 0!==n?n:0}fromBER(e,t,r){let n=s.toUint8Array(e);if(!k(this,n,t,r))return -1;let i=n.subarray(t,t+r);if(0===i.length)return this.error="Zero buffer length",-1;if(255===i[0])return this.error="Length block 0xFF is reserved by standard",-1;if(this.isIndefiniteForm=128===i[0],this.isIndefiniteForm)return this.blockLength=1,t+this.blockLength;if(this.longFormUsed=!!(128&i[0]),!1===this.longFormUsed)return this.length=i[0],this.blockLength=1,t+this.blockLength;let o=127&i[0];if(o>8)return this.error="Too big integer",-1;if(o+1>i.length)return this.error="End of input reached before message was fully decoded",-1;let a=t+1,l=n.subarray(a,a+o);return 0===l[o-1]&&this.warnings.push("Needlessly long encoded length"),this.length=y(l,8),this.longFormUsed&&this.length<=127&&this.warnings.push("Unnecessary usage of long length form"),this.blockLength=o+1,t+this.blockLength}toBER(e=!1){let t,r;if(this.length>127&&(this.longFormUsed=!0),this.isIndefiniteForm)return t=new ArrayBuffer(1),!1===e&&((r=new Uint8Array(t))[0]=128),t;if(this.longFormUsed){let n=m(this.length,8);if(n.byteLength>127)return this.error="Too big length",O;if(t=new ArrayBuffer(n.byteLength+1),e)return t;let i=new Uint8Array(n);(r=new Uint8Array(t))[0]=128|n.byteLength;for(let e=0;e<n.byteLength;e++)r[e+1]=i[e];return t}return t=new ArrayBuffer(1),!1===e&&((r=new Uint8Array(t))[0]=this.length),t}toJSON(){return{...super.toJSON(),isIndefiniteForm:this.isIndefiniteForm,longFormUsed:this.longFormUsed,length:this.length}}}L.NAME="lengthBlock";let K={};class D extends N{constructor({name:e="",optional:t=!1,primitiveSchema:r,...n}={},i){super(n),this.name=e,this.optional=t,r&&(this.primitiveSchema=r),this.idBlock=new M(n),this.lenBlock=new L(n),this.valueBlock=i?new i(n):new I(n)}fromBER(e,t,r){let n=this.valueBlock.fromBER(e,t,this.lenBlock.isIndefiniteForm?r:this.lenBlock.length);return -1===n?this.error=this.valueBlock.error:(this.idBlock.error.length||(this.blockLength+=this.idBlock.blockLength),this.lenBlock.error.length||(this.blockLength+=this.lenBlock.blockLength),this.valueBlock.error.length||(this.blockLength+=this.valueBlock.blockLength)),n}toBER(e,t){let r=t||new E;t||function e(t){if(t instanceof K.Constructed)for(let r of t.valueBlock.value)e(r)&&(t.lenBlock.isIndefiniteForm=!0);return!!t.lenBlock.isIndefiniteForm}(this);let n=this.idBlock.toBER(e);if(r.write(n),this.lenBlock.isIndefiniteForm)r.write(new Uint8Array([128]).buffer),this.valueBlock.toBER(e,r),r.write(new ArrayBuffer(2));else{let t=this.valueBlock.toBER(e);this.lenBlock.length=t.byteLength;let n=this.lenBlock.toBER(e);r.write(n),r.write(t)}return t?O:r.final()}toJSON(){let e={...super.toJSON(),idBlock:this.idBlock.toJSON(),lenBlock:this.lenBlock.toJSON(),valueBlock:this.valueBlock.toJSON(),name:this.name,optional:this.optional};return this.primitiveSchema&&(e.primitiveSchema=this.primitiveSchema.toJSON()),e}toString(e="ascii"){return"ascii"===e?this.onAsciiEncoding():p.ToHex(this.toBER())}onAsciiEncoding(){return`${this.constructor.NAME} : ${p.ToHex(this.valueBlock.valueBeforeDecodeView)}`}isEqual(e){return this===e||e instanceof this.constructor&&function(e,t){if(e.byteLength!==t.byteLength)return!1;let r=new Uint8Array(e),n=new Uint8Array(t);for(let e=0;e<r.length;e++)if(r[e]!==n[e])return!1;return!0}(this.toBER(),e.toBER())}}D.NAME="BaseBlock";class z extends D{constructor({value:e="",...t}={},r){super(t,r),e&&this.fromString(e)}getValue(){return this.valueBlock.value}setValue(e){this.valueBlock.value=e}fromBER(e,t,r){let n=this.valueBlock.fromBER(e,t,this.lenBlock.isIndefiniteForm?r:this.lenBlock.length);return -1===n?this.error=this.valueBlock.error:(this.fromBuffer(this.valueBlock.valueHexView),this.idBlock.error.length||(this.blockLength+=this.idBlock.blockLength),this.lenBlock.error.length||(this.blockLength+=this.lenBlock.blockLength),this.valueBlock.error.length||(this.blockLength+=this.valueBlock.blockLength)),n}onAsciiEncoding(){return`${this.constructor.NAME} : '${this.valueBlock.value}'`}}z.NAME="BaseStringBlock";class H extends U(I){constructor({isHexOnly:e=!0,...t}={}){super(t),this.isHexOnly=e}}H.NAME="PrimitiveValueBlock";class F extends D{constructor(e={}){super(e,H),this.idBlock.isConstructed=!1}}function $(e,t=0,r=e.length){let n=t,i=new D({},I),o=new N;if(!k(o,e,t,r))return i.error=o.error,{offset:-1,result:i};if(!e.subarray(t,t+r).length)return i.error="Zero buffer length",{offset:-1,result:i};let a=i.idBlock.fromBER(e,t,r);if(i.idBlock.warnings.length&&i.warnings.concat(i.idBlock.warnings),-1===a)return i.error=i.idBlock.error,{offset:-1,result:i};if(t=a,r-=i.idBlock.blockLength,a=i.lenBlock.fromBER(e,t,r),i.lenBlock.warnings.length&&i.warnings.concat(i.lenBlock.warnings),-1===a)return i.error=i.lenBlock.error,{offset:-1,result:i};if(t=a,r-=i.lenBlock.blockLength,!i.idBlock.isConstructed&&i.lenBlock.isIndefiniteForm)return i.error="Indefinite length form used for primitive encoding form",{offset:-1,result:i};let s=D;if(1===i.idBlock.tagClass){if(i.idBlock.tagNumber>=37&&!1===i.idBlock.isHexOnly)return i.error="UNIVERSAL 37 and upper tags are reserved by ASN.1 standard",{offset:-1,result:i};switch(i.idBlock.tagNumber){case 0:if(i.idBlock.isConstructed&&i.lenBlock.length>0)return i.error="Type [UNIVERSAL 0] is reserved",{offset:-1,result:i};s=K.EndOfContent;break;case 1:s=K.Boolean;break;case 2:s=K.Integer;break;case 3:s=K.BitString;break;case 4:s=K.OctetString;break;case 5:s=K.Null;break;case 6:s=K.ObjectIdentifier;break;case 10:s=K.Enumerated;break;case 12:s=K.Utf8String;break;case 13:s=K.RelativeObjectIdentifier;break;case 14:s=K.TIME;break;case 15:return i.error="[UNIVERSAL 15] is reserved by ASN.1 standard",{offset:-1,result:i};case 16:s=K.Sequence;break;case 17:s=K.Set;break;case 18:s=K.NumericString;break;case 19:s=K.PrintableString;break;case 20:s=K.TeletexString;break;case 21:s=K.VideotexString;break;case 22:s=K.IA5String;break;case 23:s=K.UTCTime;break;case 24:s=K.GeneralizedTime;break;case 25:s=K.GraphicString;break;case 26:s=K.VisibleString;break;case 27:s=K.GeneralString;break;case 28:s=K.UniversalString;break;case 29:s=K.CharacterString;break;case 30:s=K.BmpString;break;case 31:s=K.DATE;break;case 32:s=K.TimeOfDay;break;case 33:s=K.DateTime;break;case 34:s=K.Duration;break;default:{let e=i.idBlock.isConstructed?new K.Constructed:new K.Primitive;e.idBlock=i.idBlock,e.lenBlock=i.lenBlock,e.warnings=i.warnings,i=e}}}else s=i.idBlock.isConstructed?K.Constructed:K.Primitive;return a=(i=function(e,t){if(e instanceof t)return e;let r=new t;return r.idBlock=e.idBlock,r.lenBlock=e.lenBlock,r.warnings=e.warnings,r.valueBeforeDecodeView=e.valueBeforeDecodeView,r}(i,s)).fromBER(e,t,i.lenBlock.isIndefiniteForm?r:i.lenBlock.length),i.valueBeforeDecodeView=e.subarray(n,n+i.blockLength),{offset:a,result:i}}function q(e){if(!e.byteLength){let e=new D({},I);return e.error="Input buffer has zero length",{offset:-1,result:e}}return $(s.toUint8Array(e).slice(),0,e.byteLength)}K.Primitive=F,F.NAME="PRIMITIVE";class V extends I{constructor({value:e=[],isIndefiniteForm:t=!1,...r}={}){super(r),this.value=e,this.isIndefiniteForm=t}fromBER(e,t,r){var n,i;let o=s.toUint8Array(e);if(!k(this,o,t,r))return -1;if(this.valueBeforeDecodeView=o.subarray(t,t+r),0===this.valueBeforeDecodeView.length)return this.warnings.push("Zero buffer length"),t;let a=t;for(;n=this.isIndefiniteForm,i=r,(n?1:i)>0;){let e=$(o,a,r);if(-1===e.offset)return this.error=e.result.error,this.warnings.concat(e.result.warnings),-1;if(a=e.offset,this.blockLength+=e.result.blockLength,r-=e.result.blockLength,this.value.push(e.result),this.isIndefiniteForm&&e.result.constructor.NAME===T)break}return this.isIndefiniteForm&&(this.value[this.value.length-1].constructor.NAME===T?this.value.pop():this.warnings.push("No EndOfContent block encoded")),a}toBER(e,t){let r=t||new E;for(let t=0;t<this.value.length;t++)this.value[t].toBER(e,r);return t?O:r.final()}toJSON(){let e={...super.toJSON(),isIndefiniteForm:this.isIndefiniteForm,value:[]};for(let t of this.value)e.value.push(t.toJSON());return e}}V.NAME="ConstructedValueBlock";class W extends D{constructor(e={}){super(e,V),this.idBlock.isConstructed=!0}fromBER(e,t,r){this.valueBlock.isIndefiniteForm=this.lenBlock.isIndefiniteForm;let n=this.valueBlock.fromBER(e,t,this.lenBlock.isIndefiniteForm?r:this.lenBlock.length);return -1===n?this.error=this.valueBlock.error:(this.idBlock.error.length||(this.blockLength+=this.idBlock.blockLength),this.lenBlock.error.length||(this.blockLength+=this.lenBlock.blockLength),this.valueBlock.error.length||(this.blockLength+=this.valueBlock.blockLength)),n}onAsciiEncoding(){let e=[];for(let t of this.valueBlock.value)e.push(t.toString("ascii").split("\n").map(e=>`  ${e}`).join("\n"));let t=3===this.idBlock.tagClass?`[${this.idBlock.tagNumber}]`:this.constructor.NAME;return e.length?`${t} :
${e.join("\n")}`:`${t} :`}}K.Constructed=W,W.NAME="CONSTRUCTED";class G extends I{fromBER(e,t,r){return t}toBER(e){return O}}G.override="EndOfContentValueBlock";class J extends D{constructor(e={}){super(e,G),this.idBlock.tagClass=1,this.idBlock.tagNumber=0}}K.EndOfContent=J,J.NAME=T;class Y extends D{constructor(e={}){super(e,I),this.idBlock.tagClass=1,this.idBlock.tagNumber=5}fromBER(e,t,r){return(this.lenBlock.length>0&&this.warnings.push("Non-zero length of value block for Null type"),this.idBlock.error.length||(this.blockLength+=this.idBlock.blockLength),this.lenBlock.error.length||(this.blockLength+=this.lenBlock.blockLength),this.blockLength+=r,t+r>e.byteLength)?(this.error="End of input reached before message was fully decoded (inconsistent offset and length values)",-1):t+r}toBER(e,t){let r=new ArrayBuffer(2);if(!e){let e=new Uint8Array(r);e[0]=5,e[1]=0}return t&&t.write(r),r}onAsciiEncoding(){return`${this.constructor.NAME}`}}K.Null=Y,Y.NAME="NULL";class X extends U(I){constructor({value:e,...t}={}){super(t),t.valueHex?this.valueHexView=s.toUint8Array(t.valueHex):this.valueHexView=new Uint8Array(1),e&&(this.value=e)}get value(){for(let e of this.valueHexView)if(e>0)return!0;return!1}set value(e){this.valueHexView[0]=e?255:0}fromBER(e,t,r){let n=s.toUint8Array(e);return k(this,n,t,r)?(this.valueHexView=n.subarray(t,t+r),r>1&&this.warnings.push("Boolean value encoded in more then 1 octet"),this.isHexOnly=!0,v.call(this),this.blockLength=r,t+r):-1}toBER(){return this.valueHexView.slice()}toJSON(){return{...super.toJSON(),value:this.value}}}X.NAME="BooleanValueBlock";class Q extends D{constructor(e={}){super(e,X),this.idBlock.tagClass=1,this.idBlock.tagNumber=1}getValue(){return this.valueBlock.value}setValue(e){this.valueBlock.value=e}onAsciiEncoding(){return`${this.constructor.NAME} : ${this.getValue}`}}K.Boolean=Q,Q.NAME="BOOLEAN";class Z extends U(V){constructor({isConstructed:e=!1,...t}={}){super(t),this.isConstructed=e}fromBER(e,t,r){let n=0;if(this.isConstructed){if(this.isHexOnly=!1,-1===(n=V.prototype.fromBER.call(this,e,t,r)))return n;for(let e=0;e<this.value.length;e++){let t=this.value[e].constructor.NAME;if(t===T){if(!this.isIndefiniteForm)return this.error="EndOfContent is unexpected, OCTET STRING may consists of OCTET STRINGs only",-1;break}if(t!==B)return this.error="OCTET STRING may consists of OCTET STRINGs only",-1}}else this.isHexOnly=!0,n=super.fromBER(e,t,r),this.blockLength=r;return n}toBER(e,t){return this.isConstructed?V.prototype.toBER.call(this,e,t):e?new ArrayBuffer(this.valueHexView.byteLength):this.valueHexView.slice().buffer}toJSON(){return{...super.toJSON(),isConstructed:this.isConstructed}}}Z.NAME="OctetStringValueBlock";class ee extends D{constructor({idBlock:e={},lenBlock:t={},...r}={}){var n,i;null!==(n=r.isConstructed)&&void 0!==n||(r.isConstructed=!!(null===(i=r.value)||void 0===i?void 0:i.length)),super({idBlock:{isConstructed:r.isConstructed,...e},lenBlock:{...t,isIndefiniteForm:!!r.isIndefiniteForm},...r},Z),this.idBlock.tagClass=1,this.idBlock.tagNumber=4}fromBER(e,t,r){if(this.valueBlock.isConstructed=this.idBlock.isConstructed,this.valueBlock.isIndefiniteForm=this.lenBlock.isIndefiniteForm,0===r)return 0===this.idBlock.error.length&&(this.blockLength+=this.idBlock.blockLength),0===this.lenBlock.error.length&&(this.blockLength+=this.lenBlock.blockLength),t;if(!this.valueBlock.isConstructed){let n=(e instanceof ArrayBuffer?new Uint8Array(e):e).subarray(t,t+r);try{if(n.byteLength){let e=$(n,0,n.byteLength);-1!==e.offset&&e.offset===r&&(this.valueBlock.value=[e.result])}}catch(e){}}return super.fromBER(e,t,r)}onAsciiEncoding(){return this.valueBlock.isConstructed||this.valueBlock.value&&this.valueBlock.value.length?W.prototype.onAsciiEncoding.call(this):`${this.constructor.NAME} : ${p.ToHex(this.valueBlock.valueHexView)}`}getValue(){if(!this.idBlock.isConstructed)return this.valueBlock.valueHexView.slice().buffer;let e=[];for(let t of this.valueBlock.value)t instanceof ee&&e.push(t.valueBlock.valueHexView);return s.concat(e)}}K.OctetString=ee,ee.NAME=B;class et extends U(V){constructor({unusedBits:e=0,isConstructed:t=!1,...r}={}){super(r),this.unusedBits=e,this.isConstructed=t,this.blockLength=this.valueHexView.byteLength}fromBER(e,t,r){if(!r)return t;let n=-1;if(this.isConstructed){if(-1===(n=V.prototype.fromBER.call(this,e,t,r)))return n;for(let e of this.value){let t=e.constructor.NAME;if(t===T){if(!this.isIndefiniteForm)return this.error="EndOfContent is unexpected, BIT STRING may consists of BIT STRINGs only",-1;break}if(t!==j)return this.error="BIT STRING may consists of BIT STRINGs only",-1;let r=e.valueBlock;if(this.unusedBits>0&&r.unusedBits>0)return this.error='Using of "unused bits" inside constructive BIT STRING allowed for least one only',-1;this.unusedBits=r.unusedBits}return n}let i=s.toUint8Array(e);if(!k(this,i,t,r))return -1;let o=i.subarray(t,t+r);if(this.unusedBits=o[0],this.unusedBits>7)return this.error="Unused bits for BitString must be in range 0-7",-1;if(!this.unusedBits){let e=o.subarray(1);try{if(e.byteLength){let t=$(e,0,e.byteLength);-1!==t.offset&&t.offset===r-1&&(this.value=[t.result])}}catch(e){}}return this.valueHexView=o.subarray(1),this.blockLength=o.length,t+r}toBER(e,t){if(this.isConstructed)return V.prototype.toBER.call(this,e,t);if(e)return new ArrayBuffer(this.valueHexView.byteLength+1);if(!this.valueHexView.byteLength)return O;let r=new Uint8Array(this.valueHexView.length+1);return r[0]=this.unusedBits,r.set(this.valueHexView,1),r.buffer}toJSON(){return{...super.toJSON(),unusedBits:this.unusedBits,isConstructed:this.isConstructed}}}et.NAME="BitStringValueBlock";class er extends D{constructor({idBlock:e={},lenBlock:t={},...r}={}){var n,i;null!==(n=r.isConstructed)&&void 0!==n||(r.isConstructed=!!(null===(i=r.value)||void 0===i?void 0:i.length)),super({idBlock:{isConstructed:r.isConstructed,...e},lenBlock:{...t,isIndefiniteForm:!!r.isIndefiniteForm},...r},et),this.idBlock.tagClass=1,this.idBlock.tagNumber=3}fromBER(e,t,r){return this.valueBlock.isConstructed=this.idBlock.isConstructed,this.valueBlock.isIndefiniteForm=this.lenBlock.isIndefiniteForm,super.fromBER(e,t,r)}onAsciiEncoding(){if(this.valueBlock.isConstructed||this.valueBlock.value&&this.valueBlock.value.length)return W.prototype.onAsciiEncoding.call(this);{let e=[];for(let t of this.valueBlock.valueHexView)e.push(t.toString(2).padStart(8,"0"));let t=e.join("");return`${this.constructor.NAME} : ${t.substring(0,t.length-this.valueBlock.unusedBits)}`}}}function en(e){if(e>=A.length)for(let t=A.length;t<=e;t++){let e=new Uint8Array([0]),r=A[t-1].slice(0);for(let t=r.length-1;t>=0;t--){let n=new Uint8Array([(r[t]<<1)+e[0]]);e[0]=n[0]/10,r[t]=n[0]%10}e[0]>0&&(r=b(e,r)),A.push(r)}return A[e]}K.BitString=er,er.NAME=j;class ei extends U(I){constructor({value:e,...t}={}){super(t),this._valueDec=0,t.valueHex&&this.setValueHex(),void 0!==e&&(this.valueDec=e)}setValueHex(){this.valueHexView.length>=4?(this.warnings.push("Too big Integer for decoding, hex only"),this.isHexOnly=!0,this._valueDec=0):(this.isHexOnly=!1,this.valueHexView.length>0&&(this._valueDec=v.call(this)))}set valueDec(e){this._valueDec=e,this.isHexOnly=!1,this.valueHexView=new Uint8Array(function(e){let t=e<0?-1*e:e,r=128;for(let n=1;n<8;n++){if(t<=r){if(e<0){let e=m(r-t,8,n),i=new Uint8Array(e);return i[0]|=128,e}let i=m(t,8,n),o=new Uint8Array(i);if(128&o[0]){let e=i.slice(0),t=new Uint8Array(e);i=new ArrayBuffer(i.byteLength+1),o=new Uint8Array(i);for(let r=0;r<e.byteLength;r++)o[r+1]=t[r];o[0]=0}return i}r*=256}return new ArrayBuffer(0)}(e))}get valueDec(){return this._valueDec}fromDER(e,t,r,n=0){let i=this.fromBER(e,t,r);if(-1===i)return i;let o=this.valueHexView;return 0===o[0]&&(128&o[1])!=0?this.valueHexView=o.subarray(1):0!==n&&o.length<n&&(n-o.length>1&&(n=o.length+1),this.valueHexView=o.subarray(n-o.length)),i}toDER(e=!1){let t=this.valueHexView;switch(!0){case(128&t[0])!=0:{let e=new Uint8Array(this.valueHexView.length+1);e[0]=0,e.set(t,1),this.valueHexView=e}break;case 0===t[0]&&(128&t[1])==0:this.valueHexView=this.valueHexView.subarray(1)}return this.toBER(e)}fromBER(e,t,r){let n=super.fromBER(e,t,r);return -1===n||this.setValueHex(),n}toBER(e){return e?new ArrayBuffer(this.valueHexView.length):this.valueHexView.slice().buffer}toJSON(){return{...super.toJSON(),valueDec:this.valueDec}}toString(){let e;let t=8*this.valueHexView.length-1,r=new Uint8Array(8*this.valueHexView.length/3),n=0,i=this.valueHexView,o="",a=!1;for(let a=i.byteLength-1;a>=0;a--){e=i[a];for(let i=0;i<8;i++)(1&e)==1&&(n===t?(r=function(e,t){let r,n=0,i=new Uint8Array(e),o=new Uint8Array(t),a=i.slice(0),s=a.length-1,l=o.slice(0),u=l.length-1,c=0;for(let e=u;e>=0;e--,c++)!0==(r=a[s-c]-l[u-c]-n)<0?(n=1,a[s-c]=r+10):(n=0,a[s-c]=r);if(n>0)for(let e=s-u+1;e>=0;e--,c++)if((r=a[s-c]-n)<0)n=1,a[s-c]=r+10;else{n=0,a[s-c]=r;break}return a.slice()}(en(n),r),o="-"):r=function(e,t){let r=new Uint8Array([0]),n=new Uint8Array(e),i=new Uint8Array(t),o=n.slice(0),a=o.length-1,s=i.slice(0),l=s.length-1,u=0,c=l<a?a:l,d=0;for(let e=c;e>=0;e--,d++)u=!0==d<s.length?o[a-d]+s[l-d]+r[0]:o[a-d]+r[0],(r[0]=u/10,!0==d>=o.length)?o=b(new Uint8Array([u%10]),o):o[a-d]=u%10;return r[0]>0&&(o=b(r,o)),o}(r,en(n))),n++,e>>=1}for(let e=0;e<r.length;e++)r[e]&&(a=!0),a&&(o+=P.charAt(r[e]));return!1===a&&(o+=P.charAt(0)),o}}ei.NAME="IntegerValueBlock",Object.defineProperty(ei.prototype,"valueHex",{set:function(e){this.valueHexView=new Uint8Array(e),this.setValueHex()},get:function(){return this.valueHexView.slice().buffer}});class eo extends D{constructor(e={}){super(e,ei),this.idBlock.tagClass=1,this.idBlock.tagNumber=2}toBigInt(){return S(),BigInt(this.valueBlock.toString())}static fromBigInt(e){S();let t=BigInt(e),r=new E,n=t.toString(16).replace(/^-/,""),i=new Uint8Array(p.FromHex(n));if(t<0){let e=new Uint8Array(i.length+(128&i[0]?1:0));e[0]|=128;let n=BigInt(`0x${p.ToHex(e)}`),o=s.toUint8Array(p.FromHex((n+t).toString(16)));o[0]|=128,r.write(o)}else 128&i[0]&&r.write(new Uint8Array([0])),r.write(i);return new eo({valueHex:r.final()})}convertToDER(){let e=new eo({valueHex:this.valueBlock.valueHexView});return e.valueBlock.toDER(),e}convertFromDER(){return new eo({valueHex:0===this.valueBlock.valueHexView[0]?this.valueBlock.valueHexView.subarray(1):this.valueBlock.valueHexView})}onAsciiEncoding(){return`${this.constructor.NAME} : ${this.valueBlock.toString()}`}}K.Integer=eo,eo.NAME="INTEGER";class ea extends eo{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=10}}K.Enumerated=ea,ea.NAME="ENUMERATED";class es extends U(I){constructor({valueDec:e=-1,isFirstSid:t=!1,...r}={}){super(r),this.valueDec=e,this.isFirstSid=t}fromBER(e,t,r){if(!r)return t;let n=s.toUint8Array(e);if(!k(this,n,t,r))return -1;let i=n.subarray(t,t+r);this.valueHexView=new Uint8Array(r);for(let e=0;e<r&&(this.valueHexView[e]=127&i[e],this.blockLength++,(128&i[e])!=0);e++);let o=new Uint8Array(this.blockLength);for(let e=0;e<this.blockLength;e++)o[e]=this.valueHexView[e];return(this.valueHexView=o,(128&i[this.blockLength-1])!=0)?(this.error="End of input reached before message was fully decoded",-1):(0===this.valueHexView[0]&&this.warnings.push("Needlessly long format of SID encoding"),this.blockLength<=8?this.valueDec=y(this.valueHexView,7):(this.isHexOnly=!0,this.warnings.push("Too big SID for decoding, hex only")),t+this.blockLength)}set valueBigInt(e){S();let t=BigInt(e).toString(2);for(;t.length%7;)t="0"+t;let r=new Uint8Array(t.length/7);for(let e=0;e<r.length;e++)r[e]=parseInt(t.slice(7*e,7*e+7),2)+(e+1<r.length?128:0);this.fromBER(r.buffer,0,r.length)}toBER(e){if(this.isHexOnly){if(e)return new ArrayBuffer(this.valueHexView.byteLength);let t=this.valueHexView,r=new Uint8Array(this.blockLength);for(let e=0;e<this.blockLength-1;e++)r[e]=128|t[e];return r[this.blockLength-1]=t[this.blockLength-1],r.buffer}let t=m(this.valueDec,7);if(0===t.byteLength)return this.error="Error during encoding SID value",O;let r=new Uint8Array(t.byteLength);if(!e){let e=new Uint8Array(t),n=t.byteLength-1;for(let t=0;t<n;t++)r[t]=128|e[t];r[n]=e[n]}return r}toString(){let e="";if(this.isHexOnly)e=p.ToHex(this.valueHexView);else if(this.isFirstSid){let t=this.valueDec;this.valueDec<=39?e="0.":this.valueDec<=79?(e="1.",t-=40):(e="2.",t-=80),e+=t.toString()}else e=this.valueDec.toString();return e}toJSON(){return{...super.toJSON(),valueDec:this.valueDec,isFirstSid:this.isFirstSid}}}es.NAME="sidBlock";class el extends I{constructor({value:e="",...t}={}){super(t),this.value=[],e&&this.fromString(e)}fromBER(e,t,r){let n=t;for(;r>0;){let t=new es;if(-1===(n=t.fromBER(e,n,r))){this.blockLength=0,this.error=t.error;break}0===this.value.length&&(t.isFirstSid=!0),this.blockLength+=t.blockLength,r-=t.blockLength,this.value.push(t)}return n}toBER(e){let t=[];for(let r=0;r<this.value.length;r++){let n=this.value[r].toBER(e);if(0===n.byteLength)return this.error=this.value[r].error,O;t.push(n)}return _(t)}fromString(e){this.value=[];let t=0,r=0,n="",i=!1;do if(n=-1===(r=e.indexOf(".",t))?e.substring(t):e.substring(t,r),t=r+1,i){let e=this.value[0],t=0;switch(e.valueDec){case 0:break;case 1:t=40;break;case 2:t=80;break;default:this.value=[];return}let r=parseInt(n,10);if(isNaN(r))return;e.valueDec=r+t,i=!1}else{let e=new es;if(n>Number.MAX_SAFE_INTEGER){S();let t=BigInt(n);e.valueBigInt=t}else if(e.valueDec=parseInt(n,10),isNaN(e.valueDec))return;this.value.length||(e.isFirstSid=!0,i=!0),this.value.push(e)}while(-1!==r)}toString(){let e="",t=!1;for(let r=0;r<this.value.length;r++){t=this.value[r].isHexOnly;let n=this.value[r].toString();0!==r&&(e=`${e}.`),t?(n=`{${n}}`,this.value[r].isFirstSid?e=`2.{${n} - 80}`:e+=n):e+=n}return e}toJSON(){let e={...super.toJSON(),value:this.toString(),sidArray:[]};for(let t=0;t<this.value.length;t++)e.sidArray.push(this.value[t].toJSON());return e}}el.NAME="ObjectIdentifierValueBlock";class eu extends D{constructor(e={}){super(e,el),this.idBlock.tagClass=1,this.idBlock.tagNumber=6}getValue(){return this.valueBlock.toString()}setValue(e){this.valueBlock.fromString(e)}onAsciiEncoding(){return`${this.constructor.NAME} : ${this.valueBlock.toString()||"empty"}`}toJSON(){return{...super.toJSON(),value:this.getValue()}}}K.ObjectIdentifier=eu,eu.NAME="OBJECT IDENTIFIER";class ec extends U(N){constructor({valueDec:e=0,...t}={}){super(t),this.valueDec=e}fromBER(e,t,r){if(0===r)return t;let n=s.toUint8Array(e);if(!k(this,n,t,r))return -1;let i=n.subarray(t,t+r);this.valueHexView=new Uint8Array(r);for(let e=0;e<r&&(this.valueHexView[e]=127&i[e],this.blockLength++,(128&i[e])!=0);e++);let o=new Uint8Array(this.blockLength);for(let e=0;e<this.blockLength;e++)o[e]=this.valueHexView[e];return(this.valueHexView=o,(128&i[this.blockLength-1])!=0)?(this.error="End of input reached before message was fully decoded",-1):(0===this.valueHexView[0]&&this.warnings.push("Needlessly long format of SID encoding"),this.blockLength<=8?this.valueDec=y(this.valueHexView,7):(this.isHexOnly=!0,this.warnings.push("Too big SID for decoding, hex only")),t+this.blockLength)}toBER(e){if(this.isHexOnly){if(e)return new ArrayBuffer(this.valueHexView.byteLength);let t=this.valueHexView,r=new Uint8Array(this.blockLength);for(let e=0;e<this.blockLength-1;e++)r[e]=128|t[e];return r[this.blockLength-1]=t[this.blockLength-1],r.buffer}let t=m(this.valueDec,7);if(0===t.byteLength)return this.error="Error during encoding SID value",O;let r=new Uint8Array(t.byteLength);if(!e){let e=new Uint8Array(t),n=t.byteLength-1;for(let t=0;t<n;t++)r[t]=128|e[t];r[n]=e[n]}return r.buffer}toString(){return this.isHexOnly?p.ToHex(this.valueHexView):this.valueDec.toString()}toJSON(){return{...super.toJSON(),valueDec:this.valueDec}}}ec.NAME="relativeSidBlock";class ed extends I{constructor({value:e="",...t}={}){super(t),this.value=[],e&&this.fromString(e)}fromBER(e,t,r){let n=t;for(;r>0;){let t=new ec;if(-1===(n=t.fromBER(e,n,r))){this.blockLength=0,this.error=t.error;break}this.blockLength+=t.blockLength,r-=t.blockLength,this.value.push(t)}return n}toBER(e,t){let r=[];for(let t=0;t<this.value.length;t++){let n=this.value[t].toBER(e);if(0===n.byteLength)return this.error=this.value[t].error,O;r.push(n)}return _(r)}fromString(e){this.value=[];let t=0,r=0,n="";do{n=-1===(r=e.indexOf(".",t))?e.substring(t):e.substring(t,r),t=r+1;let i=new ec;if(i.valueDec=parseInt(n,10),isNaN(i.valueDec))break;this.value.push(i)}while(-1!==r);return!0}toString(){let e="",t=!1;for(let r=0;r<this.value.length;r++){t=this.value[r].isHexOnly;let n=this.value[r].toString();0!==r&&(e=`${e}.`),t?e+=n=`{${n}}`:e+=n}return e}toJSON(){let e={...super.toJSON(),value:this.toString(),sidArray:[]};for(let t=0;t<this.value.length;t++)e.sidArray.push(this.value[t].toJSON());return e}}ed.NAME="RelativeObjectIdentifierValueBlock";class eh extends D{constructor(e={}){super(e,ed),this.idBlock.tagClass=1,this.idBlock.tagNumber=13}getValue(){return this.valueBlock.toString()}setValue(e){this.valueBlock.fromString(e)}onAsciiEncoding(){return`${this.constructor.NAME} : ${this.valueBlock.toString()||"empty"}`}toJSON(){return{...super.toJSON(),value:this.getValue()}}}K.RelativeObjectIdentifier=eh,eh.NAME="RelativeObjectIdentifier";class ef extends W{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=16}}K.Sequence=ef,ef.NAME="SEQUENCE";class ep extends W{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=17}}K.Set=ep,ep.NAME="SET";class eg extends U(I){constructor({...e}={}){super(e),this.isHexOnly=!0,this.value=""}toJSON(){return{...super.toJSON(),value:this.value}}}eg.NAME="StringValueBlock";class ey extends eg{}ey.NAME="SimpleStringValueBlock";class em extends z{constructor({...e}={}){super(e,ey)}fromBuffer(e){this.valueBlock.value=String.fromCharCode.apply(null,s.toUint8Array(e))}fromString(e){let t=e.length,r=this.valueBlock.valueHexView=new Uint8Array(t);for(let n=0;n<t;n++)r[n]=e.charCodeAt(n);this.valueBlock.value=e}}em.NAME="SIMPLE STRING";class eb extends em{fromBuffer(e){this.valueBlock.valueHexView=s.toUint8Array(e);try{this.valueBlock.value=p.ToUtf8String(e)}catch(t){this.warnings.push(`Error during "decodeURIComponent": ${t}, using raw string`),this.valueBlock.value=p.ToBinary(e)}}fromString(e){this.valueBlock.valueHexView=new Uint8Array(p.FromUtf8String(e)),this.valueBlock.value=e}}eb.NAME="Utf8StringValueBlock";class ev extends eb{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=12}}K.Utf8String=ev,ev.NAME="UTF8String";class ew extends em{fromBuffer(e){this.valueBlock.value=p.ToUtf16String(e),this.valueBlock.valueHexView=s.toUint8Array(e)}fromString(e){this.valueBlock.value=e,this.valueBlock.valueHexView=new Uint8Array(p.FromUtf16String(e))}}ew.NAME="BmpStringValueBlock";class eS extends ew{constructor({...e}={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=30}}K.BmpString=eS,eS.NAME="BMPString";class e_ extends em{fromBuffer(e){let t=ArrayBuffer.isView(e)?e.slice().buffer:e.slice(0),r=new Uint8Array(t);for(let e=0;e<r.length;e+=4)r[e]=r[e+3],r[e+1]=r[e+2],r[e+2]=0,r[e+3]=0;this.valueBlock.value=String.fromCharCode.apply(null,new Uint32Array(t))}fromString(e){let t=e.length,r=this.valueBlock.valueHexView=new Uint8Array(4*t);for(let n=0;n<t;n++){let t=m(e.charCodeAt(n),8),i=new Uint8Array(t);if(i.length>4)continue;let o=4-i.length;for(let e=i.length-1;e>=0;e--)r[4*n+e+o]=i[e]}this.valueBlock.value=e}}e_.NAME="UniversalStringValueBlock";class ek extends e_{constructor({...e}={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=28}}K.UniversalString=ek,ek.NAME="UniversalString";class eE extends em{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=18}}K.NumericString=eE,eE.NAME="NumericString";class eA extends em{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=19}}K.PrintableString=eA,eA.NAME="PrintableString";class eP extends em{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=20}}K.TeletexString=eP,eP.NAME="TeletexString";class eC extends em{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=21}}K.VideotexString=eC,eC.NAME="VideotexString";class ex extends em{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=22}}K.IA5String=ex,ex.NAME="IA5String";class eO extends em{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=25}}K.GraphicString=eO,eO.NAME="GraphicString";class eR extends em{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=26}}K.VisibleString=eR,eR.NAME="VisibleString";class eT extends em{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=27}}K.GeneralString=eT,eT.NAME="GeneralString";class eB extends em{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=29}}K.CharacterString=eB,eB.NAME="CharacterString";class ej extends eR{constructor({value:e,valueDate:t,...r}={}){if(super(r),this.year=0,this.month=0,this.day=0,this.hour=0,this.minute=0,this.second=0,e){this.fromString(e),this.valueBlock.valueHexView=new Uint8Array(e.length);for(let t=0;t<e.length;t++)this.valueBlock.valueHexView[t]=e.charCodeAt(t)}t&&(this.fromDate(t),this.valueBlock.valueHexView=new Uint8Array(this.toBuffer())),this.idBlock.tagClass=1,this.idBlock.tagNumber=23}fromBuffer(e){this.fromString(String.fromCharCode.apply(null,s.toUint8Array(e)))}toBuffer(){let e=this.toString(),t=new ArrayBuffer(e.length),r=new Uint8Array(t);for(let t=0;t<e.length;t++)r[t]=e.charCodeAt(t);return t}fromDate(e){this.year=e.getUTCFullYear(),this.month=e.getUTCMonth()+1,this.day=e.getUTCDate(),this.hour=e.getUTCHours(),this.minute=e.getUTCMinutes(),this.second=e.getUTCSeconds()}toDate(){return new Date(Date.UTC(this.year,this.month-1,this.day,this.hour,this.minute,this.second))}fromString(e){let t=/(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})Z/ig.exec(e);if(null===t){this.error="Wrong input string for conversion";return}let r=parseInt(t[1],10);r>=50?this.year=1900+r:this.year=2e3+r,this.month=parseInt(t[2],10),this.day=parseInt(t[3],10),this.hour=parseInt(t[4],10),this.minute=parseInt(t[5],10),this.second=parseInt(t[6],10)}toString(e="iso"){if("iso"===e){let e=Array(7);return e[0]=w(this.year<2e3?this.year-1900:this.year-2e3,2),e[1]=w(this.month,2),e[2]=w(this.day,2),e[3]=w(this.hour,2),e[4]=w(this.minute,2),e[5]=w(this.second,2),e[6]="Z",e.join("")}return super.toString(e)}onAsciiEncoding(){return`${this.constructor.NAME} : ${this.toDate().toISOString()}`}toJSON(){return{...super.toJSON(),year:this.year,month:this.month,day:this.day,hour:this.hour,minute:this.minute,second:this.second}}}K.UTCTime=ej,ej.NAME="UTCTime";class eU extends ej{constructor(e={}){var t;super(e),null!==(t=this.millisecond)&&void 0!==t||(this.millisecond=0),this.idBlock.tagClass=1,this.idBlock.tagNumber=24}fromDate(e){super.fromDate(e),this.millisecond=e.getUTCMilliseconds()}toDate(){return new Date(Date.UTC(this.year,this.month-1,this.day,this.hour,this.minute,this.second,this.millisecond))}fromString(e){let t,r=!1,n="",i="",o=0,a=0,s=0;if("Z"===e[e.length-1])n=e.substring(0,e.length-1),r=!0;else{if(isNaN(new Number(e[e.length-1]).valueOf()))throw Error("Wrong input string for conversion");n=e}if(r){if(-1!==n.indexOf("+")||-1!==n.indexOf("-"))throw Error("Wrong input string for conversion")}else{let e=1,t=n.indexOf("+"),r="";if(-1===t&&(t=n.indexOf("-"),e=-1),-1!==t){if(r=n.substring(t+1),n=n.substring(0,t),2!==r.length&&4!==r.length)throw Error("Wrong input string for conversion");let i=parseInt(r.substring(0,2),10);if(isNaN(i.valueOf()))throw Error("Wrong input string for conversion");if(a=e*i,4===r.length){if(isNaN((i=parseInt(r.substring(2,4),10)).valueOf()))throw Error("Wrong input string for conversion");s=e*i}}}let l=n.indexOf(".");if(-1===l&&(l=n.indexOf(",")),-1!==l){let e=new Number(`0${n.substring(l)}`);if(isNaN(e.valueOf()))throw Error("Wrong input string for conversion");o=e.valueOf(),i=n.substring(0,l)}else i=n;switch(!0){case 8===i.length:if(t=/(\d{4})(\d{2})(\d{2})/ig,-1!==l)throw Error("Wrong input string for conversion");break;case 10===i.length:if(t=/(\d{4})(\d{2})(\d{2})(\d{2})/ig,-1!==l){let e=60*o;this.minute=Math.floor(e),e=60*(e-this.minute),this.second=Math.floor(e),e=1e3*(e-this.second),this.millisecond=Math.floor(e)}break;case 12===i.length:if(t=/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})/ig,-1!==l){let e=60*o;this.second=Math.floor(e),e=1e3*(e-this.second),this.millisecond=Math.floor(e)}break;case 14===i.length:if(t=/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/ig,-1!==l){let e=1e3*o;this.millisecond=Math.floor(e)}break;default:throw Error("Wrong input string for conversion")}let u=t.exec(i);if(null===u)throw Error("Wrong input string for conversion");for(let e=1;e<u.length;e++)switch(e){case 1:this.year=parseInt(u[e],10);break;case 2:this.month=parseInt(u[e],10);break;case 3:this.day=parseInt(u[e],10);break;case 4:this.hour=parseInt(u[e],10)+a;break;case 5:this.minute=parseInt(u[e],10)+s;break;case 6:this.second=parseInt(u[e],10);break;default:throw Error("Wrong input string for conversion")}if(!1===r){let e=new Date(this.year,this.month,this.day,this.hour,this.minute,this.second,this.millisecond);this.year=e.getUTCFullYear(),this.month=e.getUTCMonth(),this.day=e.getUTCDay(),this.hour=e.getUTCHours(),this.minute=e.getUTCMinutes(),this.second=e.getUTCSeconds(),this.millisecond=e.getUTCMilliseconds()}}toString(e="iso"){if("iso"===e){let e=[];return e.push(w(this.year,4)),e.push(w(this.month,2)),e.push(w(this.day,2)),e.push(w(this.hour,2)),e.push(w(this.minute,2)),e.push(w(this.second,2)),0!==this.millisecond&&(e.push("."),e.push(w(this.millisecond,3))),e.push("Z"),e.join("")}return super.toString(e)}toJSON(){return{...super.toJSON(),millisecond:this.millisecond}}}K.GeneralizedTime=eU,eU.NAME="GeneralizedTime";class eN extends ev{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=31}}K.DATE=eN,eN.NAME="DATE";class eI extends ev{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=32}}K.TimeOfDay=eI,eI.NAME="TimeOfDay";class eM extends ev{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=33}}K.DateTime=eM,eM.NAME="DateTime";class eL extends ev{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=34}}K.Duration=eL,eL.NAME="Duration";class eK extends ev{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=14}}K.TIME=eK,eK.NAME="TIME";class eD{constructor({name:e="",optional:t=!1}={}){this.name=e,this.optional=t}}class ez extends eD{constructor({value:e=[],...t}={}){super(t),this.value=e}}class eH extends eD{constructor({value:e=new eD,local:t=!1,...r}={}){super(r),this.value=e,this.local=t}}class eF{constructor({data:e=R}={}){this.dataView=s.toUint8Array(e)}get data(){return this.dataView.slice().buffer}set data(e){this.dataView=s.toUint8Array(e)}fromBER(e,t,r){let n=t+r;return this.dataView=s.toUint8Array(e).subarray(t,n),n}toBER(e){return this.dataView.slice().buffer}}function e$(e,t,r){if(r instanceof ez){for(let n=0;n<r.value.length;n++)if(e$(e,t,r.value[n]).verified)return{verified:!0,result:e};{let e={verified:!1,result:{error:"Wrong values for Choice type"}};return r.hasOwnProperty(C)&&(e.name=r.name),e}}if(r instanceof eD)return r.hasOwnProperty(C)&&(e[r.name]=t),{verified:!0,result:e};if(e instanceof Object==!1)return{verified:!1,result:{error:"Wrong root object"}};if(t instanceof Object==!1)return{verified:!1,result:{error:"Wrong ASN.1 data"}};if(r instanceof Object==!1||"idBlock"in r==!1||"fromBER"in r.idBlock==!1||"toBER"in r.idBlock==!1)return{verified:!1,result:{error:"Wrong ASN.1 schema"}};let n=r.idBlock.toBER(!1);if(0===n.byteLength)return{verified:!1,result:{error:"Error encoding idBlock for ASN.1 schema"}};if(-1===r.idBlock.fromBER(n,0,n.byteLength))return{verified:!1,result:{error:"Error decoding idBlock for ASN.1 schema"}};if(!1===r.idBlock.hasOwnProperty("tagClass"))return{verified:!1,result:{error:"Wrong ASN.1 schema"}};if(r.idBlock.tagClass!==t.idBlock.tagClass)return{verified:!1,result:e};if(!1===r.idBlock.hasOwnProperty("tagNumber"))return{verified:!1,result:{error:"Wrong ASN.1 schema"}};if(r.idBlock.tagNumber!==t.idBlock.tagNumber)return{verified:!1,result:e};if(!1===r.idBlock.hasOwnProperty("isConstructed"))return{verified:!1,result:{error:"Wrong ASN.1 schema"}};if(r.idBlock.isConstructed!==t.idBlock.isConstructed)return{verified:!1,result:e};if(!("isHexOnly"in r.idBlock))return{verified:!1,result:{error:"Wrong ASN.1 schema"}};if(r.idBlock.isHexOnly!==t.idBlock.isHexOnly)return{verified:!1,result:e};if(r.idBlock.isHexOnly){if(x in r.idBlock==!1)return{verified:!1,result:{error:"Wrong ASN.1 schema"}};let n=r.idBlock.valueHexView,i=t.idBlock.valueHexView;if(n.length!==i.length)return{verified:!1,result:e};for(let t=0;t<n.length;t++)if(n[t]!==i[1])return{verified:!1,result:e}}if(r.name&&(r.name=r.name.replace(/^\s+|\s+$/g,""),r.name&&(e[r.name]=t)),r instanceof K.Constructed){let n=0,i={verified:!1,result:{error:"Unknown error"}},o=r.valueBlock.value.length;if(o>0&&r.valueBlock.value[0]instanceof eH&&(o=t.valueBlock.value.length),0===o)return{verified:!0,result:e};if(0===t.valueBlock.value.length&&0!==r.valueBlock.value.length){let t=!0;for(let e=0;e<r.valueBlock.value.length;e++)t=t&&(r.valueBlock.value[e].optional||!1);return t?{verified:!0,result:e}:(r.name&&(r.name=r.name.replace(/^\s+|\s+$/g,""),r.name&&delete e[r.name]),e.error="Inconsistent object length",{verified:!1,result:e})}for(let a=0;a<o;a++)if(a-n>=t.valueBlock.value.length){if(!1===r.valueBlock.value[a].optional){let t={verified:!1,result:e};return e.error="Inconsistent length between ASN.1 data and schema",r.name&&(r.name=r.name.replace(/^\s+|\s+$/g,""),r.name&&(delete e[r.name],t.name=r.name)),t}}else if(r.valueBlock.value[0]instanceof eH){if(!1===(i=e$(e,t.valueBlock.value[a],r.valueBlock.value[0].value)).verified){if(!r.valueBlock.value[0].optional)return r.name&&(r.name=r.name.replace(/^\s+|\s+$/g,""),r.name&&delete e[r.name]),i;n++}if(C in r.valueBlock.value[0]&&r.valueBlock.value[0].name.length>0){let n={};void 0===(n="local"in r.valueBlock.value[0]&&r.valueBlock.value[0].local?t:e)[r.valueBlock.value[0].name]&&(n[r.valueBlock.value[0].name]=[]),n[r.valueBlock.value[0].name].push(t.valueBlock.value[a])}}else if(!1===(i=e$(e,t.valueBlock.value[a-n],r.valueBlock.value[a])).verified){if(!r.valueBlock.value[a].optional)return r.name&&(r.name=r.name.replace(/^\s+|\s+$/g,""),r.name&&delete e[r.name]),i;n++}if(!1===i.verified){let t={verified:!1,result:e};return r.name&&(r.name=r.name.replace(/^\s+|\s+$/g,""),r.name&&(delete e[r.name],t.name=r.name)),t}return{verified:!0,result:e}}if(r.primitiveSchema&&x in t.valueBlock){let n=$(t.valueBlock.valueHexView);if(-1===n.offset){let t={verified:!1,result:n.result};return r.name&&(r.name=r.name.replace(/^\s+|\s+$/g,""),r.name&&(delete e[r.name],t.name=r.name)),t}return e$(e,n.result,r.primitiveSchema)}return{verified:!0,result:e}}function eq(e,t){if(t instanceof Object==!1)return{verified:!1,result:{error:"Wrong ASN.1 schema type"}};let r=$(s.toUint8Array(e));return -1===r.offset?{verified:!1,result:r.result}:e$(r.result,r.result,t)}(function(e){e[e.Sequence=0]="Sequence",e[e.Set=1]="Set",e[e.Choice=2]="Choice"})(n||(n={})),function(e){e[e.Any=1]="Any",e[e.Boolean=2]="Boolean",e[e.OctetString=3]="OctetString",e[e.BitString=4]="BitString",e[e.Integer=5]="Integer",e[e.Enumerated=6]="Enumerated",e[e.ObjectIdentifier=7]="ObjectIdentifier",e[e.Utf8String=8]="Utf8String",e[e.BmpString=9]="BmpString",e[e.UniversalString=10]="UniversalString",e[e.NumericString=11]="NumericString",e[e.PrintableString=12]="PrintableString",e[e.TeletexString=13]="TeletexString",e[e.VideotexString=14]="VideotexString",e[e.IA5String=15]="IA5String",e[e.GraphicString=16]="GraphicString",e[e.VisibleString=17]="VisibleString",e[e.GeneralString=18]="GeneralString",e[e.CharacterString=19]="CharacterString",e[e.UTCTime=20]="UTCTime",e[e.GeneralizedTime=21]="GeneralizedTime",e[e.DATE=22]="DATE",e[e.TimeOfDay=23]="TimeOfDay",e[e.DateTime=24]="DateTime",e[e.Duration=25]="Duration",e[e.TIME=26]="TIME",e[e.Null=27]="Null"}(i||(i={}));let eV={fromASN:e=>e instanceof Y?null:e.valueBeforeDecodeView,toASN:e=>{if(null===e)return new Y;let t=q(e);if(t.result.error)throw Error(t.result.error);return t.result}},eW={fromASN:e=>e.valueBlock.valueHexView.byteLength>=4?e.valueBlock.toString():e.valueBlock.valueDec,toASN:e=>new eo({value:+e})},eG={fromASN:e=>e.valueBlock.valueDec,toASN:e=>new ea({value:e})},eJ={fromASN:e=>e.valueBlock.valueHexView,toASN:e=>new er({valueHex:e})},eY={fromASN:e=>e.valueBlock.toString(),toASN:e=>new eu({value:e})},eX={fromASN:e=>e.valueBlock.value,toASN:e=>new Q({value:e})},eQ={fromASN:e=>e.valueBlock.valueHexView,toASN:e=>new ee({valueHex:e})};function eZ(e){return{fromASN:e=>e.valueBlock.value,toASN:t=>new e({value:t})}}let e0=eZ(ev),e1=eZ(eS),e2=eZ(ek),e8=eZ(eE),e3=eZ(eA),e4=eZ(eP),e5=eZ(eC),e6=eZ(ex),e7=eZ(eO),e9=eZ(eR),te=eZ(eT),tt=eZ(eB),tr={fromASN:e=>e.toDate(),toASN:e=>new ej({valueDate:e})},tn={fromASN:e=>e.toDate(),toASN:e=>new eU({valueDate:e})},ti={fromASN:()=>null,toASN:()=>new Y};function to(e){switch(e){case i.Any:return eV;case i.BitString:return eJ;case i.BmpString:return e1;case i.Boolean:return eX;case i.CharacterString:return tt;case i.Enumerated:return eG;case i.GeneralString:return te;case i.GeneralizedTime:return tn;case i.GraphicString:return e7;case i.IA5String:return e6;case i.Integer:return eW;case i.Null:return ti;case i.NumericString:return e8;case i.ObjectIdentifier:return eY;case i.OctetString:return eQ;case i.PrintableString:return e3;case i.TeletexString:return e4;case i.UTCTime:return tr;case i.UniversalString:return e2;case i.Utf8String:return e0;case i.VideotexString:return e5;case i.VisibleString:return e9;default:return null}}function ta(e){return"function"==typeof e&&e.prototype?!!e.prototype.toASN&&!!e.prototype.fromASN||ta(e.prototype):!!(e&&"object"==typeof e&&"toASN"in e&&"fromASN"in e)}class ts{constructor(){this.items=new WeakMap}has(e){return this.items.has(e)}get(e,t=!1){let r=this.items.get(e);if(!r)throw Error(`Cannot get schema for '${e.prototype.constructor.name}' target`);if(t&&!r.schema)throw Error(`Schema '${e.prototype.constructor.name}' doesn't contain ASN.1 schema. Call 'AsnSchemaStorage.cache'.`);return r}cache(e){let t=this.get(e);t.schema||(t.schema=this.create(e,!0))}createDefault(e){let t={type:n.Sequence,items:{}},r=this.findParentSchema(e);return r&&(Object.assign(t,r),t.items=Object.assign({},t.items,r.items)),t}create(e,t){let r=this.items.get(e)||this.createDefault(e),o=[];for(let e in r.items){let s;let l=r.items[e],u=t?e:"";if("number"==typeof l.type){let e=i[l.type],t=a[e];if(!t)throw Error(`Cannot get ASN1 class by name '${e}'`);s=new t({name:u})}else ta(l.type)?s=new l.type().toSchema(u):l.optional?this.get(l.type).type===n.Choice?s=new eD({name:u}):(s=this.create(l.type,!1)).name=u:s=new eD({name:u});let c=!!l.optional||void 0!==l.defaultValue;if(l.repeated&&(s.name="",s=new("set"===l.repeated?ep:ef)({name:"",value:[new eH({name:u,value:s})]})),null!==l.context&&void 0!==l.context){if(l.implicit){if("number"==typeof l.type||ta(l.type)){let e=l.repeated?W:F;o.push(new e({name:u,optional:c,idBlock:{tagClass:3,tagNumber:l.context}}))}else{this.cache(l.type);let e=!!l.repeated,t=e?s:this.get(l.type,!0).schema;t="valueBlock"in t?t.valueBlock.value:t.value,o.push(new W({name:e?"":u,optional:c,idBlock:{tagClass:3,tagNumber:l.context},value:t}))}}else o.push(new W({optional:c,idBlock:{tagClass:3,tagNumber:l.context},value:[s]}))}else s.optional=c,o.push(s)}switch(r.type){case n.Sequence:return new ef({value:o,name:""});case n.Set:return new ep({value:o,name:""});case n.Choice:return new ez({value:o,name:""});default:throw Error("Unsupported ASN1 type in use")}}set(e,t){return this.items.set(e,t),this}findParentSchema(e){let t=Object.getPrototypeOf(e);return t?this.items.get(t)||this.findParentSchema(t):null}}let tl=new ts,tu=e=>t=>{let r;tl.has(t)?r=tl.get(t):(r=tl.createDefault(t),tl.set(t,r)),Object.assign(r,e)},tc=e=>(t,r)=>{let n;tl.has(t.constructor)?n=tl.get(t.constructor):(n=tl.createDefault(t.constructor),tl.set(t.constructor,n));let i=Object.assign({},e);if("number"==typeof i.type&&!i.converter){let n=to(e.type);if(!n)throw Error(`Cannot get default converter for property '${r}' of ${t.constructor.name}`);i.converter=n}n.items[r]=i};class td extends Error{constructor(){super(...arguments),this.schemas=[]}}class th{static parse(e,t){let r=q(e);if(r.result.error)throw Error(r.result.error);return this.fromASN(r.result,t)}static fromASN(e,t){var r;try{if(ta(t))return new t().fromASN(e);let o=tl.get(t);tl.cache(t);let s=o.schema;if(e.constructor===W&&o.type!==n.Choice)for(let t in s=new W({idBlock:{tagClass:3,tagNumber:e.idBlock.tagNumber},value:o.schema.valueBlock.value}),o.items)delete e[t];let l=e$({},e,s);if(!l.verified)throw new td(`Data does not match to ${t.name} ASN1 schema. ${l.result.error}`);let u=new t;if(function e(t){var r;if(t){let n=Object.getPrototypeOf(t);return(null===(r=null==n?void 0:n.prototype)||void 0===r?void 0:r.constructor)===Array||e(n)}return!1}(t)){if(!("value"in e.valueBlock&&Array.isArray(e.valueBlock.value)))throw Error("Cannot get items from the ASN.1 parsed value. ASN.1 object is not constructed.");let r=o.itemType;if("number"!=typeof r)return t.from(e.valueBlock.value,e=>this.fromASN(e,r));{let n=to(r);if(!n)throw Error(`Cannot get default converter for array item of ${t.name} ASN1 schema`);return t.from(e.valueBlock.value,e=>n.fromASN(e))}}for(let e in o.items){let t=l.result[e];if(!t)continue;let n=o.items[e],s=n.type;if("number"==typeof s||ta(s)){let o=null!==(r=n.converter)&&void 0!==r?r:ta(s)?new s:null;if(!o)throw Error("Converter is empty");if(n.repeated){if(n.implicit){let r=new("sequence"===n.repeated?ef:ep);r.valueBlock=t.valueBlock;let i=q(r.toBER(!1));if(-1===i.offset)throw Error(`Cannot parse the child item. ${i.result.error}`);if(!("value"in i.result.valueBlock&&Array.isArray(i.result.valueBlock.value)))throw Error("Cannot get items from the ASN.1 parsed value. ASN.1 object is not constructed.");let a=i.result.valueBlock.value;u[e]=Array.from(a,e=>o.fromASN(e))}else u[e]=Array.from(t,e=>o.fromASN(e))}else{let r=t;if(n.implicit){let e;if(ta(s))e=new s().toSchema("");else{let t=i[s],r=a[t];if(!r)throw Error(`Cannot get '${t}' class from asn1js module`);e=new r}e.valueBlock=r.valueBlock,r=q(e.toBER(!1)).result}u[e]=o.fromASN(r)}}else if(n.repeated){if(!Array.isArray(t))throw Error("Cannot get list of items from the ASN.1 parsed value. ASN.1 value should be iterable.");u[e]=Array.from(t,e=>this.fromASN(e,s))}else u[e]=this.fromASN(t,s)}return u}catch(e){throw e instanceof td&&e.schemas.push(t.name),e}}}class tf{static serialize(e){return e instanceof D?e.toBER(!1):this.toASN(e).toBER(!1)}static toASN(e){let t;if(e&&"object"==typeof e&&ta(e))return e.toASN();if(!(e&&"object"==typeof e))throw TypeError("Parameter 1 should be type of Object.");let r=e.constructor,i=tl.get(r);tl.cache(r);let o=[];if(i.itemType){if(!Array.isArray(e))throw TypeError("Parameter 1 should be type of Array.");if("number"==typeof i.itemType){let t=to(i.itemType);if(!t)throw Error(`Cannot get default converter for array item of ${r.name} ASN1 schema`);o=e.map(e=>t.toASN(e))}else o=e.map(e=>this.toAsnItem({type:i.itemType},"[]",r,e))}else for(let t in i.items){let n=i.items[t],a=e[t];if(void 0===a||n.defaultValue===a||"object"==typeof n.defaultValue&&"object"==typeof a&&function(e,t){if(!(e&&t)||e.byteLength!==t.byteLength)return!1;let r=new Uint8Array(e),n=new Uint8Array(t);for(let t=0;t<e.byteLength;t++)if(r[t]!==n[t])return!1;return!0}(this.serialize(n.defaultValue),this.serialize(a)))continue;let s=tf.toAsnItem(n,t,r,a);if("number"==typeof n.context){if(n.implicit){if(!n.repeated&&("number"==typeof n.type||ta(n.type))){let e={};e.valueHex=s instanceof Y?s.valueBeforeDecodeView:s.valueBlock.toBER(),o.push(new F({optional:n.optional,idBlock:{tagClass:3,tagNumber:n.context},...e}))}else o.push(new W({optional:n.optional,idBlock:{tagClass:3,tagNumber:n.context},value:s.valueBlock.value}))}else o.push(new W({optional:n.optional,idBlock:{tagClass:3,tagNumber:n.context},value:[s]}))}else n.repeated?o=o.concat(s):o.push(s)}switch(i.type){case n.Sequence:t=new ef({value:o});break;case n.Set:t=new ep({value:o});break;case n.Choice:if(!o[0])throw Error(`Schema '${r.name}' has wrong data. Choice cannot be empty.`);t=o[0]}return t}static toAsnItem(e,t,r,n){let o;if("number"==typeof e.type){let a=e.converter;if(!a)throw Error(`Property '${t}' doesn't have converter for type ${i[e.type]} in schema '${r.name}'`);if(e.repeated){if(!Array.isArray(n))throw TypeError("Parameter 'objProp' should be type of Array.");let t=Array.from(n,e=>a.toASN(e));o=new("sequence"===e.repeated?ef:ep)({value:t})}else o=a.toASN(n)}else if(e.repeated){if(!Array.isArray(n))throw TypeError("Parameter 'objProp' should be type of Array.");let t=Array.from(n,e=>this.toASN(e));o=new("sequence"===e.repeated?ef:ep)({value:t})}else o=this.toASN(n);return o}}class tp{static serialize(e){return tf.serialize(e)}static parse(e,t){return th.parse(e,t)}static toString(e){let t=q(s.isBufferSource(e)?s.toArrayBuffer(e):tp.serialize(e));if(-1===t.offset)throw Error(`Cannot decode ASN.1 data. ${t.result.error}`);return t.result.toString()}}function tg(e,t,r,n){var i,o=arguments.length,a=o<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,r,n);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(o<3?i(a):o>3?i(t,r,a):i(t,r))||a);return o>3&&a&&Object.defineProperty(t,r,a),a}Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError;class ty extends Error{constructor(e,t){super(t?`${e}. See the inner exception for more details.`:e),this.message=e,this.innerError=t}}class tm extends ty{constructor(e,t,r){super(t,r),this.schema=e}}class tb extends tm{constructor(e,t,r){super(e,`JSON doesn't match to '${e.target.name}' schema. ${t}`,r)}}class tv extends ty{}class tw extends ty{constructor(e,t,r){super(`Cannot serialize by '${e}' schema. ${t}`,r),this.schemaName=e}}class tS extends tb{constructor(e,t,r={}){super(e,"Some keys doesn't match to schema"),this.keys=t,this.errors=r}}function t_(e,t){if(!function(e,t){switch(t){case o.Boolean:return"boolean"==typeof e;case o.Number:return"number"==typeof e;case o.String:return"string"==typeof e}return!0}(e,t))throw TypeError(`Value must be ${o[t]}`)}function tk(e){return e&&e.prototype?!!e.prototype.toJSON&&!!e.prototype.fromJSON||tk(e.prototype):!!(e&&e.toJSON&&e.fromJSON)}!function(e){e[e.Any=0]="Any",e[e.Boolean=1]="Boolean",e[e.Number=2]="Number",e[e.String=3]="String"}(o||(o={}));class tE{constructor(){this.items=new Map}has(e){return this.items.has(e)||!!this.findParentSchema(e)}get(e){let t=this.items.get(e)||this.findParentSchema(e);if(!t)throw Error("Cannot get schema for current target");return t}create(e){let t={names:{}},r=this.findParentSchema(e);if(r)for(let e in Object.assign(t,r),t.names={},r.names)t.names[e]=Object.assign({},r.names[e]);return t.target=e,t}set(e,t){return this.items.set(e,t),this}findParentSchema(e){let t=e.__proto__;return t?this.items.get(t)||this.findParentSchema(t):null}}let tA="default",tP=new tE;class tC{constructor(e){this.pattern=new RegExp(e)}validate(e){let t=new RegExp(this.pattern.source,this.pattern.flags);if("string"!=typeof e)throw new tv("Incoming value must be string");if(!t.exec(e))throw new tv(`Value doesn't match to pattern '${t.toString()}'`)}}class tx{constructor(e=Number.MIN_VALUE,t=Number.MAX_VALUE){this.min=e,this.max=t}validate(e){if(t_(e,o.Number),!(this.min<=e&&e<=this.max)){let e=this.min===Number.MIN_VALUE?"MIN":this.min,t=this.max===Number.MAX_VALUE?"MAX":this.max;throw new tv(`Value doesn't match to diapason [${e},${t}]`)}}}class tO{constructor(e=Number.MIN_VALUE,t=Number.MAX_VALUE){this.min=e,this.max=t}validate(e){if(t_(e,o.Number),!(this.min<e&&e<this.max)){let e=this.min===Number.MIN_VALUE?"MIN":this.min,t=this.max===Number.MAX_VALUE?"MAX":this.max;throw new tv(`Value doesn't match to diapason (${e},${t})`)}}}class tR{constructor(e,t,r){this.length=e,this.minLength=t,this.maxLength=r}validate(e){if(void 0!==this.length){if(e.length!==this.length)throw new tv(`Value length must be exactly ${this.length}.`);return}if(void 0!==this.minLength&&e.length<this.minLength)throw new tv(`Value length must be more than ${this.minLength}.`);if(void 0!==this.maxLength&&e.length>this.maxLength)throw new tv(`Value length must be less than ${this.maxLength}.`)}}class tT{constructor(e){this.enumeration=e}validate(e){if(t_(e,o.String),!this.enumeration.includes(e))throw new tv(`Value must be one of ${this.enumeration.map(e=>`'${e}'`).join(", ")}`)}}class tB{static checkValues(e,t){for(let r of Array.isArray(e)?e:[e])for(let n of t.validations)n instanceof tR&&t.repeated?n.validate(e):n.validate(r)}static checkTypes(e,t){if(t.repeated&&!Array.isArray(e))throw TypeError("Value must be Array");if("number"==typeof t.type)for(let r of Array.isArray(e)?e:[e])t_(r,t.type)}static getSchemaByName(e,t=tA){return{...e.names[tA],...e.names[t]}}}class tj extends tB{static serialize(e,t,r,n){return JSON.stringify(this.toJSON(e,t),r,n)}static toJSON(e,t={}){let r;let n=t.targetSchema,i=t.schemaName||tA;if(tk(e))return e.toJSON();if(Array.isArray(e))for(let n of(r=[],e))r.push(this.toJSON(n,t));else if("object"==typeof e){if(n&&!tP.has(n))throw new ty("Cannot get schema for `targetSchema` param");if(n=n||e.constructor,tP.has(n)){let t=tP.get(n);r={};let o=this.getSchemaByName(t,i);for(let a in o)try{let t;let s=o[a],l=e[a];if(s.optional&&void 0===l||void 0!==s.defaultValue&&l===s.defaultValue)continue;if(!s.optional&&void 0===l)throw new tw(n.name,`Property '${a}' is required.`);t="number"==typeof s.type?s.converter?s.repeated?l.map(t=>s.converter.toJSON(t,e)):s.converter.toJSON(l,e):l:s.repeated?l.map(e=>this.toJSON(e,{schemaName:i})):this.toJSON(l,{schemaName:i}),this.checkTypes(t,s),this.checkValues(t,s),r[s.name||a]=t}catch(e){if(e instanceof tw)throw e;throw new tw(t.target.name,`Property '${a}' is wrong. ${e.message}`,e)}}else for(let t in r={},e)r[t]=this.toJSON(e[t],{schemaName:i})}else r=e;return r}}class tU extends tB{static parse(e,t){let r=JSON.parse(e);return this.fromJSON(r,t)}static fromJSON(e,t){let r=t.targetSchema,n=t.schemaName||tA,i=new r;if(tk(i))return i.fromJSON(e);let o=tP.get(r),a=this.getSchemaByName(o,n),s={};for(let r in t.strictProperty&&!Array.isArray(e)&&tU.checkStrictProperty(e,a,o),a)try{let s=a[r],l=s.name||r,u=e[l];if(void 0===u&&(s.optional||void 0!==s.defaultValue))continue;if(!s.optional&&void 0===u)throw new tb(o,`Property '${l}' is required.`);if(this.checkTypes(u,s),this.checkValues(u,s),"number"==typeof s.type)s.converter?s.repeated?i[r]=u.map(e=>s.converter.fromJSON(e,i)):i[r]=s.converter.fromJSON(u,i):i[r]=u;else{let e={...t,targetSchema:s.type,schemaName:n};s.repeated?i[r]=u.map(t=>this.fromJSON(t,e)):i[r]=this.fromJSON(u,e)}}catch(e){if(e instanceof tb||(e=new tb(o,`Property '${r}' is wrong. ${e.message}`,e)),t.strictAllKeys)s[r]=e;else throw e}let l=Object.keys(s);if(l.length)throw new tS(o,l,s);return i}static checkStrictProperty(e,t,r){let n=Object.keys(e),i=Object.keys(t),o=[];for(let e of n)-1===i.indexOf(e)&&o.push(e);if(o.length)throw new tS(r,o)}}let tN=(e={})=>(t,r)=>{let n;let i=`Cannot set type for ${r} property of ${t.constructor.name} schema`;tP.has(t.constructor)?(n=tP.get(t.constructor)).target!==t.constructor&&(n=tP.create(t.constructor),tP.set(t.constructor,n)):(n=tP.create(t.constructor),tP.set(t.constructor,n));let a=Object.assign({type:o.Any,validations:[]},e);if(a.validations=function(e){let t=[];return e.pattern&&t.push(new tC(e.pattern)),(e.type===o.Number||e.type===o.Any)&&((void 0!==e.minInclusive||void 0!==e.maxInclusive)&&t.push(new tx(e.minInclusive,e.maxInclusive)),(void 0!==e.minExclusive||void 0!==e.maxExclusive)&&t.push(new tO(e.minExclusive,e.maxExclusive)),void 0!==e.enumeration&&t.push(new tT(e.enumeration))),(e.type===o.String||e.repeated||e.type===o.Any)&&(void 0!==e.length||void 0!==e.minLength||void 0!==e.maxLength)&&t.push(new tR(e.length,e.minLength,e.maxLength)),t}(a),"number"!=typeof a.type&&!tP.has(a.type)&&!tk(a.type))throw Error(`${i}. Assigning type doesn't have schema.`);for(let t of Array.isArray(e.schema)?e.schema:[e.schema||tA])n.names[t]||(n.names[t]={}),n.names[t][r]=a};/*!
 Copyright (c) Peculiar Ventures, LLC
*/class tI extends Error{}class tM extends tI{}class tL extends tI{constructor(e){super(`Unsupported operation: ${e?`${e}`:""}`)}}class tK extends tI{}class tD extends tI{constructor(e){super(`${e}: Missing required property`)}}class tz{async digest(...e){return this.checkDigest.apply(this,e),this.onDigest.apply(this,e)}checkDigest(e,t){this.checkAlgorithmName(e)}async onDigest(e,t){throw new tL("digest")}async generateKey(...e){return this.checkGenerateKey.apply(this,e),this.onGenerateKey.apply(this,e)}checkGenerateKey(e,t,r,...n){let i;if(this.checkAlgorithmName(e),this.checkGenerateKeyParams(e),!(r&&r.length))throw TypeError("Usages cannot be empty when creating a key.");i=Array.isArray(this.usages)?this.usages:this.usages.privateKey.concat(this.usages.publicKey),this.checkKeyUsages(r,i)}checkGenerateKeyParams(e){}async onGenerateKey(e,t,r,...n){throw new tL("generateKey")}async sign(...e){return this.checkSign.apply(this,e),this.onSign.apply(this,e)}checkSign(e,t,r,...n){this.checkAlgorithmName(e),this.checkAlgorithmParams(e),this.checkCryptoKey(t,"sign")}async onSign(e,t,r,...n){throw new tL("sign")}async verify(...e){return this.checkVerify.apply(this,e),this.onVerify.apply(this,e)}checkVerify(e,t,r,n,...i){this.checkAlgorithmName(e),this.checkAlgorithmParams(e),this.checkCryptoKey(t,"verify")}async onVerify(e,t,r,n,...i){throw new tL("verify")}async encrypt(...e){return this.checkEncrypt.apply(this,e),this.onEncrypt.apply(this,e)}checkEncrypt(e,t,r,n={},...i){this.checkAlgorithmName(e),this.checkAlgorithmParams(e),this.checkCryptoKey(t,n.keyUsage?"encrypt":void 0)}async onEncrypt(e,t,r,...n){throw new tL("encrypt")}async decrypt(...e){return this.checkDecrypt.apply(this,e),this.onDecrypt.apply(this,e)}checkDecrypt(e,t,r,n={},...i){this.checkAlgorithmName(e),this.checkAlgorithmParams(e),this.checkCryptoKey(t,n.keyUsage?"decrypt":void 0)}async onDecrypt(e,t,r,...n){throw new tL("decrypt")}async deriveBits(...e){return this.checkDeriveBits.apply(this,e),this.onDeriveBits.apply(this,e)}checkDeriveBits(e,t,r,n={},...i){if(this.checkAlgorithmName(e),this.checkAlgorithmParams(e),this.checkCryptoKey(t,n.keyUsage?"deriveBits":void 0),r%8!=0)throw new tK("length: Is not multiple of 8")}async onDeriveBits(e,t,r,...n){throw new tL("deriveBits")}async exportKey(...e){return this.checkExportKey.apply(this,e),this.onExportKey.apply(this,e)}checkExportKey(e,t,...r){if(this.checkKeyFormat(e),this.checkCryptoKey(t),!t.extractable)throw new tI("key: Is not extractable")}async onExportKey(e,t,...r){throw new tL("exportKey")}async importKey(...e){return this.checkImportKey.apply(this,e),this.onImportKey.apply(this,e)}checkImportKey(e,t,r,n,i,...o){this.checkKeyFormat(e),this.checkKeyData(e,t),this.checkAlgorithmName(r),this.checkImportParams(r),Array.isArray(this.usages)&&this.checkKeyUsages(i,this.usages)}async onImportKey(e,t,r,n,i,...o){throw new tL("importKey")}checkAlgorithmName(e){if(e.name.toLowerCase()!==this.name.toLowerCase())throw new tM("Unrecognized name")}checkAlgorithmParams(e){}checkDerivedKeyParams(e){}checkKeyUsages(e,t){for(let r of e)if(-1===t.indexOf(r))throw TypeError("Cannot create a key using the specified key usages")}checkCryptoKey(e,t){if(this.checkAlgorithmName(e.algorithm),t&&-1===e.usages.indexOf(t))throw new tI("key does not match that of operation")}checkRequiredProperty(e,t){if(!(t in e))throw new tD(t)}checkHashAlgorithm(e,t){for(let r of t)if(r.toLowerCase()===e.name.toLowerCase())return;throw new tK(`hash: Must be one of ${t.join(", ")}`)}checkImportParams(e){}checkKeyFormat(e){switch(e){case"raw":case"pkcs8":case"spki":case"jwk":break;default:throw TypeError("format: Is invalid value. Must be 'jwk', 'raw', 'spki', or 'pkcs8'")}}checkKeyData(e,t){if(!t)throw TypeError("keyData: Cannot be empty on empty on key importing");if("jwk"===e){if(!("object"==typeof t&&"kty"in t))throw TypeError("keyData: Is not JsonWebToken")}else if(!s.isBufferSource(t))throw TypeError("keyData: Is not ArrayBufferView or ArrayBuffer")}prepareData(e){return s.toArrayBuffer(e)}}class tH extends tz{checkGenerateKeyParams(e){if(this.checkRequiredProperty(e,"length"),"number"!=typeof e.length)throw TypeError("length: Is not of type Number");switch(e.length){case 128:case 192:case 256:break;default:throw TypeError("length: Must be 128, 192, or 256")}}checkDerivedKeyParams(e){this.checkGenerateKeyParams(e)}}class tF extends tH{constructor(){super(...arguments),this.name="AES-CBC",this.usages=["encrypt","decrypt","wrapKey","unwrapKey"]}checkAlgorithmParams(e){if(this.checkRequiredProperty(e,"iv"),!(e.iv instanceof ArrayBuffer||ArrayBuffer.isView(e.iv)))throw TypeError("iv: Is not of type '(ArrayBuffer or ArrayBufferView)'");if(16!==e.iv.byteLength)throw TypeError("iv: Must have length 16 bytes")}}class t$ extends tH{constructor(){super(...arguments),this.name="AES-CMAC",this.usages=["sign","verify"]}checkAlgorithmParams(e){if(this.checkRequiredProperty(e,"length"),"number"!=typeof e.length)throw TypeError("length: Is not a Number");if(e.length<1)throw new tK("length: Must be more than 0")}}class tq extends tH{constructor(){super(...arguments),this.name="AES-CTR",this.usages=["encrypt","decrypt","wrapKey","unwrapKey"]}checkAlgorithmParams(e){if(this.checkRequiredProperty(e,"counter"),!(e.counter instanceof ArrayBuffer||ArrayBuffer.isView(e.counter)))throw TypeError("counter: Is not of type '(ArrayBuffer or ArrayBufferView)'");if(16!==e.counter.byteLength)throw TypeError("iv: Must have length 16 bytes");if(this.checkRequiredProperty(e,"length"),"number"!=typeof e.length)throw TypeError("length: Is not a Number");if(e.length<1)throw new tK("length: Must be more than 0")}}class tV extends tH{constructor(){super(...arguments),this.name="AES-ECB",this.usages=["encrypt","decrypt","wrapKey","unwrapKey"]}}class tW extends tH{constructor(){super(...arguments),this.name="AES-GCM",this.usages=["encrypt","decrypt","wrapKey","unwrapKey"]}checkAlgorithmParams(e){var t;if(this.checkRequiredProperty(e,"iv"),!(e.iv instanceof ArrayBuffer||ArrayBuffer.isView(e.iv)))throw TypeError("iv: Is not of type '(ArrayBuffer or ArrayBufferView)'");if(e.iv.byteLength<1)throw new tK("iv: Must have length more than 0 and less than 2^64 - 1");switch(null!==(t=e.tagLength)&&void 0!==t||(e.tagLength=128),e.tagLength){case 32:case 64:case 96:case 104:case 112:case 120:case 128:break;default:throw new tK("tagLength: Must be one of 32, 64, 96, 104, 112, 120 or 128")}}}class tG extends tH{constructor(){super(...arguments),this.name="AES-KW",this.usages=["wrapKey","unwrapKey"]}}class tJ extends tz{constructor(){super(...arguments),this.usages=["encrypt","decrypt","wrapKey","unwrapKey"]}checkAlgorithmParams(e){if(this.ivSize){if(this.checkRequiredProperty(e,"iv"),!(e.iv instanceof ArrayBuffer||ArrayBuffer.isView(e.iv)))throw TypeError("iv: Is not of type '(ArrayBuffer or ArrayBufferView)'");if(e.iv.byteLength!==this.ivSize)throw TypeError(`iv: Must have length ${this.ivSize} bytes`)}}checkGenerateKeyParams(e){if(this.checkRequiredProperty(e,"length"),"number"!=typeof e.length)throw TypeError("length: Is not of type Number");if(e.length!==this.keySizeBits)throw new tK(`algorithm.length: Must be ${this.keySizeBits}`)}checkDerivedKeyParams(e){this.checkGenerateKeyParams(e)}}class tY extends tz{constructor(){super(...arguments),this.hashAlgorithms=["SHA-1","SHA-256","SHA-384","SHA-512"]}checkGenerateKeyParams(e){if(this.checkRequiredProperty(e,"hash"),this.checkHashAlgorithm(e.hash,this.hashAlgorithms),this.checkRequiredProperty(e,"publicExponent"),!(e.publicExponent&&e.publicExponent instanceof Uint8Array))throw TypeError("publicExponent: Missing or not a Uint8Array");let t=p.ToBase64(e.publicExponent);if(!("Aw=="===t||"AQAB"===t))throw TypeError("publicExponent: Must be [3] or [1,0,1]");if(this.checkRequiredProperty(e,"modulusLength"),e.modulusLength%8||e.modulusLength<256||e.modulusLength>16384)throw TypeError("The modulus length must be a multiple of 8 bits and >= 256 and <= 16384")}checkImportParams(e){this.checkRequiredProperty(e,"hash"),this.checkHashAlgorithm(e.hash,this.hashAlgorithms)}}class tX extends tY{constructor(){super(...arguments),this.name="RSASSA-PKCS1-v1_5",this.usages={privateKey:["sign"],publicKey:["verify"]}}}class tQ extends tY{constructor(){super(...arguments),this.name="RSA-PSS",this.usages={privateKey:["sign"],publicKey:["verify"]}}checkAlgorithmParams(e){if(this.checkRequiredProperty(e,"saltLength"),"number"!=typeof e.saltLength)throw TypeError("saltLength: Is not a Number");if(e.saltLength<0)throw RangeError("saltLength: Must be positive number")}}class tZ extends tY{constructor(){super(...arguments),this.name="RSA-OAEP",this.usages={privateKey:["decrypt","unwrapKey"],publicKey:["encrypt","wrapKey"]}}checkAlgorithmParams(e){if(e.label&&!(e.label instanceof ArrayBuffer||ArrayBuffer.isView(e.label)))throw TypeError("label: Is not of type '(ArrayBuffer or ArrayBufferView)'")}}class t0 extends tz{checkGenerateKeyParams(e){this.checkRequiredProperty(e,"namedCurve"),this.checkNamedCurve(e.namedCurve)}checkNamedCurve(e){for(let t of this.namedCurves)if(t.toLowerCase()===e.toLowerCase())return;throw new tK(`namedCurve: Must be one of ${this.namedCurves.join(", ")}`)}}class t1 extends t0{constructor(){super(...arguments),this.name="ECDSA",this.hashAlgorithms=["SHA-1","SHA-256","SHA-384","SHA-512"],this.usages={privateKey:["sign"],publicKey:["verify"]},this.namedCurves=["P-256","P-384","P-521","K-256"]}checkAlgorithmParams(e){this.checkRequiredProperty(e,"hash"),this.checkHashAlgorithm(e.hash,this.hashAlgorithms)}}let t2=["secret","private","public"];class t8{static create(e,t,r,n){let i=new this;return i.algorithm=e,i.type=t,i.extractable=r,i.usages=n,i}static isKeyType(e){return -1!==t2.indexOf(e)}get[Symbol.toStringTag](){return"CryptoKey"}}class t3 extends t0{constructor(){super(...arguments),this.name="ECDH",this.usages={privateKey:["deriveBits","deriveKey"],publicKey:[]},this.namedCurves=["P-256","P-384","P-521","K-256"]}checkAlgorithmParams(e){if(this.checkRequiredProperty(e,"public"),!(e.public instanceof t8))throw TypeError("public: Is not a CryptoKey");if("public"!==e.public.type)throw new tK("public: Is not a public key");if(e.public.algorithm.name!==this.name)throw new tK(`public: Is not ${this.name} key`)}}class t4 extends t3{constructor(){super(...arguments),this.name="ECDH-ES",this.namedCurves=["X25519","X448"]}}class t5 extends t0{constructor(){super(...arguments),this.name="EdDSA",this.usages={privateKey:["sign"],publicKey:["verify"]},this.namedCurves=["Ed25519","Ed448"]}}let t6=class{constructor(e){e&&(this.value=e)}};tg([tc({type:i.ObjectIdentifier})],t6.prototype,"value",void 0),t6=tg([tu({type:n.Choice})],t6);class t7{constructor(e){Object.assign(this,e)}}tg([tc({type:i.ObjectIdentifier})],t7.prototype,"algorithm",void 0),tg([tc({type:i.Any,optional:!0})],t7.prototype,"parameters",void 0);class t9{constructor(){this.version=0,this.privateKeyAlgorithm=new t7,this.privateKey=new ArrayBuffer(0)}}tg([tc({type:i.Integer})],t9.prototype,"version",void 0),tg([tc({type:t7})],t9.prototype,"privateKeyAlgorithm",void 0),tg([tc({type:i.OctetString})],t9.prototype,"privateKey",void 0),tg([tc({type:i.Any,optional:!0})],t9.prototype,"attributes",void 0);class re{constructor(){this.publicKeyAlgorithm=new t7,this.publicKey=new ArrayBuffer(0)}}tg([tc({type:t7})],re.prototype,"publicKeyAlgorithm",void 0),tg([tc({type:i.BitString})],re.prototype,"publicKey",void 0);let rt={fromJSON:e=>p.FromBase64Url(e),toJSON:e=>p.ToBase64Url(new Uint8Array(e))},rr={fromASN:e=>{let t=e.valueBlock.valueHex;return new Uint8Array(t)[0]?e.valueBlock.valueHex:e.valueBlock.valueHex.slice(1)},toASN:e=>new eo({valueHex:new Uint8Array(e)[0]>127?g(new Uint8Array([0]).buffer,e):e})};class rn{constructor(){this.version=0,this.modulus=new ArrayBuffer(0),this.publicExponent=new ArrayBuffer(0),this.privateExponent=new ArrayBuffer(0),this.prime1=new ArrayBuffer(0),this.prime2=new ArrayBuffer(0),this.exponent1=new ArrayBuffer(0),this.exponent2=new ArrayBuffer(0),this.coefficient=new ArrayBuffer(0)}}tg([tc({type:i.Integer,converter:eW})],rn.prototype,"version",void 0),tg([tc({type:i.Integer,converter:rr}),tN({name:"n",converter:rt})],rn.prototype,"modulus",void 0),tg([tc({type:i.Integer,converter:rr}),tN({name:"e",converter:rt})],rn.prototype,"publicExponent",void 0),tg([tc({type:i.Integer,converter:rr}),tN({name:"d",converter:rt})],rn.prototype,"privateExponent",void 0),tg([tc({type:i.Integer,converter:rr}),tN({name:"p",converter:rt})],rn.prototype,"prime1",void 0),tg([tc({type:i.Integer,converter:rr}),tN({name:"q",converter:rt})],rn.prototype,"prime2",void 0),tg([tc({type:i.Integer,converter:rr}),tN({name:"dp",converter:rt})],rn.prototype,"exponent1",void 0),tg([tc({type:i.Integer,converter:rr}),tN({name:"dq",converter:rt})],rn.prototype,"exponent2",void 0),tg([tc({type:i.Integer,converter:rr}),tN({name:"qi",converter:rt})],rn.prototype,"coefficient",void 0),tg([tc({type:i.Any,optional:!0})],rn.prototype,"otherPrimeInfos",void 0);class ri{constructor(){this.modulus=new ArrayBuffer(0),this.publicExponent=new ArrayBuffer(0)}}tg([tc({type:i.Integer,converter:rr}),tN({name:"n",converter:rt})],ri.prototype,"modulus",void 0),tg([tc({type:i.Integer,converter:rr}),tN({name:"e",converter:rt})],ri.prototype,"publicExponent",void 0);let ro=class{constructor(e){this.value=new ArrayBuffer(0),e&&(this.value=e)}toJSON(){let e=new Uint8Array(this.value);if(4!==e[0])throw new tI("Wrong ECPoint. Current version supports only Uncompressed (0x04) point");let t=(e=new Uint8Array(this.value.slice(1))).length/2;return{x:p.ToBase64Url(e.buffer.slice(0,0+t)),y:p.ToBase64Url(e.buffer.slice(0+t,0+t+t))}}fromJSON(e){if(!("x"in e))throw Error("x: Missing required property");if(!("y"in e))throw Error("y: Missing required property");let t=p.FromBase64Url(e.x),r=p.FromBase64Url(e.y),n=g(new Uint8Array([4]).buffer,t,r);return this.value=new Uint8Array(n).buffer,this}};tg([tc({type:i.OctetString})],ro.prototype,"value",void 0),ro=tg([tu({type:n.Choice})],ro);class ra{constructor(){this.version=1,this.privateKey=new ArrayBuffer(0)}fromJSON(e){if(!("d"in e))throw Error("d: Missing required property");if(this.privateKey=p.FromBase64Url(e.d),"x"in e){let t=new ro;t.fromJSON(e);let r=tf.toASN(t);"valueHex"in r.valueBlock&&(this.publicKey=r.valueBlock.valueHex)}return this}toJSON(){let e={};return e.d=p.ToBase64Url(this.privateKey),this.publicKey&&Object.assign(e,new ro(this.publicKey).toJSON()),e}}tg([tc({type:i.Integer,converter:eW})],ra.prototype,"version",void 0),tg([tc({type:i.OctetString})],ra.prototype,"privateKey",void 0),tg([tc({context:0,type:i.Any,optional:!0})],ra.prototype,"parameters",void 0),tg([tc({context:1,type:i.BitString,optional:!0})],ra.prototype,"publicKey",void 0);let rs={fromASN:e=>{let t=new Uint8Array(e.valueBlock.valueHex);return 0===t[0]?t.buffer.slice(1):t.buffer},toASN:e=>{let t=new Uint8Array(e);if(t[0]>127){let e=new Uint8Array(t.length+1);return e.set(t,1),new eo({valueHex:e.buffer})}return new eo({valueHex:e})}};var rl=Object.freeze({__proto__:null,AsnIntegerWithoutPaddingConverter:rs});class ru{static decodePoint(e,t){let r=s.toUint8Array(e);if(0===r.length||4!==r[0])throw Error("Only uncompressed point format supported");let n=(r.length-1)/2;if(n!==Math.ceil(t/8))throw Error("Point does not match field size");return{x:r.slice(1,n+1),y:r.slice(n+1,n+1+n)}}static encodePoint(e,t){let r=Math.ceil(t/8);if(e.x.byteLength!==r||e.y.byteLength!==r)throw Error("X,Y coordinates don't match point size criteria");let n=s.toUint8Array(e.x),i=s.toUint8Array(e.y),o=new Uint8Array(2*r+1);return o[0]=4,o.set(n,1),o.set(i,r+1),o}static getSize(e){return Math.ceil(e/8)}static encodeSignature(e,t){let r=this.getSize(t),n=s.toUint8Array(e.r),i=s.toUint8Array(e.s),o=new Uint8Array(2*r);return o.set(this.padStart(n,r)),o.set(this.padStart(i,r),r),o}static decodeSignature(e,t){let r=this.getSize(t),n=s.toUint8Array(e);if(n.length!==2*r)throw Error("Incorrect size of the signature");let i=n.slice(0,r),o=n.slice(r);return{r:this.trimStart(i),s:this.trimStart(o)}}static trimStart(e){let t=0;for(;t<e.length-1&&0===e[t];)t++;return 0===t?e:e.slice(t,e.length)}static padStart(e,t){if(t===e.length)return e;let r=new Uint8Array(t);return r.set(e,t-e.length),r}}class rc{constructor(){this.r=new ArrayBuffer(0),this.s=new ArrayBuffer(0)}static fromWebCryptoSignature(e){let t=e.byteLength/2,r=ru.decodeSignature(e,8*t),n=new rc;return n.r=s.toArrayBuffer(r.r),n.s=s.toArrayBuffer(r.s),n}toWebCryptoSignature(e){if(!e){let t=Math.max(this.r.byteLength,this.s.byteLength);e=t<=32?256:t<=48?384:521}return ru.encodeSignature(this,e).buffer}}tg([tc({type:i.Integer,converter:rs})],rc.prototype,"r",void 0),tg([tc({type:i.Integer,converter:rs})],rc.prototype,"s",void 0);class rd extends t9{}tg([tc({context:1,implicit:!0,type:i.BitString,optional:!0})],rd.prototype,"publicKey",void 0);let rh=class{constructor(){this.value=new ArrayBuffer(0)}fromJSON(e){if(!e.d)throw Error("d: Missing required property");return this.value=p.FromBase64Url(e.d),this}toJSON(){return{d:p.ToBase64Url(this.value)}}};tg([tc({type:i.OctetString})],rh.prototype,"value",void 0),rh=tg([tu({type:n.Choice})],rh);let rf=class{constructor(e){this.value=new ArrayBuffer(0),e&&(this.value=e)}toJSON(){return{x:p.ToBase64Url(this.value)}}fromJSON(e){if(!("x"in e))throw Error("x: Missing required property");return this.value=p.FromBase64Url(e.x),this}};tg([tc({type:i.BitString})],rf.prototype,"value",void 0),rf=tg([tu({type:n.Choice})],rf);let rp=class{};tg([tc({type:i.OctetString}),tN({type:o.String,converter:rt})],rp.prototype,"d",void 0),rp=tg([tu({type:n.Choice})],rp);let rg="1.2.840.10045.3.1.7",ry="1.3.132.0",rm=`${ry}.34`,rb=`${ry}.35`,rv=`${ry}.10`,rw="1.3.36.3.3.2.8.1.1",rS=`${rw}.1`,r_=`${rw}.2`,rk=`${rw}.3`,rE=`${rw}.4`,rA=`${rw}.5`,rP=`${rw}.6`,rC=`${rw}.7`,rx=`${rw}.8`,rO=`${rw}.9`,rR=`${rw}.10`,rT=`${rw}.11`,rB=`${rw}.12`,rj=`${rw}.13`,rU=`${rw}.14`;var rN=Object.freeze({__proto__:null,AlgorithmIdentifier:t7,get CurvePrivateKey(){return rp},EcDsaSignature:rc,EcPrivateKey:ra,get EcPublicKey(){return ro},get EdPrivateKey(){return rh},get EdPublicKey(){return rf},get ObjectIdentifier(){return t6},OneAsymmetricKey:rd,PrivateKeyInfo:t9,PublicKeyInfo:re,RsaPrivateKey:rn,RsaPublicKey:ri,converters:rl,idBrainpoolP160r1:rS,idBrainpoolP160t1:r_,idBrainpoolP192r1:rk,idBrainpoolP192t1:rE,idBrainpoolP224r1:rA,idBrainpoolP224t1:rP,idBrainpoolP256r1:rC,idBrainpoolP256t1:rx,idBrainpoolP320r1:rO,idBrainpoolP320t1:rR,idBrainpoolP384r1:rT,idBrainpoolP384t1:rB,idBrainpoolP512r1:rj,idBrainpoolP512t1:rU,idEd25519:"***********",idEd448:"***********",idEllipticCurve:ry,idSecp256k1:rv,idSecp256r1:rg,idSecp384r1:rm,idSecp521r1:rb,idVersionOne:rw,idX25519:"***********",idX448:"***********"});class rI{constructor(){}static register(e){let t=new t6;t.value=e.id;let r=tp.serialize(t);this.items.push({...e,raw:r}),this.names.push(e.name)}static find(e){for(let t of(e=e.toUpperCase(),this.items))if(t.name.toUpperCase()===e||t.id.toUpperCase()===e)return t;return null}static get(e){let t=this.find(e);if(!t)throw Error(`Unsupported EC named curve '${e}'`);return t}}rI.items=[],rI.names=[],rI.register({name:"P-256",id:rg,size:256}),rI.register({name:"P-384",id:rm,size:384}),rI.register({name:"P-521",id:rb,size:521}),rI.register({name:"K-256",id:rv,size:256}),rI.register({name:"brainpoolP160r1",id:rS,size:160}),rI.register({name:"brainpoolP160t1",id:r_,size:160}),rI.register({name:"brainpoolP192r1",id:rk,size:192}),rI.register({name:"brainpoolP192t1",id:rE,size:192}),rI.register({name:"brainpoolP224r1",id:rA,size:224}),rI.register({name:"brainpoolP224t1",id:rP,size:224}),rI.register({name:"brainpoolP256r1",id:rC,size:256}),rI.register({name:"brainpoolP256t1",id:rx,size:256}),rI.register({name:"brainpoolP320r1",id:rO,size:320}),rI.register({name:"brainpoolP320t1",id:rR,size:320}),rI.register({name:"brainpoolP384r1",id:rT,size:384}),rI.register({name:"brainpoolP384t1",id:rB,size:384}),rI.register({name:"brainpoolP512r1",id:rj,size:512}),rI.register({name:"brainpoolP512t1",id:rU,size:512});class rM extends tz{constructor(){super(...arguments),this.name="HMAC",this.hashAlgorithms=["SHA-1","SHA-256","SHA-384","SHA-512"],this.usages=["sign","verify"]}getDefaultLength(e){switch(e.toUpperCase()){case"SHA-1":case"SHA-256":case"SHA-384":case"SHA-512":return 512;default:throw Error(`Unknown algorithm name '${e}'`)}}checkGenerateKeyParams(e){if(this.checkRequiredProperty(e,"hash"),this.checkHashAlgorithm(e.hash,this.hashAlgorithms),"length"in e){if("number"!=typeof e.length)throw TypeError("length: Is not a Number");if(e.length<1)throw RangeError("length: Number is out of range")}}checkImportParams(e){this.checkRequiredProperty(e,"hash"),this.checkHashAlgorithm(e.hash,this.hashAlgorithms)}}class rL extends tz{constructor(){super(...arguments),this.name="PBKDF2",this.hashAlgorithms=["SHA-1","SHA-256","SHA-384","SHA-512"],this.usages=["deriveBits","deriveKey"]}checkAlgorithmParams(e){if(this.checkRequiredProperty(e,"hash"),this.checkHashAlgorithm(e.hash,this.hashAlgorithms),this.checkRequiredProperty(e,"salt"),!(e.salt instanceof ArrayBuffer||ArrayBuffer.isView(e.salt)))throw TypeError("salt: Is not of type '(ArrayBuffer or ArrayBufferView)'");if(this.checkRequiredProperty(e,"iterations"),"number"!=typeof e.iterations)throw TypeError("iterations: Is not a Number");if(e.iterations<1)throw TypeError("iterations: Is less than 1")}checkImportKey(e,t,r,n,i,...o){if(super.checkImportKey(e,t,r,n,i,...o),n)throw SyntaxError("extractable: Must be 'false'")}}class rK extends tz{constructor(){super(...arguments),this.name="HKDF",this.hashAlgorithms=["SHA-1","SHA-256","SHA-384","SHA-512"],this.usages=["deriveKey","deriveBits"]}checkAlgorithmParams(e){if(this.checkRequiredProperty(e,"hash"),this.checkHashAlgorithm(e.hash,this.hashAlgorithms),this.checkRequiredProperty(e,"salt"),!s.isBufferSource(e.salt)||(this.checkRequiredProperty(e,"info"),!s.isBufferSource(e.info)))throw TypeError("salt: Is not of type '(ArrayBuffer or ArrayBufferView)'")}checkImportKey(e,t,r,n,i,...o){if(super.checkImportKey(e,t,r,n,i,...o),n)throw SyntaxError("extractable: Must be 'false'")}}class rD extends tz{constructor(){super(...arguments),this.usages=[],this.defaultLength=0}digest(...e){return e[0]={length:this.defaultLength,...e[0]},super.digest.apply(this,e)}checkDigest(e,t){super.checkDigest(e,t);let r=e.length||0;if("number"!=typeof r)throw TypeError("length: Is not a Number");if(r<0)throw TypeError("length: Is negative")}}class rz extends rD{constructor(){super(...arguments),this.name="shake128",this.defaultLength=16}}class rH extends rD{constructor(){super(...arguments),this.name="shake256",this.defaultLength=32}}class rF{get[Symbol.toStringTag](){return"Crypto"}randomUUID(){let e=this.getRandomValues(new Uint8Array(16));e[6]=15&e[6]|64,e[8]=63&e[8]|128;let t=p.ToHex(e).toLowerCase();return`${t.substring(0,8)}-${t.substring(8,12)}-${t.substring(12,16)}-${t.substring(16,20)}-${t.substring(20)}`}}class r${constructor(){this.items={}}get(e){return this.items[e.toLowerCase()]||null}set(e){this.items[e.name.toLowerCase()]=e}removeAt(e){let t=this.get(e.toLowerCase());return t&&delete this.items[e],t}has(e){return!!this.get(e)}get length(){return Object.keys(this.items).length}get algorithms(){let e=[];for(let t in this.items){let r=this.items[t];e.push(r.name)}return e.sort()}}let rq={jwk:["private","public","secret"],pkcs8:["private"],spki:["public"],raw:["secret","public"]},rV=["pkcs8","spki","raw"];class rW{constructor(){this.providers=new r$}static isHashedAlgorithm(e){return!!e&&"object"==typeof e&&"name"in e&&"hash"in e}get[Symbol.toStringTag](){return"SubtleCrypto"}async digest(...e){this.checkRequiredArguments(e,2,"digest");let[t,r,...n]=e,i=this.prepareAlgorithm(t),o=s.toArrayBuffer(r),a=this.getProvider(i.name);return await a.digest(i,o,...n)}async generateKey(...e){this.checkRequiredArguments(e,3,"generateKey");let[t,r,n,...i]=e,o=this.prepareAlgorithm(t),a=this.getProvider(o.name);return await a.generateKey({...o,name:a.name},r,n,...i)}async sign(...e){this.checkRequiredArguments(e,3,"sign");let[t,r,n,...i]=e;this.checkCryptoKey(r);let o=this.prepareAlgorithm(t),a=s.toArrayBuffer(n),l=this.getProvider(o.name);return await l.sign({...o,name:l.name},r,a,...i)}async verify(...e){this.checkRequiredArguments(e,4,"verify");let[t,r,n,i,...o]=e;this.checkCryptoKey(r);let a=this.prepareAlgorithm(t),l=s.toArrayBuffer(i),u=s.toArrayBuffer(n),c=this.getProvider(a.name);return await c.verify({...a,name:c.name},r,u,l,...o)}async encrypt(...e){this.checkRequiredArguments(e,3,"encrypt");let[t,r,n,...i]=e;this.checkCryptoKey(r);let o=this.prepareAlgorithm(t),a=s.toArrayBuffer(n),l=this.getProvider(o.name);return await l.encrypt({...o,name:l.name},r,a,{keyUsage:!0},...i)}async decrypt(...e){this.checkRequiredArguments(e,3,"decrypt");let[t,r,n,...i]=e;this.checkCryptoKey(r);let o=this.prepareAlgorithm(t),a=s.toArrayBuffer(n),l=this.getProvider(o.name);return await l.decrypt({...o,name:l.name},r,a,{keyUsage:!0},...i)}async deriveBits(...e){this.checkRequiredArguments(e,3,"deriveBits");let[t,r,n,...i]=e;this.checkCryptoKey(r);let o=this.prepareAlgorithm(t),a=this.getProvider(o.name);return await a.deriveBits({...o,name:a.name},r,n,{keyUsage:!0},...i)}async deriveKey(...e){this.checkRequiredArguments(e,5,"deriveKey");let[t,r,n,i,o,...a]=e,s=this.prepareAlgorithm(n);this.getProvider(s.name).checkDerivedKeyParams(s);let l=this.prepareAlgorithm(t),u=this.getProvider(l.name);u.checkCryptoKey(r,"deriveKey");let c=await u.deriveBits({...l,name:u.name},r,n.length||512,{keyUsage:!1},...a);return this.importKey("raw",c,n,i,o,...a)}async exportKey(...e){this.checkRequiredArguments(e,2,"exportKey");let[t,r,...n]=e;if(this.checkCryptoKey(r),!rq[t])throw TypeError("Invalid keyFormat argument");if(!rq[t].includes(r.type))throw new DOMException("The key is not of the expected type");let i=this.getProvider(r.algorithm.name);return await i.exportKey(t,r,...n)}async importKey(...e){this.checkRequiredArguments(e,5,"importKey");let[t,r,n,i,o,...a]=e,l=this.prepareAlgorithm(n),u=this.getProvider(l.name);if("jwk"===t){if("object"!=typeof r||!r.kty)throw TypeError("Key data must be an object for JWK import")}else if(rV.includes(t)){if(!s.isBufferSource(r))throw TypeError("Key data must be a BufferSource for non-JWK formats")}else throw TypeError("The provided value is not of type '(ArrayBuffer or ArrayBufferView or JsonWebKey)'");return u.importKey(t,r,{...l,name:u.name},i,o,...a)}async wrapKey(e,t,r,n,...i){let o=await this.exportKey(e,t,...i);if("jwk"===e){let e=JSON.stringify(o);o=p.FromUtf8String(e)}let a=this.prepareAlgorithm(n),l=s.toArrayBuffer(o),u=this.getProvider(a.name);return u.encrypt({...a,name:u.name},r,l,{keyUsage:!1},...i)}async unwrapKey(e,t,r,n,i,o,a,...l){let u=this.prepareAlgorithm(n),c=s.toArrayBuffer(t),d=this.getProvider(u.name),h=await d.decrypt({...u,name:d.name},r,c,{keyUsage:!1},...l);if("jwk"===e)try{h=JSON.parse(p.ToUtf8String(h))}catch(t){let e=TypeError("wrappedKey: Is not a JSON");throw e.internal=t,e}return this.importKey(e,h,i,o,a,...l)}checkRequiredArguments(e,t,r){if(e.length<t)throw TypeError(`Failed to execute '${r}' on 'SubtleCrypto': ${t} arguments required, but only ${e.length} present`)}prepareAlgorithm(e){if("string"==typeof e)return{name:e};if(rW.isHashedAlgorithm(e)){let t={...e};return t.hash=this.prepareAlgorithm(e.hash),t}return{...e}}getProvider(e){let t=this.providers.get(e);if(!t)throw new tM("Unrecognized name");return t}checkCryptoKey(e){if(!(e instanceof t8))throw TypeError("Key is not of type 'CryptoKey'")}}var rG=r(6113),rJ=r.n(rG),rY=r(77282);function rX(e,t,r,n){var i,o=arguments.length,a=o<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,r,n);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(o<3?i(a):o>3?i(t,r,a):i(t,r))||a);return o>3&&a&&Object.defineProperty(t,r,a),a}Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError;/*!
 Copyright (c) Peculiar Ventures, LLC
*/let rQ={fromJSON:e=>Buffer.from(p.FromBase64Url(e)),toJSON:e=>p.ToBase64Url(e)};class rZ extends t8{constructor(){super(...arguments),this.data=Buffer.alloc(0),this.algorithm={name:""},this.extractable=!1,this.type="secret",this.usages=[],this.kty="oct",this.alg=""}}rX([tN({name:"ext",type:o.Boolean,optional:!0})],rZ.prototype,"extractable",void 0),rX([tN({name:"key_ops",type:o.String,repeated:!0,optional:!0})],rZ.prototype,"usages",void 0),rX([tN({type:o.String})],rZ.prototype,"kty",void 0),rX([tN({type:o.String,optional:!0})],rZ.prototype,"alg",void 0);class r0 extends rZ{constructor(){super(...arguments),this.kty="oct",this.type="secret"}}class r1 extends rZ{}class r2 extends r0{get alg(){switch(this.algorithm.name.toUpperCase()){case"AES-CBC":return`A${this.algorithm.length}CBC`;case"AES-CTR":return`A${this.algorithm.length}CTR`;case"AES-GCM":return`A${this.algorithm.length}GCM`;case"AES-KW":return`A${this.algorithm.length}KW`;case"AES-CMAC":return`A${this.algorithm.length}CMAC`;case"AES-ECB":return`A${this.algorithm.length}ECB`;default:throw new tM("Unsupported algorithm name")}}set alg(e){}}rX([tN({name:"k",converter:rQ})],r2.prototype,"data",void 0);let r8=new WeakMap;function r3(e){let t=r8.get(e);if(!t)throw new tK("Cannot get CryptoKey from secure storage");return t}function r4(e){let t=t8.create(e.algorithm,e.type,e.extractable,e.usages);return Object.freeze(t),r8.set(t,e),t}class r5{static async generateKey(e,t,r){let n=new r2;return n.algorithm=e,n.extractable=t,n.usages=r,n.data=rJ().randomBytes(e.length>>3),n}static async exportKey(e,t){if(!(t instanceof r2))throw Error("key: Is not AesCryptoKey");switch(e.toLowerCase()){case"jwk":return tj.toJSON(t);case"raw":return new Uint8Array(t.data).buffer;default:throw new tK("format: Must be 'jwk' or 'raw'")}}static async importKey(e,t,r,n,i){let o;switch(e.toLowerCase()){case"jwk":o=tU.fromJSON(t,{targetSchema:r2});break;case"raw":(o=new r2).data=Buffer.from(t);break;default:throw new tK("format: Must be 'jwk' or 'raw'")}switch(o.algorithm=r,o.algorithm.length=o.data.length<<3,o.extractable=n,o.usages=i,o.algorithm.length){case 128:case 192:case 256:break;default:throw new tK("keyData: Is wrong key length")}return o}static async encrypt(e,t,r){switch(e.name.toUpperCase()){case"AES-CBC":return this.encryptAesCBC(e,t,Buffer.from(r));case"AES-CTR":return this.encryptAesCTR(e,t,Buffer.from(r));case"AES-GCM":return this.encryptAesGCM(e,t,Buffer.from(r));case"AES-KW":return this.encryptAesKW(e,t,Buffer.from(r));case"AES-ECB":return this.encryptAesECB(e,t,Buffer.from(r));default:throw new tK("algorithm: Is not recognized")}}static async decrypt(e,t,r){if(!(t instanceof r2))throw Error("key: Is not AesCryptoKey");switch(e.name.toUpperCase()){case"AES-CBC":return this.decryptAesCBC(e,t,Buffer.from(r));case"AES-CTR":return this.decryptAesCTR(e,t,Buffer.from(r));case"AES-GCM":return this.decryptAesGCM(e,t,Buffer.from(r));case"AES-KW":return this.decryptAesKW(e,t,Buffer.from(r));case"AES-ECB":return this.decryptAesECB(e,t,Buffer.from(r));default:throw new tK("algorithm: Is not recognized")}}static async encryptAesCBC(e,t,r){let n=rJ().createCipheriv(`aes-${t.algorithm.length}-cbc`,t.data,new Uint8Array(e.iv)),i=n.update(r);return i=Buffer.concat([i,n.final()]),new Uint8Array(i).buffer}static async decryptAesCBC(e,t,r){let n=rJ().createDecipheriv(`aes-${t.algorithm.length}-cbc`,t.data,new Uint8Array(e.iv)),i=n.update(r);return i=Buffer.concat([i,n.final()]),new Uint8Array(i).buffer}static async encryptAesCTR(e,t,r){let n=rJ().createCipheriv(`aes-${t.algorithm.length}-ctr`,t.data,Buffer.from(e.counter)),i=n.update(r);return i=Buffer.concat([i,n.final()]),new Uint8Array(i).buffer}static async decryptAesCTR(e,t,r){let n=rJ().createDecipheriv(`aes-${t.algorithm.length}-ctr`,t.data,new Uint8Array(e.counter)),i=n.update(r);return i=Buffer.concat([i,n.final()]),new Uint8Array(i).buffer}static async encryptAesGCM(e,t,r){let n=rJ().createCipheriv(`aes-${t.algorithm.length}-gcm`,t.data,Buffer.from(e.iv),{authTagLength:(e.tagLength||128)>>3});e.additionalData&&n.setAAD(Buffer.from(e.additionalData));let i=n.update(r);return i=Buffer.concat([i,n.final(),n.getAuthTag()]),new Uint8Array(i).buffer}static async decryptAesGCM(e,t,r){let n=rJ().createDecipheriv(`aes-${t.algorithm.length}-gcm`,t.data,new Uint8Array(e.iv)),i=(e.tagLength||128)>>3,o=r.slice(0,r.length-i),a=r.slice(r.length-i);e.additionalData&&n.setAAD(Buffer.from(e.additionalData)),n.setAuthTag(a);let s=n.update(o);return s=Buffer.concat([s,n.final()]),new Uint8Array(s).buffer}static async encryptAesKW(e,t,r){let n=rJ().createCipheriv(`id-aes${t.algorithm.length}-wrap`,t.data,this.AES_KW_IV),i=n.update(r);return i=Buffer.concat([i,n.final()]),new Uint8Array(i).buffer}static async decryptAesKW(e,t,r){let n=rJ().createDecipheriv(`id-aes${t.algorithm.length}-wrap`,t.data,this.AES_KW_IV),i=n.update(r);return i=Buffer.concat([i,n.final()]),new Uint8Array(i).buffer}static async encryptAesECB(e,t,r){let n=rJ().createCipheriv(`aes-${t.algorithm.length}-ecb`,t.data,new Uint8Array(0)),i=n.update(r);return i=Buffer.concat([i,n.final()]),new Uint8Array(i).buffer}static async decryptAesECB(e,t,r){let n=rJ().createDecipheriv(`aes-${t.algorithm.length}-ecb`,t.data,new Uint8Array(0)),i=n.update(r);return i=Buffer.concat([i,n.final()]),new Uint8Array(i).buffer}}r5.AES_KW_IV=Buffer.from("A6A6A6A6A6A6A6A6","hex");class r6 extends tF{async onGenerateKey(e,t,r){return r4(await r5.generateKey({name:this.name,length:e.length},t,r))}async onEncrypt(e,t,r){return r5.encrypt(e,r3(t),new Uint8Array(r))}async onDecrypt(e,t,r){return r5.decrypt(e,r3(t),new Uint8Array(r))}async onExportKey(e,t){return r5.exportKey(e,r3(t))}async onImportKey(e,t,r,n,i){return r4(await r5.importKey(e,t,{name:r.name},n,i))}checkCryptoKey(e,t){if(super.checkCryptoKey(e,t),!(r3(e) instanceof r2))throw TypeError("key: Is not a AesCryptoKey")}}let r7=Buffer.from([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]),r9=Buffer.from([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,135]);function ne(e){let t=Buffer.alloc(e.length),r=e.length-1;for(let n=0;n<r;n++)t[n]=e[n]<<1,128&e[n+1]&&(t[n]+=1);return t[r]=e[r]<<1,t}function nt(e,t){let r=Math.min(e.length,t.length),n=Buffer.alloc(r);for(let i=0;i<r;i++)n[i]=e[i]^t[i];return n}function nr(e,t){let r=rG.createCipheriv(`aes${e.length<<3}`,e,r7),n=r.update(t);return r.final(),n}function nn(e,t){let r=Buffer.alloc(16),n=16*t;return e.copy(r,0,n,n+16),r}class ni extends t${async onGenerateKey(e,t,r){return r4(await r5.generateKey({name:this.name,length:e.length},t,r))}async onSign(e,t,r){let n=function(e,t){let r,n;let i=function(e){let t=nr(e,r7),r=ne(t);128&t[0]&&(r=nt(r,r9));let n=ne(r);return 128&r[0]&&(n=nt(n,r9)),{subkey1:r,subkey2:n}}(e),o=Math.ceil(t.length/16);0===o?(o=1,r=!1):r=t.length%16==0;let a=o-1;n=r?nt(nn(t,a),i.subkey1):nt(function(e,t){let r=Buffer.alloc(16),n=16*t,i=e.length;return r.fill(0),e.copy(r,0,n,i),r[i-n]=128,r}(t,a),i.subkey2);let s=r7;for(let r=0;r<a;r++)s=nr(e,nt(s,nn(t,r)));return nr(e,nt(n,s))}(r3(t).data,Buffer.from(r));return new Uint8Array(n).buffer}async onVerify(e,t,r,n){let i=await this.sign(e,t,n);return 0===Buffer.from(r).compare(Buffer.from(i))}async onExportKey(e,t){return r5.exportKey(e,r3(t))}async onImportKey(e,t,r,n,i){return r4(await r5.importKey(e,t,{name:r.name},n,i))}checkCryptoKey(e,t){if(super.checkCryptoKey(e,t),!(r3(e) instanceof r2))throw TypeError("key: Is not a AesCryptoKey")}}class no extends tq{async onGenerateKey(e,t,r){return r4(await r5.generateKey({name:this.name,length:e.length},t,r))}async onEncrypt(e,t,r){return r5.encrypt(e,r3(t),new Uint8Array(r))}async onDecrypt(e,t,r){return r5.decrypt(e,r3(t),new Uint8Array(r))}async onExportKey(e,t){return r5.exportKey(e,r3(t))}async onImportKey(e,t,r,n,i){return r4(await r5.importKey(e,t,{name:r.name},n,i))}checkCryptoKey(e,t){if(super.checkCryptoKey(e,t),!(r3(e) instanceof r2))throw TypeError("key: Is not a AesCryptoKey")}}class na extends tW{async onGenerateKey(e,t,r){return r4(await r5.generateKey({name:this.name,length:e.length},t,r))}async onEncrypt(e,t,r){return r5.encrypt(e,r3(t),new Uint8Array(r))}async onDecrypt(e,t,r){return r5.decrypt(e,r3(t),new Uint8Array(r))}async onExportKey(e,t){return r5.exportKey(e,r3(t))}async onImportKey(e,t,r,n,i){return r4(await r5.importKey(e,t,{name:r.name},n,i))}checkCryptoKey(e,t){if(super.checkCryptoKey(e,t),!(r3(e) instanceof r2))throw TypeError("key: Is not a AesCryptoKey")}}class ns extends tG{async onGenerateKey(e,t,r){return r4(await r5.generateKey({name:this.name,length:e.length},t,r))}async onExportKey(e,t){return r5.exportKey(e,r3(t))}async onImportKey(e,t,r,n,i){return r4(await r5.importKey(e,t,{name:r.name},n,i))}async onEncrypt(e,t,r){return r5.encrypt(e,r3(t),new Uint8Array(r))}async onDecrypt(e,t,r){return r5.decrypt(e,r3(t),new Uint8Array(r))}checkCryptoKey(e,t){if(super.checkCryptoKey(e,t),!(r3(e) instanceof r2))throw TypeError("key: Is not a AesCryptoKey")}}class nl extends tV{async onGenerateKey(e,t,r){return r4(await r5.generateKey({name:this.name,length:e.length},t,r))}async onEncrypt(e,t,r){return r5.encrypt(e,r3(t),new Uint8Array(r))}async onDecrypt(e,t,r){return r5.decrypt(e,r3(t),new Uint8Array(r))}async onExportKey(e,t){return r5.exportKey(e,r3(t))}async onImportKey(e,t,r,n,i){return r4(await r5.importKey(e,t,{name:r.name},n,i))}checkCryptoKey(e,t){if(super.checkCryptoKey(e,t),!(r3(e) instanceof r2))throw TypeError("key: Is not a AesCryptoKey")}}class nu extends r0{get alg(){switch(this.algorithm.name.toUpperCase()){case"DES-CBC":return"DES-CBC";case"DES-EDE3-CBC":return"3DES-CBC";default:throw new tM("Unsupported algorithm name")}}set alg(e){}}rX([tN({name:"k",converter:rQ})],nu.prototype,"data",void 0);class nc{static async generateKey(e,t,r){let n=new nu;return n.algorithm=e,n.extractable=t,n.usages=r,n.data=rJ().randomBytes(e.length>>3),n}static async exportKey(e,t){switch(e.toLowerCase()){case"jwk":return tj.toJSON(t);case"raw":return new Uint8Array(t.data).buffer;default:throw new tK("format: Must be 'jwk' or 'raw'")}}static async importKey(e,t,r,n,i){let o;switch(e.toLowerCase()){case"jwk":o=tU.fromJSON(t,{targetSchema:nu});break;case"raw":(o=new nu).data=Buffer.from(t);break;default:throw new tK("format: Must be 'jwk' or 'raw'")}return o.algorithm=r,o.extractable=n,o.usages=i,o}static async encrypt(e,t,r){switch(e.name.toUpperCase()){case"DES-CBC":return this.encryptDesCBC(e,t,Buffer.from(r));case"DES-EDE3-CBC":return this.encryptDesEDE3CBC(e,t,Buffer.from(r));default:throw new tK("algorithm: Is not recognized")}}static async decrypt(e,t,r){if(!(t instanceof nu))throw Error("key: Is not DesCryptoKey");switch(e.name.toUpperCase()){case"DES-CBC":return this.decryptDesCBC(e,t,Buffer.from(r));case"DES-EDE3-CBC":return this.decryptDesEDE3CBC(e,t,Buffer.from(r));default:throw new tK("algorithm: Is not recognized")}}static async encryptDesCBC(e,t,r){let n=rJ().createCipheriv("des-cbc",t.data,new Uint8Array(e.iv)),i=n.update(r);return i=Buffer.concat([i,n.final()]),new Uint8Array(i).buffer}static async decryptDesCBC(e,t,r){let n=rJ().createDecipheriv("des-cbc",t.data,new Uint8Array(e.iv)),i=n.update(r);return i=Buffer.concat([i,n.final()]),new Uint8Array(i).buffer}static async encryptDesEDE3CBC(e,t,r){let n=rJ().createCipheriv("des-ede3-cbc",t.data,Buffer.from(e.iv)),i=n.update(r);return i=Buffer.concat([i,n.final()]),new Uint8Array(i).buffer}static async decryptDesEDE3CBC(e,t,r){let n=rJ().createDecipheriv("des-ede3-cbc",t.data,new Uint8Array(e.iv)),i=n.update(r);return i=Buffer.concat([i,n.final()]),new Uint8Array(i).buffer}}class nd extends tJ{constructor(){super(...arguments),this.keySizeBits=64,this.ivSize=8,this.name="DES-CBC"}async onGenerateKey(e,t,r){return r4(await nc.generateKey({name:this.name,length:this.keySizeBits},t,r))}async onEncrypt(e,t,r){return nc.encrypt(e,r3(t),new Uint8Array(r))}async onDecrypt(e,t,r){return nc.decrypt(e,r3(t),new Uint8Array(r))}async onExportKey(e,t){return nc.exportKey(e,r3(t))}async onImportKey(e,t,r,n,i){let o=await nc.importKey(e,t,{name:this.name,length:this.keySizeBits},n,i);if(o.data.length!==this.keySizeBits>>3)throw new tK("keyData: Wrong key size");return r4(o)}checkCryptoKey(e,t){if(super.checkCryptoKey(e,t),!(r3(e) instanceof nu))throw TypeError("key: Is not a DesCryptoKey")}}class nh extends tJ{constructor(){super(...arguments),this.keySizeBits=192,this.ivSize=8,this.name="DES-EDE3-CBC"}async onGenerateKey(e,t,r){return r4(await nc.generateKey({name:this.name,length:this.keySizeBits},t,r))}async onEncrypt(e,t,r){return nc.encrypt(e,r3(t),new Uint8Array(r))}async onDecrypt(e,t,r){return nc.decrypt(e,r3(t),new Uint8Array(r))}async onExportKey(e,t){return nc.exportKey(e,r3(t))}async onImportKey(e,t,r,n,i){let o=await nc.importKey(e,t,{name:this.name,length:this.keySizeBits},n,i);if(o.data.length!==this.keySizeBits>>3)throw new tK("keyData: Wrong key size");return r4(o)}checkCryptoKey(e,t){if(super.checkCryptoKey(e,t),!(r3(e) instanceof nu))throw TypeError("key: Is not a DesCryptoKey")}}function nf(e){switch(e.name.toUpperCase()){case"RSA-OAEP":{let t=/(\d+)$/.exec(e.hash.name)[1];return`RSA-OAEP${"1"!==t?`-${t}`:""}`}case"RSASSA-PKCS1-V1_5":return`RS${/(\d+)$/.exec(e.hash.name)[1]}`;case"RSA-PSS":return`PS${/(\d+)$/.exec(e.hash.name)[1]}`;case"RSA-PKCS1":return"RS1";default:throw new tK("algorithm: Is not recognized")}}class np extends r1{constructor(){super(...arguments),this.type="private"}getKey(){let e=th.parse(this.data,rN.PrivateKeyInfo);return th.parse(e.privateKey,rN.RsaPrivateKey)}toJSON(){let e=this.getKey();return Object.assign({kty:"RSA",alg:nf(this.algorithm),key_ops:this.usages,ext:this.extractable},tj.toJSON(e))}fromJSON(e){let t=tU.fromJSON(e,{targetSchema:rN.RsaPrivateKey}),r=new rN.PrivateKeyInfo;r.privateKeyAlgorithm.algorithm="1.2.840.113549.1.1.1",r.privateKeyAlgorithm.parameters=null,r.privateKey=tf.serialize(t),this.data=Buffer.from(tf.serialize(r))}}class ng extends r1{constructor(){super(...arguments),this.type="public"}getKey(){let e=th.parse(this.data,rN.PublicKeyInfo);return th.parse(e.publicKey,rN.RsaPublicKey)}toJSON(){let e=this.getKey();return Object.assign({kty:"RSA",alg:nf(this.algorithm),key_ops:this.usages,ext:this.extractable},tj.toJSON(e))}fromJSON(e){let t=tU.fromJSON(e,{targetSchema:rN.RsaPublicKey}),r=new rN.PublicKeyInfo;r.publicKeyAlgorithm.algorithm="1.2.840.113549.1.1.1",r.publicKeyAlgorithm.parameters=null,r.publicKey=tf.serialize(t),this.data=Buffer.from(tf.serialize(r))}}class ny{static async generateKey(e,t,r){let n=new np;n.algorithm=e,n.extractable=t,n.usages=r.filter(e=>-1!==this.privateKeyUsages.indexOf(e));let i=new ng;i.algorithm=e,i.extractable=!0,i.usages=r.filter(e=>-1!==this.publicKeyUsages.indexOf(e));let o=Buffer.concat([Buffer.alloc(4-e.publicExponent.byteLength,0),Buffer.from(e.publicExponent)]).readInt32BE(0),a=rJ().generateKeyPairSync("rsa",{modulusLength:e.modulusLength,publicExponent:o,publicKeyEncoding:{format:"der",type:"spki"},privateKeyEncoding:{format:"der",type:"pkcs8"}});return n.data=a.privateKey,i.data=a.publicKey,{privateKey:n,publicKey:i}}static async exportKey(e,t){switch(e.toLowerCase()){case"jwk":return tj.toJSON(t);case"pkcs8":case"spki":return new Uint8Array(t.data).buffer;default:throw new tK("format: Must be 'jwk', 'pkcs8' or 'spki'")}}static async importKey(e,t,r,n,i){switch(e.toLowerCase()){case"jwk":if(t.d){let e=tU.fromJSON(t,{targetSchema:rN.RsaPrivateKey});return this.importPrivateKey(e,r,n,i)}{let e=tU.fromJSON(t,{targetSchema:rN.RsaPublicKey});return this.importPublicKey(e,r,n,i)}case"spki":{let e=th.parse(new Uint8Array(t),rN.PublicKeyInfo),o=th.parse(e.publicKey,rN.RsaPublicKey);return this.importPublicKey(o,r,n,i)}case"pkcs8":{let e=th.parse(new Uint8Array(t),rN.PrivateKeyInfo),o=th.parse(e.privateKey,rN.RsaPrivateKey);return this.importPrivateKey(o,r,n,i)}default:throw new tK("format: Must be 'jwk', 'pkcs8' or 'spki'")}}static async sign(e,t,r){switch(e.name.toUpperCase()){case"RSA-PSS":case"RSASSA-PKCS1-V1_5":return this.signRsa(e,t,r);default:throw new tK("algorithm: Is not recognized")}}static async verify(e,t,r,n){switch(e.name.toUpperCase()){case"RSA-PSS":case"RSASSA-PKCS1-V1_5":return this.verifySSA(e,t,n,r);default:throw new tK("algorithm: Is not recognized")}}static async encrypt(e,t,r){if("RSA-OAEP"===e.name.toUpperCase())return this.encryptOAEP(e,t,r);throw new tK("algorithm: Is not recognized")}static async decrypt(e,t,r){if("RSA-OAEP"===e.name.toUpperCase())return this.decryptOAEP(e,t,r);throw new tK("algorithm: Is not recognized")}static importPrivateKey(e,t,r,n){let i=new rN.PrivateKeyInfo;i.privateKeyAlgorithm.algorithm="1.2.840.113549.1.1.1",i.privateKeyAlgorithm.parameters=null,i.privateKey=tf.serialize(e);let o=new np;return o.data=Buffer.from(tf.serialize(i)),o.algorithm=Object.assign({},t),o.algorithm.publicExponent=new Uint8Array(e.publicExponent),o.algorithm.modulusLength=e.modulus.byteLength<<3,o.extractable=r,o.usages=n,o}static importPublicKey(e,t,r,n){let i=new rN.PublicKeyInfo;i.publicKeyAlgorithm.algorithm="1.2.840.113549.1.1.1",i.publicKeyAlgorithm.parameters=null,i.publicKey=tf.serialize(e);let o=new ng;return o.data=Buffer.from(tf.serialize(i)),o.algorithm=Object.assign({},t),o.algorithm.publicExponent=new Uint8Array(e.publicExponent),o.algorithm.modulusLength=e.modulus.byteLength<<3,o.extractable=r,o.usages=n,o}static getCryptoAlgorithm(e){switch(e.hash.name.toUpperCase()){case"SHA-1":return"RSA-SHA1";case"SHA-256":return"RSA-SHA256";case"SHA-384":return"RSA-SHA384";case"SHA-512":return"RSA-SHA512";case"SHA3-256":return"RSA-SHA3-256";case"SHA3-384":return"RSA-SHA3-384";case"SHA3-512":return"RSA-SHA3-512";default:throw new tK("algorithm.hash: Is not recognized")}}static signRsa(e,t,r){let n=this.getCryptoAlgorithm(t.algorithm),i=rJ().createSign(n);i.update(Buffer.from(r)),t.pem||(t.pem=`-----BEGIN PRIVATE KEY-----
${t.data.toString("base64")}
-----END PRIVATE KEY-----`);let o={key:t.pem};"RSA-PSS"===e.name.toUpperCase()&&(o.padding=rJ().constants.RSA_PKCS1_PSS_PADDING,o.saltLength=e.saltLength);let a=i.sign(o);return new Uint8Array(a).buffer}static verifySSA(e,t,r,n){let i=this.getCryptoAlgorithm(t.algorithm),o=rJ().createVerify(i);o.update(Buffer.from(r)),t.pem||(t.pem=`-----BEGIN PUBLIC KEY-----
${t.data.toString("base64")}
-----END PUBLIC KEY-----`);let a={key:t.pem};return"RSA-PSS"===e.name.toUpperCase()&&(a.padding=rJ().constants.RSA_PKCS1_PSS_PADDING,a.saltLength=e.saltLength),o.verify(a,n)}static encryptOAEP(e,t,r){let n={key:`-----BEGIN PUBLIC KEY-----
${t.data.toString("base64")}
-----END PUBLIC KEY-----`,padding:rJ().constants.RSA_PKCS1_OAEP_PADDING};return e.label,new Uint8Array(rJ().publicEncrypt(n,r)).buffer}static decryptOAEP(e,t,r){let n={key:`-----BEGIN PRIVATE KEY-----
${t.data.toString("base64")}
-----END PRIVATE KEY-----`,padding:rJ().constants.RSA_PKCS1_OAEP_PADDING};return e.label,new Uint8Array(rJ().privateDecrypt(n,r)).buffer}}ny.publicKeyUsages=["verify","encrypt","wrapKey"],ny.privateKeyUsages=["sign","decrypt","unwrapKey"];class nm extends tX{constructor(){super(...arguments),this.hashAlgorithms=["SHA-1","SHA-256","SHA-384","SHA-512","shake128","shake256","SHA3-256","SHA3-384","SHA3-512"]}async onGenerateKey(e,t,r){let n=await ny.generateKey({...e,name:this.name},t,r);return{privateKey:r4(n.privateKey),publicKey:r4(n.publicKey)}}async onSign(e,t,r){return ny.sign(e,r3(t),new Uint8Array(r))}async onVerify(e,t,r,n){return ny.verify(e,r3(t),new Uint8Array(r),new Uint8Array(n))}async onExportKey(e,t){return ny.exportKey(e,r3(t))}async onImportKey(e,t,r,n,i){return r4(await ny.importKey(e,t,{...r,name:this.name},n,i))}checkCryptoKey(e,t){super.checkCryptoKey(e,t);let r=r3(e);if(!(r instanceof np||r instanceof ng))throw TypeError("key: Is not RSA CryptoKey")}}class nb extends tQ{constructor(){super(...arguments),this.hashAlgorithms=["SHA-1","SHA-256","SHA-384","SHA-512","shake128","shake256","SHA3-256","SHA3-384","SHA3-512"]}async onGenerateKey(e,t,r){let n=await ny.generateKey({...e,name:this.name},t,r);return{privateKey:r4(n.privateKey),publicKey:r4(n.publicKey)}}async onSign(e,t,r){return ny.sign(e,r3(t),new Uint8Array(r))}async onVerify(e,t,r,n){return ny.verify(e,r3(t),new Uint8Array(r),new Uint8Array(n))}async onExportKey(e,t){return ny.exportKey(e,r3(t))}async onImportKey(e,t,r,n,i){return r4(await ny.importKey(e,t,{...r,name:this.name},n,i))}checkCryptoKey(e,t){super.checkCryptoKey(e,t);let r=r3(e);if(!(r instanceof np||r instanceof ng))throw TypeError("key: Is not RSA CryptoKey")}}class nv{static size(e){switch(e.name.toUpperCase()){case"SHA-1":return 160;case"SHA-256":case"SHA3-256":return 256;case"SHA-384":case"SHA3-384":return 384;case"SHA-512":case"SHA3-512":return 512;default:throw Error("Unrecognized name")}}static getAlgorithmName(e){switch(e.name.toUpperCase()){case"SHA-1":return"sha1";case"SHA-256":return"sha256";case"SHA-384":return"sha384";case"SHA-512":return"sha512";case"SHA3-256":return"sha3-256";case"SHA3-384":return"sha3-384";case"SHA3-512":return"sha3-512";default:throw Error("Unrecognized name")}}static digest(e,t){let r=this.getAlgorithmName(e),n=rJ().createHash(r).update(Buffer.from(t)).digest();return new Uint8Array(n).buffer}}class nw extends tZ{async onGenerateKey(e,t,r){let n=await ny.generateKey({...e,name:this.name},t,r);return{privateKey:r4(n.privateKey),publicKey:r4(n.publicKey)}}async onEncrypt(e,t,r){let n=r3(t),i=new Uint8Array(r),o=Math.ceil(n.algorithm.modulusLength>>3),a=nv.size(n.algorithm.hash)>>3,l=i.byteLength,u=o-l-2*a-2;if(l>o-2*a-2)throw Error("Data too large");let c=new Uint8Array(o),d=c.subarray(1,a+1),h=c.subarray(a+1);h.set(i,a+u+1);let f=rJ().createHash(n.algorithm.hash.name.replace("-","")).update(s.toUint8Array(e.label||new Uint8Array(0))).digest();h.set(f,0),h[a+u]=1,rJ().randomFillSync(d);let p=this.mgf1(n.algorithm.hash,d,h.length);for(let e=0;e<h.length;e++)h[e]^=p[e];let g=this.mgf1(n.algorithm.hash,h,d.length);for(let e=0;e<d.length;e++)d[e]^=g[e];n.pem||(n.pem=`-----BEGIN PUBLIC KEY-----
${n.data.toString("base64")}
-----END PUBLIC KEY-----`);let y=rJ().publicEncrypt({key:n.pem,padding:rJ().constants.RSA_NO_PADDING},Buffer.from(c));return new Uint8Array(y).buffer}async onDecrypt(e,t,r){let n=r3(t),i=Math.ceil(n.algorithm.modulusLength>>3),o=nv.size(n.algorithm.hash)>>3;if(r.byteLength!==i)throw Error("Bad data");n.pem||(n.pem=`-----BEGIN PRIVATE KEY-----
${n.data.toString("base64")}
-----END PRIVATE KEY-----`);let a=rJ().privateDecrypt({key:n.pem,padding:rJ().constants.RSA_NO_PADDING},Buffer.from(r)),l=a[0],u=a.subarray(1,o+1),c=a.subarray(o+1);if(0!==l)throw Error("Decryption failed");let d=this.mgf1(n.algorithm.hash,c,u.length);for(let e=0;e<u.length;e++)u[e]^=d[e];let h=this.mgf1(n.algorithm.hash,u,c.length);for(let e=0;e<c.length;e++)c[e]^=h[e];let f=rJ().createHash(n.algorithm.hash.name.replace("-","")).update(s.toUint8Array(e.label||new Uint8Array(0))).digest();for(let e=0;e<o;e++)if(f[e]!==c[e])throw Error("Decryption failed");let p=o;for(;p<c.length;p++){let e=c[p];if(1===e)break;if(0!==e)throw Error("Decryption failed")}if(p===c.length)throw Error("Decryption failed");return a=c.subarray(p+1),new Uint8Array(a).buffer}async onExportKey(e,t){return ny.exportKey(e,r3(t))}async onImportKey(e,t,r,n,i){return r4(await ny.importKey(e,t,{...r,name:this.name},n,i))}checkCryptoKey(e,t){super.checkCryptoKey(e,t);let r=r3(e);if(!(r instanceof np||r instanceof ng))throw TypeError("key: Is not RSA CryptoKey")}mgf1(e,t,r=0){let n=nv.size(e)>>3,i=new Uint8Array(r),o=new Uint8Array(4),a=Math.ceil(r/n);for(let r=0;r<a;r++){o[0]=r>>>24,o[1]=r>>>16&255,o[2]=r>>>8&255,o[3]=255&r;let a=i.subarray(r*n),s=rJ().createHash(e.name.replace("-","")).update(t).update(o).digest();s.length>a.length&&(s=s.subarray(0,a.length)),a.set(s)}return i}}class nS extends tz{constructor(){super(...arguments),this.name="RSAES-PKCS1-v1_5",this.usages={publicKey:["encrypt","wrapKey"],privateKey:["decrypt","unwrapKey"]}}async onGenerateKey(e,t,r){let n=await ny.generateKey({...e,name:this.name},t,r);return{privateKey:r4(n.privateKey),publicKey:r4(n.publicKey)}}checkGenerateKeyParams(e){if(this.checkRequiredProperty(e,"publicExponent"),!(e.publicExponent&&e.publicExponent instanceof Uint8Array))throw TypeError("publicExponent: Missing or not a Uint8Array");let t=p.ToBase64(e.publicExponent);if(!("Aw=="===t||"AQAB"===t))throw TypeError("publicExponent: Must be [3] or [1,0,1]");switch(this.checkRequiredProperty(e,"modulusLength"),e.modulusLength){case 1024:case 2048:case 4096:break;default:throw TypeError("modulusLength: Must be 1024, 2048, or 4096")}}async onEncrypt(e,t,r){let n=this.toCryptoOptions(t),i=rG.publicEncrypt(n,new Uint8Array(r));return new Uint8Array(i).buffer}async onDecrypt(e,t,r){let n=this.toCryptoOptions(t),i=rG.privateDecrypt(n,new Uint8Array(r));return new Uint8Array(i).buffer}async onExportKey(e,t){return ny.exportKey(e,r3(t))}async onImportKey(e,t,r,n,i){return r4(await ny.importKey(e,t,{...r,name:this.name},n,i))}checkCryptoKey(e,t){super.checkCryptoKey(e,t);let r=r3(e);if(!(r instanceof np||r instanceof ng))throw TypeError("key: Is not RSA CryptoKey")}toCryptoOptions(e){let t=e.type.toUpperCase();return{key:`-----BEGIN ${t} KEY-----
${r3(e).data.toString("base64")}
-----END ${t} KEY-----`,padding:rG.constants.RSA_PKCS1_PADDING}}}let n_={"1.2.840.10045.3.1.7":"P-256","P-256":"1.2.840.10045.3.1.7","1.3.132.0.34":"P-384","P-384":"1.3.132.0.34","1.3.132.0.35":"P-521","P-521":"1.3.132.0.35","1.3.132.0.10":"K-256","K-256":"1.3.132.0.10",brainpoolP160r1:"1.3.36.3.3.2.8.1.1.1","1.3.36.3.3.2.8.1.1.1":"brainpoolP160r1",brainpoolP160t1:"1.3.36.3.3.2.8.1.1.2","1.3.36.3.3.2.8.1.1.2":"brainpoolP160t1",brainpoolP192r1:"1.3.36.3.3.2.8.1.1.3","1.3.36.3.3.2.8.1.1.3":"brainpoolP192r1",brainpoolP192t1:"1.3.36.3.3.2.8.1.1.4","1.3.36.3.3.2.8.1.1.4":"brainpoolP192t1",brainpoolP224r1:"1.3.36.3.3.2.8.1.1.5","1.3.36.3.3.2.8.1.1.5":"brainpoolP224r1",brainpoolP224t1:"1.3.36.3.3.2.8.1.1.6","1.3.36.3.3.2.8.1.1.6":"brainpoolP224t1",brainpoolP256r1:"1.3.36.3.3.2.8.1.1.7","1.3.36.3.3.2.8.1.1.7":"brainpoolP256r1",brainpoolP256t1:"1.3.36.3.3.2.8.1.1.8","1.3.36.3.3.2.8.1.1.8":"brainpoolP256t1",brainpoolP320r1:"1.3.36.3.3.2.8.1.1.9","1.3.36.3.3.2.8.1.1.9":"brainpoolP320r1",brainpoolP320t1:"1.3.36.3.3.2.8.1.1.10","1.3.36.3.3.2.8.1.1.10":"brainpoolP320t1",brainpoolP384r1:"1.3.36.3.3.2.8.1.1.11","1.3.36.3.3.2.8.1.1.11":"brainpoolP384r1",brainpoolP384t1:"1.3.36.3.3.2.8.1.1.12","1.3.36.3.3.2.8.1.1.12":"brainpoolP384t1",brainpoolP512r1:"1.3.36.3.3.2.8.1.1.13","1.3.36.3.3.2.8.1.1.13":"brainpoolP512r1",brainpoolP512t1:"1.3.36.3.3.2.8.1.1.14","1.3.36.3.3.2.8.1.1.14":"brainpoolP512t1"};function nk(e){let t=n_[e];if(!t)throw new tK(`Cannot convert WebCrypto named curve '${e}' to OID`);return t}class nE extends r1{constructor(){super(...arguments),this.type="private"}getKey(){let e=th.parse(this.data,rN.PrivateKeyInfo);return th.parse(e.privateKey,rN.EcPrivateKey)}toJSON(){let e=this.getKey();return Object.assign({kty:"EC",crv:this.algorithm.namedCurve,key_ops:this.usages,ext:this.extractable},tj.toJSON(e))}fromJSON(e){if(!e.crv)throw new tK("Cannot get named curve from JWK. Property 'crv' is required");let t=new rN.PrivateKeyInfo;t.privateKeyAlgorithm.algorithm="1.2.840.10045.2.1",t.privateKeyAlgorithm.parameters=tf.serialize(new rN.ObjectIdentifier(nk(e.crv)));let r=tU.fromJSON(e,{targetSchema:rN.EcPrivateKey});return t.privateKey=tf.serialize(r),this.data=Buffer.from(tf.serialize(t)),this}}class nA extends r1{constructor(){super(...arguments),this.type="public"}getKey(){let e=th.parse(this.data,rN.PublicKeyInfo);return new rN.EcPublicKey(e.publicKey)}toJSON(){let e=this.getKey();return Object.assign({kty:"EC",crv:this.algorithm.namedCurve,key_ops:this.usages,ext:this.extractable},tj.toJSON(e))}fromJSON(e){if(!e.crv)throw new tK("Cannot get named curve from JWK. Property 'crv' is required");let t=tU.fromJSON(e,{targetSchema:rN.EcPublicKey}),r=new rN.PublicKeyInfo;return r.publicKeyAlgorithm.algorithm="1.2.840.10045.2.1",r.publicKeyAlgorithm.parameters=tf.serialize(new rN.ObjectIdentifier(nk(e.crv))),r.publicKey=tf.toASN(t).valueHex,this.data=Buffer.from(tf.serialize(r)),this}}class nP extends tz{constructor(){super(...arguments),this.name="SHA-1",this.usages=[]}async onDigest(e,t){return nv.digest(e,t)}}class nC extends tz{constructor(){super(...arguments),this.name="SHA-256",this.usages=[]}async onDigest(e,t){return nv.digest(e,t)}}class nx extends tz{constructor(){super(...arguments),this.name="SHA-384",this.usages=[]}async onDigest(e,t){return nv.digest(e,t)}}class nO extends tz{constructor(){super(...arguments),this.name="SHA-512",this.usages=[]}async onDigest(e,t){return nv.digest(e,t)}}class nR extends tz{constructor(){super(...arguments),this.name="SHA3-256",this.usages=[]}async onDigest(e,t){return nv.digest(e,t)}}class nT extends tz{constructor(){super(...arguments),this.name="SHA3-384",this.usages=[]}async onDigest(e,t){return nv.digest(e,t)}}class nB extends tz{constructor(){super(...arguments),this.name="SHA3-512",this.usages=[]}async onDigest(e,t){return nv.digest(e,t)}}class nj{static async generateKey(e,t,r){let n=new nE;n.algorithm=e,n.extractable=t,n.usages=r.filter(e=>-1!==this.privateKeyUsages.indexOf(e));let i=new nA;i.algorithm=e,i.extractable=!0,i.usages=r.filter(e=>-1!==this.publicKeyUsages.indexOf(e));let o=rJ().generateKeyPairSync("ec",{namedCurve:this.getOpenSSLNamedCurve(e.namedCurve),publicKeyEncoding:{format:"der",type:"spki"},privateKeyEncoding:{format:"der",type:"pkcs8"}});return n.data=o.privateKey,i.data=o.publicKey,{privateKey:n,publicKey:i}}static async sign(e,t,r){let n=nv.getAlgorithmName(e.hash),i=rJ().createSign(n);i.update(Buffer.from(r)),t.pem||(t.pem=`-----BEGIN PRIVATE KEY-----
${t.data.toString("base64")}
-----END PRIVATE KEY-----`);let o={key:t.pem},a=i.sign(o),s=th.parse(a,rN.EcDsaSignature);return ru.encodeSignature(s,rI.get(t.algorithm.namedCurve).size).buffer}static async verify(e,t,r,n){let i=nv.getAlgorithmName(e.hash),o=rJ().createVerify(i);o.update(Buffer.from(n)),t.pem||(t.pem=`-----BEGIN PUBLIC KEY-----
${t.data.toString("base64")}
-----END PUBLIC KEY-----`);let a={key:t.pem},l=new rN.EcDsaSignature,u=rI.get(t.algorithm.namedCurve),c=ru.decodeSignature(r,u.size);l.r=s.toArrayBuffer(c.r),l.s=s.toArrayBuffer(c.s);let d=Buffer.from(tf.serialize(l));return o.verify(a,d)}static async deriveBits(e,t,r){let n=this.getOpenSSLNamedCurve(t.algorithm.namedCurve),i=rJ().createECDH(n),o=th.parse(t.data,rN.PrivateKeyInfo),a=th.parse(o.privateKey,rN.EcPrivateKey);i.setPrivateKey(Buffer.from(a.privateKey));let s=th.parse(e.public.data,rN.PublicKeyInfo),l=i.computeSecret(Buffer.from(s.publicKey));return null===r?l:new Uint8Array(l).buffer.slice(0,r>>3)}static async exportKey(e,t){switch(e.toLowerCase()){case"jwk":return tj.toJSON(t);case"pkcs8":case"spki":return new Uint8Array(t.data).buffer;case"raw":return th.parse(t.data,rN.PublicKeyInfo).publicKey;default:throw new tK("format: Must be 'jwk', 'raw', pkcs8' or 'spki'")}}static async importKey(e,t,r,n,i){switch(e.toLowerCase()){case"jwk":if(t.d){let e=tU.fromJSON(t,{targetSchema:rN.EcPrivateKey});return this.importPrivateKey(e,r,n,i)}{let e=tU.fromJSON(t,{targetSchema:rN.EcPublicKey});return this.importPublicKey(e,r,n,i)}case"raw":{let e=new rN.EcPublicKey(t);return this.importPublicKey(e,r,n,i)}case"spki":{let e=th.parse(new Uint8Array(t),rN.PublicKeyInfo),o=new rN.EcPublicKey(e.publicKey);return this.assertKeyParameters(e.publicKeyAlgorithm.parameters,r.namedCurve),this.importPublicKey(o,r,n,i)}case"pkcs8":{let e=th.parse(new Uint8Array(t),rN.PrivateKeyInfo),o=th.parse(e.privateKey,rN.EcPrivateKey);return this.assertKeyParameters(e.privateKeyAlgorithm.parameters,r.namedCurve),this.importPrivateKey(o,r,n,i)}default:throw new tK("format: Must be 'jwk', 'raw', 'pkcs8' or 'spki'")}}static assertKeyParameters(e,t){if(!e)throw new tI("Key info doesn't have required parameters");let r="";try{r=th.parse(e,rN.ObjectIdentifier).value}catch(e){throw new tI("Cannot read key info parameters")}if(nk(t)!==r)throw new tI("Key info parameter doesn't match to named curve")}static async importPrivateKey(e,t,r,n){let i=new rN.PrivateKeyInfo;i.privateKeyAlgorithm.algorithm="1.2.840.10045.2.1",i.privateKeyAlgorithm.parameters=tf.serialize(new rN.ObjectIdentifier(nk(t.namedCurve))),i.privateKey=tf.serialize(e);let o=new nE;return o.data=Buffer.from(tf.serialize(i)),o.algorithm=Object.assign({},t),o.extractable=r,o.usages=n,o}static async importPublicKey(e,t,r,n){let i=new rN.PublicKeyInfo;i.publicKeyAlgorithm.algorithm="1.2.840.10045.2.1";let o=nk(t.namedCurve);i.publicKeyAlgorithm.parameters=tf.serialize(new rN.ObjectIdentifier(o)),i.publicKey=e.value;let a=new nA;return a.data=Buffer.from(tf.serialize(i)),a.algorithm=Object.assign({},t),a.extractable=r,a.usages=n,a}static getOpenSSLNamedCurve(e){switch(e.toUpperCase()){case"P-256":return"prime256v1";case"K-256":return"secp256k1";case"P-384":return"secp384r1";case"P-521":return"secp521r1";default:return e}}}nj.publicKeyUsages=["verify"],nj.privateKeyUsages=["sign","deriveKey","deriveBits"];class nU extends t1{constructor(){super(...arguments),this.namedCurves=rI.names,this.hashAlgorithms=["SHA-1","SHA-256","SHA-384","SHA-512","shake128","shake256","SHA3-256","SHA3-384","SHA3-512"]}async onGenerateKey(e,t,r){let n=await nj.generateKey({...e,name:this.name},t,r);return{privateKey:r4(n.privateKey),publicKey:r4(n.publicKey)}}async onSign(e,t,r){return nj.sign(e,r3(t),new Uint8Array(r))}async onVerify(e,t,r,n){return nj.verify(e,r3(t),new Uint8Array(r),new Uint8Array(n))}async onExportKey(e,t){return nj.exportKey(e,r3(t))}async onImportKey(e,t,r,n,i){return r4(await nj.importKey(e,t,{...r,name:this.name},n,i))}checkCryptoKey(e,t){super.checkCryptoKey(e,t);let r=r3(e);if(!(r instanceof nE||r instanceof nA))throw TypeError("key: Is not EC CryptoKey")}}class nN extends t3{constructor(){super(...arguments),this.namedCurves=rI.names}async onGenerateKey(e,t,r){let n=await nj.generateKey({...e,name:this.name},t,r);return{privateKey:r4(n.privateKey),publicKey:r4(n.publicKey)}}async onExportKey(e,t){return nj.exportKey(e,r3(t))}async onImportKey(e,t,r,n,i){return r4(await nj.importKey(e,t,{...r,name:this.name},n,i))}checkCryptoKey(e,t){super.checkCryptoKey(e,t);let r=r3(e);if(!(r instanceof nE||r instanceof nA))throw TypeError("key: Is not EC CryptoKey")}async onDeriveBits(e,t,r){return await nj.deriveBits({...e,public:r3(e.public)},r3(t),r)}}let nI={[rN.idEd448]:"Ed448",ed448:rN.idEd448,[rN.idX448]:"X448",x448:rN.idX448,[rN.idEd25519]:"Ed25519",ed25519:rN.idEd25519,[rN.idX25519]:"X25519",x25519:rN.idX25519};function nM(e){let t=nI[e.toLowerCase()];if(!t)throw new tK(`Cannot convert WebCrypto named curve '${e}' to OID`);return t}class nL extends r1{constructor(){super(...arguments),this.type="private"}getKey(){let e=th.parse(this.data,rN.PrivateKeyInfo);return th.parse(e.privateKey,rN.CurvePrivateKey)}toJSON(){let e=this.getKey();return Object.assign({kty:"OKP",crv:this.algorithm.namedCurve,key_ops:this.usages,ext:this.extractable},tj.toJSON(e))}fromJSON(e){if(!e.crv)throw new tK("Cannot get named curve from JWK. Property 'crv' is required");let t=new rN.PrivateKeyInfo;t.privateKeyAlgorithm.algorithm=nM(e.crv);let r=tU.fromJSON(e,{targetSchema:rN.CurvePrivateKey});return t.privateKey=tf.serialize(r),this.data=Buffer.from(tf.serialize(t)),this}}class nK extends r1{constructor(){super(...arguments),this.type="public"}getKey(){return th.parse(this.data,rN.PublicKeyInfo).publicKey}toJSON(){let e=this.getKey();return Object.assign({kty:"OKP",crv:this.algorithm.namedCurve,key_ops:this.usages,ext:this.extractable},{x:p.ToBase64Url(e)})}fromJSON(e){if(!e.crv)throw new tK("Cannot get named curve from JWK. Property 'crv' is required");if(!e.x)throw new tK("Cannot get property from JWK. Property 'x' is required");let t=new rN.PublicKeyInfo;return t.publicKeyAlgorithm.algorithm=nM(e.crv),t.publicKey=p.FromBase64Url(e.x),this.data=Buffer.from(tf.serialize(t)),this}}class nD{static async generateKey(e,t,r){let n=new nL;n.algorithm=e,n.extractable=t,n.usages=r.filter(e=>-1!==this.privateKeyUsages.indexOf(e));let i=new nK;i.algorithm=e,i.extractable=!0,i.usages=r.filter(e=>-1!==this.publicKeyUsages.indexOf(e));let o=e.namedCurve.toLowerCase(),a=rJ().generateKeyPairSync(o,{publicKeyEncoding:{format:"der",type:"spki"},privateKeyEncoding:{format:"der",type:"pkcs8"}});return n.data=a.privateKey,i.data=a.publicKey,{privateKey:n,publicKey:i}}static async sign(e,t,r){t.pem||(t.pem=`-----BEGIN PRIVATE KEY-----
${t.data.toString("base64")}
-----END PRIVATE KEY-----`);let n={key:t.pem},i=rJ().sign(null,Buffer.from(r),n);return s.toArrayBuffer(i)}static async verify(e,t,r,n){t.pem||(t.pem=`-----BEGIN PUBLIC KEY-----
${t.data.toString("base64")}
-----END PUBLIC KEY-----`);let i={key:t.pem};return rJ().verify(null,Buffer.from(n),i,Buffer.from(r))}static async deriveBits(e,t,r){let n=rJ().createPublicKey({key:e.public.data,format:"der",type:"spki"}),i=rJ().createPrivateKey({key:t.data,format:"der",type:"pkcs8"}),o=rJ().diffieHellman({publicKey:n,privateKey:i});return new Uint8Array(o).buffer.slice(0,r>>3)}static async exportKey(e,t){switch(e.toLowerCase()){case"jwk":return tj.toJSON(t);case"pkcs8":case"spki":return new Uint8Array(t.data).buffer;case"raw":return th.parse(t.data,rN.PublicKeyInfo).publicKey;default:throw new tK("format: Must be 'jwk', 'raw', pkcs8' or 'spki'")}}static async importKey(e,t,r,n,i){switch(e.toLowerCase()){case"jwk":if(t.d){let e=tU.fromJSON(t,{targetSchema:rN.CurvePrivateKey});return this.importPrivateKey(e,r,n,i)}if(!t.x)throw TypeError("keyData: Cannot get required 'x' filed");return this.importPublicKey(p.FromBase64Url(t.x),r,n,i);case"raw":return this.importPublicKey(t,r,n,i);case"spki":{let e=th.parse(new Uint8Array(t),rN.PublicKeyInfo);return this.importPublicKey(e.publicKey,r,n,i)}case"pkcs8":{let e=th.parse(new Uint8Array(t),rN.PrivateKeyInfo),o=th.parse(e.privateKey,rN.CurvePrivateKey);return this.importPrivateKey(o,r,n,i)}default:throw new tK("format: Must be 'jwk', 'raw', 'pkcs8' or 'spki'")}}static importPrivateKey(e,t,r,n){let i=new nL;return i.fromJSON({crv:t.namedCurve,d:p.ToBase64Url(e.d)}),i.algorithm=Object.assign({},t),i.extractable=r,i.usages=n,i}static async importPublicKey(e,t,r,n){let i=new nK;return i.fromJSON({crv:t.namedCurve,x:p.ToBase64Url(e)}),i.algorithm=Object.assign({},t),i.extractable=r,i.usages=n,i}}nD.publicKeyUsages=["verify"],nD.privateKeyUsages=["sign","deriveKey","deriveBits"];class nz extends t5{async onGenerateKey(e,t,r){let n=await nD.generateKey({name:this.name,namedCurve:e.namedCurve.replace(/^ed/i,"Ed")},t,r);return{privateKey:r4(n.privateKey),publicKey:r4(n.publicKey)}}async onSign(e,t,r){return nD.sign(e,r3(t),new Uint8Array(r))}async onVerify(e,t,r,n){return nD.verify(e,r3(t),new Uint8Array(r),new Uint8Array(n))}async onExportKey(e,t){return nD.exportKey(e,r3(t))}async onImportKey(e,t,r,n,i){return r4(await nD.importKey(e,t,{...r,name:this.name},n,i))}}class nH extends t4{async onGenerateKey(e,t,r){let n=await nD.generateKey({name:this.name,namedCurve:e.namedCurve.toUpperCase()},t,r);return{privateKey:r4(n.privateKey),publicKey:r4(n.publicKey)}}async onDeriveBits(e,t,r){return await nD.deriveBits({...e,public:r3(e.public)},r3(t),r)}async onExportKey(e,t){return nD.exportKey(e,r3(t))}async onImportKey(e,t,r,n,i){return r4(await nD.importKey(e,t,{...r,name:this.name},n,i))}}class nF extends rZ{}class n$ extends rL{async onDeriveBits(e,t,r){return new Promise((n,i)=>{let o=s.toArrayBuffer(e.salt),a=e.hash.name.replace("-","");rJ().pbkdf2(r3(t).data,Buffer.from(o),e.iterations,r>>3,a,(e,t)=>{e?i(e):n(new Uint8Array(t).buffer)})})}async onImportKey(e,t,r,n,i){if("raw"===e){let e=new nF;return e.data=Buffer.from(t),e.algorithm={name:this.name},e.extractable=!1,e.usages=i,r4(e)}throw new tK("format: Must be 'raw'")}checkCryptoKey(e,t){if(super.checkCryptoKey(e,t),!(r3(e) instanceof nF))throw TypeError("key: Is not PBKDF CryptoKey")}}class nq extends rZ{get alg(){let e=this.algorithm.hash.name.toUpperCase();return`HS${e.replace("SHA-","")}`}set alg(e){}}rX([tN({name:"k",converter:rQ})],nq.prototype,"data",void 0);class nV extends rM{async onGenerateKey(e,t,r){let n=(e.length||this.getDefaultLength(e.hash.name))>>3<<3,i=new nq;return i.algorithm={...e,length:n,name:this.name},i.extractable=t,i.usages=r,i.data=rJ().randomBytes(n>>3),r4(i)}async onSign(e,t,r){let n=nv.getAlgorithmName(t.algorithm.hash),i=rJ().createHmac(n,r3(t).data).update(Buffer.from(r)).digest();return new Uint8Array(i).buffer}async onVerify(e,t,r,n){let i=nv.getAlgorithmName(t.algorithm.hash);return 0===rJ().createHmac(i,r3(t).data).update(Buffer.from(n)).digest().compare(Buffer.from(r))}async onImportKey(e,t,r,n,i){let o;switch(e.toLowerCase()){case"jwk":o=tU.fromJSON(t,{targetSchema:nq});break;case"raw":(o=new nq).data=Buffer.from(t);break;default:throw new tK("format: Must be 'jwk' or 'raw'")}return o.algorithm={hash:{name:r.hash.name},name:this.name,length:o.data.length<<3},o.extractable=n,o.usages=i,r4(o)}async onExportKey(e,t){switch(e.toLowerCase()){case"jwk":return tj.toJSON(r3(t));case"raw":return new Uint8Array(r3(t).data).buffer;default:throw new tK("format: Must be 'jwk' or 'raw'")}}checkCryptoKey(e,t){if(super.checkCryptoKey(e,t),!(r3(e) instanceof nq))throw TypeError("key: Is not HMAC CryptoKey")}}class nW extends rZ{}class nG extends rK{async onImportKey(e,t,r,n,i){if("raw"!==e.toLowerCase())throw new tK("Operation not supported");let o=new nW;return o.data=Buffer.from(t),o.algorithm={name:this.name},o.extractable=n,o.usages=i,r4(o)}async onDeriveBits(e,t,r){let n=e.hash.name.replace("-",""),i=rJ().createHash(n).digest().length,o=r/8,a=s.toUint8Array(e.info),l=rJ().createHmac(n,s.toUint8Array(e.salt)).update(s.toUint8Array(r3(t).data)).digest(),u=[Buffer.alloc(0)],c=Math.ceil(o/i)+1;for(let e=1;e<c;++e)u.push(rJ().createHmac(n,l).update(Buffer.concat([u[e-1],a,Buffer.from([e])])).digest());return Buffer.concat(u).slice(0,o)}checkCryptoKey(e,t){if(super.checkCryptoKey(e,t),!(r3(e) instanceof nW))throw TypeError("key: Is not HKDF CryptoKey")}}class nJ{static digest(e,t){let r=rJ().createHash(e.name.toLowerCase(),{outputLength:e.length}).update(Buffer.from(t)).digest();return new Uint8Array(r).buffer}}class nY extends rz{async onDigest(e,t){return nJ.digest(e,t)}}class nX extends rH{async onDigest(e,t){return nJ.digest(e,t)}}class nQ extends rW{constructor(){var e;super(),this.providers.set(new r6),this.providers.set(new no),this.providers.set(new na),this.providers.set(new ni),this.providers.set(new ns),this.providers.set(new nl),this.providers.set(new nd),this.providers.set(new nh),this.providers.set(new nm),this.providers.set(new nb),this.providers.set(new nw),this.providers.set(new nS),this.providers.set(new nU),this.providers.set(new nN),this.providers.set(new nP),this.providers.set(new nC),this.providers.set(new nx),this.providers.set(new nO),this.providers.set(new n$),this.providers.set(new nV),this.providers.set(new nG);let t=null===(e=/^v(\d+)/.exec(rY.version))||void 0===e?void 0:e[1];t&&parseInt(t,10)>=12&&(this.providers.set(new nY),this.providers.set(new nX));let r=rG.getHashes();r.includes("sha3-256")&&this.providers.set(new nR),r.includes("sha3-384")&&this.providers.set(new nT),r.includes("sha3-512")&&this.providers.set(new nB),t&&parseInt(t,10)>=14&&(this.providers.set(new nz),this.providers.set(new nH))}}class nZ extends rF{constructor(){super(...arguments),this.subtle=new nQ}getRandomValues(e){if(!ArrayBuffer.isView(e))throw TypeError("Failed to execute 'getRandomValues' on 'Crypto': parameter 1 is not of type 'ArrayBufferView'");let t=Buffer.from(e.buffer,e.byteOffset,e.byteLength);return rJ().randomFillSync(t),e}}},83191:(e,t)=>{"use strict";function r(e){return -1!==e.indexOf("%")?decodeURIComponent(e):e}/*!
 * cookie
 * Copyright(c) 2012-2014 Roman Shtylman
 * Copyright(c) 2015 Douglas Christopher Wilson
 * MIT Licensed
 */t.Q=function(e,t){if("string"!=typeof e)throw TypeError("argument str must be a string");for(var n={},i=(t||{}).decode||r,o=0;o<e.length;){var a=e.indexOf("=",o);if(-1===a)break;var s=e.indexOf(";",o);if(-1===s)s=e.length;else if(s<a){o=e.lastIndexOf(";",a-1)+1;continue}var l=e.slice(o,a).trim();if(void 0===n[l]){var u=e.slice(a+1,s).trim();34===u.charCodeAt(0)&&(u=u.slice(1,-1)),n[l]=function(e,t){try{return t(e)}catch(t){return e}}(u,i)}o=s+1}return n},Object.prototype.toString},14438:e=>{"use strict";var t=function(e){var t;return!!e&&"object"==typeof e&&"[object RegExp]"!==(t=Object.prototype.toString.call(e))&&"[object Date]"!==t&&e.$$typeof!==r},r="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function n(e,t){return!1!==t.clone&&t.isMergeableObject(e)?s(Array.isArray(e)?[]:{},e,t):e}function i(e,t,r){return e.concat(t).map(function(e){return n(e,r)})}function o(e){return Object.keys(e).concat(Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter(function(t){return e.propertyIsEnumerable(t)}):[])}function a(e,t){try{return t in e}catch(e){return!1}}function s(e,r,l){(l=l||{}).arrayMerge=l.arrayMerge||i,l.isMergeableObject=l.isMergeableObject||t,l.cloneUnlessOtherwiseSpecified=n;var u,c,d=Array.isArray(r);return d!==Array.isArray(e)?n(r,l):d?l.arrayMerge(e,r,l):(c={},(u=l).isMergeableObject(e)&&o(e).forEach(function(t){c[t]=n(e[t],u)}),o(r).forEach(function(t){(!a(e,t)||Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))&&(a(e,t)&&u.isMergeableObject(r[t])?c[t]=(function(e,t){if(!t.customMerge)return s;var r=t.customMerge(e);return"function"==typeof r?r:s})(t,u)(e[t],r[t],u):c[t]=n(r[t],u))}),c)}s.all=function(e,t){if(!Array.isArray(e))throw Error("first argument should be an array");return e.reduce(function(e,r){return s(e,r,t)},{})},e.exports=s},10573:e=>{"use strict";let t=e=>"object"==typeof e&&null!==e,r=Symbol("skip"),n=e=>t(e)&&!(e instanceof RegExp)&&!(e instanceof Error)&&!(e instanceof Date),i=(e,t,o,a=new WeakMap)=>{if(o={deep:!1,target:{},...o},a.has(e))return a.get(e);a.set(e,o.target);let{target:s}=o;delete o.target;let l=e=>e.map(e=>n(e)?i(e,t,o,a):e);if(Array.isArray(e))return l(e);for(let[u,c]of Object.entries(e)){let d=t(u,c,e);if(d===r)continue;let[h,f,{shouldRecurse:p=!0}={}]=d;"__proto__"!==h&&(o.deep&&p&&n(f)&&(f=Array.isArray(f)?l(f):i(f,t,o,a)),s[h]=f)}return s};e.exports=(e,r,n)=>{if(!t(e))throw TypeError(`Expected an object, got \`${e}\` (${typeof e})`);return i(e,r,n)},e.exports.mapObjectSkip=r},7439:(e,t,r)=>{"use strict";r.r(t);var n=r(28290),i={};for(let e in n)"default"!==e&&(i[e]=()=>n[e]);r.d(t,i)},867:(e,t,r)=>{"use strict";r.r(t);var n=r(19738),i={};for(let e in n)"default"!==e&&(i[e]=()=>n[e]);r.d(t,i)},86843:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createProxy",{enumerable:!0,get:function(){return n}});let n=r(18195).createClientModuleProxy},77519:(e,t,r)=>{"use strict";let{createProxy:n}=r(86843);e.exports=n("/Users/<USER>/Downloads/Coding/IBC/Group Creator/node_modules/next/dist/client/components/app-router.js")},34778:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"bailoutToClientRendering",{enumerable:!0,get:function(){return o}});let n=r(38675),i=r(45869);function o(e){let t=i.staticGenerationAsyncStorage.getStore();if((null==t||!t.forceStatic)&&(null==t?void 0:t.isStaticGeneration))throw new n.BailoutToCSRError(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},84552:(e,t,r)=>{"use strict";function n(e){}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clientHookInServerComponentError",{enumerable:!0,get:function(){return n}}),r(46783),r(40002),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},58284:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"DraftMode",{enumerable:!0,get:function(){return i}});let n=r(72973);class i{get isEnabled(){return this._provider.isEnabled}enable(){if(!(0,n.staticGenerationBailout)("draftMode().enable()"))return this._provider.enable()}disable(){if(!(0,n.staticGenerationBailout)("draftMode().disable()"))return this._provider.disable()}constructor(e){this._provider=e}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},62563:(e,t,r)=>{"use strict";let{createProxy:n}=r(86843);e.exports=n("/Users/<USER>/Downloads/Coding/IBC/Group Creator/node_modules/next/dist/client/components/error-boundary.js")},28290:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{headers:function(){return c},cookies:function(){return d},draftMode:function(){return h}});let n=r(97366),i=r(3022),o=r(63608),a=r(54580),s=r(72934),l=r(72973),u=r(58284);function c(){if((0,l.staticGenerationBailout)("headers",{link:"https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering"}))return i.HeadersAdapter.seal(new Headers({}));let e=a.requestAsyncStorage.getStore();if(!e)throw Error("Invariant: headers() expects to have requestAsyncStorage, none available.");return e.headers}function d(){if((0,l.staticGenerationBailout)("cookies",{link:"https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering"}))return n.RequestCookiesAdapter.seal(new o.RequestCookies(new Headers({})));let e=a.requestAsyncStorage.getStore();if(!e)throw Error("Invariant: cookies() expects to have requestAsyncStorage, none available.");let t=s.actionAsyncStorage.getStore();return t&&(t.isAction||t.isAppRoute)?e.mutableCookies:e.cookies}function h(){let e=a.requestAsyncStorage.getStore();if(!e)throw Error("Invariant: draftMode() expects to have requestAsyncStorage, none available.");return new u.DraftMode(e.draftMode)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},48096:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DynamicServerError:function(){return n},isDynamicServerError:function(){return i}});let r="DYNAMIC_SERVER_USAGE";class n extends Error{constructor(e){super("Dynamic server usage: "+e),this.description=e,this.digest=r}}function i(e){return"object"==typeof e&&null!==e&&"digest"in e&&"string"==typeof e.digest&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},72517:(e,t,r)=>{"use strict";let{createProxy:n}=r(86843);e.exports=n("/Users/<USER>/Downloads/Coding/IBC/Group Creator/node_modules/next/dist/client/components/layout-router.js")},19738:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return p},useSearchParams:function(){return g},usePathname:function(){return y},ServerInsertedHTMLContext:function(){return u.ServerInsertedHTMLContext},useServerInsertedHTML:function(){return u.useServerInsertedHTML},useRouter:function(){return m},useParams:function(){return b},useSelectedLayoutSegments:function(){return v},useSelectedLayoutSegment:function(){return w},redirect:function(){return c.redirect},permanentRedirect:function(){return c.permanentRedirect},RedirectType:function(){return c.RedirectType},notFound:function(){return d.notFound}});let n=r(40002),i=r(78726),o=r(57210),a=r(84552),s=r(83092),l=r(65458),u=r(80545),c=r(8010),d=r(1988),h=Symbol("internal for urlsearchparams readonly");function f(){return Error("ReadonlyURLSearchParams cannot be modified")}class p{[Symbol.iterator](){return this[h][Symbol.iterator]()}append(){throw f()}delete(){throw f()}set(){throw f()}sort(){throw f()}constructor(e){this[h]=e,this.entries=e.entries.bind(e),this.forEach=e.forEach.bind(e),this.get=e.get.bind(e),this.getAll=e.getAll.bind(e),this.has=e.has.bind(e),this.keys=e.keys.bind(e),this.values=e.values.bind(e),this.toString=e.toString.bind(e),this.size=e.size}}function g(){(0,a.clientHookInServerComponentError)("useSearchParams");let e=(0,n.useContext)(o.SearchParamsContext),t=(0,n.useMemo)(()=>e?new p(e):null,[e]);{let{bailoutToClientRendering:e}=r(34778);e("useSearchParams()")}return t}function y(){return(0,a.clientHookInServerComponentError)("usePathname"),(0,n.useContext)(o.PathnameContext)}function m(){(0,a.clientHookInServerComponentError)("useRouter");let e=(0,n.useContext)(i.AppRouterContext);if(null===e)throw Error("invariant expected app router to be mounted");return e}function b(){(0,a.clientHookInServerComponentError)("useParams");let e=(0,n.useContext)(i.GlobalLayoutRouterContext),t=(0,n.useContext)(o.PathParamsContext);return(0,n.useMemo)(()=>(null==e?void 0:e.tree)?function e(t,r){for(let n of(void 0===r&&(r={}),Object.values(t[1]))){let t=n[0],i=Array.isArray(t),o=i?t[1]:t;!o||o.startsWith(l.PAGE_SEGMENT_KEY)||(i&&("c"===t[2]||"oc"===t[2])?r[t[0]]=t[1].split("/"):i&&(r[t[0]]=t[1]),r=e(n,r))}return r}(e.tree):t,[null==e?void 0:e.tree,t])}function v(e){void 0===e&&(e="children"),(0,a.clientHookInServerComponentError)("useSelectedLayoutSegments");let{tree:t}=(0,n.useContext)(i.LayoutRouterContext);return function e(t,r,n,i){let o;if(void 0===n&&(n=!0),void 0===i&&(i=[]),n)o=t[1][r];else{var a;let e=t[1];o=null!=(a=e.children)?a:Object.values(e)[0]}if(!o)return i;let u=o[0],c=(0,s.getSegmentValue)(u);return!c||c.startsWith(l.PAGE_SEGMENT_KEY)?i:(i.push(c),e(o,r,!1,i))}(t,e)}function w(e){void 0===e&&(e="children"),(0,a.clientHookInServerComponentError)("useSelectedLayoutSegment");let t=v(e);return 0===t.length?null:t[0]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},31150:(e,t,r)=>{"use strict";let{createProxy:n}=r(86843);e.exports=n("/Users/<USER>/Downloads/Coding/IBC/Group Creator/node_modules/next/dist/client/components/not-found-boundary.js")},69361:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}}),r(46783);let n=r(25036);r(40002);let i={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},desc:{display:"inline-block"},h1:{display:"inline-block",margin:"0 20px 0 0",padding:"0 23px 0 0",fontSize:24,fontWeight:500,verticalAlign:"top",lineHeight:"49px"},h2:{fontSize:14,fontWeight:400,lineHeight:"49px",margin:0}};function o(){return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("title",{children:"404: This page could not be found."}),(0,n.jsx)("div",{style:i.error,children:(0,n.jsxs)("div",{children:[(0,n.jsx)("style",{dangerouslySetInnerHTML:{__html:"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}),(0,n.jsx)("h1",{className:"next-error-h1",style:i.h1,children:"404"}),(0,n.jsx)("div",{style:i.desc,children:(0,n.jsx)("h2",{style:i.h2,children:"This page could not be found."})})]})})]})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1988:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{notFound:function(){return n},isNotFoundError:function(){return i}});let r="NEXT_NOT_FOUND";function n(){let e=Error(r);throw e.digest=r,e}function i(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},45858:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8010:(e,t,r)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectType:function(){return n},getRedirectError:function(){return l},redirect:function(){return u},permanentRedirect:function(){return c},isRedirectError:function(){return d},getURLFromRedirectError:function(){return h},getRedirectTypeFromError:function(){return f},getRedirectStatusCodeFromError:function(){return p}});let i=r(54580),o=r(72934),a=r(45858),s="NEXT_REDIRECT";function l(e,t,r){void 0===r&&(r=a.RedirectStatusCode.TemporaryRedirect);let n=Error(s);n.digest=s+";"+t+";"+e+";"+r+";";let o=i.requestAsyncStorage.getStore();return o&&(n.mutableCookies=o.mutableCookies),n}function u(e,t){void 0===t&&(t="replace");let r=o.actionAsyncStorage.getStore();throw l(e,t,(null==r?void 0:r.isAction)?a.RedirectStatusCode.SeeOther:a.RedirectStatusCode.TemporaryRedirect)}function c(e,t){void 0===t&&(t="replace");let r=o.actionAsyncStorage.getStore();throw l(e,t,(null==r?void 0:r.isAction)?a.RedirectStatusCode.SeeOther:a.RedirectStatusCode.PermanentRedirect)}function d(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r,n,i]=e.digest.split(";",4),o=Number(i);return t===s&&("replace"===r||"push"===r)&&"string"==typeof n&&!isNaN(o)&&o in a.RedirectStatusCode}function h(e){return d(e)?e.digest.split(";",3)[2]:null}function f(e){if(!d(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function p(e){if(!d(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(n||(n={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},80571:(e,t,r)=>{"use strict";let{createProxy:n}=r(86843);e.exports=n("/Users/<USER>/Downloads/Coding/IBC/Group Creator/node_modules/next/dist/client/components/render-from-template-context.js")},83092:(e,t)=>{"use strict";function r(e){return Array.isArray(e)?e[1]:e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getSegmentValue",{enumerable:!0,get:function(){return r}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},88650:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createSearchParamsBailoutProxy",{enumerable:!0,get:function(){return i}});let n=r(72973);function i(){return new Proxy({},{get(e,t){"string"==typeof t&&(0,n.staticGenerationBailout)("searchParams."+t)}})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},72973:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isStaticGenBailoutError:function(){return s},staticGenerationBailout:function(){return u}});let n=r(48096),i=r(45869),o="NEXT_STATIC_GEN_BAILOUT";class a extends Error{constructor(...e){super(...e),this.code=o}}function s(e){return"object"==typeof e&&null!==e&&"code"in e&&e.code===o}function l(e,t){let{dynamic:r,link:n}=t||{};return"Page"+(r?' with `dynamic = "'+r+'"`':"")+" couldn't be rendered statically because it used `"+e+"`."+(n?" See more info here: "+n:"")}let u=(e,t)=>{let{dynamic:r,link:o}=void 0===t?{}:t,s=i.staticGenerationAsyncStorage.getStore();if(!s)return!1;if(s.forceStatic)return!0;if(s.dynamicShouldError)throw new a(l(e,{link:o,dynamic:null!=r?r:"error"}));let u=l(e,{dynamic:r,link:"https://nextjs.org/docs/messages/dynamic-server-error"});if(null==s.postpone||s.postpone.call(s,e),s.revalidate=0,s.isStaticGeneration){let t=new n.DynamicServerError(u);throw s.dynamicUsageDescription=e,s.dynamicUsageStack=t.stack,t}return!1};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2336:(e,t,r)=>{"use strict";let{createProxy:n}=r(86843);e.exports=n("/Users/<USER>/Downloads/Coding/IBC/Group Creator/node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js")},68300:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{renderToReadableStream:function(){return n.renderToReadableStream},decodeReply:function(){return n.decodeReply},decodeAction:function(){return n.decodeAction},decodeFormState:function(){return n.decodeFormState},AppRouter:function(){return i.default},LayoutRouter:function(){return o.default},RenderFromTemplateContext:function(){return a.default},staticGenerationAsyncStorage:function(){return s.staticGenerationAsyncStorage},requestAsyncStorage:function(){return l.requestAsyncStorage},actionAsyncStorage:function(){return u.actionAsyncStorage},staticGenerationBailout:function(){return c.staticGenerationBailout},createSearchParamsBailoutProxy:function(){return h.createSearchParamsBailoutProxy},serverHooks:function(){return f},preloadStyle:function(){return y.preloadStyle},preloadFont:function(){return y.preloadFont},preconnect:function(){return y.preconnect},taintObjectReference:function(){return m.taintObjectReference},StaticGenerationSearchParamsBailoutProvider:function(){return d.default},NotFoundBoundary:function(){return p.NotFoundBoundary},patchFetch:function(){return w}});let n=r(18195),i=b(r(77519)),o=b(r(72517)),a=b(r(80571)),s=r(45869),l=r(54580),u=r(72934),c=r(72973),d=b(r(2336)),h=r(88650),f=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=v(t);if(r&&r.has(e))return r.get(e);var n={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var a=i?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(48096)),p=r(31150),g=r(99678);r(62563);let y=r(31806),m=r(22730);function b(e){return e&&e.__esModule?e:{default:e}}function v(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(v=function(e){return e?r:t})(e)}function w(){return(0,g.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:s.staticGenerationAsyncStorage})}},31806:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{preloadStyle:function(){return i},preloadFont:function(){return o},preconnect:function(){return a}});let n=function(e){return e&&e.__esModule?e:{default:e}}(r(25091));function i(e,t){let r={as:"style"};"string"==typeof t&&(r.crossOrigin=t),n.default.preload(e,r)}function o(e,t,r){let i={as:"font",type:t};"string"==typeof r&&(i.crossOrigin=r),n.default.preload(e,i)}function a(e,t){n.default.preconnect(e,"string"==typeof t?{crossOrigin:t}:void 0)}},22730:(e,t,r)=>{"use strict";function n(){throw Error("Taint can only be used with the taint flag.")}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{taintObjectReference:function(){return i},taintUniqueValue:function(){return o}}),r(40002);let i=n,o=n},50482:(e,t,r)=>{"use strict";e.exports=r(20399)},78726:(e,t,r)=>{"use strict";e.exports=r(50482).vendored.contexts.AppRouterContext},57210:(e,t,r)=>{"use strict";e.exports=r(50482).vendored.contexts.HooksClientContext},80545:(e,t,r)=>{"use strict";e.exports=r(50482).vendored.contexts.ServerInsertedHtml},25091:(e,t,r)=>{"use strict";e.exports=r(50482).vendored["react-rsc"].ReactDOM},25036:(e,t,r)=>{"use strict";e.exports=r(50482).vendored["react-rsc"].ReactJsxRuntime},18195:(e,t,r)=>{"use strict";e.exports=r(50482).vendored["react-rsc"].ReactServerDOMWebpackServerEdge},40002:(e,t,r)=>{"use strict";e.exports=r(50482).vendored["react-rsc"].React},8397:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{PageSignatureError:function(){return r},RemovedPageError:function(){return n},RemovedUAError:function(){return i}});class r extends Error{constructor({page:e}){super(`The middleware "${e}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class n extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class i extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}},80424:(e,t,r)=>{"use strict";Object.defineProperty(t,"Z",{enumerable:!0,get:function(){return n.NextRequest}});let n=r(71868)},3022:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyHeadersError:function(){return i},HeadersAdapter:function(){return o}});let n=r(6250);class i extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new i}}class o extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,i){if("symbol"==typeof r)return n.ReflectAdapter.get(t,r,i);let o=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===o);if(void 0!==a)return n.ReflectAdapter.get(t,a,i)},set(t,r,i,o){if("symbol"==typeof r)return n.ReflectAdapter.set(t,r,i,o);let a=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===a);return n.ReflectAdapter.set(t,s??r,i,o)},has(t,r){if("symbol"==typeof r)return n.ReflectAdapter.has(t,r);let i=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0!==o&&n.ReflectAdapter.has(t,o)},deleteProperty(t,r){if("symbol"==typeof r)return n.ReflectAdapter.deleteProperty(t,r);let i=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0===o||n.ReflectAdapter.deleteProperty(t,o)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return i.callable;default:return n.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new o(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},6250:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ReflectAdapter",{enumerable:!0,get:function(){return r}});class r{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},97366:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyRequestCookiesError:function(){return o},RequestCookiesAdapter:function(){return a},getModifiedCookieValues:function(){return l},appendMutableCookies:function(){return u},MutableRequestCookiesAdapter:function(){return c}});let n=r(63608),i=r(6250);class o extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#cookiessetname-value-options")}static callable(){throw new o}}class a{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return o.callable;default:return i.ReflectAdapter.get(e,t,r)}}})}}let s=Symbol.for("next.mutated.cookies");function l(e){let t=e[s];return t&&Array.isArray(t)&&0!==t.length?t:[]}function u(e,t){let r=l(t);if(0===r.length)return!1;let i=new n.ResponseCookies(e),o=i.getAll();for(let e of r)i.set(e);for(let e of o)i.set(e);return!0}class c{static wrap(e,t){let r=new n.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let o=[],a=new Set,l=()=>{var e;let i=null==fetch.__nextGetStaticStore?void 0:null==(e=fetch.__nextGetStaticStore.call(fetch))?void 0:e.getStore();if(i&&(i.pathWasRevalidated=!0),o=r.getAll().filter(e=>a.has(e.name)),t){let e=[];for(let t of o){let r=new n.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}};return new Proxy(r,{get(e,t,r){switch(t){case s:return o;case"delete":return function(...t){a.add("string"==typeof t[0]?t[0]:t[0].name);try{e.delete(...t)}finally{l()}};case"set":return function(...t){a.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t)}finally{l()}};default:return i.ReflectAdapter.get(e,t,r)}}})}}},71868:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERNALS:function(){return s},NextRequest:function(){return l}});let n=r(10514),i=r(68670),o=r(8397),a=r(63608),s=Symbol("internal request");class l extends Request{constructor(e,t={}){let r="string"!=typeof e&&"url"in e?e.url:String(e);(0,i.validateURL)(r),e instanceof Request?super(e,t):super(r,t);let o=new n.NextURL(r,{headers:(0,i.toNodeOutgoingHttpHeaders)(this.headers),nextConfig:t.nextConfig});this[s]={cookies:new a.RequestCookies(this.headers),geo:t.geo||{},ip:t.ip,nextUrl:o,url:o.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,geo:this.geo,ip:this.ip,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[s].cookies}get geo(){return this[s].geo}get ip(){return this[s].ip}get nextUrl(){return this[s].nextUrl}get page(){throw new o.RemovedPageError}get ua(){throw new o.RemovedUAError}get url(){return this[s].url}}},38675:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BailoutToCSRError:function(){return n},isBailoutToCSRError:function(){return i}});let r="BAILOUT_TO_CLIENT_SIDE_RENDERING";class n extends Error{constructor(e){super("Bail out to client-side rendering: "+e),this.reason=e,this.digest=r}}function i(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}},65458:(e,t)=>{"use strict";function r(e){return"("===e[0]&&e.endsWith(")")}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isGroupSegment:function(){return r},PAGE_SEGMENT_KEY:function(){return n},DEFAULT_SEGMENT_KEY:function(){return i}});let n="__PAGE__",i="__DEFAULT__"},11774:(e,t,r)=>{"use strict";r.r(t),r.d(t,{snakeCase:()=>u});var n=function(){return(n=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError;var i=function(){return(i=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};function o(e){return e.toLowerCase()}Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError;var a=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],s=/[^A-Z0-9]+/gi;function l(e,t,r){return t instanceof RegExp?e.replace(t,r):t.reduce(function(e,t){return e.replace(t,r)},e)}function u(e,t){var r;return void 0===t&&(t={}),void 0===(r=n({delimiter:"_"},t))&&(r={}),function(e,t){void 0===t&&(t={});for(var r=t.splitRegexp,n=t.stripRegexp,i=t.transform,u=t.delimiter,c=l(l(e,void 0===r?a:r,"$1\x00$2"),void 0===n?s:n,"\x00"),d=0,h=c.length;"\x00"===c.charAt(d);)d++;for(;"\x00"===c.charAt(h-1);)h--;return c.slice(d,h).split("\x00").map(void 0===i?o:i).join(void 0===u?" ":u)}(e,i({delimiter:"."},r))}},78591:(e,t,r)=>{"use strict";let n=r(10573),{snakeCase:i}=r(11774);e.exports=function(e,t){return n(e,function(e,r){return[t.exclude.some(function(t){return"string"==typeof t?t===e:t.test(e)})?e:i(e,t.parsingOptions),r]},t=Object.assign({deep:!0,exclude:[],parsingOptions:{}},t))}},10680:(e,t,r)=>{"use strict";r(87561),r(49411);let n=r(39306);r(88849),r(22286),r(65628),r(84492),r(72254),r(47261),r(41041),r(87503);let i=0,o={START_BOUNDARY:i++,HEADER_FIELD_START:i++,HEADER_FIELD:i++,HEADER_VALUE_START:i++,HEADER_VALUE:i++,HEADER_VALUE_ALMOST_DONE:i++,HEADERS_ALMOST_DONE:i++,PART_DATA_START:i++,PART_DATA:i++,END:i++},a={PART_BOUNDARY:1,LAST_BOUNDARY:2},s=e=>32|e,l=()=>{};class u{constructor(e){this.index=0,this.flags=0,this.onHeaderEnd=l,this.onHeaderField=l,this.onHeadersEnd=l,this.onHeaderValue=l,this.onPartBegin=l,this.onPartData=l,this.onPartEnd=l,this.boundaryChars={},e="\r\n--"+e;let t=new Uint8Array(e.length);for(let r=0;r<e.length;r++)t[r]=e.charCodeAt(r),this.boundaryChars[t[r]]=!0;this.boundary=t,this.lookbehind=new Uint8Array(this.boundary.length+8),this.state=o.START_BOUNDARY}write(e){let t,r,n=0,i=e.length,l=this.index,{lookbehind:u,boundary:c,boundaryChars:d,index:h,state:f,flags:p}=this,g=this.boundary.length,y=g-1,m=e.length,b=e=>{this[e+"Mark"]=n},v=e=>{delete this[e+"Mark"]},w=(e,t,r,n)=>{(void 0===t||t!==r)&&this[e](n&&n.subarray(t,r))},S=(t,r)=>{let i=t+"Mark";i in this&&(r?(w(t,this[i],n,e),delete this[i]):(w(t,this[i],e.length,e),this[i]=0))};for(n=0;n<i;n++)switch(t=e[n],f){case o.START_BOUNDARY:if(h===c.length-2){if(45===t)p|=a.LAST_BOUNDARY;else if(13!==t)return;h++;break}if(h-1==c.length-2){if(p&a.LAST_BOUNDARY&&45===t)f=o.END,p=0;else{if(p&a.LAST_BOUNDARY||10!==t)return;h=0,w("onPartBegin"),f=o.HEADER_FIELD_START}break}t!==c[h+2]&&(h=-2),t===c[h+2]&&h++;break;case o.HEADER_FIELD_START:f=o.HEADER_FIELD,b("onHeaderField"),h=0;case o.HEADER_FIELD:if(13===t){v("onHeaderField"),f=o.HEADERS_ALMOST_DONE;break}if(h++,45===t)break;if(58===t){if(1===h)return;S("onHeaderField",!0),f=o.HEADER_VALUE_START;break}if((r=s(t))<97||r>122)return;break;case o.HEADER_VALUE_START:if(32===t)break;b("onHeaderValue"),f=o.HEADER_VALUE;case o.HEADER_VALUE:13===t&&(S("onHeaderValue",!0),w("onHeaderEnd"),f=o.HEADER_VALUE_ALMOST_DONE);break;case o.HEADER_VALUE_ALMOST_DONE:if(10!==t)return;f=o.HEADER_FIELD_START;break;case o.HEADERS_ALMOST_DONE:if(10!==t)return;w("onHeadersEnd"),f=o.PART_DATA_START;break;case o.PART_DATA_START:f=o.PART_DATA,b("onPartData");case o.PART_DATA:if(l=h,0===h){for(n+=y;n<m&&!(e[n]in d);)n+=g;n-=y,t=e[n]}if(h<c.length)c[h]===t?(0===h&&S("onPartData",!0),h++):h=0;else if(h===c.length)h++,13===t?p|=a.PART_BOUNDARY:45===t?p|=a.LAST_BOUNDARY:h=0;else if(h-1===c.length){if(p&a.PART_BOUNDARY){if(h=0,10===t){p&=~a.PART_BOUNDARY,w("onPartEnd"),w("onPartBegin"),f=o.HEADER_FIELD_START;break}}else p&a.LAST_BOUNDARY&&45===t?(w("onPartEnd"),f=o.END,p=0):h=0}h>0?u[h-1]=t:l>0&&(w("onPartData",0,l,new Uint8Array(u.buffer,u.byteOffset,u.byteLength)),l=0,b("onPartData"),n--);break;case o.END:break;default:throw Error(`Unexpected state entered: ${f}`)}S("onHeaderField"),S("onHeaderValue"),S("onPartData"),this.index=h,this.state=f,this.flags=p}end(){if(this.state===o.HEADER_FIELD_START&&0===this.index||this.state===o.PART_DATA&&this.index===this.boundary.length)this.onPartEnd();else if(this.state!==o.END)throw Error("MultipartParser.end(): stream ended unexpectedly")}}async function c(e,t){let r,i,o,a,s,l;if(!/multipart/i.test(t))throw TypeError("Failed to fetch");let c=t.match(/boundary=(?:"([^"]+)"|([^;]+))/i);if(!c)throw TypeError("no or bad content-type header, no multipart boundary");let d=new u(c[1]||c[2]),h=[],f=new n.FormData,p=e=>{o+=b.decode(e,{stream:!0})},g=e=>{h.push(e)},y=()=>{let e=new n.File(h,l,{type:s});f.append(a,e)},m=()=>{f.append(a,o)},b=new TextDecoder("utf-8");for await(let t of(b.decode(),d.onPartBegin=function(){d.onPartData=p,d.onPartEnd=m,r="",i="",o="",a="",s="",l=null,h.length=0},d.onHeaderField=function(e){r+=b.decode(e,{stream:!0})},d.onHeaderValue=function(e){i+=b.decode(e,{stream:!0})},d.onHeaderEnd=function(){if(i+=b.decode(),"content-disposition"===(r=r.toLowerCase())){let e=i.match(/\bname=("([^"]*)"|([^()<>@,;:\\"/[\]?={}\s\t]+))/i);e&&(a=e[2]||e[3]||""),(l=function(e){let t=e.match(/\bfilename=("(.*?)"|([^()<>@,;:\\"/[\]?={}\s\t]+))($|;\s)/i);if(!t)return;let r=t[2]||t[3]||"",n=r.slice(r.lastIndexOf("\\")+1);return(n=n.replace(/%22/g,'"')).replace(/&#(\d{4});/g,(e,t)=>String.fromCharCode(t))}(i))&&(d.onPartData=g,d.onPartEnd=y)}else"content-type"===r&&(s=i);i="",r=""},e))d.write(t);return d.end(),f}t.toFormData=c},2218:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(39306),i=r(87561),o=r(49411);r(88849),r(22286),r(65628),r(84492),r(72254),r(47261),r(41041),r(87503);let{stat:a}=i.promises,s=(e,t,r="")=>new n._Blob([new u({path:t,size:e.size,lastModified:e.mtimeMs,start:0})],{type:r}),l=(e,t,r="")=>new n.File([new u({path:t,size:e.size,lastModified:e.mtimeMs,start:0})],o.basename(t),{type:r,lastModified:e.mtimeMs});class u{#e;#t;constructor(e){this.#e=e.path,this.#t=e.start,this.size=e.size,this.lastModified=e.lastModified,this.originalSize=void 0===e.originalSize?e.size:e.originalSize}slice(e,t){return new u({path:this.#e,lastModified:this.lastModified,originalSize:this.originalSize,size:t-e,start:this.#t+e})}async *stream(){let{mtimeMs:e,size:t}=await a(this.#e);if(e>this.lastModified||this.originalSize!==t)throw new n.nodeDomexception("The requested file could not be read, typically due to permission problems that have occurred after a reference to a file was acquired.","NotReadableError");yield*i.createReadStream(this.#e,{start:this.#t,end:this.#t+this.size-1})}get[Symbol.toStringTag](){return"Blob"}}let c=globalThis.fetch||n.fetch,d=globalThis.Blob||n._Blob,h=globalThis.File||n.File,f=globalThis.FormData||n.FormData,p=globalThis.Headers||n.Headers,g=globalThis.Request||n.Request,y=globalThis.Response||n.Response,m=globalThis.AbortController||n.AbortController;t.AbortError=n.AbortError,t.FetchError=n.FetchError,t.isRedirect=n.isRedirect,t.AbortController=m,t.Blob=d,t.File=h,t.FormData=f,t.Headers=p,t.Request=g,t.Response=y,t.blobFrom=(e,t)=>a(e).then(r=>s(r,e,t)),t.blobFromSync=(e,t)=>s(i.statSync(e),e,t),t.default=c,t.fetch=c,t.fileFrom=(e,t)=>a(e).then(r=>l(r,e,t)),t.fileFromSync=(e,t)=>l(i.statSync(e),e,t)},39306:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(57519),i=r(88849),o=r(22286),a=r(65628),s=r(84492),l=r(72254),u=r(47261),c=r(41041),d=r(87503);r(87561),r(49411);var h,f="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},p={exports:{}};if(!globalThis.ReadableStream)try{let e=r(97742),{emitWarning:t}=e;try{e.emitWarning=()=>{},Object.assign(globalThis,r(72477)),e.emitWarning=t}catch(r){throw e.emitWarning=t,r}}catch(e){Object.assign(globalThis,(h||(h=1,function(e){let t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol:e=>`Symbol(${e})`;function r(){}let n="undefined"!=typeof self?self:void 0!==f?f:void 0;function i(e){return"object"==typeof e&&null!==e||"function"==typeof e}let o=Promise,a=Promise.prototype.then,s=Promise.resolve.bind(o),l=Promise.reject.bind(o);function u(e){return new o(e)}function c(e,t,r){return a.call(e,t,r)}function d(e,t,n){c(c(e,t,n),void 0,r)}function h(e,t){d(e,void 0,t)}function p(e){c(e,void 0,r)}let g=(()=>{let e=n&&n.queueMicrotask;if("function"==typeof e)return e;let t=s(void 0);return e=>c(t,e)})();function y(e,t,r){if("function"!=typeof e)throw TypeError("Argument is not a function");return Function.prototype.apply.call(e,t,r)}function m(e,t,r){try{var n;return n=y(e,t,r),s(n)}catch(e){return l(e)}}class b{constructor(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}get length(){return this._size}push(e){let t=this._back,r=t;16383===t._elements.length&&(r={_elements:[],_next:void 0}),t._elements.push(e),r!==t&&(this._back=r,t._next=r),++this._size}shift(){let e=this._front,t=e,r=this._cursor,n=r+1,i=e._elements,o=i[r];return 16384===n&&(t=e._next,n=0),--this._size,this._cursor=n,e!==t&&(this._front=t),i[r]=void 0,o}forEach(e){let t=this._cursor,r=this._front,n=r._elements;for(;(t!==n.length||void 0!==r._next)&&(t!==n.length||(n=(r=r._next)._elements,t=0,0!==n.length));)e(n[t]),++t}peek(){let e=this._front,t=this._cursor;return e._elements[t]}}function v(e,t){var r;e._ownerReadableStream=t,t._reader=e,"readable"===t._state?k(e):"closed"===t._state?(k(e),A(e)):(r=t._storedError,k(e),E(e,r))}function w(e,t){return tF(e._ownerReadableStream,t)}function S(e){var t,r;"readable"===e._ownerReadableStream._state?E(e,TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):(t=e,r=TypeError("Reader was released and can no longer be used to monitor the stream's closedness"),k(t),E(t,r)),e._ownerReadableStream._reader=void 0,e._ownerReadableStream=void 0}function _(e){return TypeError("Cannot "+e+" a stream using a released reader")}function k(e){e._closedPromise=u((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r})}function E(e,t){void 0!==e._closedPromise_reject&&(p(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}function A(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}let P=t("[[AbortSteps]]"),C=t("[[ErrorSteps]]"),x=t("[[CancelSteps]]"),O=t("[[PullSteps]]"),R=Number.isFinite||function(e){return"number"==typeof e&&isFinite(e)},T=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function B(e,t){if(void 0!==e&&!("object"==typeof e||"function"==typeof e))throw TypeError(`${t} is not an object.`)}function j(e,t){if("function"!=typeof e)throw TypeError(`${t} is not a function.`)}function U(e,t){if(!("object"==typeof e&&null!==e||"function"==typeof e))throw TypeError(`${t} is not an object.`)}function N(e,t,r){if(void 0===e)throw TypeError(`Parameter ${t} is required in '${r}'.`)}function I(e,t,r){if(void 0===e)throw TypeError(`${t} is required in '${r}'.`)}function M(e){return Number(e)}function L(e,t){var r,n;let i=Number.MAX_SAFE_INTEGER,o=Number(e);if(!R(o=0===(r=o)?0:r))throw TypeError(`${t} is not a finite number`);if((o=0===(n=T(o))?0:n)<0||o>i)throw TypeError(`${t} is outside the accepted range of 0 to ${i}, inclusive`);return R(o)&&0!==o?o:0}function K(e,t){if(!tz(e))throw TypeError(`${t} is not a ReadableStream.`)}function D(e){return new q(e)}function z(e,t){e._reader._readRequests.push(t)}function H(e,t,r){let n=e._reader._readRequests.shift();r?n._closeSteps():n._chunkSteps(t)}function F(e){return e._reader._readRequests.length}function $(e){let t=e._reader;return!!(void 0!==t&&V(t))}class q{constructor(e){if(N(e,1,"ReadableStreamDefaultReader"),K(e,"First parameter"),tH(e))throw TypeError("This stream has already been locked for exclusive reading by another reader");v(this,e),this._readRequests=new b}get closed(){return V(this)?this._closedPromise:l(G("closed"))}cancel(e){return V(this)?void 0===this._ownerReadableStream?l(_("cancel")):w(this,e):l(G("cancel"))}read(){let e,t;if(!V(this))return l(G("read"));if(void 0===this._ownerReadableStream)return l(_("read from"));let r=u((r,n)=>{e=r,t=n});return W(this,{_chunkSteps:t=>e({value:t,done:!1}),_closeSteps:()=>e({value:void 0,done:!0}),_errorSteps:e=>t(e)}),r}releaseLock(){if(!V(this))throw G("releaseLock");if(void 0!==this._ownerReadableStream){if(this._readRequests.length>0)throw TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");S(this)}}}function V(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_readRequests"))&&e instanceof q}function W(e,t){let r=e._ownerReadableStream;r._disturbed=!0,"closed"===r._state?t._closeSteps():"errored"===r._state?t._errorSteps(r._storedError):r._readableStreamController[O](t)}function G(e){return TypeError(`ReadableStreamDefaultReader.prototype.${e} can only be used on a ReadableStreamDefaultReader`)}Object.defineProperties(q.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),"symbol"==typeof t.toStringTag&&Object.defineProperty(q.prototype,t.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0});let J=Object.getPrototypeOf(Object.getPrototypeOf(async function*(){}).prototype);class Y{constructor(e,t){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=e,this._preventCancel=t}next(){let e=()=>this._nextSteps();return this._ongoingPromise=this._ongoingPromise?c(this._ongoingPromise,e,e):e(),this._ongoingPromise}return(e){let t=()=>this._returnSteps(e);return this._ongoingPromise?c(this._ongoingPromise,t,t):t()}_nextSteps(){let e,t;if(this._isFinished)return Promise.resolve({value:void 0,done:!0});let r=this._reader;if(void 0===r._ownerReadableStream)return l(_("iterate"));let n=u((r,n)=>{e=r,t=n});return W(r,{_chunkSteps:t=>{this._ongoingPromise=void 0,g(()=>e({value:t,done:!1}))},_closeSteps:()=>{this._ongoingPromise=void 0,this._isFinished=!0,S(r),e({value:void 0,done:!0})},_errorSteps:e=>{this._ongoingPromise=void 0,this._isFinished=!0,S(r),t(e)}}),n}_returnSteps(e){if(this._isFinished)return Promise.resolve({value:e,done:!0});this._isFinished=!0;let t=this._reader;if(void 0===t._ownerReadableStream)return l(_("finish iterating"));if(!this._preventCancel){let r=w(t,e);return S(t),c(r,()=>({value:e,done:!0}),void 0)}return S(t),s({value:e,done:!0})}}let X={next(){return Q(this)?this._asyncIteratorImpl.next():l(Z("next"))},return(e){return Q(this)?this._asyncIteratorImpl.return(e):l(Z("return"))}};function Q(e){if(!i(e)||!Object.prototype.hasOwnProperty.call(e,"_asyncIteratorImpl"))return!1;try{return e._asyncIteratorImpl instanceof Y}catch(e){return!1}}function Z(e){return TypeError(`ReadableStreamAsyncIterator.${e} can only be used on a ReadableSteamAsyncIterator`)}void 0!==J&&Object.setPrototypeOf(X,J);let ee=Number.isNaN||function(e){return e!=e};function et(e){return e.slice()}function er(e,t,r,n,i){new Uint8Array(e).set(new Uint8Array(r,n,i),t)}function en(e,t,r){if(e.slice)return e.slice(t,r);let n=r-t,i=new ArrayBuffer(n);return er(i,0,e,t,n),i}function ei(e){let t=en(e.buffer,e.byteOffset,e.byteOffset+e.byteLength);return new Uint8Array(t)}function eo(e){let t=e._queue.shift();return e._queueTotalSize-=t.size,e._queueTotalSize<0&&(e._queueTotalSize=0),t.value}function ea(e,t,r){if(!(!("number"!=typeof r||ee(r))&&!(r<0))||r===1/0)throw RangeError("Size must be a finite, non-NaN, non-negative number.");e._queue.push({value:t,size:r}),e._queueTotalSize+=r}function es(e){e._queue=new b,e._queueTotalSize=0}class el{constructor(){throw TypeError("Illegal constructor")}get view(){if(!ed(this))throw ej("view");return this._view}respond(e){if(!ed(this))throw ej("respond");if(N(e,1,"respond"),e=L(e,"First parameter"),void 0===this._associatedReadableByteStreamController)throw TypeError("This BYOB request has been invalidated");this._view.buffer,eR(this._associatedReadableByteStreamController,e)}respondWithNewView(e){if(!ed(this))throw ej("respondWithNewView");if(N(e,1,"respondWithNewView"),!ArrayBuffer.isView(e))throw TypeError("You can only respond with array buffer views");if(void 0===this._associatedReadableByteStreamController)throw TypeError("This BYOB request has been invalidated");e.buffer,eT(this._associatedReadableByteStreamController,e)}}Object.defineProperties(el.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),"symbol"==typeof t.toStringTag&&Object.defineProperty(el.prototype,t.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});class eu{constructor(){throw TypeError("Illegal constructor")}get byobRequest(){if(!ec(this))throw eU("byobRequest");return ex(this)}get desiredSize(){if(!ec(this))throw eU("desiredSize");return eO(this)}close(){if(!ec(this))throw eU("close");if(this._closeRequested)throw TypeError("The stream has already been closed; do not close it again!");let e=this._controlledReadableByteStream._state;if("readable"!==e)throw TypeError(`The stream (in ${e} state) is not in the readable state and cannot be closed`);eA(this)}enqueue(e){if(!ec(this))throw eU("enqueue");if(N(e,1,"enqueue"),!ArrayBuffer.isView(e))throw TypeError("chunk must be an array buffer view");if(0===e.byteLength)throw TypeError("chunk must have non-zero byteLength");if(0===e.buffer.byteLength)throw TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw TypeError("stream is closed or draining");let t=this._controlledReadableByteStream._state;if("readable"!==t)throw TypeError(`The stream (in ${t} state) is not in the readable state and cannot be enqueued to`);eP(this,e)}error(e){if(!ec(this))throw eU("error");eC(this,e)}[x](e){ef(this),es(this);let t=this._cancelAlgorithm(e);return eE(this),t}[O](e){let t=this._controlledReadableByteStream;if(this._queueTotalSize>0){let t=this._queue.shift();this._queueTotalSize-=t.byteLength,ev(this);let r=new Uint8Array(t.buffer,t.byteOffset,t.byteLength);e._chunkSteps(r);return}let r=this._autoAllocateChunkSize;if(void 0!==r){let t;try{t=new ArrayBuffer(r)}catch(t){e._errorSteps(t);return}let n={buffer:t,bufferByteLength:r,byteOffset:0,byteLength:r,bytesFilled:0,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(n)}z(t,e),eh(this)}}function ec(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_controlledReadableByteStream"))&&e instanceof eu}function ed(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController"))&&e instanceof el}function eh(e){if(function(e){let t=e._controlledReadableByteStream;return"readable"===t._state&&!e._closeRequested&&!!e._started&&!!($(t)&&F(t)>0||eM(t)&&eI(t)>0||eO(e)>0)}(e)){if(e._pulling){e._pullAgain=!0;return}e._pulling=!0,d(e._pullAlgorithm(),()=>{e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,eh(e))},t=>{eC(e,t)})}}function ef(e){ew(e),e._pendingPullIntos=new b}function ep(e,t){let r=!1;"closed"===e._state&&(r=!0);let n=eg(t);"default"===t.readerType?H(e,n,r):function(e,t,r){let n=e._reader._readIntoRequests.shift();r?n._closeSteps(t):n._chunkSteps(t)}(e,n,r)}function eg(e){let t=e.bytesFilled,r=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,t/r)}function ey(e,t,r,n){e._queue.push({buffer:t,byteOffset:r,byteLength:n}),e._queueTotalSize+=n}function em(e,t){let r=t.elementSize,n=t.bytesFilled-t.bytesFilled%r,i=Math.min(e._queueTotalSize,t.byteLength-t.bytesFilled),o=t.bytesFilled+i,a=o-o%r,s=i,l=!1;a>n&&(s=a-t.bytesFilled,l=!0);let u=e._queue;for(;s>0;){let r=u.peek(),n=Math.min(s,r.byteLength),i=t.byteOffset+t.bytesFilled;er(t.buffer,i,r.buffer,r.byteOffset,n),r.byteLength===n?u.shift():(r.byteOffset+=n,r.byteLength-=n),e._queueTotalSize-=n,eb(e,n,t),s-=n}return l}function eb(e,t,r){r.bytesFilled+=t}function ev(e){0===e._queueTotalSize&&e._closeRequested?(eE(e),t$(e._controlledReadableByteStream)):eh(e)}function ew(e){null!==e._byobRequest&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null)}function eS(e){for(;e._pendingPullIntos.length>0;){if(0===e._queueTotalSize)return;let t=e._pendingPullIntos.peek();em(e,t)&&(ek(e),ep(e._controlledReadableByteStream,t))}}function e_(e,t){let r=e._pendingPullIntos.peek();ew(e),"closed"===e._controlledReadableByteStream._state?function(e,t){let r=e._controlledReadableByteStream;if(eM(r))for(;eI(r)>0;)ep(r,ek(e))}(e):function(e,t,r){if(eb(e,t,r),r.bytesFilled<r.elementSize)return;ek(e);let n=r.bytesFilled%r.elementSize;if(n>0){let t=r.byteOffset+r.bytesFilled,i=en(r.buffer,t-n,t);ey(e,i,0,i.byteLength)}r.bytesFilled-=n,ep(e._controlledReadableByteStream,r),eS(e)}(e,t,r),eh(e)}function ek(e){return e._pendingPullIntos.shift()}function eE(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}function eA(e){let t=e._controlledReadableByteStream;if(!e._closeRequested&&"readable"===t._state){if(e._queueTotalSize>0){e._closeRequested=!0;return}if(e._pendingPullIntos.length>0&&e._pendingPullIntos.peek().bytesFilled>0){let t=TypeError("Insufficient bytes to fill elements in the given buffer");throw eC(e,t),t}eE(e),t$(t)}}function eP(e,t){let r=e._controlledReadableByteStream;if(e._closeRequested||"readable"!==r._state)return;let n=t.buffer,i=t.byteOffset,o=t.byteLength;if(e._pendingPullIntos.length>0){let t=e._pendingPullIntos.peek();t.buffer,t.buffer=t.buffer}ew(e),$(r)?0===F(r)?ey(e,n,i,o):(e._pendingPullIntos.length>0&&ek(e),H(r,new Uint8Array(n,i,o),!1)):eM(r)?(ey(e,n,i,o),eS(e)):ey(e,n,i,o),eh(e)}function eC(e,t){let r=e._controlledReadableByteStream;"readable"===r._state&&(ef(e),es(e),eE(e),tq(r,t))}function ex(e){if(null===e._byobRequest&&e._pendingPullIntos.length>0){let t=e._pendingPullIntos.peek(),r=new Uint8Array(t.buffer,t.byteOffset+t.bytesFilled,t.byteLength-t.bytesFilled),n=Object.create(el.prototype);n._associatedReadableByteStreamController=e,n._view=r,e._byobRequest=n}return e._byobRequest}function eO(e){let t=e._controlledReadableByteStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}function eR(e,t){let r=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==t)throw TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(0===t)throw TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(r.bytesFilled+t>r.byteLength)throw RangeError("bytesWritten out of range")}r.buffer=r.buffer,e_(e,t)}function eT(e,t){let r=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==t.byteLength)throw TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(0===t.byteLength)throw TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(r.byteOffset+r.bytesFilled!==t.byteOffset)throw RangeError("The region specified by view does not match byobRequest");if(r.bufferByteLength!==t.buffer.byteLength)throw RangeError("The buffer of view has different capacity than byobRequest");if(r.bytesFilled+t.byteLength>r.byteLength)throw RangeError("The region specified by view is larger than byobRequest");let n=t.byteLength;r.buffer=t.buffer,e_(e,n)}function eB(e,t,r,n,i,o,a){t._controlledReadableByteStream=e,t._pullAgain=!1,t._pulling=!1,t._byobRequest=null,t._queue=t._queueTotalSize=void 0,es(t),t._closeRequested=!1,t._started=!1,t._strategyHWM=o,t._pullAlgorithm=n,t._cancelAlgorithm=i,t._autoAllocateChunkSize=a,t._pendingPullIntos=new b,e._readableStreamController=t,d(s(r()),()=>{t._started=!0,eh(t)},e=>{eC(t,e)})}function ej(e){return TypeError(`ReadableStreamBYOBRequest.prototype.${e} can only be used on a ReadableStreamBYOBRequest`)}function eU(e){return TypeError(`ReadableByteStreamController.prototype.${e} can only be used on a ReadableByteStreamController`)}function eN(e,t){e._reader._readIntoRequests.push(t)}function eI(e){return e._reader._readIntoRequests.length}function eM(e){let t=e._reader;return!!(void 0!==t&&eK(t))}Object.defineProperties(eu.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),"symbol"==typeof t.toStringTag&&Object.defineProperty(eu.prototype,t.toStringTag,{value:"ReadableByteStreamController",configurable:!0});class eL{constructor(e){if(N(e,1,"ReadableStreamBYOBReader"),K(e,"First parameter"),tH(e))throw TypeError("This stream has already been locked for exclusive reading by another reader");if(!ec(e._readableStreamController))throw TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");v(this,e),this._readIntoRequests=new b}get closed(){return eK(this)?this._closedPromise:l(ez("closed"))}cancel(e){return eK(this)?void 0===this._ownerReadableStream?l(_("cancel")):w(this,e):l(ez("cancel"))}read(e){let t,r;if(!eK(this))return l(ez("read"));if(!ArrayBuffer.isView(e))return l(TypeError("view must be an array buffer view"));if(0===e.byteLength)return l(TypeError("view must have non-zero byteLength"));if(0===e.buffer.byteLength)return l(TypeError("view's buffer must have non-zero byteLength"));if(e.buffer,void 0===this._ownerReadableStream)return l(_("read from"));let n=u((e,n)=>{t=e,r=n});return eD(this,e,{_chunkSteps:e=>t({value:e,done:!1}),_closeSteps:e=>t({value:e,done:!0}),_errorSteps:e=>r(e)}),n}releaseLock(){if(!eK(this))throw ez("releaseLock");if(void 0!==this._ownerReadableStream){if(this._readIntoRequests.length>0)throw TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");S(this)}}}function eK(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_readIntoRequests"))&&e instanceof eL}function eD(e,t,r){let n=e._ownerReadableStream;n._disturbed=!0,"errored"===n._state?r._errorSteps(n._storedError):function(e,t,r){let n=e._controlledReadableByteStream,i=1;t.constructor!==DataView&&(i=t.constructor.BYTES_PER_ELEMENT);let o=t.constructor,a=t.buffer,s={buffer:a,bufferByteLength:a.byteLength,byteOffset:t.byteOffset,byteLength:t.byteLength,bytesFilled:0,elementSize:i,viewConstructor:o,readerType:"byob"};if(e._pendingPullIntos.length>0){e._pendingPullIntos.push(s),eN(n,r);return}if("closed"===n._state){let e=new o(s.buffer,s.byteOffset,0);r._closeSteps(e);return}if(e._queueTotalSize>0){if(em(e,s)){let t=eg(s);ev(e),r._chunkSteps(t);return}if(e._closeRequested){let t=TypeError("Insufficient bytes to fill elements in the given buffer");eC(e,t),r._errorSteps(t);return}}e._pendingPullIntos.push(s),eN(n,r),eh(e)}(n._readableStreamController,t,r)}function ez(e){return TypeError(`ReadableStreamBYOBReader.prototype.${e} can only be used on a ReadableStreamBYOBReader`)}function eH(e,t){let{highWaterMark:r}=e;if(void 0===r)return t;if(ee(r)||r<0)throw RangeError("Invalid highWaterMark");return r}function eF(e){let{size:t}=e;return t||(()=>1)}function e$(e,t){B(e,t);let r=null==e?void 0:e.highWaterMark,n=null==e?void 0:e.size;return{highWaterMark:void 0===r?void 0:M(r),size:void 0===n?void 0:(j(n,`${t} has member 'size' that`),e=>M(n(e)))}}function eq(e,t){if(!eJ(e))throw TypeError(`${t} is not a WritableStream.`)}Object.defineProperties(eL.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),"symbol"==typeof t.toStringTag&&Object.defineProperty(eL.prototype,t.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});let eV="function"==typeof AbortController;class eW{constructor(e={},t={}){void 0===e?e=null:U(e,"First parameter");let r=e$(t,"Second parameter"),n=function(e,t){B(e,t);let r=null==e?void 0:e.abort,n=null==e?void 0:e.close,i=null==e?void 0:e.start,o=null==e?void 0:e.type,a=null==e?void 0:e.write;return{abort:void 0===r?void 0:(j(r,`${t} has member 'abort' that`),t=>m(r,e,[t])),close:void 0===n?void 0:(j(n,`${t} has member 'close' that`),()=>m(n,e,[])),start:void 0===i?void 0:(j(i,`${t} has member 'start' that`),t=>y(i,e,[t])),write:void 0===a?void 0:(j(a,`${t} has member 'write' that`),(t,r)=>m(a,e,[t,r])),type:o}}(e,"First parameter");if(eG(this),void 0!==n.type)throw RangeError("Invalid type is specified");let i=eF(r);(function(e,t,r,n){let i=Object.create(tr.prototype),o=()=>void 0,a=()=>s(void 0),l=()=>s(void 0),u=()=>s(void 0);void 0!==t.start&&(o=()=>t.start(i)),void 0!==t.write&&(a=e=>t.write(e,i)),void 0!==t.close&&(l=()=>t.close()),void 0!==t.abort&&(u=e=>t.abort(e)),ti(e,i,o,a,l,u,r,n)})(this,n,eH(r,1),i)}get locked(){if(!eJ(this))throw tc("locked");return eY(this)}abort(e){return eJ(this)?eY(this)?l(TypeError("Cannot abort a stream that already has a writer")):eX(this,e):l(tc("abort"))}close(){return eJ(this)?eY(this)?l(TypeError("Cannot close a stream that already has a writer")):e2(this)?l(TypeError("Cannot close an already-closing stream")):eQ(this):l(tc("close"))}getWriter(){if(!eJ(this))throw tc("getWriter");return new e4(this)}}function eG(e){e._state="writable",e._storedError=void 0,e._writer=void 0,e._writableStreamController=void 0,e._writeRequests=new b,e._inFlightWriteRequest=void 0,e._closeRequest=void 0,e._inFlightCloseRequest=void 0,e._pendingAbortRequest=void 0,e._backpressure=!1}function eJ(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_writableStreamController"))&&e instanceof eW}function eY(e){return void 0!==e._writer}function eX(e,t){var r;if("closed"===e._state||"errored"===e._state)return s(void 0);e._writableStreamController._abortReason=t,null===(r=e._writableStreamController._abortController)||void 0===r||r.abort();let n=e._state;if("closed"===n||"errored"===n)return s(void 0);if(void 0!==e._pendingAbortRequest)return e._pendingAbortRequest._promise;let i=!1;"erroring"===n&&(i=!0,t=void 0);let o=u((r,n)=>{e._pendingAbortRequest={_promise:void 0,_resolve:r,_reject:n,_reason:t,_wasAlreadyErroring:i}});return e._pendingAbortRequest._promise=o,i||e0(e,t),o}function eQ(e){var t;let r=e._state;if("closed"===r||"errored"===r)return l(TypeError(`The stream (in ${r} state) is not in the writable state and cannot be closed`));let n=u((t,r)=>{e._closeRequest={_resolve:t,_reject:r}}),i=e._writer;return void 0!==i&&e._backpressure&&"writable"===r&&tw(i),ea(t=e._writableStreamController,tt,0),ts(t),n}function eZ(e,t){if("writable"===e._state){e0(e,t);return}e1(e)}function e0(e,t){let r=e._writableStreamController;e._state="erroring",e._storedError=t;let n=e._writer;void 0!==n&&e7(n,t),!(void 0!==e._inFlightWriteRequest||void 0!==e._inFlightCloseRequest)&&r._started&&e1(e)}function e1(e){e._state="errored",e._writableStreamController[C]();let t=e._storedError;if(e._writeRequests.forEach(e=>{e._reject(t)}),e._writeRequests=new b,void 0===e._pendingAbortRequest){e8(e);return}let r=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,r._wasAlreadyErroring){r._reject(t),e8(e);return}d(e._writableStreamController[P](r._reason),()=>{r._resolve(),e8(e)},t=>{r._reject(t),e8(e)})}function e2(e){return void 0!==e._closeRequest||void 0!==e._inFlightCloseRequest}function e8(e){void 0!==e._closeRequest&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);let t=e._writer;void 0!==t&&tg(t,e._storedError)}function e3(e,t){let r=e._writer;void 0!==r&&t!==e._backpressure&&(t?tm(r):tw(r)),e._backpressure=t}Object.defineProperties(eW.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),"symbol"==typeof t.toStringTag&&Object.defineProperty(eW.prototype,t.toStringTag,{value:"WritableStream",configurable:!0});class e4{constructor(e){if(N(e,1,"WritableStreamDefaultWriter"),eq(e,"First parameter"),eY(e))throw TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=e,e._writer=this;let t=e._state;if("writable"===t)!e2(e)&&e._backpressure?tm(this):(tm(this),tw(this)),tp(this);else if("erroring"===t)tb(this,e._storedError),tp(this);else if("closed"===t)tm(this),tw(this),tp(this),ty(this);else{let t=e._storedError;tb(this,t),tp(this),tg(this,t)}}get closed(){return e5(this)?this._closedPromise:l(th("closed"))}get desiredSize(){if(!e5(this))throw th("desiredSize");if(void 0===this._ownerWritableStream)throw tf("desiredSize");return function(e){let t=e._ownerWritableStream,r=t._state;return"errored"===r||"erroring"===r?null:"closed"===r?0:ta(t._writableStreamController)}(this)}get ready(){return e5(this)?this._readyPromise:l(th("ready"))}abort(e){return e5(this)?void 0===this._ownerWritableStream?l(tf("abort")):eX(this._ownerWritableStream,e):l(th("abort"))}close(){if(!e5(this))return l(th("close"));let e=this._ownerWritableStream;return void 0===e?l(tf("close")):e2(e)?l(TypeError("Cannot close an already-closing stream")):e6(this)}releaseLock(){if(!e5(this))throw th("releaseLock");void 0!==this._ownerWritableStream&&e9(this)}write(e){return e5(this)?void 0===this._ownerWritableStream?l(tf("write to")):te(this,e):l(th("write"))}}function e5(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream"))&&e instanceof e4}function e6(e){return eQ(e._ownerWritableStream)}function e7(e,t){"pending"===e._readyPromiseState?tv(e,t):tb(e,t)}function e9(e){var t,r;let n=e._ownerWritableStream,i=TypeError("Writer was released and can no longer be used to monitor the stream's closedness");e7(e,i),"pending"===e._closedPromiseState?tg(e,i):(t=e,r=i,tp(t),tg(t,r)),n._writer=void 0,e._ownerWritableStream=void 0}function te(e,t){let r=e._ownerWritableStream,n=r._writableStreamController,i=function(e,t){try{return e._strategySizeAlgorithm(t)}catch(t){return tl(e,t),1}}(n,t);if(r!==e._ownerWritableStream)return l(tf("write to"));let o=r._state;if("errored"===o)return l(r._storedError);if(e2(r)||"closed"===o)return l(TypeError("The stream is closing or closed and cannot be written to"));if("erroring"===o)return l(r._storedError);let a=u((e,t)=>{r._writeRequests.push({_resolve:e,_reject:t})});return function(e,t,r){try{ea(e,t,r)}catch(t){tl(e,t);return}let n=e._controlledWritableStream;e2(n)||"writable"!==n._state||e3(n,0>=ta(e)),ts(e)}(n,t,i),a}Object.defineProperties(e4.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),"symbol"==typeof t.toStringTag&&Object.defineProperty(e4.prototype,t.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});let tt={};class tr{constructor(){throw TypeError("Illegal constructor")}get abortReason(){if(!tn(this))throw td("abortReason");return this._abortReason}get signal(){if(!tn(this))throw td("signal");if(void 0===this._abortController)throw TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal}error(e){if(!tn(this))throw td("error");"writable"===this._controlledWritableStream._state&&tu(this,e)}[P](e){let t=this._abortAlgorithm(e);return to(this),t}[C](){es(this)}}function tn(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_controlledWritableStream"))&&e instanceof tr}function ti(e,t,r,n,i,o,a,l){t._controlledWritableStream=e,e._writableStreamController=t,t._queue=void 0,t._queueTotalSize=void 0,es(t),t._abortReason=void 0,t._abortController=function(){if(eV)return new AbortController}(),t._started=!1,t._strategySizeAlgorithm=l,t._strategyHWM=a,t._writeAlgorithm=n,t._closeAlgorithm=i,t._abortAlgorithm=o,e3(e,0>=ta(t)),d(s(r()),()=>{t._started=!0,ts(t)},r=>{t._started=!0,eZ(e,r)})}function to(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function ta(e){return e._strategyHWM-e._queueTotalSize}function ts(e){let t=e._controlledWritableStream;if(!e._started||void 0!==t._inFlightWriteRequest)return;if("erroring"===t._state){e1(t);return}if(0===e._queue.length)return;let r=e._queue.peek().value;r===tt?function(e){let t=e._controlledWritableStream;t._inFlightCloseRequest=t._closeRequest,t._closeRequest=void 0,eo(e);let r=e._closeAlgorithm();to(e),d(r,()=>{!function(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,"erroring"===e._state&&(e._storedError=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";let t=e._writer;void 0!==t&&ty(t)}(t)},e=>{t._inFlightCloseRequest._reject(e),t._inFlightCloseRequest=void 0,void 0!==t._pendingAbortRequest&&(t._pendingAbortRequest._reject(e),t._pendingAbortRequest=void 0),eZ(t,e)})}(e):function(e,t){let r=e._controlledWritableStream;r._inFlightWriteRequest=r._writeRequests.shift(),d(e._writeAlgorithm(t),()=>{r._inFlightWriteRequest._resolve(void 0),r._inFlightWriteRequest=void 0;let t=r._state;eo(e),e2(r)||"writable"!==t||e3(r,0>=ta(e)),ts(e)},t=>{"writable"===r._state&&to(e),r._inFlightWriteRequest._reject(t),r._inFlightWriteRequest=void 0,eZ(r,t)})}(e,r)}function tl(e,t){"writable"===e._controlledWritableStream._state&&tu(e,t)}function tu(e,t){let r=e._controlledWritableStream;to(e),e0(r,t)}function tc(e){return TypeError(`WritableStream.prototype.${e} can only be used on a WritableStream`)}function td(e){return TypeError(`WritableStreamDefaultController.prototype.${e} can only be used on a WritableStreamDefaultController`)}function th(e){return TypeError(`WritableStreamDefaultWriter.prototype.${e} can only be used on a WritableStreamDefaultWriter`)}function tf(e){return TypeError("Cannot "+e+" a stream using a released writer")}function tp(e){e._closedPromise=u((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r,e._closedPromiseState="pending"})}function tg(e,t){void 0!==e._closedPromise_reject&&(p(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected")}function ty(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved")}function tm(e){e._readyPromise=u((t,r)=>{e._readyPromise_resolve=t,e._readyPromise_reject=r}),e._readyPromiseState="pending"}function tb(e,t){tm(e),tv(e,t)}function tv(e,t){void 0!==e._readyPromise_reject&&(p(e._readyPromise),e._readyPromise_reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected")}function tw(e){void 0!==e._readyPromise_resolve&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled")}Object.defineProperties(tr.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),"symbol"==typeof t.toStringTag&&Object.defineProperty(tr.prototype,t.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});let tS="undefined"!=typeof DOMException?DOMException:void 0,t_=!function(e){if(!("function"==typeof e||"object"==typeof e))return!1;try{return new e,!0}catch(e){return!1}}(tS)?function(){let e=function(e,t){this.message=e||"",this.name=t||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)};return e.prototype=Object.create(Error.prototype),Object.defineProperty(e.prototype,"constructor",{value:e,writable:!0,configurable:!0}),e}():tS;function tk(e,t,n,i,o,a){let f=D(e),g=new e4(t);e._disturbed=!0;let y=!1,m=s(void 0);return u((b,v)=>{var w,_;let k;if(void 0!==a){if(k=()=>{let r=new t_("Aborted","AbortError"),n=[];i||n.push(()=>"writable"===t._state?eX(t,r):s(void 0)),o||n.push(()=>"readable"===e._state?tF(e,r):s(void 0)),P(()=>Promise.all(n.map(e=>e())),!0,r)},a.aborted){k();return}a.addEventListener("abort",k)}if(A(e,f._closedPromise,e=>{i?C(!0,e):P(()=>eX(t,e),!0,e)}),A(t,g._closedPromise,t=>{o?C(!0,t):P(()=>tF(e,t),!0,t)}),w=f._closedPromise,_=()=>{n?C():P(()=>(function(e){let t=e._ownerWritableStream,r=t._state;return e2(t)||"closed"===r?s(void 0):"errored"===r?l(t._storedError):e6(e)})(g))},"closed"===e._state?_():d(w,_),e2(t)||"closed"===t._state){let t=TypeError("the destination writable stream closed before all data could be piped to it");o?C(!0,t):P(()=>tF(e,t),!0,t)}function E(){let e=m;return c(m,()=>e!==m?E():void 0)}function A(e,t,r){"errored"===e._state?r(e._storedError):h(t,r)}function P(e,r,n){!y&&((y=!0,"writable"!==t._state||e2(t))?i():d(E(),i));function i(){d(e(),()=>x(r,n),e=>x(!0,e))}}function C(e,r){!y&&((y=!0,"writable"!==t._state||e2(t))?x(e,r):d(E(),()=>x(e,r)))}function x(e,t){e9(g),S(f),void 0!==a&&a.removeEventListener("abort",k),e?v(t):b(void 0)}p(u((e,t)=>{!function n(i){i?e():c(y?s(!0):c(g._readyPromise,()=>u((e,t)=>{W(f,{_chunkSteps:t=>{m=c(te(g,t),void 0,r),e(!1)},_closeSteps:()=>e(!0),_errorSteps:t})})),n,t)}(!1)}))})}class tE{constructor(){throw TypeError("Illegal constructor")}get desiredSize(){if(!tA(this))throw tN("desiredSize");return tB(this)}close(){if(!tA(this))throw tN("close");if(!tj(this))throw TypeError("The stream is not in a state that permits close");tO(this)}enqueue(e){if(!tA(this))throw tN("enqueue");if(!tj(this))throw TypeError("The stream is not in a state that permits enqueue");return tR(this,e)}error(e){if(!tA(this))throw tN("error");tT(this,e)}[x](e){es(this);let t=this._cancelAlgorithm(e);return tx(this),t}[O](e){let t=this._controlledReadableStream;if(this._queue.length>0){let r=eo(this);this._closeRequested&&0===this._queue.length?(tx(this),t$(t)):tP(this),e._chunkSteps(r)}else z(t,e),tP(this)}}function tA(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_controlledReadableStream"))&&e instanceof tE}function tP(e){if(tC(e)){if(e._pulling){e._pullAgain=!0;return}e._pulling=!0,d(e._pullAlgorithm(),()=>{e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,tP(e))},t=>{tT(e,t)})}}function tC(e){let t=e._controlledReadableStream;return!!tj(e)&&!!e._started&&!!(tH(t)&&F(t)>0||tB(e)>0)}function tx(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function tO(e){if(!tj(e))return;let t=e._controlledReadableStream;e._closeRequested=!0,0===e._queue.length&&(tx(e),t$(t))}function tR(e,t){if(!tj(e))return;let r=e._controlledReadableStream;if(tH(r)&&F(r)>0)H(r,t,!1);else{let r;try{r=e._strategySizeAlgorithm(t)}catch(t){throw tT(e,t),t}try{ea(e,t,r)}catch(t){throw tT(e,t),t}}tP(e)}function tT(e,t){let r=e._controlledReadableStream;"readable"===r._state&&(es(e),tx(e),tq(r,t))}function tB(e){let t=e._controlledReadableStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}function tj(e){let t=e._controlledReadableStream._state;return!e._closeRequested&&"readable"===t}function tU(e,t,r,n,i,o,a){t._controlledReadableStream=e,t._queue=void 0,t._queueTotalSize=void 0,es(t),t._started=!1,t._closeRequested=!1,t._pullAgain=!1,t._pulling=!1,t._strategySizeAlgorithm=a,t._strategyHWM=o,t._pullAlgorithm=n,t._cancelAlgorithm=i,e._readableStreamController=t,d(s(r()),()=>{t._started=!0,tP(t)},e=>{tT(t,e)})}function tN(e){return TypeError(`ReadableStreamDefaultController.prototype.${e} can only be used on a ReadableStreamDefaultController`)}function tI(e,t){B(e,t);let r=null==e?void 0:e.preventAbort,n=null==e?void 0:e.preventCancel,i=null==e?void 0:e.preventClose,o=null==e?void 0:e.signal;return void 0!==o&&function(e,t){if(!function(e){if("object"!=typeof e||null===e)return!1;try{return"boolean"==typeof e.aborted}catch(e){return!1}}(e))throw TypeError(`${t} is not an AbortSignal.`)}(o,`${t} has member 'signal' that`),{preventAbort:!!r,preventCancel:!!n,preventClose:!!i,signal:o}}Object.defineProperties(tE.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),"symbol"==typeof t.toStringTag&&Object.defineProperty(tE.prototype,t.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});class tM{constructor(e={},t={}){void 0===e?e=null:U(e,"First parameter");let r=e$(t,"Second parameter"),n=function(e,t){B(e,t);let r=null==e?void 0:e.autoAllocateChunkSize,n=null==e?void 0:e.cancel,i=null==e?void 0:e.pull,o=null==e?void 0:e.start,a=null==e?void 0:e.type;return{autoAllocateChunkSize:void 0===r?void 0:L(r,`${t} has member 'autoAllocateChunkSize' that`),cancel:void 0===n?void 0:(j(n,`${t} has member 'cancel' that`),t=>m(n,e,[t])),pull:void 0===i?void 0:(j(i,`${t} has member 'pull' that`),t=>m(i,e,[t])),start:void 0===o?void 0:(j(o,`${t} has member 'start' that`),t=>y(o,e,[t])),type:void 0===a?void 0:function(e,t){if("bytes"!=(e=`${e}`))throw TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamType`);return e}(a,`${t} has member 'type' that`)}}(e,"First parameter");if(tD(this),"bytes"===n.type){if(void 0!==r.size)throw RangeError("The strategy for a byte stream cannot have a size function");!function(e,t,r){let n=Object.create(eu.prototype),i=()=>void 0,o=()=>s(void 0),a=()=>s(void 0);void 0!==t.start&&(i=()=>t.start(n)),void 0!==t.pull&&(o=()=>t.pull(n)),void 0!==t.cancel&&(a=e=>t.cancel(e));let l=t.autoAllocateChunkSize;if(0===l)throw TypeError("autoAllocateChunkSize must be greater than 0");eB(e,n,i,o,a,r,l)}(this,n,eH(r,0))}else{let e=eF(r);!function(e,t,r,n){let i=Object.create(tE.prototype),o=()=>void 0,a=()=>s(void 0),l=()=>s(void 0);void 0!==t.start&&(o=()=>t.start(i)),void 0!==t.pull&&(a=()=>t.pull(i)),void 0!==t.cancel&&(l=e=>t.cancel(e)),tU(e,i,o,a,l,r,n)}(this,n,eH(r,1),e)}}get locked(){if(!tz(this))throw tV("locked");return tH(this)}cancel(e){return tz(this)?tH(this)?l(TypeError("Cannot cancel a stream that already has a reader")):tF(this,e):l(tV("cancel"))}getReader(e){if(!tz(this))throw tV("getReader");return void 0===function(e,t){B(e,t);let r=null==e?void 0:e.mode;return{mode:void 0===r?void 0:function(e,t){if("byob"!=(e=`${e}`))throw TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamReaderMode`);return e}(r,`${t} has member 'mode' that`)}}(e,"First parameter").mode?D(this):new eL(this)}pipeThrough(e,t={}){if(!tz(this))throw tV("pipeThrough");N(e,1,"pipeThrough");let r=function(e,t){B(e,t);let r=null==e?void 0:e.readable;I(r,"readable","ReadableWritablePair"),K(r,`${t} has member 'readable' that`);let n=null==e?void 0:e.writable;return I(n,"writable","ReadableWritablePair"),eq(n,`${t} has member 'writable' that`),{readable:r,writable:n}}(e,"First parameter"),n=tI(t,"Second parameter");if(tH(this))throw TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(eY(r.writable))throw TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");return p(tk(this,r.writable,n.preventClose,n.preventAbort,n.preventCancel,n.signal)),r.readable}pipeTo(e,t={}){let r;if(!tz(this))return l(tV("pipeTo"));if(void 0===e)return l("Parameter 1 is required in 'pipeTo'.");if(!eJ(e))return l(TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));try{r=tI(t,"Second parameter")}catch(e){return l(e)}return tH(this)?l(TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):eY(e)?l(TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):tk(this,e,r.preventClose,r.preventAbort,r.preventCancel,r.signal)}tee(){if(!tz(this))throw tV("tee");let e=ec(this._readableStreamController)?function(e){let t,r,n,i,o,a=D(e),l=!1,c=!1,d=!1,f=!1,p=!1,y=u(e=>{o=e});function m(e){h(e._closedPromise,t=>{e===a&&(eC(n._readableStreamController,t),eC(i._readableStreamController,t),f&&p||o(void 0))})}function b(){eK(a)&&(S(a),m(a=D(e))),W(a,{_chunkSteps:t=>{g(()=>{c=!1,d=!1;let r=t;if(!f&&!p)try{r=ei(t)}catch(t){eC(n._readableStreamController,t),eC(i._readableStreamController,t),o(tF(e,t));return}f||eP(n._readableStreamController,t),p||eP(i._readableStreamController,r),l=!1,c?w():d&&_()})},_closeSteps:()=>{l=!1,f||eA(n._readableStreamController),p||eA(i._readableStreamController),n._readableStreamController._pendingPullIntos.length>0&&eR(n._readableStreamController,0),i._readableStreamController._pendingPullIntos.length>0&&eR(i._readableStreamController,0),f&&p||o(void 0)},_errorSteps:()=>{l=!1}})}function v(t,r){V(a)&&(S(a),m(a=new eL(e)));let s=r?i:n,u=r?n:i;eD(a,t,{_chunkSteps:t=>{g(()=>{c=!1,d=!1;let n=r?p:f;if(r?f:p)n||eT(s._readableStreamController,t);else{let r;try{r=ei(t)}catch(t){eC(s._readableStreamController,t),eC(u._readableStreamController,t),o(tF(e,t));return}n||eT(s._readableStreamController,t),eP(u._readableStreamController,r)}l=!1,c?w():d&&_()})},_closeSteps:e=>{l=!1;let t=r?p:f,n=r?f:p;t||eA(s._readableStreamController),n||eA(u._readableStreamController),void 0!==e&&(t||eT(s._readableStreamController,e),!n&&u._readableStreamController._pendingPullIntos.length>0&&eR(u._readableStreamController,0)),t&&n||o(void 0)},_errorSteps:()=>{l=!1}})}function w(){if(l)return c=!0,s(void 0);l=!0;let e=ex(n._readableStreamController);return null===e?b():v(e._view,!1),s(void 0)}function _(){if(l)return d=!0,s(void 0);l=!0;let e=ex(i._readableStreamController);return null===e?b():v(e._view,!0),s(void 0)}function k(){}return n=tK(k,w,function(n){if(f=!0,t=n,p){let n=tF(e,et([t,r]));o(n)}return y}),i=tK(k,_,function(n){if(p=!0,r=n,f){let n=tF(e,et([t,r]));o(n)}return y}),m(a),[n,i]}(this):function(e,t){let r,n,i,o,a;let l=D(e),c=!1,d=!1,f=!1,p=!1,y=u(e=>{a=e});function m(){return c?d=!0:(c=!0,W(l,{_chunkSteps:e=>{g(()=>{d=!1,f||tR(i._readableStreamController,e),p||tR(o._readableStreamController,e),c=!1,d&&m()})},_closeSteps:()=>{c=!1,f||tO(i._readableStreamController),p||tO(o._readableStreamController),f&&p||a(void 0)},_errorSteps:()=>{c=!1}})),s(void 0)}function b(){}return i=tL(b,m,function(t){if(f=!0,r=t,p){let t=tF(e,et([r,n]));a(t)}return y}),o=tL(b,m,function(t){if(p=!0,n=t,f){let t=tF(e,et([r,n]));a(t)}return y}),h(l._closedPromise,e=>{tT(i._readableStreamController,e),tT(o._readableStreamController,e),f&&p||a(void 0)}),[i,o]}(this);return et(e)}values(e){if(!tz(this))throw tV("values");return function(e,t){let r=new Y(D(e),t),n=Object.create(X);return n._asyncIteratorImpl=r,n}(this,(B(e,"First parameter"),{preventCancel:!!(null==e?void 0:e.preventCancel)}).preventCancel)}}function tL(e,t,r,n=1,i=()=>1){let o=Object.create(tM.prototype);return tD(o),tU(o,Object.create(tE.prototype),e,t,r,n,i),o}function tK(e,t,r){let n=Object.create(tM.prototype);return tD(n),eB(n,Object.create(eu.prototype),e,t,r,0,void 0),n}function tD(e){e._state="readable",e._reader=void 0,e._storedError=void 0,e._disturbed=!1}function tz(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_readableStreamController"))&&e instanceof tM}function tH(e){return void 0!==e._reader}function tF(e,t){if(e._disturbed=!0,"closed"===e._state)return s(void 0);if("errored"===e._state)return l(e._storedError);t$(e);let n=e._reader;return void 0!==n&&eK(n)&&(n._readIntoRequests.forEach(e=>{e._closeSteps(void 0)}),n._readIntoRequests=new b),c(e._readableStreamController[x](t),r,void 0)}function t$(e){e._state="closed";let t=e._reader;void 0!==t&&(A(t),V(t)&&(t._readRequests.forEach(e=>{e._closeSteps()}),t._readRequests=new b))}function tq(e,t){e._state="errored",e._storedError=t;let r=e._reader;void 0!==r&&(E(r,t),V(r)?(r._readRequests.forEach(e=>{e._errorSteps(t)}),r._readRequests=new b):(r._readIntoRequests.forEach(e=>{e._errorSteps(t)}),r._readIntoRequests=new b))}function tV(e){return TypeError(`ReadableStream.prototype.${e} can only be used on a ReadableStream`)}function tW(e,t){B(e,t);let r=null==e?void 0:e.highWaterMark;return I(r,"highWaterMark","QueuingStrategyInit"),{highWaterMark:M(r)}}Object.defineProperties(tM.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),"symbol"==typeof t.toStringTag&&Object.defineProperty(tM.prototype,t.toStringTag,{value:"ReadableStream",configurable:!0}),"symbol"==typeof t.asyncIterator&&Object.defineProperty(tM.prototype,t.asyncIterator,{value:tM.prototype.values,writable:!0,configurable:!0});let tG=e=>e.byteLength;try{Object.defineProperty(tG,"name",{value:"size",configurable:!0})}catch(e){}class tJ{constructor(e){N(e,1,"ByteLengthQueuingStrategy"),e=tW(e,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!tX(this))throw tY("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark}get size(){if(!tX(this))throw tY("size");return tG}}function tY(e){return TypeError(`ByteLengthQueuingStrategy.prototype.${e} can only be used on a ByteLengthQueuingStrategy`)}function tX(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_byteLengthQueuingStrategyHighWaterMark"))&&e instanceof tJ}Object.defineProperties(tJ.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof t.toStringTag&&Object.defineProperty(tJ.prototype,t.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});let tQ=()=>1;try{Object.defineProperty(tQ,"name",{value:"size",configurable:!0})}catch(e){}class tZ{constructor(e){N(e,1,"CountQueuingStrategy"),e=tW(e,"First parameter"),this._countQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!t1(this))throw t0("highWaterMark");return this._countQueuingStrategyHighWaterMark}get size(){if(!t1(this))throw t0("size");return tQ}}function t0(e){return TypeError(`CountQueuingStrategy.prototype.${e} can only be used on a CountQueuingStrategy`)}function t1(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_countQueuingStrategyHighWaterMark"))&&e instanceof tZ}Object.defineProperties(tZ.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof t.toStringTag&&Object.defineProperty(tZ.prototype,t.toStringTag,{value:"CountQueuingStrategy",configurable:!0});class t2{constructor(e={},t={},r={}){let n;void 0===e&&(e=null);let i=e$(t,"Second parameter"),o=e$(r,"Third parameter"),a=function(e,t){B(e,t);let r=null==e?void 0:e.flush,n=null==e?void 0:e.readableType,i=null==e?void 0:e.start,o=null==e?void 0:e.transform,a=null==e?void 0:e.writableType;return{flush:void 0===r?void 0:(j(r,`${t} has member 'flush' that`),t=>m(r,e,[t])),readableType:n,start:void 0===i?void 0:(j(i,`${t} has member 'start' that`),t=>y(i,e,[t])),transform:void 0===o?void 0:(j(o,`${t} has member 'transform' that`),(t,r)=>m(o,e,[t,r])),writableType:a}}(e,"First parameter");if(void 0!==a.readableType)throw RangeError("Invalid readableType specified");if(void 0!==a.writableType)throw RangeError("Invalid writableType specified");let d=eH(o,0),h=eF(o),f=eH(i,1),p=eF(i);(function(e,t,r,n,i,o){function a(){return t}e._writable=function(e,t,r,n,i=1,o=()=>1){let a=Object.create(eW.prototype);return eG(a),ti(a,Object.create(tr.prototype),e,t,r,n,i,o),a}(a,function(t){return function(e,t){let r=e._transformStreamController;return e._backpressure?c(e._backpressureChangePromise,()=>{let n=e._writable;if("erroring"===n._state)throw n._storedError;return rt(r,t)},void 0):rt(r,t)}(e,t)},function(){return function(e){let t=e._readable,r=e._transformStreamController,n=r._flushAlgorithm();return t9(r),c(n,()=>{if("errored"===t._state)throw t._storedError;tO(t._readableStreamController)},r=>{throw t3(e,r),t._storedError})}(e)},function(t){return t3(e,t),s(void 0)},r,n),e._readable=tL(a,function(){return t5(e,!1),e._backpressureChangePromise},function(t){return t4(e,t),s(void 0)},i,o),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,t5(e,!0),e._transformStreamController=void 0})(this,u(e=>{n=e}),f,p,d,h),function(e,t){var r,n;let i=Object.create(t6.prototype),o=e=>{try{return re(i,e),s(void 0)}catch(e){return l(e)}},a=()=>s(void 0);void 0!==t.transform&&(o=e=>t.transform(e,i)),void 0!==t.flush&&(a=()=>t.flush(i)),r=o,n=a,i._controlledTransformStream=e,e._transformStreamController=i,i._transformAlgorithm=r,i._flushAlgorithm=n}(this,a),void 0!==a.start?n(a.start(this._transformStreamController)):n(void 0)}get readable(){if(!t8(this))throw rn("readable");return this._readable}get writable(){if(!t8(this))throw rn("writable");return this._writable}}function t8(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_transformStreamController"))&&e instanceof t2}function t3(e,t){tT(e._readable._readableStreamController,t),t4(e,t)}function t4(e,t){t9(e._transformStreamController),tl(e._writable._writableStreamController,t),e._backpressure&&t5(e,!1)}function t5(e,t){void 0!==e._backpressureChangePromise&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=u(t=>{e._backpressureChangePromise_resolve=t}),e._backpressure=t}Object.defineProperties(t2.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),"symbol"==typeof t.toStringTag&&Object.defineProperty(t2.prototype,t.toStringTag,{value:"TransformStream",configurable:!0});class t6{constructor(){throw TypeError("Illegal constructor")}get desiredSize(){if(!t7(this))throw rr("desiredSize");return tB(this._controlledTransformStream._readable._readableStreamController)}enqueue(e){if(!t7(this))throw rr("enqueue");re(this,e)}error(e){if(!t7(this))throw rr("error");t3(this._controlledTransformStream,e)}terminate(){if(!t7(this))throw rr("terminate");(function(e){let t=e._controlledTransformStream;tO(t._readable._readableStreamController),t4(t,TypeError("TransformStream terminated"))})(this)}}function t7(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream"))&&e instanceof t6}function t9(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0}function re(e,t){let r=e._controlledTransformStream,n=r._readable._readableStreamController;if(!tj(n))throw TypeError("Readable side is not in a state that permits enqueue");try{tR(n,t)}catch(e){throw t4(r,e),r._readable._storedError}!tC(n)!==r._backpressure&&t5(r,!0)}function rt(e,t){return c(e._transformAlgorithm(t),void 0,t=>{throw t3(e._controlledTransformStream,t),t})}function rr(e){return TypeError(`TransformStreamDefaultController.prototype.${e} can only be used on a TransformStreamDefaultController`)}function rn(e){return TypeError(`TransformStream.prototype.${e} can only be used on a TransformStream`)}Object.defineProperties(t6.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),"symbol"==typeof t.toStringTag&&Object.defineProperty(t6.prototype,t.toStringTag,{value:"TransformStreamDefaultController",configurable:!0}),e.ByteLengthQueuingStrategy=tJ,e.CountQueuingStrategy=tZ,e.ReadableByteStreamController=eu,e.ReadableStream=tM,e.ReadableStreamBYOBReader=eL,e.ReadableStreamBYOBRequest=el,e.ReadableStreamDefaultController=tE,e.ReadableStreamDefaultReader=q,e.TransformStream=t2,e.TransformStreamDefaultController=t6,e.WritableStream=eW,e.WritableStreamDefaultController=tr,e.WritableStreamDefaultWriter=e4,Object.defineProperty(e,"__esModule",{value:!0})}(p.exports)),p.exports))}try{let{Blob:e}=r(14300);e&&!e.prototype.stream&&(e.prototype.stream=function(e){let t=0,r=this;return new ReadableStream({type:"bytes",async pull(e){let n=r.slice(t,Math.min(r.size,t+65536)),i=await n.arrayBuffer();t+=i.byteLength,e.enqueue(new Uint8Array(i)),t===r.size&&e.close()}})})}catch(e){}async function*g(e,t=!0){for(let r of e)if("stream"in r)yield*r.stream();else if(ArrayBuffer.isView(r)){if(t){let e=r.byteOffset,t=r.byteOffset+r.byteLength;for(;e!==t;){let n=Math.min(t-e,65536),i=r.buffer.slice(e,e+n);e+=i.byteLength,yield new Uint8Array(i)}}else yield r}else{let e=0;for(;e!==r.size;){let t=r.slice(e,Math.min(r.size,e+65536)),n=await t.arrayBuffer();e+=n.byteLength,yield new Uint8Array(n)}}}let y=class e{#r;#n;#i;#o;constructor(t=[],r={}){if(this.#r=[],this.#n="",this.#i=0,this.#o="transparent","object"!=typeof t||null===t)throw TypeError("Failed to construct 'Blob': The provided value cannot be converted to a sequence.");if("function"!=typeof t[Symbol.iterator])throw TypeError("Failed to construct 'Blob': The object must have a callable @@iterator property.");if("object"!=typeof r&&"function"!=typeof r)throw TypeError("Failed to construct 'Blob': parameter 2 cannot convert to dictionary.");null===r&&(r={});let n=new TextEncoder;for(let r of t){let t;t=ArrayBuffer.isView(r)?new Uint8Array(r.buffer.slice(r.byteOffset,r.byteOffset+r.byteLength)):r instanceof ArrayBuffer?new Uint8Array(r.slice(0)):r instanceof e?r:n.encode(`${r}`);let i=ArrayBuffer.isView(t)?t.byteLength:t.size;i&&(this.#i+=i,this.#r.push(t))}this.#o=`${void 0===r.endings?"transparent":r.endings}`;let i=void 0===r.type?"":String(r.type);this.#n=/^[\x20-\x7E]*$/.test(i)?i:""}get size(){return this.#i}get type(){return this.#n}async text(){let e=new TextDecoder,t="";for await(let r of g(this.#r,!1))t+=e.decode(r,{stream:!0});return t+e.decode()}async arrayBuffer(){let e=new Uint8Array(this.size),t=0;for await(let r of g(this.#r,!1))e.set(r,t),t+=r.length;return e.buffer}stream(){let e=g(this.#r,!0);return new globalThis.ReadableStream({type:"bytes",async pull(t){let r=await e.next();r.done?t.close():t.enqueue(r.value)},async cancel(){await e.return()}})}slice(t=0,r=this.size,n=""){let{size:i}=this,o=t<0?Math.max(i+t,0):Math.min(t,i),a=r<0?Math.max(i+r,0):Math.min(r,i),s=Math.max(a-o,0),l=this.#r,u=[],c=0;for(let e of l){if(c>=s)break;let t=ArrayBuffer.isView(e)?e.byteLength:e.size;if(o&&t<=o)o-=t,a-=t;else{let r;ArrayBuffer.isView(e)?c+=(r=e.subarray(o,Math.min(t,a))).byteLength:c+=(r=e.slice(o,Math.min(t,a))).size,a-=t,u.push(r),o=0}}let d=new e([],{type:String(n).toLowerCase()});return d.#i=s,d.#r=u,d}get[Symbol.toStringTag](){return"Blob"}static[Symbol.hasInstance](e){return e&&"object"==typeof e&&"function"==typeof e.constructor&&("function"==typeof e.stream||"function"==typeof e.arrayBuffer)&&/^(Blob|File)$/.test(e[Symbol.toStringTag])}};Object.defineProperties(y.prototype,{size:{enumerable:!0},type:{enumerable:!0},slice:{enumerable:!0}});let m=y,b=class extends m{#a;#s;constructor(e,t,r={}){if(arguments.length<2)throw TypeError(`Failed to construct 'File': 2 arguments required, but only ${arguments.length} present.`);super(e,r),this.#a=0,this.#s="",null===r&&(r={});let n=void 0===r.lastModified?Date.now():Number(r.lastModified);Number.isNaN(n)||(this.#a=n),this.#s=String(t)}get name(){return this.#s}get lastModified(){return this.#a}get[Symbol.toStringTag](){return"File"}static[Symbol.hasInstance](e){return!!e&&e instanceof m&&/^(File)$/.test(e[Symbol.toStringTag])}};/*! formdata-polyfill. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> */var{toStringTag:v,iterator:w,hasInstance:S}=Symbol,_=Math.random,k="append,set,get,getAll,delete,keys,values,entries,forEach,constructor".split(","),E=(e,t,r)=>(e+="",/^(Blob|File)$/.test(t&&t[v])?[(r=void 0!==r?r+"":"File"==t[v]?t.name:"blob",e),t.name!==r||"blob"==t[v]?new b([t],r,t):t]:[e,t+""]),A=(e,t)=>(t?e:e.replace(/\r?\n|\r/g,"\r\n")).replace(/\n/g,"%0A").replace(/\r/g,"%0D").replace(/"/g,"%22"),P=(e,t,r)=>{if(t.length<r)throw TypeError(`Failed to execute '${e}' on 'FormData': ${r} arguments required, but only ${t.length} present.`)};let C=class{#l;constructor(...e){if(this.#l=[],e.length)throw TypeError("Failed to construct 'FormData': parameter 1 is not of type 'HTMLFormElement'.")}get[v](){return"FormData"}[w](){return this.entries()}static[S](e){return e&&"object"==typeof e&&"FormData"===e[v]&&!k.some(t=>"function"!=typeof e[t])}append(...e){P("append",arguments,2),this.#l.push(E(...e))}delete(e){P("delete",arguments,1),e+="",this.#l=this.#l.filter(([t])=>t!==e)}get(e){P("get",arguments,1),e+="";for(var t=this.#l,r=t.length,n=0;n<r;n++)if(t[n][0]===e)return t[n][1];return null}getAll(e,t){return P("getAll",arguments,1),t=[],e+="",this.#l.forEach(r=>r[0]===e&&t.push(r[1])),t}has(e){return P("has",arguments,1),e+="",this.#l.some(t=>t[0]===e)}forEach(e,t){for(var[r,n]of(P("forEach",arguments,1),this))e.call(t,n,r,this)}set(...e){P("set",arguments,2);var t=[],r=!0;e=E(...e),this.#l.forEach(n=>{n[0]===e[0]?r&&(r=!t.push(e)):t.push(n)}),r&&t.push(e),this.#l=t}*entries(){yield*this.#l}*keys(){for(var[e]of this)yield e}*values(){for(var[,e]of this)yield e}};class x extends Error{constructor(e,t){super(e),Error.captureStackTrace(this,this.constructor),this.type=t}get name(){return this.constructor.name}get[Symbol.toStringTag](){return this.constructor.name}}class O extends x{constructor(e,t,r){super(e,t),r&&(this.code=this.errno=r.code,this.erroredSysCall=r.syscall)}}let R=Symbol.toStringTag,T=e=>"object"==typeof e&&"function"==typeof e.append&&"function"==typeof e.delete&&"function"==typeof e.get&&"function"==typeof e.getAll&&"function"==typeof e.has&&"function"==typeof e.set&&"function"==typeof e.sort&&"URLSearchParams"===e[R],B=e=>e&&"object"==typeof e&&"function"==typeof e.arrayBuffer&&"string"==typeof e.type&&"function"==typeof e.stream&&"function"==typeof e.constructor&&/^(Blob|File)$/.test(e[R]),j=e=>"object"==typeof e&&("AbortSignal"===e[R]||"EventTarget"===e[R]),U=(e,t)=>{let r=new URL(t).hostname,n=new URL(e).hostname;return r===n||r.endsWith(`.${n}`)},N=(e,t)=>new URL(t).protocol===new URL(e).protocol,I=u.promisify(s.pipeline),M=Symbol("Body internals");class L{constructor(e,{size:t=0}={}){let r=null;null===e?e=null:T(e)?e=l.Buffer.from(e.toString()):B(e)||l.Buffer.isBuffer(e)||(u.types.isAnyArrayBuffer(e)?e=l.Buffer.from(e):ArrayBuffer.isView(e)?e=l.Buffer.from(e.buffer,e.byteOffset,e.byteLength):e instanceof s||(e instanceof C?r=(e=function(e,t=m){var r=`${_()}${_()}`.replace(/\./g,"").slice(-28).padStart(32,"-"),n=[],i=`--${r}\r
Content-Disposition: form-data; name="`;return e.forEach((e,t)=>"string"==typeof e?n.push(i+A(t)+`"\r
\r
${e.replace(/\r(?!\n)|(?<!\r)\n/g,"\r\n")}\r
`):n.push(i+A(t)+`"; filename="${A(e.name,1)}"\r
Content-Type: ${e.type||"application/octet-stream"}\r
\r
`,e,"\r\n")),n.push(`--${r}--`),new t(n,{type:"multipart/form-data; boundary="+r})}(e)).type.split("=")[1]:e=l.Buffer.from(String(e))));let n=e;l.Buffer.isBuffer(e)?n=s.Readable.from(e):B(e)&&(n=s.Readable.from(e.stream())),this[M]={body:e,stream:n,boundary:r,disturbed:!1,error:null},this.size=t,e instanceof s&&e.on("error",e=>{let t=e instanceof x?e:new O(`Invalid response body while trying to fetch ${this.url}: ${e.message}`,"system",e);this[M].error=t})}get body(){return this[M].stream}get bodyUsed(){return this[M].disturbed}async arrayBuffer(){let{buffer:e,byteOffset:t,byteLength:r}=await K(this);return e.slice(t,t+r)}async formData(){let e=this.headers.get("content-type");if(e.startsWith("application/x-www-form-urlencoded")){let e=new C;for(let[t,r]of new URLSearchParams(await this.text()))e.append(t,r);return e}let{toFormData:t}=await Promise.resolve().then(()=>n._(r(10680)));return t(this.body,e)}async blob(){let e=this.headers&&this.headers.get("content-type")||this[M].body&&this[M].body.type||"";return new m([await this.arrayBuffer()],{type:e})}async json(){return JSON.parse(await this.text())}async text(){let e=await K(this);return new TextDecoder().decode(e)}buffer(){return K(this)}}async function K(e){if(e[M].disturbed)throw TypeError(`body used already for: ${e.url}`);if(e[M].disturbed=!0,e[M].error)throw e[M].error;let{body:t}=e;if(null===t||!(t instanceof s))return l.Buffer.alloc(0);let r=[],n=0;try{for await(let i of t){if(e.size>0&&n+i.length>e.size){let r=new O(`content size at ${e.url} over limit: ${e.size}`,"max-size");throw t.destroy(r),r}n+=i.length,r.push(i)}}catch(t){throw t instanceof x?t:new O(`Invalid response body while trying to fetch ${e.url}: ${t.message}`,"system",t)}if(!0===t.readableEnded||!0===t._readableState.ended)try{if(r.every(e=>"string"==typeof e))return l.Buffer.from(r.join(""));return l.Buffer.concat(r,n)}catch(t){throw new O(`Could not create Buffer from response body for ${e.url}: ${t.message}`,"system",t)}else throw new O(`Premature close of server response while trying to fetch ${e.url}`)}L.prototype.buffer=u.deprecate(L.prototype.buffer,"Please use 'response.arrayBuffer()' instead of 'response.buffer()'","node-fetch#buffer"),Object.defineProperties(L.prototype,{body:{enumerable:!0},bodyUsed:{enumerable:!0},arrayBuffer:{enumerable:!0},blob:{enumerable:!0},json:{enumerable:!0},text:{enumerable:!0},data:{get:u.deprecate(()=>{},"data doesn't exist, use json(), text(), arrayBuffer(), or body instead","https://github.com/node-fetch/node-fetch/issues/1000 (response)")}});let D=(e,t)=>{let r,n;let{body:i}=e[M];if(e.bodyUsed)throw Error("cannot clone body after it is used");return i instanceof s&&"function"!=typeof i.getBoundary&&(r=new s.PassThrough({highWaterMark:t}),n=new s.PassThrough({highWaterMark:t}),i.pipe(r),i.pipe(n),e[M].stream=r,i=n),i},z=u.deprecate(e=>e.getBoundary(),"form-data doesn't follow the spec and requires special treatment. Use alternative package","https://github.com/node-fetch/node-fetch/issues/1167"),H=(e,t)=>null===e?null:"string"==typeof e?"text/plain;charset=UTF-8":T(e)?"application/x-www-form-urlencoded;charset=UTF-8":B(e)?e.type||null:l.Buffer.isBuffer(e)||u.types.isAnyArrayBuffer(e)||ArrayBuffer.isView(e)?null:e instanceof C?`multipart/form-data; boundary=${t[M].boundary}`:e&&"function"==typeof e.getBoundary?`multipart/form-data;boundary=${z(e)}`:e instanceof s?null:"text/plain;charset=UTF-8",F=e=>{let{body:t}=e[M];return null===t?0:B(t)?t.size:l.Buffer.isBuffer(t)?t.length:t&&"function"==typeof t.getLengthSync&&t.hasKnownLength&&t.hasKnownLength()?t.getLengthSync():null},$=async(e,{body:t})=>{null===t?e.end():await I(t,e)},q="function"==typeof i.validateHeaderName?i.validateHeaderName:e=>{if(!/^[\^`\-\w!#$%&'*+.|~]+$/.test(e)){let t=TypeError(`Header name must be a valid HTTP token [${e}]`);throw Object.defineProperty(t,"code",{value:"ERR_INVALID_HTTP_TOKEN"}),t}},V="function"==typeof i.validateHeaderValue?i.validateHeaderValue:(e,t)=>{if(/[^\t\u0020-\u007E\u0080-\u00FF]/.test(t)){let t=TypeError(`Invalid character in header content ["${e}"]`);throw Object.defineProperty(t,"code",{value:"ERR_INVALID_CHAR"}),t}};class W extends URLSearchParams{constructor(e){let t=[];if(e instanceof W)for(let[r,n]of Object.entries(e.raw()))t.push(...n.map(e=>[r,e]));else if(null==e);else if("object"!=typeof e||u.types.isBoxedPrimitive(e))throw TypeError("Failed to construct 'Headers': The provided value is not of type '(sequence<sequence<ByteString>> or record<ByteString, ByteString>)");else{let r=e[Symbol.iterator];if(null==r)t.push(...Object.entries(e));else{if("function"!=typeof r)throw TypeError("Header pairs must be iterable");t=[...e].map(e=>{if("object"!=typeof e||u.types.isBoxedPrimitive(e))throw TypeError("Each header pair must be an iterable object");return[...e]}).map(e=>{if(2!==e.length)throw TypeError("Each header pair must be a name/value tuple");return[...e]})}}return super(t=t.length>0?t.map(([e,t])=>(q(e),V(e,String(t)),[String(e).toLowerCase(),String(t)])):void 0),new Proxy(this,{get(e,t,r){switch(t){case"append":case"set":return(r,n)=>(q(r),V(r,String(n)),URLSearchParams.prototype[t].call(e,String(r).toLowerCase(),String(n)));case"delete":case"has":case"getAll":return r=>(q(r),URLSearchParams.prototype[t].call(e,String(r).toLowerCase()));case"keys":return()=>(e.sort(),new Set(URLSearchParams.prototype.keys.call(e)).keys());default:return Reflect.get(e,t,r)}}})}get[Symbol.toStringTag](){return this.constructor.name}toString(){return Object.prototype.toString.call(this)}get(e){let t=this.getAll(e);if(0===t.length)return null;let r=t.join(", ");return/^content-encoding$/i.test(e)&&(r=r.toLowerCase()),r}forEach(e,t){for(let r of this.keys())Reflect.apply(e,t,[this.get(r),r,this])}*values(){for(let e of this.keys())yield this.get(e)}*entries(){for(let e of this.keys())yield[e,this.get(e)]}[Symbol.iterator](){return this.entries()}raw(){return[...this.keys()].reduce((e,t)=>(e[t]=this.getAll(t),e),{})}[Symbol.for("nodejs.util.inspect.custom")](){return[...this.keys()].reduce((e,t)=>{let r=this.getAll(t);return"host"===t?e[t]=r[0]:e[t]=r.length>1?r:r[0],e},{})}}Object.defineProperties(W.prototype,["get","entries","forEach","values"].reduce((e,t)=>(e[t]={enumerable:!0},e),{}));let G=new Set([301,302,303,307,308]),J=e=>G.has(e),Y=Symbol("Response internals");class X extends L{constructor(e=null,t={}){super(e,t);let r=null!=t.status?t.status:200,n=new W(t.headers);if(null!==e&&!n.has("Content-Type")){let t=H(e,this);t&&n.append("Content-Type",t)}this[Y]={type:"default",url:t.url,status:r,statusText:t.statusText||"",headers:n,counter:t.counter,highWaterMark:t.highWaterMark}}get type(){return this[Y].type}get url(){return this[Y].url||""}get status(){return this[Y].status}get ok(){return this[Y].status>=200&&this[Y].status<300}get redirected(){return this[Y].counter>0}get statusText(){return this[Y].statusText}get headers(){return this[Y].headers}get highWaterMark(){return this[Y].highWaterMark}clone(){return new X(D(this,this.highWaterMark),{type:this.type,url:this.url,status:this.status,statusText:this.statusText,headers:this.headers,ok:this.ok,redirected:this.redirected,size:this.size,highWaterMark:this.highWaterMark})}static redirect(e,t=302){if(!J(t))throw RangeError('Failed to execute "redirect" on "response": Invalid status code');return new X(null,{headers:{location:new URL(e).toString()},status:t})}static error(){let e=new X(null,{status:0,statusText:""});return e[Y].type="error",e}static json(e,t={}){let r=JSON.stringify(e);if(void 0===r)throw TypeError("data is not JSON serializable");let n=new W(t&&t.headers);return n.has("content-type")||n.set("content-type","application/json"),new X(r,{...t,headers:n})}get[Symbol.toStringTag](){return"Response"}}Object.defineProperties(X.prototype,{type:{enumerable:!0},url:{enumerable:!0},status:{enumerable:!0},ok:{enumerable:!0},redirected:{enumerable:!0},statusText:{enumerable:!0},headers:{enumerable:!0},clone:{enumerable:!0}});let Q=e=>{if(e.search)return e.search;let t=e.href.length-1,r=e.hash||("#"===e.href[t]?"#":"");return"?"===e.href[t-r.length]?"?":""};function Z(e,t=!1){return null==e?"no-referrer":(e=new URL(e),/^(about|blob|data):$/.test(e.protocol))?"no-referrer":(e.username="",e.password="",e.hash="",t&&(e.pathname="",e.search=""),e)}let ee=new Set(["","no-referrer","no-referrer-when-downgrade","same-origin","origin","strict-origin","origin-when-cross-origin","strict-origin-when-cross-origin","unsafe-url"]);function et(e){return!!(/^about:(blank|srcdoc)$/.test(e)||"data:"===e.protocol||/^(blob|filesystem):$/.test(e.protocol))||function(e){if(/^(http|ws)s:$/.test(e.protocol))return!0;let t=e.host.replace(/(^\[)|(]$)/g,""),r=d.isIP(t);return!!(4===r&&/^127\./.test(t)||6===r&&/^(((0+:){7})|(::(0+:){0,6}))0*1$/.test(t))||!("localhost"===e.host||e.host.endsWith(".localhost"))&&"file:"===e.protocol}(e)}let er=Symbol("Request internals"),en=e=>"object"==typeof e&&"object"==typeof e[er],ei=u.deprecate(()=>{},".data is not a valid RequestInit property, use .body instead","https://github.com/node-fetch/node-fetch/issues/1000 (request)");class eo extends L{constructor(e,t={}){let r;if(en(e)?r=new URL(e.url):(r=new URL(e),e={}),""!==r.username||""!==r.password)throw TypeError(`${r} is an url with embedded credentials.`);let n=t.method||e.method||"GET";if(/^(delete|get|head|options|post|put)$/i.test(n)&&(n=n.toUpperCase()),!en(t)&&"data"in t&&ei(),(null!=t.body||en(e)&&null!==e.body)&&("GET"===n||"HEAD"===n))throw TypeError("Request with GET/HEAD method cannot have body");let i=t.body?t.body:en(e)&&null!==e.body?D(e):null;super(i,{size:t.size||e.size||0});let o=new W(t.headers||e.headers||{});if(null!==i&&!o.has("Content-Type")){let e=H(i,this);e&&o.set("Content-Type",e)}let a=en(e)?e.signal:null;if("signal"in t&&(a=t.signal),null!=a&&!j(a))throw TypeError("Expected signal to be an instanceof AbortSignal or EventTarget");let s=null==t.referrer?e.referrer:t.referrer;if(""===s)s="no-referrer";else if(s){let e=new URL(s);s=/^about:(\/\/)?client$/.test(e)?"client":e}else s=void 0;this[er]={method:n,redirect:t.redirect||e.redirect||"follow",headers:o,parsedURL:r,signal:a,referrer:s},this.follow=void 0===t.follow?void 0===e.follow?20:e.follow:t.follow,this.compress=void 0===t.compress?void 0===e.compress||e.compress:t.compress,this.counter=t.counter||e.counter||0,this.agent=t.agent||e.agent,this.highWaterMark=t.highWaterMark||e.highWaterMark||16384,this.insecureHTTPParser=t.insecureHTTPParser||e.insecureHTTPParser||!1,this.referrerPolicy=t.referrerPolicy||e.referrerPolicy||""}get method(){return this[er].method}get url(){return c.format(this[er].parsedURL)}get headers(){return this[er].headers}get redirect(){return this[er].redirect}get signal(){return this[er].signal}get referrer(){return"no-referrer"===this[er].referrer?"":"client"===this[er].referrer?"about:client":this[er].referrer?this[er].referrer.toString():void 0}get referrerPolicy(){return this[er].referrerPolicy}set referrerPolicy(e){this[er].referrerPolicy=function(e){if(!ee.has(e))throw TypeError(`Invalid referrerPolicy: ${e}`);return e}(e)}clone(){return new eo(this)}get[Symbol.toStringTag](){return"Request"}}Object.defineProperties(eo.prototype,{method:{enumerable:!0},url:{enumerable:!0},headers:{enumerable:!0},redirect:{enumerable:!0},clone:{enumerable:!0},signal:{enumerable:!0},referrer:{enumerable:!0},referrerPolicy:{enumerable:!0}});let ea=e=>{let{parsedURL:t}=e[er],r=new W(e[er].headers);r.has("Accept")||r.set("Accept","*/*");let n=null;if(null===e.body&&/^(post|put)$/i.test(e.method)&&(n="0"),null!==e.body){let t=F(e);"number"!=typeof t||Number.isNaN(t)||(n=String(t))}n&&r.set("Content-Length",n),""===e.referrerPolicy&&(e.referrerPolicy="strict-origin-when-cross-origin"),e.referrer&&"no-referrer"!==e.referrer?e[er].referrer=function(e,{referrerURLCallback:t,referrerOriginCallback:r}={}){if("no-referrer"===e.referrer||""===e.referrerPolicy)return null;let n=e.referrerPolicy;if("about:client"===e.referrer)return"no-referrer";let i=e.referrer,o=Z(i),a=Z(i,!0);o.toString().length>4096&&(o=a),t&&(o=t(o)),r&&(a=r(a));let s=new URL(e.url);switch(n){case"no-referrer":return"no-referrer";case"origin":return a;case"unsafe-url":return o;case"strict-origin":if(et(o)&&!et(s))return"no-referrer";return a.toString();case"strict-origin-when-cross-origin":if(o.origin===s.origin)return o;if(et(o)&&!et(s))return"no-referrer";return a;case"same-origin":if(o.origin===s.origin)return o;return"no-referrer";case"origin-when-cross-origin":if(o.origin===s.origin)return o;return a;case"no-referrer-when-downgrade":if(et(o)&&!et(s))return"no-referrer";return o;default:throw TypeError(`Invalid referrerPolicy: ${n}`)}}(e):e[er].referrer="no-referrer",e[er].referrer instanceof URL&&r.set("Referer",e.referrer),r.has("User-Agent")||r.set("User-Agent","node-fetch"),e.compress&&!r.has("Accept-Encoding")&&r.set("Accept-Encoding","gzip, deflate, br");let{agent:i}=e;"function"==typeof i&&(i=i(t)),r.has("Connection")||i||r.set("Connection","close");let o=Q(t),a={path:t.pathname+o,method:e.method,headers:r[Symbol.for("nodejs.util.inspect.custom")](),insecureHTTPParser:e.insecureHTTPParser,agent:i};return{parsedURL:t,options:a}};class es extends x{constructor(e,t="aborted"){super(e,t)}}/*! node-domexception. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> */if(!globalThis.DOMException)try{let{MessageChannel:e}=r(71267),t=new e().port1,n=new ArrayBuffer;t.postMessage(n,[n,n])}catch(e){"DOMException"===e.constructor.name&&(globalThis.DOMException=e.constructor)}var el=globalThis.DOMException;let eu=new Set(["data:","http:","https:"]);async function ec(e,t){return new Promise((r,n)=>{let u=new eo(e,t),{parsedURL:c,options:d}=ea(u);if(!eu.has(c.protocol))throw TypeError(`node-fetch cannot load ${e}. URL scheme "${c.protocol.replace(/:$/,"")}" is not supported.`);if("data:"===c.protocol){let e=function(e){if(!/^data:/i.test(e))throw TypeError('`uri` does not appear to be a Data URI (must begin with "data:")');let t=(e=e.replace(/\r?\n/g,"")).indexOf(",");if(-1===t||t<=4)throw TypeError("malformed data: URI");let r=e.substring(5,t).split(";"),n="",i=!1,o=r[0]||"text/plain",a=o;for(let e=1;e<r.length;e++)"base64"===r[e]?i=!0:(a+=`;${r[e]}`,0===r[e].indexOf("charset=")&&(n=r[e].substring(8)));r[0]||n.length||(a+=";charset=US-ASCII",n="US-ASCII");let s=i?"base64":"ascii",l=unescape(e.substring(t+1)),u=Buffer.from(l,s);return u.type=o,u.typeFull=a,u.charset=n,u}(u.url);r(new X(e,{headers:{"Content-Type":e.typeFull}}));return}let h=("https:"===c.protocol?o:i).request,{signal:f}=u,p=null,g=()=>{let e=new es("The operation was aborted.");n(e),u.body&&u.body instanceof s.Readable&&u.body.destroy(e),p&&p.body&&p.body.emit("error",e)};if(f&&f.aborted){g();return}let y=()=>{g(),b()},m=h(c.toString(),d);f&&f.addEventListener("abort",y);let b=()=>{m.abort(),f&&f.removeEventListener("abort",y)};m.on("error",e=>{n(new O(`request to ${u.url} failed, reason: ${e.message}`,"system",e)),b()}),function(e,t){let r;let n=l.Buffer.from("0\r\n\r\n"),i=!1,o=!1;e.on("response",e=>{let{headers:t}=e;i="chunked"===t["transfer-encoding"]&&!t["content-length"]}),e.on("socket",a=>{let s=()=>{if(i&&!o){let e=Error("Premature close");e.code="ERR_STREAM_PREMATURE_CLOSE",t(e)}},u=e=>{(o=0===l.Buffer.compare(e.slice(-5),n))||!r||(o=0===l.Buffer.compare(r.slice(-3),n.slice(0,3))&&0===l.Buffer.compare(e.slice(-2),n.slice(3))),r=e};a.prependListener("close",s),a.on("data",u),e.on("close",()=>{a.removeListener("close",s),a.removeListener("data",u)})})}(m,e=>{p&&p.body&&p.body.destroy(e)}),process.version<"v14"&&m.on("socket",e=>{let t;e.prependListener("end",()=>{t=e._eventsCount}),e.prependListener("close",r=>{if(p&&t<e._eventsCount&&!r){let e=Error("Premature close");e.code="ERR_STREAM_PREMATURE_CLOSE",p.body.emit("error",e)}})}),m.on("response",e=>{m.setTimeout(0);let i=function(e=[]){return new W(e.reduce((e,t,r,n)=>(r%2==0&&e.push(n.slice(r,r+2)),e),[]).filter(([e,t])=>{try{return q(e),V(e,String(t)),!0}catch{return!1}}))}(e.rawHeaders);if(J(e.statusCode)){let o=i.get("Location"),a=null;try{a=null===o?null:new URL(o,u.url)}catch{if("manual"!==u.redirect){n(new O(`uri requested responds with an invalid redirect URL: ${o}`,"invalid-redirect")),b();return}}switch(u.redirect){case"error":n(new O(`uri requested responds with a redirect, redirect mode is set to error: ${u.url}`,"no-redirect")),b();return;case"manual":break;case"follow":{if(null===a)break;if(u.counter>=u.follow){n(new O(`maximum redirect reached at: ${u.url}`,"max-redirect")),b();return}let o={headers:new W(u.headers),follow:u.follow,counter:u.counter+1,agent:u.agent,compress:u.compress,method:u.method,body:D(u),signal:u.signal,size:u.size,referrer:u.referrer,referrerPolicy:u.referrerPolicy};if(!U(u.url,a)||!N(u.url,a))for(let e of["authorization","www-authenticate","cookie","cookie2"])o.headers.delete(e);if(303!==e.statusCode&&u.body&&t.body instanceof s.Readable){n(new O("Cannot follow redirect with body being a readable stream","unsupported-redirect")),b();return}(303===e.statusCode||(301===e.statusCode||302===e.statusCode)&&"POST"===u.method)&&(o.method="GET",o.body=void 0,o.headers.delete("content-length"));let l=function(e){let t=(e.get("referrer-policy")||"").split(/[,\s]+/),r="";for(let e of t)e&&ee.has(e)&&(r=e);return r}(i);l&&(o.referrerPolicy=l),r(ec(new eo(a,o))),b();return}default:return n(TypeError(`Redirect option '${u.redirect}' is not a valid value of RequestRedirect`))}}f&&e.once("end",()=>{f.removeEventListener("abort",y)});let o=s.pipeline(e,new s.PassThrough,e=>{e&&n(e)});process.version<"v12.10"&&e.on("aborted",y);let l={url:u.url,status:e.statusCode,statusText:e.statusMessage,headers:i,size:u.size,counter:u.counter,highWaterMark:u.highWaterMark},c=i.get("Content-Encoding");if(!u.compress||"HEAD"===u.method||null===c||204===e.statusCode||304===e.statusCode){r(p=new X(o,l));return}let d={flush:a.Z_SYNC_FLUSH,finishFlush:a.Z_SYNC_FLUSH};if("gzip"===c||"x-gzip"===c){r(p=new X(o=s.pipeline(o,a.createGunzip(d),e=>{e&&n(e)}),l));return}if("deflate"===c||"x-deflate"===c){let t=s.pipeline(e,new s.PassThrough,e=>{e&&n(e)});t.once("data",e=>{r(p=new X(o=(15&e[0])==8?s.pipeline(o,a.createInflate(),e=>{e&&n(e)}):s.pipeline(o,a.createInflateRaw(),e=>{e&&n(e)}),l))}),t.once("end",()=>{p||r(p=new X(o,l))});return}if("br"===c){r(p=new X(o=s.pipeline(o,a.createBrotliDecompress(),e=>{e&&n(e)}),l));return}r(p=new X(o,l))}),$(m,u).catch(n)})}/**
 * <AUTHOR> Nagashima <https://github.com/mysticatea>
 * @copyright 2015 Toru Nagashima. All rights reserved.
 * See LICENSE file in root directory for full license.
 */let ed=new WeakMap,eh=new WeakMap;function ef(e){let t=ed.get(e);return console.assert(null!=t,"'this' is expected an Event object, but got",e),t}function ep(e){if(null!=e.passiveListener){"undefined"!=typeof console&&"function"==typeof console.error&&console.error("Unable to preventDefault inside passive event listener invocation.",e.passiveListener);return}e.event.cancelable&&(e.canceled=!0,"function"==typeof e.event.preventDefault&&e.event.preventDefault())}function eg(e,t){ed.set(this,{eventTarget:e,event:t,eventPhase:2,currentTarget:e,canceled:!1,stopped:!1,immediateStopped:!1,passiveListener:null,timeStamp:t.timeStamp||Date.now()}),Object.defineProperty(this,"isTrusted",{value:!1,enumerable:!0});let r=Object.keys(t);for(let e=0;e<r.length;++e){let t=r[e];t in this||Object.defineProperty(this,t,ey(t))}}function ey(e){return{get(){return ef(this).event[e]},set(t){ef(this).event[e]=t},configurable:!0,enumerable:!0}}function em(e,t){ef(e).passiveListener=t}eg.prototype={get type(){return ef(this).event.type},get target(){return ef(this).eventTarget},get currentTarget(){return ef(this).currentTarget},composedPath(){let e=ef(this).currentTarget;return null==e?[]:[e]},get NONE(){return 0},get CAPTURING_PHASE(){return 1},get AT_TARGET(){return 2},get BUBBLING_PHASE(){return 3},get eventPhase(){return ef(this).eventPhase},stopPropagation(){let e=ef(this);e.stopped=!0,"function"==typeof e.event.stopPropagation&&e.event.stopPropagation()},stopImmediatePropagation(){let e=ef(this);e.stopped=!0,e.immediateStopped=!0,"function"==typeof e.event.stopImmediatePropagation&&e.event.stopImmediatePropagation()},get bubbles(){return!!ef(this).event.bubbles},get cancelable(){return!!ef(this).event.cancelable},preventDefault(){ep(ef(this))},get defaultPrevented(){return ef(this).canceled},get composed(){return!!ef(this).event.composed},get timeStamp(){return ef(this).timeStamp},get srcElement(){return ef(this).eventTarget},get cancelBubble(){return ef(this).stopped},set cancelBubble(value){if(!value)return;let e=ef(this);e.stopped=!0,"boolean"==typeof e.event.cancelBubble&&(e.event.cancelBubble=!0)},get returnValue(){return!ef(this).canceled},set returnValue(value){value||ep(ef(this))},initEvent(){}},Object.defineProperty(eg.prototype,"constructor",{value:eg,configurable:!0,writable:!0});let eb=new WeakMap;function ev(e){return null!==e&&"object"==typeof e}function ew(e){let t=eb.get(e);if(null==t)throw TypeError("'this' is expected an EventTarget object, but got another value.");return t}function eS(e,t){Object.defineProperty(e,`on${t}`,{get(){let e=ew(this).get(t);for(;null!=e;){if(3===e.listenerType)return e.listener;e=e.next}return null},set(e){"function"==typeof e||ev(e)||(e=null);let r=ew(this),n=null,i=r.get(t);for(;null!=i;)3===i.listenerType?null!==n?n.next=i.next:null!==i.next?r.set(t,i.next):r.delete(t):n=i,i=i.next;if(null!==e){let i={listener:e,listenerType:3,passive:!1,once:!1,next:null};null===n?r.set(t,i):n.next=i}},configurable:!0,enumerable:!0})}function e_(e){function t(){ek.call(this)}t.prototype=Object.create(ek.prototype,{constructor:{value:t,configurable:!0,writable:!0}});for(let r=0;r<e.length;++r)eS(t.prototype,e[r]);return t}function ek(){if(this instanceof ek){eb.set(this,new Map);return}if(1==arguments.length&&Array.isArray(arguments[0]))return e_(arguments[0]);if(arguments.length>0){let e=Array(arguments.length);for(let t=0;t<arguments.length;++t)e[t]=arguments[t];return e_(e)}throw TypeError("Cannot call a class as a function")}ek.prototype={addEventListener(e,t,r){if(null==t)return;if("function"!=typeof t&&!ev(t))throw TypeError("'listener' should be a function or an object.");let n=ew(this),i=ev(r),o=(i?r.capture:r)?1:2,a={listener:t,listenerType:o,passive:i&&!!r.passive,once:i&&!!r.once,next:null},s=n.get(e);if(void 0===s){n.set(e,a);return}let l=null;for(;null!=s;){if(s.listener===t&&s.listenerType===o)return;l=s,s=s.next}l.next=a},removeEventListener(e,t,r){if(null==t)return;let n=ew(this),i=(ev(r)?r.capture:r)?1:2,o=null,a=n.get(e);for(;null!=a;){if(a.listener===t&&a.listenerType===i){null!==o?o.next=a.next:null!==a.next?n.set(e,a.next):n.delete(e);return}o=a,a=a.next}},dispatchEvent(e){if(null==e||"string"!=typeof e.type)throw TypeError('"event.type" should be a string.');let t=ew(this),r=e.type,n=t.get(r);if(null==n)return!0;let i=new(function e(t){if(null==t||t===Object.prototype)return eg;let r=eh.get(t);return null==r&&(r=function(e,t){let r=Object.keys(t);if(0===r.length)return e;function n(t,r){e.call(this,t,r)}n.prototype=Object.create(e.prototype,{constructor:{value:n,configurable:!0,writable:!0}});for(let i=0;i<r.length;++i){let o=r[i];if(!(o in e.prototype)){let e="function"==typeof Object.getOwnPropertyDescriptor(t,o).value;Object.defineProperty(n.prototype,o,e?function(e){return{value(){let t=ef(this).event;return t[e].apply(t,arguments)},configurable:!0,enumerable:!0}}(o):ey(o))}}return n}(e(Object.getPrototypeOf(t)),t),eh.set(t,r)),r}(Object.getPrototypeOf(e)))(this,e),o=null;for(;null!=n;){if(n.once?null!==o?o.next=n.next:null!==n.next?t.set(r,n.next):t.delete(r):o=n,em(i,n.passive?n.listener:null),"function"==typeof n.listener)try{n.listener.call(this,i)}catch(e){"undefined"!=typeof console&&"function"==typeof console.error&&console.error(e)}else 3!==n.listenerType&&"function"==typeof n.listener.handleEvent&&n.listener.handleEvent(i);if(ef(i).immediateStopped)break;n=n.next}return em(i,null),ef(i).eventPhase=0,ef(i).currentTarget=null,!i.defaultPrevented}},Object.defineProperty(ek.prototype,"constructor",{value:ek,configurable:!0,writable:!0});class eE extends ek{constructor(){throw super(),TypeError("AbortSignal cannot be constructed directly")}get aborted(){let e=eA.get(this);if("boolean"!=typeof e)throw TypeError(`Expected 'this' to be an 'AbortSignal' object, but got ${this===null?"null":typeof this}`);return e}}eS(eE.prototype,"abort");let eA=new WeakMap;Object.defineProperties(eE.prototype,{aborted:{enumerable:!0}}),"function"==typeof Symbol&&"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(eE.prototype,Symbol.toStringTag,{configurable:!0,value:"AbortSignal"});class eP{constructor(){eC.set(this,function(){let e=Object.create(eE.prototype);return ek.call(e),eA.set(e,!1),e}())}get signal(){return ex(this)}abort(){var e;e=ex(this),!1===eA.get(e)&&(eA.set(e,!0),e.dispatchEvent({type:"abort"}))}}let eC=new WeakMap;function ex(e){let t=eC.get(e);if(null==t)throw TypeError(`Expected 'this' to be an 'AbortController' object, but got ${null===e?"null":typeof e}`);return t}Object.defineProperties(eP.prototype,{signal:{enumerable:!0},abort:{enumerable:!0}}),"function"==typeof Symbol&&"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(eP.prototype,Symbol.toStringTag,{configurable:!0,value:"AbortController"}),t.AbortController=eP,t.AbortError=es,t.FetchError=O,t.File=b,t.FormData=C,t.Headers=W,t.Request=eo,t.Response=X,t._Blob=m,t.fetch=ec,t.isRedirect=J,t.nodeDomexception=el},41434:(e,t,r)=>{"use strict";let n=r(2218);function i(e,t){return n.fetch(e,t)}for(let e in n)i[e]=n[e];e.exports=i},69996:(e,t,r)=>{"use strict";function n(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}r.r(t),r.d(t,{_:()=>n,_class_private_field_loose_base:()=>n})},67074:(e,t,r)=>{"use strict";r.r(t),r.d(t,{_:()=>i,_class_private_field_loose_key:()=>i});var n=0;function i(e){return"__private_"+n+++"_"+e}},39694:(e,t,r)=>{"use strict";function n(e){return e&&e.__esModule?e:{default:e}}r.r(t),r.d(t,{_:()=>n,_interop_require_default:()=>n})},17824:(e,t,r)=>{"use strict";function n(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(n=function(e){return e?r:t})(e)}function i(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=n(t);if(r&&r.has(e))return r.get(e);var i={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var s=o?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(i,a,s):i[a]=e[a]}return i.default=e,r&&r.set(e,i),i}r.r(t),r.d(t,{_:()=>i,_interop_require_wildcard:()=>i})},34755:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Toaster:()=>b,toast:()=>g});var n=r(3729),i=r(81202);!function(e,{insertAt:t}={}){if(!e||"undefined"==typeof document)return;let r=document.head||document.getElementsByTagName("head")[0],n=document.createElement("style");n.type="text/css","top"===t&&r.firstChild?r.insertBefore(n,r.firstChild):r.appendChild(n),n.styleSheet?n.styleSheet.cssText=e:n.appendChild(document.createTextNode(e))}(`html[dir=ltr],[data-sonner-toaster][dir=ltr]{--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}html[dir=rtl],[data-sonner-toaster][dir=rtl]{--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}[data-sonner-toaster]{position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999}[data-sonner-toaster][data-x-position=right]{right:max(var(--offset),env(safe-area-inset-right))}[data-sonner-toaster][data-x-position=left]{left:max(var(--offset),env(safe-area-inset-left))}[data-sonner-toaster][data-x-position=center]{left:50%;transform:translate(-50%)}[data-sonner-toaster][data-y-position=top]{top:max(var(--offset),env(safe-area-inset-top))}[data-sonner-toaster][data-y-position=bottom]{bottom:max(var(--offset),env(safe-area-inset-bottom))}[data-sonner-toast]{--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);touch-action:none;will-change:transform,opacity,height;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}[data-sonner-toast][data-styled=true]{padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}[data-sonner-toast]:focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}[data-sonner-toast][data-y-position=top]{top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}[data-sonner-toast][data-y-position=bottom]{bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}[data-sonner-toast] [data-description]{font-weight:400;line-height:1.4;color:inherit}[data-sonner-toast] [data-title]{font-weight:500;line-height:1.5;color:inherit}[data-sonner-toast] [data-icon]{display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}[data-sonner-toast][data-promise=true] [data-icon]>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}[data-sonner-toast] [data-icon]>*{flex-shrink:0}[data-sonner-toast] [data-icon] svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}[data-sonner-toast] [data-content]{display:flex;flex-direction:column;gap:2px}[data-sonner-toast] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}[data-sonner-toast] [data-button]:focus-visible{box-shadow:0 0 0 2px #0006}[data-sonner-toast] [data-button]:first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}[data-sonner-toast] [data-cancel]{color:var(--normal-text);background:rgba(0,0,0,.08)}[data-sonner-toast][data-theme=dark] [data-cancel]{background:rgba(255,255,255,.3)}[data-sonner-toast] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;background:var(--gray1);color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast] [data-close-button]:focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}[data-sonner-toast] [data-disabled=true]{cursor:not-allowed}[data-sonner-toast]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}[data-sonner-toast][data-swiping=true]:before{content:"";position:absolute;left:0;right:0;height:100%;z-index:-1}[data-sonner-toast][data-y-position=top][data-swiping=true]:before{bottom:50%;transform:scaleY(3) translateY(50%)}[data-sonner-toast][data-y-position=bottom][data-swiping=true]:before{top:50%;transform:scaleY(3) translateY(-50%)}[data-sonner-toast][data-swiping=false][data-removed=true]:before{content:"";position:absolute;inset:0;transform:scaleY(2)}[data-sonner-toast]:after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}[data-sonner-toast][data-mounted=true]{--y: translateY(0);opacity:1}[data-sonner-toast][data-expanded=false][data-front=false]{--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}[data-sonner-toast]>*{transition:opacity .4s}[data-sonner-toast][data-expanded=false][data-front=false][data-styled=true]>*{opacity:0}[data-sonner-toast][data-visible=false]{opacity:0;pointer-events:none}[data-sonner-toast][data-mounted=true][data-expanded=true]{--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}[data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]{--y: translateY(calc(var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]{--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]{--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}[data-sonner-toast][data-removed=true][data-front=false]:before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount, 0px));transition:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation:swipe-out .2s ease-out forwards}@keyframes swipe-out{0%{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount)));opacity:1}to{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount) + var(--lift) * -100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;--mobile-offset: 16px;right:var(--mobile-offset);left:var(--mobile-offset);width:100%}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - 32px)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset)}[data-sonner-toaster][data-y-position=bottom]{bottom:20px}[data-sonner-toaster][data-y-position=top]{top:20px}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset);right:var(--mobile-offset);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-rich-colors=true] [data-sonner-toast][data-type=success],[data-rich-colors=true] [data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true] [data-sonner-toast][data-type=info],[data-rich-colors=true] [data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true] [data-sonner-toast][data-type=warning],[data-rich-colors=true] [data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true] [data-sonner-toast][data-type=error],[data-rich-colors=true] [data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}
`);var o=e=>{switch(e){case"success":return l;case"info":return c;case"warning":return u;case"error":return d;default:return null}},a=Array(12).fill(0),s=({visible:e})=>n.createElement("div",{className:"sonner-loading-wrapper","data-visible":e},n.createElement("div",{className:"sonner-spinner"},a.map((e,t)=>n.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${t}`})))),l=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),u=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),c=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),d=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),h=()=>{let[e,t]=n.useState(!1);return n.useEffect(()=>{let e=()=>{t(document.hidden)};return document.addEventListener("visibilitychange",e),()=>window.removeEventListener("visibilitychange",e)},[]),e},f=1,p=new class{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}),this.publish=e=>{this.subscribers.forEach(t=>t(e))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var t;let{message:r,...n}=e,i="number"==typeof(null==e?void 0:e.id)||(null==(t=e.id)?void 0:t.length)>0?e.id:f++,o=this.toasts.find(e=>e.id===i),a=void 0===e.dismissible||e.dismissible;return o?this.toasts=this.toasts.map(t=>t.id===i?(this.publish({...t,...e,id:i,title:r}),{...t,...e,id:i,dismissible:a,title:r}):t):this.addToast({title:r,...n,dismissible:a,id:i}),i},this.dismiss=e=>(e||this.toasts.forEach(e=>{this.subscribers.forEach(t=>t({id:e.id,dismiss:!0}))}),this.subscribers.forEach(t=>t({id:e,dismiss:!0})),e),this.message=(e,t)=>this.create({...t,message:e}),this.error=(e,t)=>this.create({...t,message:e,type:"error"}),this.success=(e,t)=>this.create({...t,type:"success",message:e}),this.info=(e,t)=>this.create({...t,type:"info",message:e}),this.warning=(e,t)=>this.create({...t,type:"warning",message:e}),this.loading=(e,t)=>this.create({...t,type:"loading",message:e}),this.promise=(e,t)=>{let r;if(!t)return;void 0!==t.loading&&(r=this.create({...t,promise:e,type:"loading",message:t.loading,description:"function"!=typeof t.description?t.description:void 0}));let n=e instanceof Promise?e:e(),i=void 0!==r;return n.then(e=>{if(e&&"boolean"==typeof e.ok&&!e.ok){i=!1;let n="function"==typeof t.error?t.error(`HTTP error! status: ${e.status}`):t.error,o="function"==typeof t.description?t.description(`HTTP error! status: ${e.status}`):t.description;this.create({id:r,type:"error",message:n,description:o})}else if(void 0!==t.success){i=!1;let n="function"==typeof t.success?t.success(e):t.success,o="function"==typeof t.description?t.description(e):t.description;this.create({id:r,type:"success",message:n,description:o})}}).catch(e=>{if(void 0!==t.error){i=!1;let n="function"==typeof t.error?t.error(e):t.error,o="function"==typeof t.description?t.description(e):t.description;this.create({id:r,type:"error",message:n,description:o})}}).finally(()=>{var e;i&&(this.dismiss(r),r=void 0),null==(e=t.finally)||e.call(t)}),r},this.custom=(e,t)=>{let r=(null==t?void 0:t.id)||f++;return this.create({jsx:e(r),id:r,...t}),r},this.subscribers=[],this.toasts=[]}},g=Object.assign((e,t)=>{let r=(null==t?void 0:t.id)||f++;return p.addToast({title:e,...t,id:r}),r},{success:p.success,info:p.info,warning:p.warning,error:p.error,custom:p.custom,message:p.message,promise:p.promise,dismiss:p.dismiss,loading:p.loading});function y(...e){return e.filter(Boolean).join(" ")}var m=e=>{var t,r,i,a,l,u,c;let{invert:d,toast:f,unstyled:p,interacting:g,setHeights:m,visibleToasts:b,heights:v,index:w,toasts:S,expanded:_,removeToast:k,closeButton:E,style:A,cancelButtonStyle:P,actionButtonStyle:C,className:x="",descriptionClassName:O="",duration:R,position:T,gap:B=14,loadingIcon:j,expandByDefault:U,classNames:N,closeButtonAriaLabel:I="Close toast",pauseWhenPageIsHidden:M}=e,[L,K]=n.useState(!1),[D,z]=n.useState(!1),[H,F]=n.useState(!1),[$,q]=n.useState(!1),[V,W]=n.useState(0),[G,J]=n.useState(0),Y=n.useRef(null),X=n.useRef(null),Q=0===w,Z=w+1<=b,ee=f.type,et=!1!==f.dismissible,er=f.className||"",en=f.descriptionClassName||"",ei=n.useMemo(()=>v.findIndex(e=>e.toastId===f.id)||0,[v,f.id]),eo=n.useMemo(()=>{var e;return null!=(e=f.closeButton)?e:E},[f.closeButton,E]),ea=n.useMemo(()=>f.duration||R||4e3,[f.duration,R]),es=n.useRef(0),el=n.useRef(0),eu=n.useRef(0),ec=n.useRef(null),[ed,eh]=T.split("-"),ef=n.useMemo(()=>v.reduce((e,t,r)=>r>=ei?e:e+t.height,0),[v,ei]),ep=h(),eg=f.invert||d,ey="loading"===ee;el.current=n.useMemo(()=>ei*B+ef,[ei,ef]),n.useEffect(()=>{K(!0)},[]),n.useLayoutEffect(()=>{if(!L)return;let e=X.current,t=e.style.height;e.style.height="auto";let r=e.getBoundingClientRect().height;e.style.height=t,J(r),m(e=>e.find(e=>e.toastId===f.id)?e.map(e=>e.toastId===f.id?{...e,height:r}:e):[{toastId:f.id,height:r,position:f.position},...e])},[L,f.title,f.description,m,f.id]);let em=n.useCallback(()=>{z(!0),W(el.current),m(e=>e.filter(e=>e.toastId!==f.id)),setTimeout(()=>{k(f)},200)},[f,k,m,el]);return n.useEffect(()=>{if(f.promise&&"loading"===ee||f.duration===1/0||"loading"===f.type)return;let e,t=ea;return _||g||M&&ep?(()=>{if(eu.current<es.current){let e=new Date().getTime()-es.current;t-=e}eu.current=new Date().getTime()})():(es.current=new Date().getTime(),e=setTimeout(()=>{var e;null==(e=f.onAutoClose)||e.call(f,f),em()},t)),()=>clearTimeout(e)},[_,g,U,f,ea,em,f.promise,ee,M,ep]),n.useEffect(()=>{let e=X.current;if(e){let t=e.getBoundingClientRect().height;return J(t),m(e=>[{toastId:f.id,height:t,position:f.position},...e]),()=>m(e=>e.filter(e=>e.toastId!==f.id))}},[m,f.id]),n.useEffect(()=>{f.delete&&em()},[em,f.delete]),n.createElement("li",{"aria-live":f.important?"assertive":"polite","aria-atomic":"true",role:"status",tabIndex:0,ref:X,className:y(x,er,null==N?void 0:N.toast,null==(t=null==f?void 0:f.classNames)?void 0:t.toast,null==N?void 0:N[ee],null==(r=null==f?void 0:f.classNames)?void 0:r[ee]),"data-sonner-toast":"","data-styled":!(f.jsx||f.unstyled||p),"data-mounted":L,"data-promise":!!f.promise,"data-removed":D,"data-visible":Z,"data-y-position":ed,"data-x-position":eh,"data-index":w,"data-front":Q,"data-swiping":H,"data-dismissible":et,"data-type":ee,"data-invert":eg,"data-swipe-out":$,"data-expanded":!!(_||U&&L),style:{"--index":w,"--toasts-before":w,"--z-index":S.length-w,"--offset":`${D?V:el.current}px`,"--initial-height":U?"auto":`${G}px`,...A,...f.style},onPointerDown:e=>{ey||!et||(Y.current=new Date,W(el.current),e.target.setPointerCapture(e.pointerId),"BUTTON"!==e.target.tagName&&(F(!0),ec.current={x:e.clientX,y:e.clientY}))},onPointerUp:()=>{var e,t,r,n;if($||!et)return;ec.current=null;let i=Number((null==(e=X.current)?void 0:e.style.getPropertyValue("--swipe-amount").replace("px",""))||0),o=new Date().getTime()-(null==(t=Y.current)?void 0:t.getTime());if(Math.abs(i)>=20||Math.abs(i)/o>.11){W(el.current),null==(r=f.onDismiss)||r.call(f,f),em(),q(!0);return}null==(n=X.current)||n.style.setProperty("--swipe-amount","0px"),F(!1)},onPointerMove:e=>{var t;if(!ec.current||!et)return;let r=e.clientY-ec.current.y,n=e.clientX-ec.current.x,i=("top"===ed?Math.min:Math.max)(0,r),o="touch"===e.pointerType?10:2;Math.abs(i)>o?null==(t=X.current)||t.style.setProperty("--swipe-amount",`${r}px`):Math.abs(n)>o&&(ec.current=null)}},eo&&!f.jsx?n.createElement("button",{"aria-label":I,"data-disabled":ey,"data-close-button":!0,onClick:ey||!et?()=>{}:()=>{var e;em(),null==(e=f.onDismiss)||e.call(f,f)},className:y(null==N?void 0:N.closeButton,null==(i=null==f?void 0:f.classNames)?void 0:i.closeButton)},n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},n.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),n.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"}))):null,f.jsx||n.isValidElement(f.title)?f.jsx||f.title:n.createElement(n.Fragment,null,ee||f.icon||f.promise?n.createElement("div",{"data-icon":""},(f.promise||"loading"===f.type)&&!f.icon?j?n.createElement("div",{className:"sonner-loader","data-visible":"loading"===ee},j):n.createElement(s,{visible:"loading"===ee}):null,f.icon||o(ee)):null,n.createElement("div",{"data-content":""},n.createElement("div",{"data-title":"",className:y(null==N?void 0:N.title,null==(a=null==f?void 0:f.classNames)?void 0:a.title)},f.title),f.description?n.createElement("div",{"data-description":"",className:y(O,en,null==N?void 0:N.description,null==(l=null==f?void 0:f.classNames)?void 0:l.description)},f.description):null),f.cancel?n.createElement("button",{"data-button":!0,"data-cancel":!0,style:f.cancelButtonStyle||P,onClick:e=>{var t;et&&(em(),null!=(t=f.cancel)&&t.onClick&&f.cancel.onClick(e))},className:y(null==N?void 0:N.cancelButton,null==(u=null==f?void 0:f.classNames)?void 0:u.cancelButton)},f.cancel.label):null,f.action?n.createElement("button",{"data-button":"",style:f.actionButtonStyle||C,onClick:e=>{var t;null==(t=f.action)||t.onClick(e),e.defaultPrevented||em()},className:y(null==N?void 0:N.actionButton,null==(c=null==f?void 0:f.classNames)?void 0:c.actionButton)},f.action.label):null))},b=e=>{let{invert:t,position:r="bottom-right",hotkey:o=["altKey","KeyT"],expand:a,closeButton:s,className:l,offset:u,theme:c="light",richColors:d,duration:h,style:f,visibleToasts:g=3,toastOptions:y,dir:b="ltr",gap:v,loadingIcon:w,containerAriaLabel:S="Notifications",pauseWhenPageIsHidden:_}=e,[k,E]=n.useState([]),A=n.useMemo(()=>Array.from(new Set([r].concat(k.filter(e=>e.position).map(e=>e.position)))),[k,r]),[P,C]=n.useState([]),[x,O]=n.useState(!1),[R,T]=n.useState(!1),[B,j]=n.useState("system"!==c?c:"light"),U=n.useRef(null),N=o.join("+").replace(/Key/g,"").replace(/Digit/g,""),I=n.useRef(null),M=n.useRef(!1),L=n.useCallback(e=>E(t=>t.filter(({id:t})=>t!==e.id)),[]);return n.useEffect(()=>p.subscribe(e=>{if(e.dismiss){E(t=>t.map(t=>t.id===e.id?{...t,delete:!0}:t));return}setTimeout(()=>{i.flushSync(()=>{E(t=>{let r=t.findIndex(t=>t.id===e.id);return -1!==r?[...t.slice(0,r),{...t[r],...e},...t.slice(r+1)]:[e,...t]})})})}),[]),n.useEffect(()=>{if("system"!==c){j(c);return}"system"===c&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?j("dark"):j("light"))},[c]),n.useEffect(()=>{k.length<=1&&O(!1)},[k]),n.useEffect(()=>{let e=e=>{var t,r;o.every(t=>e[t]||e.code===t)&&(O(!0),null==(t=U.current)||t.focus()),"Escape"===e.code&&(document.activeElement===U.current||null!=(r=U.current)&&r.contains(document.activeElement))&&O(!1)};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[o]),n.useEffect(()=>{if(U.current)return()=>{I.current&&(I.current.focus({preventScroll:!0}),I.current=null,M.current=!1)}},[U.current]),k.length?n.createElement("section",{"aria-label":`${S} ${N}`,tabIndex:-1},A.map((e,r)=>{var i;let[o,c]=e.split("-");return n.createElement("ol",{key:e,dir:"auto"===b?"ltr":b,tabIndex:-1,ref:U,className:l,"data-sonner-toaster":!0,"data-theme":B,"data-rich-colors":d,"data-y-position":o,"data-x-position":c,style:{"--front-toast-height":`${null==(i=P[0])?void 0:i.height}px`,"--offset":"number"==typeof u?`${u}px`:u||"32px","--width":"356px","--gap":"14px",...f},onBlur:e=>{M.current&&!e.currentTarget.contains(e.relatedTarget)&&(M.current=!1,I.current&&(I.current.focus({preventScroll:!0}),I.current=null))},onFocus:e=>{e.target instanceof HTMLElement&&"false"===e.target.dataset.dismissible||M.current||(M.current=!0,I.current=e.relatedTarget)},onMouseEnter:()=>O(!0),onMouseMove:()=>O(!0),onMouseLeave:()=>{R||O(!1)},onPointerDown:e=>{e.target instanceof HTMLElement&&"false"===e.target.dataset.dismissible||T(!0)},onPointerUp:()=>T(!1)},k.filter(t=>!t.position&&0===r||t.position===e).map((r,i)=>{var o,l;return n.createElement(m,{key:r.id,index:i,toast:r,duration:null!=(o=null==y?void 0:y.duration)?o:h,className:null==y?void 0:y.className,descriptionClassName:null==y?void 0:y.descriptionClassName,invert:t,visibleToasts:g,closeButton:null!=(l=null==y?void 0:y.closeButton)?l:s,interacting:R,position:e,style:null==y?void 0:y.style,unstyled:null==y?void 0:y.unstyled,classNames:null==y?void 0:y.classNames,cancelButtonStyle:null==y?void 0:y.cancelButtonStyle,actionButtonStyle:null==y?void 0:y.actionButtonStyle,removeToast:L,toasts:k.filter(e=>e.position==r.position),heights:P.filter(e=>e.position==r.position),setHeights:C,expandByDefault:a,gap:v,loadingIcon:w,expanded:x,pauseWhenPageIsHidden:_})}))})):null}},17801:(e,t,r)=>{"use strict";r.d(t,{Mr:()=>s,x6:()=>l,x9:()=>a});var n=()=>!1,i=()=>{try{return!0}catch(e){}return!1},o=new Set,a=(e,t,r)=>{let a=n()||i(),s=r??e;o.has(s)||a||(o.add(s),console.warn(`Clerk - DEPRECATION WARNING: "${e}" is deprecated and will be removed in the next major release.
${t}`))},s=(e,t,r,n=!1)=>{let i=n?e:e.prototype,o=i[t];Object.defineProperty(i,t,{get:()=>(a(t,r,`${e.name}:${t}`),o),set(e){o=e}})},l=(e,t,r,n)=>{let i=e[t];Object.defineProperty(e,t,{get:()=>(a(t,r,n),i),set(e){i=e}})}},9124:(e,t,r)=>{"use strict";Object.getOwnPropertyDescriptor,Object.getOwnPropertyNames,Object.prototype.hasOwnProperty},83614:(e,t,r)=>{"use strict";r.d(t,{Mr:()=>n.Mr,x6:()=>n.x6,x9:()=>n.x9});var n=r(17801);r(9124)},46783:(e,t,r)=>{"use strict";function n(e){return e&&e.__esModule?e:{default:e}}r.r(t),r.d(t,{_:()=>n,_interop_require_default:()=>n})},57519:(e,t,r)=>{"use strict";function n(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(n=function(e){return e?r:t})(e)}function i(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=n(t);if(r&&r.has(e))return r.get(e);var i={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var s=o?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(i,a,s):i[a]=e[a]}return i.default=e,r&&r.set(e,i),i}r.r(t),r.d(t,{_:()=>i,_interop_require_wildcard:()=>i})},27171:(e,t,r)=>{"use strict";r.d(t,{x:()=>s});var n=r(86843);let i=(0,n.createProxy)(String.raw`/Users/<USER>/Downloads/Coding/IBC/Group Creator/node_modules/sonner/dist/index.mjs`),{__esModule:o,$$typeof:a}=i;i.default;let s=(0,n.createProxy)(String.raw`/Users/<USER>/Downloads/Coding/IBC/Group Creator/node_modules/sonner/dist/index.mjs#Toaster`);(0,n.createProxy)(String.raw`/Users/<USER>/Downloads/Coding/IBC/Group Creator/node_modules/sonner/dist/index.mjs#toast`)}};