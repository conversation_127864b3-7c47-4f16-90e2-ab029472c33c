(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[335],{1693:function(e,t,n){"use strict";n.d(t,{vn:function(){return nh},a7:function(){return ni},qI:function(){return na},El:function(){return tT},Gp:function(){return t3},_L:function(){return N},X1:function(){return R},KM:function(){return np},Bg:function(){return t8},A:function(){return t4},Li:function(){return t9},Cv:function(){return no},gM:function(){return nc},yB:function(){return nd},N1:function(){return ns},C2:function(){return nl},sO:function(){return nu},cL:function(){return tJ},$d:function(){return nw},qu:function(){return nC},AM:function(){return nS},Mo:function(){return t$},gX:function(){return nE},CH:function(){return nn},tj:function(){return nr},l8:function(){return t1},Iw:function(){return t0},_E:function(){return tq},CJ:function(){return nv},Gi:function(){return ng},kD:function(){return U},G1:function(){return T},sZ:function(){return _},V9:function(){return I},ZC:function(){return L},aC:function(){return nt},ll:function(){return nz},E2:function(){return nj},jS:function(){return nL},o8:function(){return tk},eW:function(){return tS},qi:function(){return tC},kP:function(){return nx},xo:function(){return nU},zq:function(){return nO},QS:function(){return n_},aF:function(){return nP},r0:function(){return tH},NA:function(){return nm},ns:function(){return nf}});var r,i,a,o,s,l,u,d,c,h={};n.r(h),n.d(h,{SWRConfig:function(){return e8},default:function(){return e7},mutate:function(){return eH},preload:function(){return e1},unstable_serialize:function(){return e4},useSWRConfig:function(){return e0}}),n(218);var p=e=>"undefined"!=typeof atob&&"function"==typeof atob?atob(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e,"base64").toString():e,f="pk_live_";function g(e){if(!m(e=e||""))return null;let t=e.startsWith(f)?"production":"development",n=p(e.split("_")[2]);return n.endsWith("$")?{instanceType:t,frontendApi:n=n.slice(0,-1)}:null}function m(e){let t=(e=e||"").startsWith(f)||e.startsWith("pk_test_"),n=p(e.split("_")[2]||"").endsWith("$");return t&&n}var v=Object.defineProperty,b=Object.getOwnPropertyDescriptor,y=Object.getOwnPropertyNames,k=Object.prototype.hasOwnProperty,w=(e,t,n,r)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let i of y(t))k.call(e,i)||i===n||v(e,i,{get:()=>t[i],enumerable:!(r=b(t,i))||r.enumerable});return e},E=n(4090),S=()=>!1,C=()=>!1,P=()=>{try{return!0}catch(e){}return!1},x=new Set,z=(e,t,n)=>{let r=C()||P(),i=null!=n?n:e;x.has(i)||r||(x.add(i),console.warn('Clerk - DEPRECATION WARNING: "'.concat(e,'" is deprecated and will be removed in the next major release.\n').concat(t)))},O=(e,t,n,r)=>{let i=e[t];Object.defineProperty(e,t,{get:()=>(z(t,n,r),i),set(e){i=e}})};function _(e){return U(e)||L(e)||"clerkRuntimeError"in e}function U(e){return"clerkError"in e}function L(e){return"code"in e&&[4001,32602,32603].includes(e.code)&&"message"in e}var j=class e extends Error{constructor(t){super(t),this.code=t,Object.setPrototypeOf(this,e.prototype),z("MagicLinkError","Use `EmailLinkError` instead.")}},M=class e extends Error{constructor(t){super(t),this.code=t,Object.setPrototypeOf(this,e.prototype)}};function I(e){return z("isMagicLinkError","Use `isEmailLinkError` instead."),e instanceof j}function T(e){return e instanceof M}var R=new Proxy({Expired:"expired",Failed:"failed"},{get:(e,t,n)=>(z("MagicLinkErrorCode","Use `EmailLinkErrorCode` instead."),Reflect.get(e,t,n))}),N={Expired:"expired",Failed:"failed"},A=Object.freeze({InvalidFrontendApiErrorMessage:"The frontendApi passed to Clerk is invalid. You can get your Frontend API key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})",InvalidProxyUrlErrorMessage:"The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})",InvalidPublishableKeyErrorMessage:"The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})",MissingPublishableKeyErrorMessage:"Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys."});let B="Clerk: Child of WithClerk must be a function.",D=e=>"Clerk: You've passed multiple children components to <".concat(e,"/>. You can only pass a single child component or text."),W="Clerk: Unsupported usage of isSatellite, domain or proxyUrl. The usage of isSatellite, domain or proxyUrl as function is not supported in non-browser environments.",V=e=>"Clerk: <".concat(e," /> can only accept <").concat(e,".Page /> and <").concat(e,".Link /> as its children. Any other provided component will be ignored."),F=e=>"Clerk: Missing props. <".concat(e,".Page /> component requires the following props: url, label, labelIcon, alongside with children to be rendered inside the page."),K=e=>"Clerk: Missing props. <".concat(e,".Link /> component requires the following props: url, label and labelIcon."),Y=function(e){let{packageName:t,customMessages:n}=e,r=t,i={...A,...n};function a(e,t){if(!t)return"".concat(r,": ").concat(e);let n=e;for(let r of e.matchAll(/{{([a-zA-Z0-9-_]+)}}/g)){let e=(t[r[1]]||"").toString();n=n.replace("{{".concat(r[1],"}}"),e)}return"".concat(r,": ").concat(n)}return{setPackageName(e){let{packageName:t}=e;return"string"==typeof t&&(r=t),this},setMessages(e){let{customMessages:t}=e;return Object.assign(i,t||{}),this},throwInvalidPublishableKeyError(e){throw Error(a(i.InvalidPublishableKeyErrorMessage,e))},throwInvalidFrontendApiError(e){throw Error(a(i.InvalidFrontendApiErrorMessage,e))},throwInvalidProxyUrl(e){throw Error(a(i.InvalidProxyUrlErrorMessage,e))},throwMissingPublishableKeyError(){throw Error(a(i.MissingPublishableKeyErrorMessage))}}}({packageName:"@clerk/react"}),G=new Map;var H=(e,t,n)=>{if(!t.has(e))throw TypeError("Cannot "+n)},q=(e,t,n)=>(H(e,t,"read from private field"),n?n.call(e):t.get(e)),X=(e,t,n)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,n)},J=(e,t,n,r)=>(H(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n);function $(e,t,n){return"function"==typeof e?e(t):void 0!==e?e:void 0!==n?n:void 0}async function Z(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1?arguments[1]:void 0,{async:n,defer:r,beforeLoad:i,crossOrigin:a}=t||{};return new Promise((t,o)=>{e||o("loadScript cannot be called without a src"),document&&document.body||o("loadScript cannot be called when document does not exist");let s=document.createElement("script");a&&s.setAttribute("crossorigin",a),s.async=n||!1,s.defer=r||!1,s.addEventListener("load",()=>{s.remove(),t(s)}),s.addEventListener("error",()=>{s.remove(),o()}),s.src=e,null==i||i(s),document.body.appendChild(s)})}function Q(e){return e.startsWith("/")}RegExp("bot|spider|crawl|APIs-Google|AdsBot|Googlebot|mediapartners|Google Favicon|FeedFetcher|Google-Read-Aloud|DuplexWeb-Google|googleweblight|bing|yandex|baidu|duckduck|yahoo|ecosia|ia_archiver|facebook|instagram|pinterest|reddit|slack|twitter|whatsapp|youtube|semrush","i");let{isDevOrStagingUrl:ee}=function(){let e=[".lcl.dev",".stg.dev",".lclstage.dev",".stgstage.dev",".dev.lclclerk.com",".stg.lclclerk.com",".accounts.lclclerk.com","accountsstage.dev","accounts.dev"],t=new Map;return{isDevOrStagingUrl:n=>{if(!n)return!1;let r="string"==typeof n?n:n.hostname,i=t.get(r);return void 0===i&&(i=e.some(e=>r.endsWith(e)),t.set(r,i)),i}}}(),et=e=>{if(e)return e;let t=en("4.30.5");return t?"snapshot"===t?"4.70.0":t:er("4.30.5")},en=e=>{var t;return null===(t=e.match(/-(.*)\./))||void 0===t?void 0:t[1]},er=e=>e.split(".")[0],ei=e=>{let{frontendApi:t,publishableKey:n}=e;return n||t||Y.throwMissingPublishableKeyError(),Z(ea(e),{async:!0,crossOrigin:"anonymous",beforeLoad:eo(e)}).catch(()=>{throw Error("Clerk: Failed to load Clerk")})},ea=e=>{var t,n;let{clerkJSUrl:r,clerkJSVariant:i,clerkJSVersion:a,proxyUrl:o,domain:s,publishableKey:l,frontendApi:u}=e;if(r)return r;let d="";d=o&&(!o||/^http(s)?:\/\//.test(o||"")||Q(o))?(o?Q(o)?new URL(o,window.location.origin).toString():o:"").replace(/http(s)?:\/\//,""):s&&!ee((null===(t=g(l))||void 0===t?void 0:t.frontendApi)||u||"")?function(e){let t;if(!e)return"";if(e.match(/^(clerk\.)+\w*$/))t=/(clerk\.)*(?=clerk\.)/;else{if(e.match(/\.clerk.accounts/))return e;t=/^(clerk\.)*/gi}let n=e.replace(t,"");return"clerk.".concat(n)}(s):(null===(n=g(l))||void 0===n?void 0:n.frontendApi)||u||"";let c=i?"".concat(i.replace(/\.+$/,""),"."):"",h=et(a);return"https://".concat(d,"/npm/@clerk/clerk-js@").concat(h,"/dist/clerk.").concat(c,"browser.js")},eo=e=>t=>{let{publishableKey:n,frontendApi:r,proxyUrl:i,domain:a}=e;n?t.setAttribute("data-clerk-publishable-key",n):r&&t.setAttribute("data-clerk-frontend-api",r),i&&t.setAttribute("data-clerk-proxy-url",i),a&&t.setAttribute("data-clerk-domain",a)},es=class e{get publishableKey(){return q(this,d)}get loaded(){return q(this,o)}static getOrCreateInstance(t){return(!q(this,c)||t.Clerk&&q(this,c).Clerk!==t.Clerk)&&J(this,c,new e(t)),q(this,c)}static clearInstance(){J(this,c,null)}get domain(){if(window.location)return $(q(this,s),new URL(window.location.href),"");if("function"==typeof q(this,s))throw Error(W);return q(this,s)||""}get proxyUrl(){if(window.location)return $(q(this,l),new URL(window.location.href),"");if("function"==typeof q(this,l))throw Error(W);return q(this,l)||""}get sdkMetadata(){var e;return(null===(e=this.clerkjs)||void 0===e?void 0:e.sdkMetadata)||this.options.sdkMetadata||void 0}get instanceType(){var e;return null===(e=this.clerkjs)||void 0===e?void 0:e.instanceType}get frontendApi(){var e;return(null===(e=this.clerkjs)||void 0===e?void 0:e.frontendApi)||q(this,u)||""}get isStandardBrowser(){var e;return(null===(e=this.clerkjs)||void 0===e?void 0:e.isStandardBrowser)||this.options.standardBrowser||!1}get isSatellite(){if(window.location)return $(this.options.isSatellite,new URL(window.location.href),!1);if("function"==typeof this.options.isSatellite)throw Error(W);return!1}async loadClerkJS(){if(!("browser"!==this.mode||q(this,o))){window.__clerk_frontend_api=this.frontendApi,window.__clerk_publishable_key=this.publishableKey,window.__clerk_proxy_url=this.proxyUrl,window.__clerk_domain=this.domain;try{var e,t,r,i;if(this.Clerk){let e;(i=this.Clerk,"function"==typeof i)?(e=new this.Clerk(this.publishableKey||this.frontendApi||"",{proxyUrl:this.proxyUrl,domain:this.domain}),await e.load(this.options)):(e=this.Clerk).isReady()||await e.load(this.options),n.g.Clerk=e}else{if(n.g.Clerk||await ei({...this.options,frontendApi:this.frontendApi,publishableKey:this.publishableKey,proxyUrl:this.proxyUrl,domain:this.domain}),!n.g.Clerk)throw Error("Failed to download latest ClerkJS. Contact <EMAIL>.");await n.g.Clerk.load(this.options)}if(n.g.Clerk.sdkMetadata=null!==(r=this.options.sdkMetadata)&&void 0!==r?r:{name:"@clerk/clerk-react",version:"4.30.5"},(null===(e=n.g.Clerk)||void 0===e?void 0:e.loaded)||(null===(t=n.g.Clerk)||void 0===t?void 0:t.isReady()))return this.hydrateClerkJS(n.g.Clerk);return}catch(e){console.error(e.stack||e.message||e);return}}}get version(){var e;return null===(e=this.clerkjs)||void 0===e?void 0:e.version}get client(){return this.clerkjs?this.clerkjs.client:void 0}get session(){return this.clerkjs?this.clerkjs.session:void 0}get user(){return this.clerkjs?this.clerkjs.user:void 0}get organization(){return this.clerkjs?this.clerkjs.organization:void 0}get __unstable__environment(){return this.clerkjs?this.clerkjs.__unstable__environment:void 0}__unstable__setEnvironment(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];this.clerkjs&&"__unstable__setEnvironment"in this.clerkjs&&this.clerkjs.__unstable__setEnvironment(t)}constructor(e){var t=this;this.clerkjs=null,this.preopenSignIn=null,this.preopenSignUp=null,this.preopenUserProfile=null,this.preopenOrganizationProfile=null,this.preopenCreateOrganization=null,this.premountSignInNodes=new Map,this.premountSignUpNodes=new Map,this.premountUserProfileNodes=new Map,this.premountUserButtonNodes=new Map,this.premountOrganizationProfileNodes=new Map,this.premountCreateOrganizationNodes=new Map,this.premountOrganizationSwitcherNodes=new Map,this.premountOrganizationListNodes=new Map,this.premountMethodCalls=new Map,this.loadedListeners=[],X(this,o,!1),X(this,s,void 0),X(this,l,void 0),X(this,u,void 0),X(this,d,void 0),this.isReady=()=>{var e;return!!(null===(e=this.clerkjs)||void 0===e?void 0:e.isReady())},this.buildSignInUrl=e=>{let t=()=>{var t;return(null===(t=this.clerkjs)||void 0===t?void 0:t.buildSignInUrl(e))||""};if(this.clerkjs&&q(this,o))return t();this.premountMethodCalls.set("buildSignInUrl",t)},this.buildSignUpUrl=e=>{let t=()=>{var t;return(null===(t=this.clerkjs)||void 0===t?void 0:t.buildSignUpUrl(e))||""};if(this.clerkjs&&q(this,o))return t();this.premountMethodCalls.set("buildSignUpUrl",t)},this.buildUserProfileUrl=()=>{let e=()=>{var e;return(null===(e=this.clerkjs)||void 0===e?void 0:e.buildUserProfileUrl())||""};if(this.clerkjs&&q(this,o))return e();this.premountMethodCalls.set("buildUserProfileUrl",e)},this.buildCreateOrganizationUrl=()=>{let e=()=>{var e;return(null===(e=this.clerkjs)||void 0===e?void 0:e.buildCreateOrganizationUrl())||""};if(this.clerkjs&&q(this,o))return e();this.premountMethodCalls.set("buildCreateOrganizationUrl",e)},this.buildOrganizationProfileUrl=()=>{let e=()=>{var e;return(null===(e=this.clerkjs)||void 0===e?void 0:e.buildOrganizationProfileUrl())||""};if(this.clerkjs&&q(this,o))return e();this.premountMethodCalls.set("buildOrganizationProfileUrl",e)},this.buildHomeUrl=()=>{let e=()=>{var e;return(null===(e=this.clerkjs)||void 0===e?void 0:e.buildHomeUrl())||""};if(this.clerkjs&&q(this,o))return e();this.premountMethodCalls.set("buildHomeUrl",e)},this.buildUrlWithAuth=(e,t)=>{let n=()=>{var n;return(null===(n=this.clerkjs)||void 0===n?void 0:n.buildUrlWithAuth(e,t))||""};if(this.clerkjs&&q(this,o))return n();this.premountMethodCalls.set("buildUrlWithAuth",n)},this.handleUnauthenticated=()=>{let e=()=>{var e;return null===(e=this.clerkjs)||void 0===e?void 0:e.handleUnauthenticated()};this.clerkjs&&q(this,o)?e():this.premountMethodCalls.set("handleUnauthenticated",e)},this.addOnLoaded=e=>{this.loadedListeners.push(e),this.loaded&&this.emitLoaded()},this.emitLoaded=()=>{this.loadedListeners.forEach(e=>e()),this.loadedListeners=[]},this.hydrateClerkJS=e=>{if(!e)throw Error("Failed to hydrate latest Clerk JS");return this.clerkjs=e,this.premountMethodCalls.forEach(e=>e()),null!==this.preopenSignIn&&e.openSignIn(this.preopenSignIn),null!==this.preopenSignUp&&e.openSignUp(this.preopenSignUp),null!==this.preopenUserProfile&&e.openUserProfile(this.preopenUserProfile),null!==this.preopenOrganizationProfile&&e.openOrganizationProfile(this.preopenOrganizationProfile),null!==this.preopenCreateOrganization&&e.openCreateOrganization(this.preopenCreateOrganization),this.premountSignInNodes.forEach((t,n)=>{e.mountSignIn(n,t)}),this.premountSignUpNodes.forEach((t,n)=>{e.mountSignUp(n,t)}),this.premountUserProfileNodes.forEach((t,n)=>{e.mountUserProfile(n,t)}),this.premountUserButtonNodes.forEach((t,n)=>{e.mountUserButton(n,t)}),this.premountOrganizationListNodes.forEach((t,n)=>{e.mountOrganizationList(n,t)}),J(this,o,!0),this.emitLoaded(),this.clerkjs},this.__unstable__updateProps=e=>{this.clerkjs&&"__unstable__updateProps"in this.clerkjs&&this.clerkjs.__unstable__updateProps(e)},this.setActive=e=>{let{session:t,organization:n,beforeEmit:r}=e;return this.clerkjs?this.clerkjs.setActive({session:t,organization:n,beforeEmit:r}):Promise.reject()},this.setSession=(e,t)=>(z("setSession","Use `Clerk.setActive` instead"),this.setActive({session:e,beforeEmit:t})),this.openSignIn=e=>{this.clerkjs&&q(this,o)?this.clerkjs.openSignIn(e):this.preopenSignIn=e},this.closeSignIn=()=>{this.clerkjs&&q(this,o)?this.clerkjs.closeSignIn():this.preopenSignIn=null},this.openUserProfile=e=>{this.clerkjs&&q(this,o)?this.clerkjs.openUserProfile(e):this.preopenUserProfile=e},this.closeUserProfile=()=>{this.clerkjs&&q(this,o)?this.clerkjs.closeUserProfile():this.preopenUserProfile=null},this.openOrganizationProfile=e=>{this.clerkjs&&q(this,o)?this.clerkjs.openOrganizationProfile(e):this.preopenOrganizationProfile=e},this.closeOrganizationProfile=()=>{this.clerkjs&&q(this,o)?this.clerkjs.closeOrganizationProfile():this.preopenOrganizationProfile=null},this.openCreateOrganization=e=>{this.clerkjs&&q(this,o)?this.clerkjs.openCreateOrganization(e):this.preopenCreateOrganization=e},this.closeCreateOrganization=()=>{this.clerkjs&&q(this,o)?this.clerkjs.closeCreateOrganization():this.preopenCreateOrganization=null},this.openSignUp=e=>{this.clerkjs&&q(this,o)?this.clerkjs.openSignUp(e):this.preopenSignUp=e},this.closeSignUp=()=>{this.clerkjs&&q(this,o)?this.clerkjs.closeSignUp():this.preopenSignUp=null},this.mountSignIn=(e,t)=>{this.clerkjs&&q(this,o)?this.clerkjs.mountSignIn(e,t):this.premountSignInNodes.set(e,t)},this.unmountSignIn=e=>{this.clerkjs&&q(this,o)?this.clerkjs.unmountSignIn(e):this.premountSignInNodes.delete(e)},this.mountSignUp=(e,t)=>{this.clerkjs&&q(this,o)?this.clerkjs.mountSignUp(e,t):this.premountSignUpNodes.set(e,t)},this.unmountSignUp=e=>{this.clerkjs&&q(this,o)?this.clerkjs.unmountSignUp(e):this.premountSignUpNodes.delete(e)},this.mountUserProfile=(e,t)=>{this.clerkjs&&q(this,o)?this.clerkjs.mountUserProfile(e,t):this.premountUserProfileNodes.set(e,t)},this.unmountUserProfile=e=>{this.clerkjs&&q(this,o)?this.clerkjs.unmountUserProfile(e):this.premountUserProfileNodes.delete(e)},this.mountOrganizationProfile=(e,t)=>{this.clerkjs&&q(this,o)?this.clerkjs.mountOrganizationProfile(e,t):this.premountOrganizationProfileNodes.set(e,t)},this.unmountOrganizationProfile=e=>{this.clerkjs&&q(this,o)?this.clerkjs.unmountOrganizationProfile(e):this.premountOrganizationProfileNodes.delete(e)},this.mountCreateOrganization=(e,t)=>{this.clerkjs&&q(this,o)?this.clerkjs.mountCreateOrganization(e,t):this.premountCreateOrganizationNodes.set(e,t)},this.unmountCreateOrganization=e=>{this.clerkjs&&q(this,o)?this.clerkjs.unmountCreateOrganization(e):this.premountCreateOrganizationNodes.delete(e)},this.mountOrganizationSwitcher=(e,t)=>{this.clerkjs&&q(this,o)?this.clerkjs.mountOrganizationSwitcher(e,t):this.premountOrganizationSwitcherNodes.set(e,t)},this.unmountOrganizationSwitcher=e=>{this.clerkjs&&q(this,o)?this.clerkjs.unmountOrganizationSwitcher(e):this.premountOrganizationSwitcherNodes.delete(e)},this.mountOrganizationList=(e,t)=>{this.clerkjs&&q(this,o)?this.clerkjs.mountOrganizationList(e,t):this.premountOrganizationListNodes.set(e,t)},this.unmountOrganizationList=e=>{this.clerkjs&&q(this,o)?this.clerkjs.unmountOrganizationList(e):this.premountOrganizationListNodes.delete(e)},this.mountUserButton=(e,t)=>{this.clerkjs&&q(this,o)?this.clerkjs.mountUserButton(e,t):this.premountUserButtonNodes.set(e,t)},this.unmountUserButton=e=>{this.clerkjs&&q(this,o)?this.clerkjs.unmountUserButton(e):this.premountUserButtonNodes.delete(e)},this.addListener=e=>{let t=()=>{var t;return null===(t=this.clerkjs)||void 0===t?void 0:t.addListener(e)};return this.clerkjs?t():(this.premountMethodCalls.set("addListener",t),()=>this.premountMethodCalls.delete("addListener"))},this.navigate=e=>{let t=()=>{var t;return null===(t=this.clerkjs)||void 0===t?void 0:t.navigate(e)};this.clerkjs&&q(this,o)?t():this.premountMethodCalls.set("navigate",t)},this.redirectWithAuth=function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];let i=()=>{var e;return null===(e=t.clerkjs)||void 0===e?void 0:e.redirectWithAuth(...n)};t.clerkjs&&q(t,o)?i():t.premountMethodCalls.set("redirectWithAuth",i)},this.redirectToSignIn=e=>{let t=()=>{var t;return null===(t=this.clerkjs)||void 0===t?void 0:t.redirectToSignIn(e)};this.clerkjs&&q(this,o)?t():this.premountMethodCalls.set("redirectToSignIn",t)},this.redirectToSignUp=e=>{let t=()=>{var t;return null===(t=this.clerkjs)||void 0===t?void 0:t.redirectToSignUp(e)};this.clerkjs&&q(this,o)?t():this.premountMethodCalls.set("redirectToSignUp",t)},this.redirectToUserProfile=()=>{let e=()=>{var e;return null===(e=this.clerkjs)||void 0===e?void 0:e.redirectToUserProfile()};this.clerkjs&&q(this,o)?e():this.premountMethodCalls.set("redirectToUserProfile",e)},this.redirectToHome=()=>{let e=()=>{var e;return null===(e=this.clerkjs)||void 0===e?void 0:e.redirectToHome()};this.clerkjs&&q(this,o)?e():this.premountMethodCalls.set("redirectToHome",e)},this.redirectToOrganizationProfile=()=>{let e=()=>{var e;return null===(e=this.clerkjs)||void 0===e?void 0:e.redirectToOrganizationProfile()};this.clerkjs&&q(this,o)?e():this.premountMethodCalls.set("redirectToOrganizationProfile",e)},this.redirectToCreateOrganization=()=>{let e=()=>{var e;return null===(e=this.clerkjs)||void 0===e?void 0:e.redirectToCreateOrganization()};this.clerkjs&&q(this,o)?e():this.premountMethodCalls.set("redirectToCreateOrganization",e)},this.handleRedirectCallback=e=>{let t=()=>{var t;return null===(t=this.clerkjs)||void 0===t?void 0:t.handleRedirectCallback(e)};if(this.clerkjs&&q(this,o)){var n;null===(n=t())||void 0===n||n.catch(()=>{})}else this.premountMethodCalls.set("handleRedirectCallback",t)},this.handleMagicLinkVerification=async e=>{z("handleMagicLinkVerification","Use `handleEmailLinkVerification` instead.");let t=()=>{var t;return null===(t=this.clerkjs)||void 0===t?void 0:t.handleMagicLinkVerification(e)};if(this.clerkjs&&q(this,o))return t();this.premountMethodCalls.set("handleMagicLinkVerification",t)},this.handleEmailLinkVerification=async e=>{let t=()=>{var t;return null===(t=this.clerkjs)||void 0===t?void 0:t.handleEmailLinkVerification(e)};if(this.clerkjs&&q(this,o))return t();this.premountMethodCalls.set("handleEmailLinkVerification",t)},this.authenticateWithMetamask=async e=>{let t=()=>{var t;return null===(t=this.clerkjs)||void 0===t?void 0:t.authenticateWithMetamask(e)};if(this.clerkjs&&q(this,o))return t();this.premountMethodCalls.set("authenticateWithMetamask",t)},this.createOrganization=async e=>{let t=()=>{var t;return null===(t=this.clerkjs)||void 0===t?void 0:t.createOrganization(e)};if(this.clerkjs&&q(this,o))return t();this.premountMethodCalls.set("createOrganization",t)},this.getOrganizationMemberships=async()=>{let e=()=>{var e;return null===(e=this.clerkjs)||void 0===e?void 0:e.getOrganizationMemberships()};if(this.clerkjs&&q(this,o))return e();this.premountMethodCalls.set("getOrganizationMemberships",e)},this.getOrganization=async e=>{let t=()=>{var t;return null===(t=this.clerkjs)||void 0===t?void 0:t.getOrganization(e)};if(this.clerkjs&&q(this,o))return t();this.premountMethodCalls.set("getOrganization",t)},this.signOut=async(e,t)=>{let n=()=>{var n;return null===(n=this.clerkjs)||void 0===n?void 0:n.signOut(e,t)};if(this.clerkjs&&q(this,o))return n();this.premountMethodCalls.set("signOut",n)};let{Clerk:n=null,frontendApi:r,publishableKey:i}=e||{};J(this,u,r),J(this,d,i),J(this,l,null==e?void 0:e.proxyUrl),J(this,s,null==e?void 0:e.domain),this.options=e,this.Clerk=n,this.mode="browser",this.loadClerkJS()}};o=new WeakMap,s=new WeakMap,l=new WeakMap,u=new WeakMap,d=new WeakMap,X(es,c=new WeakMap,void 0);let el=(e,t,n)=>!e&&n?eu(n):ed(t),eu=e=>{let t=e.userId,n=e.user,r=e.sessionId,i=e.session,a=e.organization,o=e.orgId,s=e.orgRole;return{userId:t,user:n,sessionId:r,session:i,organization:a,orgId:o,orgRole:s,orgPermissions:e.orgPermissions,orgSlug:e.orgSlug,actor:e.actor,lastOrganizationInvitation:null,lastOrganizationMember:null}},ed=e=>{var t;let n=e.user?e.user.id:e.user,r=e.user,i=e.session?e.session.id:e.session,a=e.session,o=null==a?void 0:a.actor,s=e.organization,l=e.organization?e.organization.id:e.organization,u=null==s?void 0:s.slug,d=s?null==r?void 0:null===(t=r.organizationMemberships)||void 0===t?void 0:t.find(e=>e.organization.id===l):s,c=d?d.permissions:d;return{userId:n,user:r,sessionId:i,session:a,organization:s,orgId:l,orgRole:d?d.role:d,orgSlug:u,orgPermissions:c,actor:o,lastOrganizationInvitation:e.lastOrganizationInvitation,lastOrganizationMember:e.lastOrganizationMember}};var ec=n(2362);let eh=()=>{},ep=eh(),ef=Object,eg=e=>e===ep,em=e=>"function"==typeof e,ev=(e,t)=>({...e,...t}),eb=e=>em(e.then),ey=new WeakMap,ek=0,ew=e=>{let t,n;let r=typeof e,i=e&&e.constructor,a=i==Date;if(ef(e)!==e||a||i==RegExp)t=a?e.toJSON():"symbol"==r?e.toString():"string"==r?JSON.stringify(e):""+e;else{if(t=ey.get(e))return t;if(t=++ek+"~",ey.set(e,t),i==Array){for(n=0,t="@";n<e.length;n++)t+=ew(e[n])+",";ey.set(e,t)}if(i==ef){t="#";let r=ef.keys(e).sort();for(;!eg(n=r.pop());)eg(e[n])||(t+=n+":"+ew(e[n])+",");ey.set(e,t)}}return t},eE=new WeakMap,eS={},eC={},eP="undefined",ex=typeof document!=eP,ez=()=>typeof window.requestAnimationFrame!=eP,eO=(e,t)=>{let n=eE.get(e);return[()=>!eg(t)&&e.get(t)||eS,r=>{if(!eg(t)){let i=e.get(t);t in eC||(eC[t]=i),n[5](t,ev(i,r),i||eS)}},n[6],()=>!eg(t)&&t in eC?eC[t]:!eg(t)&&e.get(t)||eS]},e_=!0,[eU,eL]=window.addEventListener?[window.addEventListener.bind(window),window.removeEventListener.bind(window)]:[eh,eh],ej={initFocus:e=>(ex&&document.addEventListener("visibilitychange",e),eU("focus",e),()=>{ex&&document.removeEventListener("visibilitychange",e),eL("focus",e)}),initReconnect:e=>{let t=()=>{e_=!0,e()},n=()=>{e_=!1};return eU("online",t),eU("offline",n),()=>{eL("online",t),eL("offline",n)}}},eM=!E.useId,eI="Deno"in window,eT=e=>ez()?window.requestAnimationFrame(e):setTimeout(e,1),eR=eI?E.useEffect:E.useLayoutEffect,eN="undefined"!=typeof navigator&&navigator.connection,eA=!eI&&eN&&(["slow-2g","2g"].includes(eN.effectiveType)||eN.saveData),eB=e=>{if(em(e))try{e=e()}catch(t){e=""}let t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?ew(e):"",t]},eD=0,eW=()=>++eD;var eV={ERROR_REVALIDATE_EVENT:3,FOCUS_EVENT:0,MUTATE_EVENT:2,RECONNECT_EVENT:1};async function eF(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let[r,i,a,o]=t,s=ev({populateCache:!0,throwOnError:!0},"boolean"==typeof o?{revalidate:o}:o||{}),l=s.populateCache,u=s.rollbackOnError,d=s.optimisticData,c=!1!==s.revalidate,h=e=>"function"==typeof u?u(e):!1!==u,p=s.throwOnError;if(em(i)){let e=[];for(let t of r.keys())!/^\$(inf|sub)\$/.test(t)&&i(r.get(t)._k)&&e.push(t);return Promise.all(e.map(f))}return f(i);async function f(e){let n;let[i]=eB(e);if(!i)return;let[o,s]=eO(r,i),[u,f,g,m]=eE.get(r),v=u[i],b=()=>c&&(delete g[i],delete m[i],v&&v[0])?v[0](2).then(()=>o().data):o().data;if(t.length<3)return b();let y=a,k=eW();f[i]=[k,0];let w=!eg(d),E=o(),S=E.data,C=E._c,P=eg(C)?S:C;if(w&&s({data:d=em(d)?d(P,S):d,_c:P}),em(y))try{y=y(P)}catch(e){n=e}if(y&&eb(y)){if(y=await y.catch(e=>{n=e}),k!==f[i][0]){if(n)throw n;return y}n&&w&&h(n)&&(l=!0,s({data:y=P,_c:ep}))}l&&!n&&(em(l)&&(y=l(y,P)),s({data:y,error:ep,_c:ep})),f[i][1]=eW();let x=await b();if(s({_c:ep}),n){if(p)throw n;return}return l?x:y}}let eK=(e,t)=>{for(let n in e)e[n][0]&&e[n][0](t)},eY=(e,t)=>{if(!eE.has(e)){let n=ev(ej,t),r={},i=eF.bind(ep,e),a=eh,o={},s=(e,t)=>{let n=o[e]||[];return o[e]=n,n.push(t),()=>n.splice(n.indexOf(t),1)},l=(t,n,r)=>{e.set(t,n);let i=o[t];if(i)for(let e of i)e(n,r)},u=()=>{if(!eE.has(e)&&(eE.set(e,[r,{},{},{},i,l,s]),!eI)){let t=n.initFocus(setTimeout.bind(ep,eK.bind(ep,r,0))),i=n.initReconnect(setTimeout.bind(ep,eK.bind(ep,r,1)));a=()=>{t&&t(),i&&i(),eE.delete(e)}}};return u(),[e,i,u,a]}return[e,eE.get(e)[4]]},[eG,eH]=eY(new Map),eq=ev({onLoadingSlow:eh,onSuccess:eh,onError:eh,onErrorRetry:(e,t,n,r,i)=>{let a=n.errorRetryCount,o=i.retryCount,s=~~((Math.random()+.5)*(1<<(o<8?o:8)))*n.errorRetryInterval;(eg(a)||!(o>a))&&setTimeout(r,s,i)},onDiscarded:eh,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:eA?1e4:5e3,focusThrottleInterval:5e3,dedupingInterval:2e3,loadingTimeout:eA?5e3:3e3,compare:(e,t)=>ew(e)==ew(t),isPaused:()=>!1,cache:eG,mutate:eH,fallback:{}},{isOnline:()=>e_,isVisible:()=>{let e=ex&&document.visibilityState;return eg(e)||"hidden"!==e}}),eX=(e,t)=>{let n=ev(e,t);if(t){let{use:r,fallback:i}=e,{use:a,fallback:o}=t;r&&a&&(n.use=r.concat(a)),i&&o&&(n.fallback=ev(i,o))}return n},eJ=(0,E.createContext)({}),e$=window.__SWR_DEVTOOLS_USE__,eZ=e$?window.__SWR_DEVTOOLS_USE__:[],eQ=e=>em(e[1])?[e[0],e[1],e[2]||{}]:[e[0],null,(null===e[1]?e[2]:e[1])||{}],e0=()=>ev(eq,(0,E.useContext)(eJ)),e1=(e,t)=>{let[n,r]=eB(e),[,,,i]=eE.get(eG);if(i[n])return i[n];let a=t(r);return i[n]=a,a},e2=eZ.concat(e=>(t,n,r)=>{let i=n&&function(){for(var e=arguments.length,r=Array(e),i=0;i<e;i++)r[i]=arguments[i];let[a]=eB(t),[,,,o]=eE.get(eG),s=o[a];return eg(s)?n(...r):(delete o[a],s)};return e(t,i,r)}),e5=(e,t,n)=>{let r=t[e]||(t[e]=[]);return r.push(n),()=>{let e=r.indexOf(n);e>=0&&(r[e]=r[r.length-1],r.pop())}};e$&&(window.__SWR_DEVTOOLS_REACT__=E);let e4=e=>eB(e)[0],e3=E.use||(e=>{if("pending"===e.status)throw e;if("fulfilled"===e.status)return e.value;if("rejected"===e.status)throw e.reason;throw e.status="pending",e.then(t=>{e.status="fulfilled",e.value=t},t=>{e.status="rejected",e.reason=t}),e}),e9={dedupe:!0},e8=ef.defineProperty(e=>{let{value:t}=e,n=(0,E.useContext)(eJ),r=em(t),i=(0,E.useMemo)(()=>r?t(n):t,[r,n,t]),a=(0,E.useMemo)(()=>r?i:eX(n,i),[r,n,i]),o=i&&i.provider,s=(0,E.useRef)(ep);o&&!s.current&&(s.current=eY(o(a.cache||eG),i));let l=s.current;return l&&(a.cache=l[0],a.mutate=l[1]),eR(()=>{if(l)return l[2]&&l[2](),l[3]},[]),(0,E.createElement)(eJ.Provider,ev(e,{value:a}))},"defaultValue",{value:eq}),e7=(r=(e,t,n)=>{let{cache:r,compare:i,suspense:a,fallbackData:o,revalidateOnMount:s,revalidateIfStale:l,refreshInterval:u,refreshWhenHidden:d,refreshWhenOffline:c,keepPreviousData:h}=n,[p,f,g,m]=eE.get(r),[v,b]=eB(e),y=(0,E.useRef)(!1),k=(0,E.useRef)(!1),w=(0,E.useRef)(v),S=(0,E.useRef)(t),C=(0,E.useRef)(n),P=()=>C.current,x=()=>P().isVisible()&&P().isOnline(),[z,O,_,U]=eO(r,v),L=(0,E.useRef)({}).current,j=eg(o)?n.fallback[v]:o,M=(e,t)=>{for(let n in L)if("data"===n){if(!i(e[n],t[n])&&(!eg(e[n])||!i(V,t[n])))return!1}else if(t[n]!==e[n])return!1;return!0},I=(0,E.useMemo)(()=>{let e=!!v&&!!t&&(eg(s)?!P().isPaused()&&!a&&(!!eg(l)||l):s),n=t=>{let n=ev(t);return(delete n._k,e)?{isValidating:!0,isLoading:!0,...n}:n},r=z(),i=U(),o=n(r),u=r===i?o:n(i),d=o;return[()=>{let e=n(z());return M(e,d)?(d.data=e.data,d.isLoading=e.isLoading,d.isValidating=e.isValidating,d.error=e.error,d):(d=e,e)},()=>u]},[r,v]),T=(0,ec.useSyncExternalStore)((0,E.useCallback)(e=>_(v,(t,n)=>{M(n,t)||e()}),[r,v]),I[0],I[1]),R=!y.current,N=p[v]&&p[v].length>0,A=T.data,B=eg(A)?j:A,D=T.error,W=(0,E.useRef)(B),V=h?eg(A)?W.current:A:B,F=(!N||!!eg(D))&&(R&&!eg(s)?s:!P().isPaused()&&(a?!eg(B)&&l:eg(B)||l)),K=!!(v&&t&&R&&F),Y=eg(T.isValidating)?K:T.isValidating,G=eg(T.isLoading)?K:T.isLoading,H=(0,E.useCallback)(async e=>{let t,r;let a=S.current;if(!v||!a||k.current||P().isPaused())return!1;let o=!0,s=e||{},l=!g[v]||!s.dedupe,u=()=>eM?!k.current&&v===w.current&&y.current:v===w.current,d={isValidating:!1,isLoading:!1},c=()=>{O(d)},h=()=>{let e=g[v];e&&e[1]===r&&delete g[v]},m={isValidating:!0};eg(z().data)&&(m.isLoading=!0);try{if(l&&(O(m),n.loadingTimeout&&eg(z().data)&&setTimeout(()=>{o&&u()&&P().onLoadingSlow(v,n)},n.loadingTimeout),g[v]=[a(b),eW()]),[t,r]=g[v],t=await t,l&&setTimeout(h,n.dedupingInterval),!g[v]||g[v][1]!==r)return l&&u()&&P().onDiscarded(v),!1;d.error=ep;let e=f[v];if(!eg(e)&&(r<=e[0]||r<=e[1]||0===e[1]))return c(),l&&u()&&P().onDiscarded(v),!1;let s=z().data;d.data=i(s,t)?s:t,l&&u()&&P().onSuccess(t,v,n)}catch(n){h();let e=P(),{shouldRetryOnError:t}=e;!e.isPaused()&&(d.error=n,l&&u()&&(e.onError(n,v,e),(!0===t||em(t)&&t(n))&&x()&&e.onErrorRetry(n,v,e,e=>{let t=p[v];t&&t[0]&&t[0](eV.ERROR_REVALIDATE_EVENT,e)},{retryCount:(s.retryCount||0)+1,dedupe:!0})))}return o=!1,c(),!0},[v,r]),q=(0,E.useCallback)(function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return eF(r,w.current,...t)},[]);if(eR(()=>{S.current=t,C.current=n,eg(A)||(W.current=A)}),eR(()=>{if(!v)return;let e=H.bind(ep,e9),t=0,n=e5(v,p,function(n){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(n==eV.FOCUS_EVENT){let n=Date.now();P().revalidateOnFocus&&n>t&&x()&&(t=n+P().focusThrottleInterval,e())}else if(n==eV.RECONNECT_EVENT)P().revalidateOnReconnect&&x()&&e();else if(n==eV.MUTATE_EVENT)return H();else if(n==eV.ERROR_REVALIDATE_EVENT)return H(r)});return k.current=!1,w.current=v,y.current=!0,O({_k:b}),F&&(eg(B)||eI?e():eT(e)),()=>{k.current=!0,n()}},[v]),eR(()=>{let e;function t(){let t=em(u)?u(z().data):u;t&&-1!==e&&(e=setTimeout(n,t))}function n(){!z().error&&(d||P().isVisible())&&(c||P().isOnline())?H(e9).then(t):t()}return t(),()=>{e&&(clearTimeout(e),e=-1)}},[u,d,c,v]),(0,E.useDebugValue)(V),a&&eg(B)&&v){if(!eM&&eI)throw Error("Fallback data is required when using suspense in SSR.");S.current=t,C.current=n,k.current=!1;let e=m[v];if(eg(e)||e3(q(e)),eg(D)){let e=H(e9);eg(V)||(e.status="fulfilled",e.value=!0),e3(e)}else throw D}return{mutate:q,get data(){return L.data=!0,V},get error(){return L.error=!0,D},get isValidating(){return L.isValidating=!0,Y},get isLoading(){return L.isLoading=!0,G}}},function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let i=e0(),[a,o,s]=eQ(t),l=eX(i,s),u=r,{use:d}=l,c=(d||[]).concat(e2);for(let e=c.length;e--;)u=c[e](u);return u(a,o||l.fetcher||null,l)}),e6=e=>eB(e?e(0,null):null)[0],te=Promise.resolve(),tt=(i=e=>(t,n,r)=>{let i;let a=(0,E.useRef)(!1),{cache:o,initialSize:s=1,revalidateAll:l=!1,persistSize:u=!1,revalidateFirstPage:d=!0,revalidateOnMount:c=!1,parallel:h=!1}=r;try{(i=e6(t))&&(i="$inf$"+i)}catch(e){}let[p,f,g]=eO(o,i),m=(0,E.useCallback)(()=>eg(p()._l)?s:p()._l,[o,i,s]);(0,ec.useSyncExternalStore)((0,E.useCallback)(e=>i?g(i,()=>{e()}):()=>{},[o,i]),m,m);let v=(0,E.useCallback)(()=>{let e=p()._l;return eg(e)?s:e},[i,s]),b=(0,E.useRef)(v());eR(()=>{if(!a.current){a.current=!0;return}i&&f({_l:u?b.current:v()})},[i,o]);let y=c&&!a.current,k=e(i,async e=>{let i=p()._i,a=[],s=v(),[u]=eO(o,e),c=u().data,g=[],m=null;for(let e=0;e<s;++e){let[s,u]=eB(t(e,h?null:m));if(!s)break;let[p,f]=eO(o,s),v=p().data,b=l||i||eg(v)||d&&!e&&!eg(c)||y||c&&!eg(c[e])&&!r.compare(c[e],v);if(n&&b){let t=async()=>{f({data:v=await n(u),_k:u}),a[e]=v};h?g.push(t):await t()}else a[e]=v;h||(m=v)}return h&&await Promise.all(g.map(e=>e())),f({_i:ep}),a},r),w=(0,E.useCallback)(function(e,t){let n="boolean"==typeof t?{revalidate:t}:t||{},r=!1!==n.revalidate;return i?(r&&(eg(e)?f({_i:!0}):f({_i:!1})),arguments.length?k.mutate(e,{...n,revalidate:r}):k.mutate()):te},[i,o]),S=(0,E.useCallback)(e=>{let n;if(!i)return te;let[,r]=eO(o,i);if(em(e)?n=e(v()):"number"==typeof e&&(n=e),"number"!=typeof n)return te;r({_l:n}),b.current=n;let a=[],[s]=eO(o,i),l=null;for(let e=0;e<n;++e){let[n]=eB(t(e,l)),[r]=eO(o,n),i=n?r().data:ep;if(eg(i))return w(s().data);a.push(i),l=i}return w(a)},[i,o,w,v]);return{size:v(),setSize:S,mutate:w,get data(){return k.data},get error(){return k.error},get isValidating(){return k.isValidating},get isLoading(){return k.isLoading}}},function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let[r,a,o]=eQ(t),s=(o.use||[]).concat(i);return e7(r,a,{...o,use:s})});function tn(e,t){if(!e)throw"string"==typeof t?Error(t):Error("".concat(t.displayName," not found"))}var tr=(e,t)=>{let{assertCtxFn:n=tn}=t||{},r=E.createContext(void 0);return r.displayName=e,[r,()=>{let t=E.useContext(r);return n(t,"".concat(e," not found")),t.value},()=>{let e=E.useContext(r);return e?e.value:{}}]},ti={};((e,t)=>{for(var n in t)v(e,n,{get:t[n],enumerable:!0})})(ti,{SWRConfig:()=>e8,useSWR:()=>e7,useSWRInfinite:()=>tt}),w(ti,h,"default"),a&&w(a,h,"default");var[ta,to]=tr("ClerkInstanceContext"),[ts,tl]=tr("UserContext"),[tu,td]=tr("ClientContext"),[tc,th]=tr("SessionContext"),[tp,tf]=tr("OrganizationContext"),tg=e=>{let{children:t,organization:n,lastOrganizationMember:r,lastOrganizationInvitation:i,swrConfig:a}=e;return E.createElement(e8,{value:a},E.createElement(tp.Provider,{value:{value:{organization:n,lastOrganizationMember:r,lastOrganizationInvitation:i}}},t))};function tm(e,t){let n=new Set(Object.keys(t)),r={};for(let t of Object.keys(e))n.has(t)||(r[t]=e[t]);return r}var tv=(e,t)=>{var n,r,i;let a="boolean"==typeof e&&e,o=(0,E.useRef)(a?t.initialPage:null!==(n=null==e?void 0:e.initialPage)&&void 0!==n?n:t.initialPage),s=(0,E.useRef)(a?t.pageSize:null!==(r=null==e?void 0:e.pageSize)&&void 0!==r?r:t.pageSize),l={};for(let n of Object.keys(t))l[n]=a?t[n]:null!==(i=null==e?void 0:e[n])&&void 0!==i?i:t[n];return{...l,initialPage:o.current,pageSize:s.current}},tb=(e,t,n,r)=>{var i,a,o,s,l,u;let[d,c]=(0,E.useState)(null!==(i=e.initialPage)&&void 0!==i?i:1),h=(0,E.useRef)(null!==(a=e.initialPage)&&void 0!==a?a:1),p=(0,E.useRef)(null!==(o=e.pageSize)&&void 0!==o?o:10),f=null===(s=n.enabled)||void 0===s||s,g=null!==(l=n.infinite)&&void 0!==l&&l,m=null!==(u=n.keepPreviousData)&&void 0!==u&&u,v={...r,...e,initialPage:d,pageSize:p.current},{data:b,isValidating:y,isLoading:k,error:w,mutate:S}=e7(!g&&t&&f?v:null,e=>{let n=tm(e,r);return null==t?void 0:t(n)},{keepPreviousData:m}),{data:C,isLoading:P,isValidating:x,error:z,size:O,setSize:_,mutate:U}=tt(t=>g&&f?{...e,...r,initialPage:h.current+t,pageSize:p.current}:null,e=>{let n=tm(e,r);return null==t?void 0:t(n)}),L=(0,E.useMemo)(()=>g?O:d,[g,O,d]),j=(0,E.useCallback)(e=>{if(g){_(e);return}return c(e)},[_]),M=(0,E.useMemo)(()=>{var e,t;return g?null!==(e=null==C?void 0:C.map(e=>null==e?void 0:e.data).flat())&&void 0!==e?e:[]:null!==(t=null==b?void 0:b.data)&&void 0!==t?t:[]},[g,b,C]),I=(0,E.useMemo)(()=>{var e,t;return g?(null==C?void 0:null===(e=C[(null==C?void 0:C.length)-1])||void 0===e?void 0:e.total_count)||0:null!==(t=null==b?void 0:b.total_count)&&void 0!==t?t:0},[g,b,C]),T=g?P:k,R=g?x:y,N=!!(g?z:w),A=(0,E.useCallback)(()=>{j(e=>Math.max(0,e+1))},[j]),B=(0,E.useCallback)(()=>{j(e=>Math.max(0,e-1))},[j]),D=(h.current-1)*p.current,W=Math.ceil((I-D)/p.current),V=I-D*p.current>L*p.current,F=(L-1)*p.current>D*p.current,K=g?e=>U(e,{revalidate:!1}):e=>S(e,{revalidate:!1});return{data:M,count:I,isLoading:T,isFetching:R,isError:N,page:L,pageCount:W,fetchPage:j,fetchNext:A,fetchPrevious:B,hasNextPage:V,hasPreviousPage:F,revalidate:g?()=>U():()=>S(),setData:K}},ty={data:void 0,count:void 0,isLoading:!1,isFetching:!1,isError:!1,page:void 0,pageCount:void 0,fetchPage:void 0,fetchNext:void 0,fetchPrevious:void 0,hasNextPage:!1,hasPreviousPage:!1,revalidate:void 0,setData:void 0},tk=e=>{var t,n;let{invitationList:r,membershipList:i,domains:a,membershipRequests:o,memberships:s,invitations:l}=e||{},{organization:u,lastOrganizationMember:d,lastOrganizationInvitation:c}=tf(),h=th(),p=tv(a,{initialPage:1,pageSize:10,keepPreviousData:!1,infinite:!1,enrollmentMode:void 0}),f=tv(o,{initialPage:1,pageSize:10,status:"pending",keepPreviousData:!1,infinite:!1}),g=tv(s,{initialPage:1,pageSize:10,role:void 0,keepPreviousData:!1,infinite:!1}),m=tv(l,{initialPage:1,pageSize:10,status:["pending"],keepPreviousData:!1,infinite:!1}),v=to(),b=!!(v.loaded&&h&&u),y=void 0===a?void 0:{initialPage:p.initialPage,pageSize:p.pageSize,enrollmentMode:p.enrollmentMode},k=void 0===o?void 0:{initialPage:f.initialPage,pageSize:f.pageSize,status:f.status},w=void 0===s?void 0:{initialPage:g.initialPage,pageSize:g.pageSize,role:g.role},E=void 0===l?void 0:{initialPage:m.initialPage,pageSize:m.pageSize,status:m.status},S=tb({...y},null==u?void 0:u.getDomains,{keepPreviousData:p.keepPreviousData,infinite:p.infinite,enabled:!!y},{type:"domains",organizationId:null==u?void 0:u.id}),C=tb({...k},null==u?void 0:u.getMembershipRequests,{keepPreviousData:f.keepPreviousData,infinite:f.infinite,enabled:!!k},{type:"membershipRequests",organizationId:null==u?void 0:u.id}),P=tb({...w,paginated:!0},null==u?void 0:u.getMemberships,{keepPreviousData:g.keepPreviousData,infinite:g.infinite,enabled:!!w},{type:"members",organizationId:null==u?void 0:u.id}),x=tb({...E},null==u?void 0:u.getInvitations,{keepPreviousData:m.keepPreviousData,infinite:m.infinite,enabled:!!E},{type:"invitations",organizationId:null==u?void 0:u.id}),O=v.loaded?()=>{var e;return null===(e=v.organization)||void 0===e?void 0:e.getPendingInvitations(r)}:()=>[],_=v.loaded?()=>{var e;return null===(e=v.organization)||void 0===e?void 0:e.getMemberships(i)}:()=>[];r&&z("invitationList in useOrganization","Use the `invitations` property and return value instead.");let{data:U,isValidating:L,mutate:j}=e7(b&&r?tw("invites",u,c,r):null,O);i&&z("membershipList in useOrganization","Use the `memberships` property and return value instead.");let{data:M,isValidating:I,mutate:T}=e7(b&&i?tw("memberships",u,d,i):null,_);return void 0===u?{isLoaded:!1,organization:void 0,invitationList:void 0,membershipList:void 0,membership:void 0,domains:ty,membershipRequests:ty,memberships:ty,invitations:ty}:null===u?{isLoaded:!0,organization:null,invitationList:null,membershipList:null,membership:null,domains:null,membershipRequests:null,memberships:null,invitations:null}:!v.loaded&&u?{isLoaded:!0,organization:u,invitationList:void 0,membershipList:void 0,membership:void 0,domains:ty,membershipRequests:ty,memberships:ty,invitations:ty}:{isLoaded:!I&&!L,organization:u,membershipList:M,membership:(t=h.user.organizationMemberships,n=u.id,t.find(e=>e.organization.id===n)),invitationList:U,unstable__mutate:()=>{T(),j()},domains:S,membershipRequests:C,memberships:P,invitations:x}};function tw(e,t,n,r){return[e,t.id,null==n?void 0:n.id,null==n?void 0:n.updatedAt,r.offset,r.limit].filter(Boolean).join("-")}var tE={data:void 0,count:void 0,isLoading:!1,isFetching:!1,isError:!1,page:void 0,pageCount:void 0,fetchPage:void 0,fetchNext:void 0,fetchPrevious:void 0,hasNextPage:!1,hasPreviousPage:!1,revalidate:void 0,setData:void 0},tS=e=>{let{userMemberships:t,userInvitations:n,userSuggestions:r}=e||{},i=tv(t,{initialPage:1,pageSize:10,keepPreviousData:!1,infinite:!1}),a=tv(n,{initialPage:1,pageSize:10,status:"pending",keepPreviousData:!1,infinite:!1}),o=tv(r,{initialPage:1,pageSize:10,status:"pending",keepPreviousData:!1,infinite:!1}),s=to(),l=tl(),u=void 0===t?void 0:{initialPage:i.initialPage,pageSize:i.pageSize},d=void 0===n?void 0:{initialPage:a.initialPage,pageSize:a.pageSize,status:a.status},c=void 0===r?void 0:{initialPage:o.initialPage,pageSize:o.pageSize,status:o.status},h=!!(s.loaded&&l),p=tb({...u,paginated:!0},null==l?void 0:l.getOrganizationMemberships,{keepPreviousData:i.keepPreviousData,infinite:i.infinite,enabled:!!u},{type:"userMemberships",userId:null==l?void 0:l.id}),f=tb({...d},null==l?void 0:l.getOrganizationInvitations,{keepPreviousData:a.keepPreviousData,infinite:a.infinite,enabled:!!d},{type:"userInvitations",userId:null==l?void 0:l.id}),g=tb({...c},null==l?void 0:l.getOrganizationSuggestions,{keepPreviousData:o.keepPreviousData,infinite:o.infinite,enabled:!!c},{type:"userSuggestions",userId:null==l?void 0:l.id});if(!h)return{isLoaded:!1,organizationList:void 0,createOrganization:void 0,setActive:void 0,userMemberships:tE,userInvitations:tE,userSuggestions:tE};let m={isLoaded:h,organizationList:l.organizationMemberships.map(e=>({membership:e,organization:e.organization})),setActive:s.setActive,createOrganization:s.createOrganization,userMemberships:p,userInvitations:f,userSuggestions:g};return O(m,"organizationList","Use `userMemberships` instead."),m},tC=()=>{z("useOrganizations","Use useOrganizationList, useOrganization, or useClerk instead.");let e=to();return e.loaded?{isLoaded:!0,createOrganization:e.createOrganization,getOrganizationMemberships:e.getOrganizationMemberships,getOrganization:e.getOrganization}:{isLoaded:!1,createOrganization:void 0,getOrganizationMemberships:void 0,getOrganization:void 0}};E.useLayoutEffect;let[tP,tx]=tr("AuthContext"),[tz,tO]=[ta,to];function t_(e){let{isomorphicClerkOptions:t,initialState:n,children:r}=e,{isomorphicClerk:i,loaded:a}=tU(t);t.frontendApi&&z("frontendApi","Use `publishableKey` instead.");let[o,s]=E.useState({client:i.client,session:i.session,user:i.user,organization:i.organization,lastOrganizationInvitation:null,lastOrganizationMember:null});E.useEffect(()=>i.addListener(e=>s({...e})),[]);let l=el(a,o,n),u=E.useMemo(()=>({value:i}),[a]),d=E.useMemo(()=>({value:o.client}),[o.client]),{sessionId:c,session:h,userId:p,user:f,orgId:g,actor:m,lastOrganizationInvitation:v,lastOrganizationMember:b,organization:y,orgRole:k,orgSlug:w,orgPermissions:S}=l,C=E.useMemo(()=>({value:{sessionId:c,userId:p,actor:m,orgId:g,orgRole:k,orgSlug:w,orgPermissions:S}}),[c,p,m,g,k,w]),P=E.useMemo(()=>({value:f}),[p,f]),x=E.useMemo(()=>({value:h}),[c,h]),O=E.useMemo(()=>({value:{organization:y,lastOrganizationInvitation:v,lastOrganizationMember:b}}),[g,y,v,b]);return E.createElement(tz.Provider,{value:u},E.createElement(tu.Provider,{value:d},E.createElement(tc.Provider,{value:x},E.createElement(tg,{...O.value},E.createElement(tP.Provider,{value:C},E.createElement(ts.Provider,{value:P},r))))))}let tU=e=>{let[t,n]=E.useState(!1),r=E.useMemo(()=>es.getOrCreateInstance(e),[]);return E.useEffect(()=>{r.__unstable__updateProps({appearance:e.appearance})},[e.appearance]),E.useEffect(()=>{r.__unstable__updateProps({options:e})},[e.localization]),E.useEffect(()=>{r.addOnLoaded(()=>n(!0))},[]),E.useEffect(()=>()=>{es.clearInstance()},[]),{isomorphicClerk:r,loaded:t}},tL=Object.freeze({noGuarantees:Object.freeze({guaranteedLoaded:!1}),guaranteedLoaded:Object.freeze({guaranteedLoaded:!0})}),tj=E.createContext(void 0);tj.displayName="StructureContext";let tM=()=>{let e=E.useContext(tj);return!function(e){if(!e)throw Error("Clerk: You must wrap your application in a <ClerkProvider> component.")}(e),e},tI=e=>{let{children:t}=e;return tM().guaranteedLoaded?E.createElement(E.Fragment,null,t):E.createElement(tj.Provider,{value:tL.guaranteedLoaded},t)};!function(e){Y.setMessages(e).setPackageName(e)}({packageName:"@clerk/clerk-react"});let tT=function(e,t,n){let r=e.displayName||e.name||t||"Component",i=r=>(!function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;E.useEffect(()=>{let r=G.get(e)||0;if(r==n)throw Error(t);return G.set(e,r+1),()=>{G.set(e,(G.get(e)||1)-1)}},[])}(t,n),E.createElement(e,{...r}));return i.displayName="withMaxAllowedInstancesGuard(".concat(r,")"),i}(function(e){let{initialState:t,children:n,...r}=e,{frontendApi:i="",publishableKey:a="",Clerk:o}=r;return!o&&(a||i?a&&!m(a)?Y.throwInvalidPublishableKeyError({key:a}):a||!i||(i||"").startsWith("clerk.")||Y.throwInvalidFrontendApiError({key:i}):Y.throwMissingPublishableKeyError()),E.createElement(tj.Provider,{value:tL.noGuarantees},E.createElement(t_,{initialState:t,isomorphicClerkOptions:r},n))},"ClerkProvider","Clerk: You've added multiple <ClerkProvider> components in your React component tree. Wrap your components in a single <ClerkProvider>.");tT.displayName="ClerkProvider";var tR=e=>{let t=n=>{if(!n)return n;if(Array.isArray(n))return n.map(e=>"object"==typeof e||Array.isArray(e)?t(e):e);let r={...n};for(let n of Object.keys(r)){let i=e(n.toString());i!==n&&(r[i]=r[n],delete r[n]),"object"==typeof r[i]&&(r[i]=t(r[i]))}return r};return t};tR(function(e){return e?e.replace(/[A-Z]/g,e=>"_".concat(e.toLowerCase())):""}),tR(function(e){return e?e.replace(/([-_][a-z])/g,e=>e.toUpperCase().replace(/-|_/,"")):""}),Object.freeze({"image/png":"png","image/jpeg":"jpg","image/gif":"gif","image/webp":"webp","image/x-icon":"ico","image/vnd.microsoft.icon":"ico"});var tN=e=>{S()&&console.error(e)},tA=n(9542);let tB=e=>{let t=Array(e.length).fill(null),[n,r]=(0,E.useState)(t);return e.map((e,t)=>({id:e.id,mount:e=>r(n=>n.map((n,r)=>r===t?e:n)),unmount:()=>r(e=>e.map((e,n)=>n===t?null:e)),portal:()=>E.createElement(E.Fragment,null,n[t]?(0,tA.createPortal)(e.component,n[t]):null)}))},tD=(e,t)=>!!e&&E.isValidElement(e)&&(null==e?void 0:e.type)===t,tW=e=>tF({children:e,reorderItemsLabels:["account","security"],LinkComponent:tQ,PageComponent:tZ,componentName:"UserProfile"}),tV=e=>tF({children:e,reorderItemsLabels:["members","settings"],LinkComponent:t5,PageComponent:t2,componentName:"OrganizationProfile"}),tF=e=>{let{children:t,LinkComponent:n,PageComponent:r,reorderItemsLabels:i,componentName:a}=e,o=[];E.Children.forEach(t,e=>{if(!tD(e,r)&&!tD(e,n)){e&&tN(V(a));return}let{props:t}=e,{children:s,label:l,url:u,labelIcon:d}=t;if(tD(e,r)){if(tK(t,i))o.push({label:l});else if(tY(t))o.push({label:l,labelIcon:d,children:s,url:u});else{tN(F(a));return}}if(tD(e,n)){if(tG(t))o.push({label:l,labelIcon:d,url:u});else{tN(K(a));return}}});let s=[],l=[],u=[];o.forEach((e,t)=>{if(tY(e)){s.push({component:e.children,id:t}),l.push({component:e.labelIcon,id:t});return}tG(e)&&u.push({component:e.labelIcon,id:t})});let d=tB(s),c=tB(l),h=tB(u),p=[],f=[];return o.forEach((e,t)=>{if(tK(e,i)){p.push({label:e.label});return}if(tY(e)){let{portal:n,mount:r,unmount:i}=d.find(e=>e.id===t),{portal:a,mount:o,unmount:s}=c.find(e=>e.id===t);p.push({label:e.label,url:e.url,mount:r,unmount:i,mountIcon:o,unmountIcon:s}),f.push(n),f.push(a);return}if(tG(e)){let{portal:n,mount:r,unmount:i}=h.find(e=>e.id===t);p.push({label:e.label,url:e.url,mountIcon:r,unmountIcon:i}),f.push(n);return}}),{customPages:p,customPagesPortals:f}},tK=(e,t)=>{let{children:n,label:r,url:i,labelIcon:a}=e;return!n&&!i&&!a&&t.some(e=>e===r)},tY=e=>{let{children:t,label:n,url:r,labelIcon:i}=e;return!!t&&!!r&&!!i&&!!n},tG=e=>{let{children:t,label:n,url:r,labelIcon:i}=e;return!t&&!!r&&!!i&&!!n},tH=(e,t)=>{t=t||e.displayName||e.name||"Component",e.displayName=t;let n=t=>{let n=tO();return n.loaded?E.createElement(tI,null,E.createElement(e,{...t,clerk:n})):null};return n.displayName="withClerk(".concat(t,")"),n},tq=e=>{let{children:t}=e,n=tO();if("function"!=typeof t)throw Error(B);return n.loaded?E.createElement(tI,null,t(n)):null};class tX extends E.PureComponent{componentDidUpdate(e){var t,n,r,i;(e.props.appearance!==this.props.props.appearance||(null===(n=e.props)||void 0===n?void 0:null===(t=n.customPages)||void 0===t?void 0:t.length)!==(null===(i=this.props.props)||void 0===i?void 0:null===(r=i.customPages)||void 0===r?void 0:r.length))&&this.props.updateProps({node:this.portalRef.current,props:this.props.props})}componentDidMount(){this.portalRef.current&&this.props.mount(this.portalRef.current,this.props.props)}componentWillUnmount(){this.portalRef.current&&this.props.unmount(this.portalRef.current)}render(){var e,t;return E.createElement(E.Fragment,null,E.createElement("div",{ref:this.portalRef}),null===(t=this.props)||void 0===t?void 0:null===(e=t.customPagesPortals)||void 0===e?void 0:e.map((e,t)=>(0,E.createElement)(e,{key:t})))}constructor(){super(...arguments),this.portalRef=E.createRef()}}let tJ=tH(e=>{let{clerk:t,...n}=e;return E.createElement(tX,{mount:t.mountSignIn,unmount:t.unmountSignIn,updateProps:t.__unstable__updateProps,props:n})},"SignIn"),t$=tH(e=>{let{clerk:t,...n}=e;return E.createElement(tX,{mount:t.mountSignUp,unmount:t.unmountSignUp,updateProps:t.__unstable__updateProps,props:n})},"SignUp");function tZ(e){let{children:t}=e;return tN("Clerk: <UserProfile.Page /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`."),E.createElement(E.Fragment,null,t)}function tQ(e){let{children:t}=e;return tN("Clerk: <UserProfile.Link /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`."),E.createElement(E.Fragment,null,t)}let t0=Object.assign(tH(e=>{let{clerk:t,...n}=e,{customPages:r,customPagesPortals:i}=tW(n.children);return E.createElement(tX,{mount:t.mountUserProfile,unmount:t.unmountUserProfile,updateProps:t.__unstable__updateProps,props:{...n,customPages:r},customPagesPortals:i})},"UserProfile"),{Page:tZ,Link:tQ}),t1=Object.assign(tH(e=>{let{clerk:t,...n}=e,{customPages:r,customPagesPortals:i}=tW(n.children),a=Object.assign(n.userProfileProps||{},{customPages:r});return E.createElement(tX,{mount:t.mountUserButton,unmount:t.unmountUserButton,updateProps:t.__unstable__updateProps,props:{...n,userProfileProps:a},customPagesPortals:i})},"UserButton"),{UserProfilePage:tZ,UserProfileLink:tQ});function t2(e){let{children:t}=e;return tN("Clerk: <OrganizationProfile.Page /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`."),E.createElement(E.Fragment,null,t)}function t5(e){let{children:t}=e;return tN("Clerk: <OrganizationProfile.Link /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`."),E.createElement(E.Fragment,null,t)}let t4=Object.assign(tH(e=>{let{clerk:t,...n}=e,{customPages:r,customPagesPortals:i}=tV(n.children);return E.createElement(tX,{mount:t.mountOrganizationProfile,unmount:t.unmountOrganizationProfile,updateProps:t.__unstable__updateProps,props:{...n,customPages:r},customPagesPortals:i})},"OrganizationProfile"),{Page:t2,Link:t5}),t3=tH(e=>{let{clerk:t,...n}=e;return E.createElement(tX,{mount:t.mountCreateOrganization,unmount:t.unmountCreateOrganization,updateProps:t.__unstable__updateProps,props:n})},"CreateOrganization"),t9=Object.assign(tH(e=>{let{clerk:t,...n}=e,{customPages:r,customPagesPortals:i}=tV(n.children),a=Object.assign(n.organizationProfileProps||{},{customPages:r});return E.createElement(tX,{mount:t.mountOrganizationSwitcher,unmount:t.unmountOrganizationSwitcher,updateProps:t.__unstable__updateProps,props:{...n,organizationProfileProps:a},customPagesPortals:i})},"OrganizationSwitcher"),{OrganizationProfilePage:t2,OrganizationProfileLink:t5}),t8=tH(e=>{let{clerk:t,...n}=e;return E.createElement(tX,{mount:t.mountOrganizationList,unmount:t.unmountOrganizationList,updateProps:t.__unstable__updateProps,props:n})},"OrganizationList"),t7=e=>new Promise(t=>{e.loaded&&t(),e.addOnLoaded(t)}),t6=e=>async t=>(await t7(e),e.session)?e.session.getToken(t):null,ne=e=>async function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return await t7(e),e.signOut(...n)},nt=()=>{let{sessionId:e,userId:t,actor:n,orgId:r,orgRole:i,orgSlug:a,orgPermissions:o}=tx(),s=tO(),l=(0,E.useCallback)(t6(s),[s]),u=(0,E.useCallback)(ne(s),[s]),d=(0,E.useCallback)(e=>{if(!(null==e?void 0:e.permission)&&!(null==e?void 0:e.role))throw Error('Clerk: Missing parameters. `has` from `useAuth` requires a permission or role key to be passed. Example usage: `has({permission: "org:posts:edit"`');return!!r&&!!t&&!!i&&!!o&&(e.permission?o.includes(e.permission):!!e.role&&i===e.role)},[r,i,t,o]);if(void 0===e&&void 0===t)return{isLoaded:!1,isSignedIn:void 0,sessionId:e,userId:t,actor:void 0,orgId:void 0,orgRole:void 0,orgSlug:void 0,has:void 0,signOut:u,getToken:l};if(null===e&&null===t)return{isLoaded:!0,isSignedIn:!1,sessionId:e,userId:t,actor:null,orgId:null,orgRole:null,orgSlug:null,has:()=>!1,signOut:u,getToken:l};if(e&&t&&r&&i)return{isLoaded:!0,isSignedIn:!0,sessionId:e,userId:t,actor:n||null,orgId:r,orgRole:i,orgSlug:a||null,has:d,signOut:u,getToken:l};if(e&&t&&!r)return{isLoaded:!0,isSignedIn:!0,sessionId:e,userId:t,actor:n||null,orgId:null,orgRole:null,orgSlug:null,has:()=>!1,signOut:u,getToken:l};throw Error("Clerk: Invalid state. Feel free to submit a bug or reach out to support here: https://clerk.com/support")},nn=e=>{let{children:t}=e,{userId:n}=tx();return n?E.createElement(E.Fragment,null,t):null},nr=e=>{let{children:t}=e,{userId:n}=tx();return null===n?E.createElement(E.Fragment,null,t):null},ni=e=>{let{children:t}=e;return tO().loaded?E.createElement(tI,null,t):null},na=e=>{let{children:t}=e;return tO().loaded?null:E.createElement(E.Fragment,null,t)},no=e=>{let{children:t,fallback:n,...r}=e,{isLoaded:i,has:a,userId:o}=nt();if(!i)return null;let s=E.createElement(E.Fragment,null,null!=n?n:null),l=E.createElement(E.Fragment,null,t);return o?"function"==typeof r.condition?r.condition(a)?l:s:r.role||r.permission?a(r)?l:s:l:s},ns=tH(e=>{let{clerk:t,...n}=e,{client:r,session:i}=t,{__unstable__environment:a}=t,o=r.activeSessions&&r.activeSessions.length>0;return E.useEffect(()=>{if(null===i&&o&&a){let{afterSignOutOneUrl:e}=a.displayConfig;t.navigate(e)}else t.redirectToSignIn(n)},[]),null},"RedirectToSignIn"),nl=tH(e=>{let{clerk:t,...n}=e;return E.useEffect(()=>{t.redirectToSignUp(n)},[]),null},"RedirectToSignUp"),nu=tH(e=>{let{clerk:t}=e;return E.useEffect(()=>{t.redirectToUserProfile()},[]),null},"RedirectToUserProfile"),nd=tH(e=>{let{clerk:t}=e;return E.useEffect(()=>{t.redirectToOrganizationProfile()},[]),null},"RedirectToOrganizationProfile"),nc=tH(e=>{let{clerk:t}=e;return E.useEffect(()=>{t.redirectToCreateOrganization()},[]),null},"RedirectToCreateOrganization"),nh=tH(e=>{let{clerk:t,...n}=e;return E.useEffect(()=>{t.handleRedirectCallback(n)},[]),null},"AuthenticateWithRedirectCallback"),np=e=>{let{children:t}=e,n=th();return E.createElement(E.Fragment,{key:n?n.id:"no-users"},t)},nf=(e,t)=>{t=t||e.displayName||e.name||"Component",e.displayName=t;let n=t=>{let n=tl();return n?E.createElement(e,{...t,user:n}):null};return n.displayName="withUser(".concat(t,")"),n},ng=e=>{let{children:t}=e,n=tl();if("function"!=typeof t)throw Error(B);return n?E.createElement(E.Fragment,null,t(n)):null},nm=(e,t)=>{t=t||e.displayName||e.name||"Component",e.displayName=t;let n=t=>{let n=th();return n?E.createElement(e,{...t,session:n}):null};return n.displayName="withSession(".concat(t,")"),n},nv=e=>{let{children:t}=e,n=th();if("function"!=typeof t)throw Error(B);return n?E.createElement(E.Fragment,null,t(n)):null},nb=e=>t=>{try{return E.Children.only(e)}catch(e){throw Error(D(t))}},ny=(e,t)=>(e||(e=t),"string"==typeof e&&(e=E.createElement("button",null,e)),e),nk=e=>function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];if(e&&"function"==typeof e)return e(...n)},nw=tH(e=>{let{clerk:t,children:n,...r}=e,{afterSignInUrl:i,afterSignUpUrl:a,redirectUrl:o,mode:s,...l}=r,u=nb(n=ny(n,"Sign in"))("SignInButton"),d=()=>{let e={afterSignInUrl:i,afterSignUpUrl:a,redirectUrl:o};return"modal"===s?t.openSignIn(e):t.redirectToSignIn(e)},c=async e=>(await nk(u.props.onClick)(e),d()),h={...l,onClick:c};return E.cloneElement(u,h)},"SignInButton"),nE=tH(e=>{let{clerk:t,children:n,...r}=e,{afterSignInUrl:i,afterSignUpUrl:a,redirectUrl:o,mode:s,unsafeMetadata:l,...u}=r,d=nb(n=ny(n,"Sign up"))("SignUpButton"),c=()=>{let e={afterSignInUrl:i,afterSignUpUrl:a,redirectUrl:o,unsafeMetadata:l};return"modal"===s?t.openSignUp(e):t.redirectToSignUp(e)},h=async e=>(await nk(d.props.onClick)(e),c()),p={...u,onClick:h};return E.cloneElement(d,p)},"SignUpButton"),nS=tH(e=>{let{clerk:t,children:n,...r}=e,{signOutCallback:i,signOutOptions:a,...o}=r,s=nb(n=ny(n,"Sign out"))("SignOutButton"),l=()=>t.signOut(i,a),u=async e=>(await nk(s.props.onClick)(e),l()),d={...o,onClick:u};return E.cloneElement(s,d)},"SignOutButton"),nC=tH(e=>{let{clerk:t,children:n,...r}=e,{redirectUrl:i,...a}=r,o=nb(n=ny(n,"Sign in with Metamask"))("SignInWithMetamaskButton"),s=async()=>{!async function(){await t.authenticateWithMetamask({redirectUrl:i})}()},l=async e=>(await nk(o.props.onClick)(e),s()),u={...a,onClick:l};return E.cloneElement(o,u)},"SignInWithMetamask");function nP(){let e=tl();return void 0===e?{isLoaded:!1,isSignedIn:void 0,user:void 0}:null===e?{isLoaded:!0,isSignedIn:!1,user:null}:{isLoaded:!0,isSignedIn:!0,user:e}}let nx=()=>{let e=th();return void 0===e?{isLoaded:!1,isSignedIn:void 0,session:void 0}:null===e?{isLoaded:!0,isSignedIn:!1,session:null}:{isLoaded:!0,isSignedIn:!0,session:e}},nz=()=>tO(),nO=()=>{let e=tO(),t=td();return t?{isLoaded:!0,signIn:t.signIn,setSession:e.setSession,setActive:e.setActive}:{isLoaded:!1,signIn:void 0,setSession:void 0,setActive:void 0}},n_=()=>{let e=tO(),t=td();return t?{isLoaded:!0,signUp:t.signUp,setSession:e.setSession,setActive:e.setActive}:{isLoaded:!1,signUp:void 0,setSession:void 0,setActive:void 0}},nU=()=>{let e=tO(),t=td();return t?{isLoaded:!0,sessions:t.sessions,setSession:e.setSession,setActive:e.setActive}:{isLoaded:!1,sessions:void 0,setSession:void 0,setActive:void 0}};function nL(e){z("useMagicLink","Use `useEmailLink` instead.");let{startMagicLinkFlow:t,cancelMagicLinkFlow:n}=E.useMemo(()=>e.createMagicLinkFlow(),[e]);return E.useEffect(()=>n,[]),{startMagicLinkFlow:t,cancelMagicLinkFlow:n}}function nj(e){let{startEmailLinkFlow:t,cancelEmailLinkFlow:n}=E.useMemo(()=>e.createEmailLinkFlow(),[e]);return E.useEffect(()=>n,[]),{startEmailLinkFlow:t,cancelEmailLinkFlow:n}}},218:function(e,t,n){window.global||(window.global=void 0===n.g?window:n.g)},4041:function(e,t,n){"use strict";n.r(t),n.d(t,{ClientClerkProvider:function(){return c}});var r=n(1693),i=n(5313),a=n(4090),o=n(9854);let s=a.useLayoutEffect;var l=n(9079);let u=e=>({...e,frontendApi:e.frontendApi||l.env.NEXT_PUBLIC_CLERK_FRONTEND_API||"",publishableKey:e.publishableKey||"pk_test_aGFybWxlc3Mtam9leS0zMi5jbGVyay5hY2NvdW50cy5kZXYk",clerkJSUrl:e.clerkJSUrl||l.env.NEXT_PUBLIC_CLERK_JS,clerkJSVersion:e.clerkJSVersion||l.env.NEXT_PUBLIC_CLERK_JS_VERSION,proxyUrl:e.proxyUrl||l.env.NEXT_PUBLIC_CLERK_PROXY_URL||"",domain:e.domain||l.env.NEXT_PUBLIC_CLERK_DOMAIN||"",isSatellite:e.isSatellite||"true"===l.env.NEXT_PUBLIC_CLERK_IS_SATELLITE,signInUrl:e.signInUrl||l.env.NEXT_PUBLIC_CLERK_SIGN_IN_URL||"",signUpUrl:e.signUpUrl||l.env.NEXT_PUBLIC_CLERK_SIGN_UP_URL||"",afterSignInUrl:e.afterSignInUrl||l.env.NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL||"",afterSignUpUrl:e.afterSignUpUrl||l.env.NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL||"",sdkMetadata:{name:"@clerk/nextjs",version:"4.29.7"}}),d=()=>{let{push:e}=(0,i.useRouter)(),[t,n]=(0,a.useTransition)(),r=(0,a.useRef)(),o=(0,a.useRef)([]);return r.current||(r.current=(t,r)=>new Promise(i=>{o.current.push(i),n(()=>{e(t,r)})})),(0,a.useEffect)(()=>{var e;t||((null==o?void 0:null===(e=o.current)||void 0===e?void 0:e.length)&&o.current.forEach(e=>e()),o.current=[])},[t]),r.current},c=e=>{let{__unstable_invokeMiddlewareOnAuthStateChange:t=!0}=e,n=(0,i.useRouter)(),l=d();s(()=>{window.__unstable__onBeforeSetActive=()=>{t&&(n.refresh(),n.push(window.location.href))},window.__unstable__onAfterSetActive=()=>{n.refresh()}},[]);let c=u({...e,navigate:l});return a.createElement(o.f,{options:c},a.createElement(r.El,{...c}))}},9854:function(e,t,n){"use strict";n.d(t,{f:function(){return o},k:function(){return a}});var r=n(4090);let i=r.createContext(void 0);i.displayName="ClerkNextOptionsCtx";let a=()=>r.useContext(i).value,o=e=>{let{children:t,options:n}=e;return r.createElement(i.Provider,{value:{value:n}},t)}},8999:function(e,t,n){"use strict";n.r(t),n.d(t,{AuthenticateWithRedirectCallback:function(){return r.vn},ClerkLoaded:function(){return r.a7},ClerkLoading:function(){return r.qI},MultisessionAppSupport:function(){return r.KM},Protect:function(){return r.Cv},RedirectToCreateOrganization:function(){return r.gM},RedirectToOrganizationProfile:function(){return r.yB},RedirectToSignIn:function(){return r.N1},RedirectToSignUp:function(){return r.C2},RedirectToUserProfile:function(){return r.sO},SignedIn:function(){return r.CH},SignedOut:function(){return r.tj}});var r=n(1693)},747:function(e,t,n){"use strict";n.r(t),n.d(t,{EmailLinkErrorCode:function(){return r._L},MagicLinkErrorCode:function(){return r.X1},WithClerk:function(){return r._E},WithSession:function(){return r.CJ},WithUser:function(){return r.Gi},isClerkAPIResponseError:function(){return r.kD},isEmailLinkError:function(){return r.G1},isKnownError:function(){return r.sZ},isMagicLinkError:function(){return r.V9},isMetamaskError:function(){return r.ZC},useAuth:function(){return r.aC},useClerk:function(){return r.ll},useEmailLink:function(){return r.E2},useMagicLink:function(){return r.jS},useOrganization:function(){return r.o8},useOrganizationList:function(){return r.eW},useOrganizations:function(){return r.qi},useSession:function(){return r.kP},useSessionList:function(){return r.xo},useSignIn:function(){return r.zq},useSignUp:function(){return r.QS},useUser:function(){return r.aF},withClerk:function(){return r.r0},withSession:function(){return r.NA},withUser:function(){return r.ns}});var r=n(1693)},1934:function(e,t,n){"use strict";n.r(t),n.d(t,{CreateOrganization:function(){return r.Gp},OrganizationList:function(){return r.Bg},OrganizationProfile:function(){return r.A},OrganizationSwitcher:function(){return r.Li},SignIn:function(){return o},SignInButton:function(){return r.$d},SignInWithMetamaskButton:function(){return r.qu},SignOutButton:function(){return r.AM},SignUp:function(){return s},SignUpButton:function(){return r.gX},UserButton:function(){return r.l8},UserProfile:function(){return r.Iw}});var r=n(1693),i=n(4090),a=n(9854);let o=e=>{let{signInUrl:t}=(0,a.k)();return t?i.createElement(r.cL,{routing:"path",path:t,...e}):i.createElement(r.cL,{...e})},s=e=>{let{signUpUrl:t}=(0,a.k)();return t?i.createElement(r.Mo,{routing:"path",path:t,...e}):i.createElement(r.Mo,{...e})}},9079:function(e,t,n){"use strict";var r,i;e.exports=(null==(r=n.g.process)?void 0:r.env)&&"object"==typeof(null==(i=n.g.process)?void 0:i.env)?n.g.process:n(3127)},3127:function(e){"use strict";!function(){var t={229:function(e){var t,n,r,i=e.exports={};function a(){throw Error("setTimeout has not been defined")}function o(){throw Error("clearTimeout has not been defined")}function s(e){if(t===setTimeout)return setTimeout(e,0);if((t===a||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(n){try{return t.call(null,e,0)}catch(n){return t.call(this,e,0)}}}!function(){try{t="function"==typeof setTimeout?setTimeout:a}catch(e){t=a}try{n="function"==typeof clearTimeout?clearTimeout:o}catch(e){n=o}}();var l=[],u=!1,d=-1;function c(){u&&r&&(u=!1,r.length?l=r.concat(l):d=-1,l.length&&h())}function h(){if(!u){var e=s(c);u=!0;for(var t=l.length;t;){for(r=l,l=[];++d<t;)r&&r[d].run();d=-1,t=l.length}r=null,u=!1,function(e){if(n===clearTimeout)return clearTimeout(e);if((n===o||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(e);try{n(e)}catch(t){try{return n.call(null,e)}catch(t){return n.call(this,e)}}}(e)}}function p(e,t){this.fun=e,this.array=t}function f(){}i.nextTick=function(e){var t=Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];l.push(new p(e,t)),1!==l.length||u||s(h)},p.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=f,i.addListener=f,i.once=f,i.off=f,i.removeListener=f,i.removeAllListeners=f,i.emit=f,i.prependListener=f,i.prependOnceListener=f,i.listeners=function(e){return[]},i.binding=function(e){throw Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw Error("process.chdir is not supported")},i.umask=function(){return 0}}},n={};function r(e){var i=n[e];if(void 0!==i)return i.exports;var a=n[e]={exports:{}},o=!0;try{t[e](a,a.exports,r),o=!1}finally{o&&delete n[e]}return a.exports}r.ab="//";var i=r(229);e.exports=i}()},221:function(e,t,n){"use strict";/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=n(4090),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=r.useState,o=r.useEffect,s=r.useLayoutEffect,l=r.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!i(e,n)}catch(e){return!0}}var d=void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=a({inst:{value:n,getSnapshot:t}}),i=r[0].inst,d=r[1];return s(function(){i.value=n,i.getSnapshot=t,u(i)&&d({inst:i})},[e,n,t]),o(function(){return u(i)&&d({inst:i}),e(function(){u(i)&&d({inst:i})})},[e]),l(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:d},2362:function(e,t,n){"use strict";e.exports=n(221)},6288:function(e,t,n){"use strict";n.r(t),n.d(t,{Toaster:function(){return y},toast:function(){return g}});var r=n(4090),i=n(9542);!function(e){let{insertAt:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!e||"undefined"==typeof document)return;let n=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css","top"===t&&n.firstChild?n.insertBefore(r,n.firstChild):n.appendChild(r),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e))}('html[dir=ltr],[data-sonner-toaster][dir=ltr]{--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}html[dir=rtl],[data-sonner-toaster][dir=rtl]{--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}[data-sonner-toaster]{position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999}[data-sonner-toaster][data-x-position=right]{right:max(var(--offset),env(safe-area-inset-right))}[data-sonner-toaster][data-x-position=left]{left:max(var(--offset),env(safe-area-inset-left))}[data-sonner-toaster][data-x-position=center]{left:50%;transform:translate(-50%)}[data-sonner-toaster][data-y-position=top]{top:max(var(--offset),env(safe-area-inset-top))}[data-sonner-toaster][data-y-position=bottom]{bottom:max(var(--offset),env(safe-area-inset-bottom))}[data-sonner-toast]{--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);touch-action:none;will-change:transform,opacity,height;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}[data-sonner-toast][data-styled=true]{padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}[data-sonner-toast]:focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}[data-sonner-toast][data-y-position=top]{top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}[data-sonner-toast][data-y-position=bottom]{bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}[data-sonner-toast] [data-description]{font-weight:400;line-height:1.4;color:inherit}[data-sonner-toast] [data-title]{font-weight:500;line-height:1.5;color:inherit}[data-sonner-toast] [data-icon]{display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}[data-sonner-toast][data-promise=true] [data-icon]>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}[data-sonner-toast] [data-icon]>*{flex-shrink:0}[data-sonner-toast] [data-icon] svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}[data-sonner-toast] [data-content]{display:flex;flex-direction:column;gap:2px}[data-sonner-toast] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}[data-sonner-toast] [data-button]:focus-visible{box-shadow:0 0 0 2px #0006}[data-sonner-toast] [data-button]:first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}[data-sonner-toast] [data-cancel]{color:var(--normal-text);background:rgba(0,0,0,.08)}[data-sonner-toast][data-theme=dark] [data-cancel]{background:rgba(255,255,255,.3)}[data-sonner-toast] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;background:var(--gray1);color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast] [data-close-button]:focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}[data-sonner-toast] [data-disabled=true]{cursor:not-allowed}[data-sonner-toast]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}[data-sonner-toast][data-swiping=true]:before{content:"";position:absolute;left:0;right:0;height:100%;z-index:-1}[data-sonner-toast][data-y-position=top][data-swiping=true]:before{bottom:50%;transform:scaleY(3) translateY(50%)}[data-sonner-toast][data-y-position=bottom][data-swiping=true]:before{top:50%;transform:scaleY(3) translateY(-50%)}[data-sonner-toast][data-swiping=false][data-removed=true]:before{content:"";position:absolute;inset:0;transform:scaleY(2)}[data-sonner-toast]:after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}[data-sonner-toast][data-mounted=true]{--y: translateY(0);opacity:1}[data-sonner-toast][data-expanded=false][data-front=false]{--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}[data-sonner-toast]>*{transition:opacity .4s}[data-sonner-toast][data-expanded=false][data-front=false][data-styled=true]>*{opacity:0}[data-sonner-toast][data-visible=false]{opacity:0;pointer-events:none}[data-sonner-toast][data-mounted=true][data-expanded=true]{--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}[data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]{--y: translateY(calc(var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]{--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]{--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}[data-sonner-toast][data-removed=true][data-front=false]:before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount, 0px));transition:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation:swipe-out .2s ease-out forwards}@keyframes swipe-out{0%{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount)));opacity:1}to{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount) + var(--lift) * -100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;--mobile-offset: 16px;right:var(--mobile-offset);left:var(--mobile-offset);width:100%}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - 32px)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset)}[data-sonner-toaster][data-y-position=bottom]{bottom:20px}[data-sonner-toaster][data-y-position=top]{top:20px}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset);right:var(--mobile-offset);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-rich-colors=true] [data-sonner-toast][data-type=success],[data-rich-colors=true] [data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true] [data-sonner-toast][data-type=info],[data-rich-colors=true] [data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true] [data-sonner-toast][data-type=warning],[data-rich-colors=true] [data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true] [data-sonner-toast][data-type=error],[data-rich-colors=true] [data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}\n');var a=e=>{switch(e){case"success":return l;case"info":return d;case"warning":return u;case"error":return c;default:return null}},o=Array(12).fill(0),s=e=>{let{visible:t}=e;return r.createElement("div",{className:"sonner-loading-wrapper","data-visible":t},r.createElement("div",{className:"sonner-spinner"},o.map((e,t)=>r.createElement("div",{className:"sonner-loading-bar",key:"spinner-bar-".concat(t)}))))},l=r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},r.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),u=r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},r.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),d=r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},r.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),c=r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},r.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),h=()=>{let[e,t]=r.useState(!1);return r.useEffect(()=>{let e=()=>{t(document.hidden)};return document.addEventListener("visibilitychange",e),()=>window.removeEventListener("visibilitychange",e)},[]),e},p=1,f=new class{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}),this.publish=e=>{this.subscribers.forEach(t=>t(e))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var t;let{message:n,...r}=e,i="number"==typeof(null==e?void 0:e.id)||(null==(t=e.id)?void 0:t.length)>0?e.id:p++,a=this.toasts.find(e=>e.id===i),o=void 0===e.dismissible||e.dismissible;return a?this.toasts=this.toasts.map(t=>t.id===i?(this.publish({...t,...e,id:i,title:n}),{...t,...e,id:i,dismissible:o,title:n}):t):this.addToast({title:n,...r,dismissible:o,id:i}),i},this.dismiss=e=>(e||this.toasts.forEach(e=>{this.subscribers.forEach(t=>t({id:e.id,dismiss:!0}))}),this.subscribers.forEach(t=>t({id:e,dismiss:!0})),e),this.message=(e,t)=>this.create({...t,message:e}),this.error=(e,t)=>this.create({...t,message:e,type:"error"}),this.success=(e,t)=>this.create({...t,type:"success",message:e}),this.info=(e,t)=>this.create({...t,type:"info",message:e}),this.warning=(e,t)=>this.create({...t,type:"warning",message:e}),this.loading=(e,t)=>this.create({...t,type:"loading",message:e}),this.promise=(e,t)=>{let n;if(!t)return;void 0!==t.loading&&(n=this.create({...t,promise:e,type:"loading",message:t.loading,description:"function"!=typeof t.description?t.description:void 0}));let r=e instanceof Promise?e:e(),i=void 0!==n;return r.then(e=>{if(e&&"boolean"==typeof e.ok&&!e.ok){i=!1;let r="function"==typeof t.error?t.error("HTTP error! status: ".concat(e.status)):t.error,a="function"==typeof t.description?t.description("HTTP error! status: ".concat(e.status)):t.description;this.create({id:n,type:"error",message:r,description:a})}else if(void 0!==t.success){i=!1;let r="function"==typeof t.success?t.success(e):t.success,a="function"==typeof t.description?t.description(e):t.description;this.create({id:n,type:"success",message:r,description:a})}}).catch(e=>{if(void 0!==t.error){i=!1;let r="function"==typeof t.error?t.error(e):t.error,a="function"==typeof t.description?t.description(e):t.description;this.create({id:n,type:"error",message:r,description:a})}}).finally(()=>{var e;i&&(this.dismiss(n),n=void 0),null==(e=t.finally)||e.call(t)}),n},this.custom=(e,t)=>{let n=(null==t?void 0:t.id)||p++;return this.create({jsx:e(n),id:n,...t}),n},this.subscribers=[],this.toasts=[]}},g=Object.assign((e,t)=>{let n=(null==t?void 0:t.id)||p++;return f.addToast({title:e,...t,id:n}),n},{success:f.success,info:f.info,warning:f.warning,error:f.error,custom:f.custom,message:f.message,promise:f.promise,dismiss:f.dismiss,loading:f.loading});function m(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter(Boolean).join(" ")}var v=e=>{var t,n,i,o,l,u,d;let{invert:c,toast:p,unstyled:f,interacting:g,setHeights:v,visibleToasts:b,heights:y,index:k,toasts:w,expanded:E,removeToast:S,closeButton:C,style:P,cancelButtonStyle:x,actionButtonStyle:z,className:O="",descriptionClassName:_="",duration:U,position:L,gap:j=14,loadingIcon:M,expandByDefault:I,classNames:T,closeButtonAriaLabel:R="Close toast",pauseWhenPageIsHidden:N}=e,[A,B]=r.useState(!1),[D,W]=r.useState(!1),[V,F]=r.useState(!1),[K,Y]=r.useState(!1),[G,H]=r.useState(0),[q,X]=r.useState(0),J=r.useRef(null),$=r.useRef(null),Z=0===k,Q=k+1<=b,ee=p.type,et=!1!==p.dismissible,en=p.className||"",er=p.descriptionClassName||"",ei=r.useMemo(()=>y.findIndex(e=>e.toastId===p.id)||0,[y,p.id]),ea=r.useMemo(()=>{var e;return null!=(e=p.closeButton)?e:C},[p.closeButton,C]),eo=r.useMemo(()=>p.duration||U||4e3,[p.duration,U]),es=r.useRef(0),el=r.useRef(0),eu=r.useRef(0),ed=r.useRef(null),[ec,eh]=L.split("-"),ep=r.useMemo(()=>y.reduce((e,t,n)=>n>=ei?e:e+t.height,0),[y,ei]),ef=h(),eg=p.invert||c,em="loading"===ee;el.current=r.useMemo(()=>ei*j+ep,[ei,ep]),r.useEffect(()=>{B(!0)},[]),r.useLayoutEffect(()=>{if(!A)return;let e=$.current,t=e.style.height;e.style.height="auto";let n=e.getBoundingClientRect().height;e.style.height=t,X(n),v(e=>e.find(e=>e.toastId===p.id)?e.map(e=>e.toastId===p.id?{...e,height:n}:e):[{toastId:p.id,height:n,position:p.position},...e])},[A,p.title,p.description,v,p.id]);let ev=r.useCallback(()=>{W(!0),H(el.current),v(e=>e.filter(e=>e.toastId!==p.id)),setTimeout(()=>{S(p)},200)},[p,S,v,el]);return r.useEffect(()=>{if(p.promise&&"loading"===ee||p.duration===1/0||"loading"===p.type)return;let e,t=eo;return E||g||N&&ef?(()=>{if(eu.current<es.current){let e=new Date().getTime()-es.current;t-=e}eu.current=new Date().getTime()})():(es.current=new Date().getTime(),e=setTimeout(()=>{var e;null==(e=p.onAutoClose)||e.call(p,p),ev()},t)),()=>clearTimeout(e)},[E,g,I,p,eo,ev,p.promise,ee,N,ef]),r.useEffect(()=>{let e=$.current;if(e){let t=e.getBoundingClientRect().height;return X(t),v(e=>[{toastId:p.id,height:t,position:p.position},...e]),()=>v(e=>e.filter(e=>e.toastId!==p.id))}},[v,p.id]),r.useEffect(()=>{p.delete&&ev()},[ev,p.delete]),r.createElement("li",{"aria-live":p.important?"assertive":"polite","aria-atomic":"true",role:"status",tabIndex:0,ref:$,className:m(O,en,null==T?void 0:T.toast,null==(t=null==p?void 0:p.classNames)?void 0:t.toast,null==T?void 0:T[ee],null==(n=null==p?void 0:p.classNames)?void 0:n[ee]),"data-sonner-toast":"","data-styled":!(p.jsx||p.unstyled||f),"data-mounted":A,"data-promise":!!p.promise,"data-removed":D,"data-visible":Q,"data-y-position":ec,"data-x-position":eh,"data-index":k,"data-front":Z,"data-swiping":V,"data-dismissible":et,"data-type":ee,"data-invert":eg,"data-swipe-out":K,"data-expanded":!!(E||I&&A),style:{"--index":k,"--toasts-before":k,"--z-index":w.length-k,"--offset":"".concat(D?G:el.current,"px"),"--initial-height":I?"auto":"".concat(q,"px"),...P,...p.style},onPointerDown:e=>{em||!et||(J.current=new Date,H(el.current),e.target.setPointerCapture(e.pointerId),"BUTTON"!==e.target.tagName&&(F(!0),ed.current={x:e.clientX,y:e.clientY}))},onPointerUp:()=>{var e,t,n,r;if(K||!et)return;ed.current=null;let i=Number((null==(e=$.current)?void 0:e.style.getPropertyValue("--swipe-amount").replace("px",""))||0),a=new Date().getTime()-(null==(t=J.current)?void 0:t.getTime());if(Math.abs(i)>=20||Math.abs(i)/a>.11){H(el.current),null==(n=p.onDismiss)||n.call(p,p),ev(),Y(!0);return}null==(r=$.current)||r.style.setProperty("--swipe-amount","0px"),F(!1)},onPointerMove:e=>{var t;if(!ed.current||!et)return;let n=e.clientY-ed.current.y,r=e.clientX-ed.current.x,i=("top"===ec?Math.min:Math.max)(0,n),a="touch"===e.pointerType?10:2;Math.abs(i)>a?null==(t=$.current)||t.style.setProperty("--swipe-amount","".concat(n,"px")):Math.abs(r)>a&&(ed.current=null)}},ea&&!p.jsx?r.createElement("button",{"aria-label":R,"data-disabled":em,"data-close-button":!0,onClick:em||!et?()=>{}:()=>{var e;ev(),null==(e=p.onDismiss)||e.call(p,p)},className:m(null==T?void 0:T.closeButton,null==(i=null==p?void 0:p.classNames)?void 0:i.closeButton)},r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},r.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),r.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"}))):null,p.jsx||r.isValidElement(p.title)?p.jsx||p.title:r.createElement(r.Fragment,null,ee||p.icon||p.promise?r.createElement("div",{"data-icon":""},(p.promise||"loading"===p.type)&&!p.icon?M?r.createElement("div",{className:"sonner-loader","data-visible":"loading"===ee},M):r.createElement(s,{visible:"loading"===ee}):null,p.icon||a(ee)):null,r.createElement("div",{"data-content":""},r.createElement("div",{"data-title":"",className:m(null==T?void 0:T.title,null==(o=null==p?void 0:p.classNames)?void 0:o.title)},p.title),p.description?r.createElement("div",{"data-description":"",className:m(_,er,null==T?void 0:T.description,null==(l=null==p?void 0:p.classNames)?void 0:l.description)},p.description):null),p.cancel?r.createElement("button",{"data-button":!0,"data-cancel":!0,style:p.cancelButtonStyle||x,onClick:e=>{var t;et&&(ev(),null!=(t=p.cancel)&&t.onClick&&p.cancel.onClick(e))},className:m(null==T?void 0:T.cancelButton,null==(u=null==p?void 0:p.classNames)?void 0:u.cancelButton)},p.cancel.label):null,p.action?r.createElement("button",{"data-button":"",style:p.actionButtonStyle||z,onClick:e=>{var t;null==(t=p.action)||t.onClick(e),e.defaultPrevented||ev()},className:m(null==T?void 0:T.actionButton,null==(d=null==p?void 0:p.classNames)?void 0:d.actionButton)},p.action.label):null))};function b(){if("undefined"==typeof document)return"ltr";let e=document.documentElement.getAttribute("dir");return"auto"!==e&&e?e:window.getComputedStyle(document.documentElement).direction}var y=e=>{let{invert:t,position:n="bottom-right",hotkey:a=["altKey","KeyT"],expand:o,closeButton:s,className:l,offset:u,theme:d="light",richColors:c,duration:h,style:p,visibleToasts:g=3,toastOptions:m,dir:y=b(),gap:k,loadingIcon:w,containerAriaLabel:E="Notifications",pauseWhenPageIsHidden:S}=e,[C,P]=r.useState([]),x=r.useMemo(()=>Array.from(new Set([n].concat(C.filter(e=>e.position).map(e=>e.position)))),[C,n]),[z,O]=r.useState([]),[_,U]=r.useState(!1),[L,j]=r.useState(!1),[M,I]=r.useState("system"!==d?d:window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),T=r.useRef(null),R=a.join("+").replace(/Key/g,"").replace(/Digit/g,""),N=r.useRef(null),A=r.useRef(!1),B=r.useCallback(e=>P(t=>t.filter(t=>{let{id:n}=t;return n!==e.id})),[]);return r.useEffect(()=>f.subscribe(e=>{if(e.dismiss){P(t=>t.map(t=>t.id===e.id?{...t,delete:!0}:t));return}setTimeout(()=>{i.flushSync(()=>{P(t=>{let n=t.findIndex(t=>t.id===e.id);return -1!==n?[...t.slice(0,n),{...t[n],...e},...t.slice(n+1)]:[e,...t]})})})}),[]),r.useEffect(()=>{if("system"!==d){I(d);return}"system"===d&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?I("dark"):I("light")),window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",e=>{let{matches:t}=e;I(t?"dark":"light")})},[d]),r.useEffect(()=>{C.length<=1&&U(!1)},[C]),r.useEffect(()=>{let e=e=>{var t,n;a.every(t=>e[t]||e.code===t)&&(U(!0),null==(t=T.current)||t.focus()),"Escape"===e.code&&(document.activeElement===T.current||null!=(n=T.current)&&n.contains(document.activeElement))&&U(!1)};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[a]),r.useEffect(()=>{if(T.current)return()=>{N.current&&(N.current.focus({preventScroll:!0}),N.current=null,A.current=!1)}},[T.current]),C.length?r.createElement("section",{"aria-label":"".concat(E," ").concat(R),tabIndex:-1},x.map((e,n)=>{var i;let[a,d]=e.split("-");return r.createElement("ol",{key:e,dir:"auto"===y?b():y,tabIndex:-1,ref:T,className:l,"data-sonner-toaster":!0,"data-theme":M,"data-rich-colors":c,"data-y-position":a,"data-x-position":d,style:{"--front-toast-height":"".concat(null==(i=z[0])?void 0:i.height,"px"),"--offset":"number"==typeof u?"".concat(u,"px"):u||"32px","--width":"".concat(356,"px"),"--gap":"".concat(14,"px"),...p},onBlur:e=>{A.current&&!e.currentTarget.contains(e.relatedTarget)&&(A.current=!1,N.current&&(N.current.focus({preventScroll:!0}),N.current=null))},onFocus:e=>{e.target instanceof HTMLElement&&"false"===e.target.dataset.dismissible||A.current||(A.current=!0,N.current=e.relatedTarget)},onMouseEnter:()=>U(!0),onMouseMove:()=>U(!0),onMouseLeave:()=>{L||U(!1)},onPointerDown:e=>{e.target instanceof HTMLElement&&"false"===e.target.dataset.dismissible||j(!0)},onPointerUp:()=>j(!1)},C.filter(t=>!t.position&&0===n||t.position===e).map((n,i)=>{var a,l;return r.createElement(v,{key:n.id,index:i,toast:n,duration:null!=(a=null==m?void 0:m.duration)?a:h,className:null==m?void 0:m.className,descriptionClassName:null==m?void 0:m.descriptionClassName,invert:t,visibleToasts:g,closeButton:null!=(l=null==m?void 0:m.closeButton)?l:s,interacting:L,position:e,style:null==m?void 0:m.style,unstyled:null==m?void 0:m.unstyled,classNames:null==m?void 0:m.classNames,cancelButtonStyle:null==m?void 0:m.cancelButtonStyle,actionButtonStyle:null==m?void 0:m.actionButtonStyle,removeToast:B,toasts:C.filter(e=>e.position==n.position),heights:z.filter(e=>e.position==n.position),setHeights:O,expandByDefault:o,gap:k,loadingIcon:w,expanded:_,pauseWhenPageIsHidden:S})}))})):null}}}]);