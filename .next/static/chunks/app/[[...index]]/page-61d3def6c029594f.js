(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[169],{372:function(e,s,a){Promise.resolve().then(a.bind(a,4041)),Promise.resolve().then(a.bind(a,8999)),Promise.resolve().then(a.bind(a,747)),Promise.resolve().then(a.bind(a,1934)),Promise.resolve().then(a.bind(a,9461))},9461:function(e,s,a){"use strict";a.r(s),a.d(s,{TelegramGroupForm:function(){return E}});var r=a(3827),l=a(4090),t=a(9143),n=a(7742),i=a(3167),o=a(1367);function c(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,o.m6)((0,i.W)(s))}let m=(0,n.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),d=l.forwardRef((e,s)=>{let{className:a,variant:l,size:n,asChild:i=!1,...o}=e,d=i?t.g7:"button";return(0,r.jsx)(d,{className:c(m({variant:l,size:n,className:a})),ref:s,...o})});d.displayName="Button";let u=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)("div",{ref:s,className:c("rounded-xl border bg-card text-card-foreground shadow",a),...l})});u.displayName="Card";let p=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)("div",{ref:s,className:c("flex flex-col space-y-1.5 p-6",a),...l})});p.displayName="CardHeader";let h=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)("div",{ref:s,className:c("font-semibold leading-none tracking-tight",a),...l})});h.displayName="CardTitle",l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)("div",{ref:s,className:c("text-sm text-muted-foreground",a),...l})}).displayName="CardDescription";let x=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)("div",{ref:s,className:c("p-6 pt-0",a),...l})});x.displayName="CardContent",l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)("div",{ref:s,className:c("flex items-center p-6 pt-0",a),...l})}).displayName="CardFooter";let j=l.forwardRef((e,s)=>{let{className:a,type:l,...t}=e;return(0,r.jsx)("input",{type:l,className:c("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:s,...t})});j.displayName="Input";var f=a(4602);let b=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),y=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)(f.f,{ref:s,className:c(b(),a),...l})});y.displayName=f.f.displayName;var g=a(6411),v=a(3441),N=a(5159),U=a(9259);let C=g.fC;g.ZA;let R=g.B4,S=l.forwardRef((e,s)=>{let{className:a,children:l,...t}=e;return(0,r.jsxs)(g.xz,{ref:s,className:c("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...t,children:[l,(0,r.jsx)(g.JO,{asChild:!0,children:(0,r.jsx)(v.Z,{className:"h-4 w-4 opacity-50"})})]})});S.displayName=g.xz.displayName;let M=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)(g.u_,{ref:s,className:c("flex cursor-default items-center justify-center py-1",a),...l,children:(0,r.jsx)(N.Z,{className:"h-4 w-4"})})});M.displayName=g.u_.displayName;let w=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)(g.$G,{ref:s,className:c("flex cursor-default items-center justify-center py-1",a),...l,children:(0,r.jsx)(v.Z,{className:"h-4 w-4"})})});w.displayName=g.$G.displayName;let k=l.forwardRef((e,s)=>{let{className:a,children:l,position:t="popper",...n}=e;return(0,r.jsx)(g.h_,{children:(0,r.jsxs)(g.VY,{ref:s,className:c("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:t,...n,children:[(0,r.jsx)(M,{}),(0,r.jsx)(g.l_,{className:c("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:l}),(0,r.jsx)(w,{})]})})});k.displayName=g.VY.displayName,l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)(g.__,{ref:s,className:c("px-2 py-1.5 text-sm font-semibold",a),...l})}).displayName=g.__.displayName;let D=l.forwardRef((e,s)=>{let{className:a,children:l,...t}=e;return(0,r.jsxs)(g.ck,{ref:s,className:c("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...t,children:[(0,r.jsx)("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(g.wU,{children:(0,r.jsx)(U.Z,{className:"h-4 w-4"})})}),(0,r.jsx)(g.eT,{children:l})]})});D.displayName=g.ck.displayName,l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)(g.Z0,{ref:s,className:c("-mx-1 my-1 h-px bg-muted",a),...l})}).displayName=g.Z0.displayName;var L=a(1693),O=a(7908),z=a(6288),P=a(124);let _=P.z.string().min(2,"Username must be at least 2 characters").max(32,"Username must be at most 32 characters").regex(/^@[a-zA-Z0-9_]+$/,"Username must start with @ and contain only letters, numbers, and underscores"),T=P.z.string().min(1,"Project name is required").max(100,"Project name must be at most 100 characters").trim(),F=P.z.string().url("Must be a valid URL").regex(/^https:\/\/(calendly\.com|cal\.com)/,"Must be a Calendly or Cal.com link").optional().or(P.z.literal("")),B=P.z.string().email("Must be a valid email address").optional();function V(e){try{return _.parse(e),{isValid:!0}}catch(e){if(e instanceof P.z.ZodError){var s;return{isValid:!1,error:null===(s=e.errors[0])||void 0===s?void 0:s.message}}return{isValid:!1,error:"Invalid username"}}}function I(e){try{return T.parse(e),{isValid:!0}}catch(e){if(e instanceof P.z.ZodError){var s;return{isValid:!1,error:null===(s=e.errors[0])||void 0===s?void 0:s.message}}return{isValid:!1,error:"Invalid project name"}}}function A(e,s){if(!e)return"";let a=(null==s?void 0:s.emoji)||"";return"".concat(a," ").concat(e," <> IBC Group")}function E(){var e,s,a;let{user:t}=(0,L.aF)(),[n,i]=(0,l.useState)([]),[o,c]=(0,l.useState)([]),[m,f]=(0,l.useState)(!0);(0,l.useEffect)(()=>{(async()=>{try{f(!0);let e=await O.Z.get("/api/team-data");console.log("\uD83D\uDCCA Team data response:",e.data);let s=(e.data.salesReps||[]).filter(e=>e.username&&""!==e.username.trim());console.log("✅ Filtered salesReps:",s),i(s),c(e.data.outreachMembers||[]),e.data.warning&&z.toast.warning(e.data.warning)}catch(e){console.error("❌ Failed to fetch team data:",e),z.toast.error("Failed to load team data. Some features may not work properly.")}finally{f(!1)}})()},[]);let[b,g]=(0,l.useState)(!1),[v,N]=(0,l.useState)(null),[U,M]=(0,l.useState)({projectName:"",projectLeads:[""],outreachTeamMember:"",outreachMemberName:"",outreachMemberUsername:"",customOutreachUsername:"",useCustomOutreachUsername:!1,salesperson:"",customSalesRepUsername:"",useCustomSalesRepUsername:!1,enterSalesRepManually:!1,customCalendlyLink:"",includeCalendly:!0,errors:{projectName:"",projectLead:"",outreachTeamMember:"",salesperson:"",outreachMemberUsername:"",customOutreachUsername:"",customSalesRepUsername:"",customCalendlyLink:""}});(0,l.useEffect)(()=>{(null==t?void 0:t.fullName)&&M(e=>({...e,outreachTeamMember:t.fullName||""}))},[t]);let w=(s=U.salesperson,n.find(e=>e.username===s)),_=(a=U.outreachMemberName,o.find(e=>e.name===a)),T=()=>{let e={projectName:"",projectLead:"",outreachTeamMember:"",salesperson:"",outreachMemberUsername:"",customOutreachUsername:"",customSalesRepUsername:"",customCalendlyLink:""},s=I(U.projectName);s.isValid||(e.projectName=s.error||"Invalid project name");let a=U.projectLeads.map(e=>e.trim()).filter(e=>e.length>0);if(0===a.length)e.projectLead="At least one project lead username is required";else for(let s of a){let a=V(s);if(!a.isValid){e.projectLead=a.error||"Invalid username format";break}}if(U.useCustomOutreachUsername){if(U.customOutreachUsername){let s=V(U.customOutreachUsername);s.isValid||(e.customOutreachUsername=s.error||"Invalid username format")}else e.customOutreachUsername="Custom BDR username is required"}else U.outreachMemberName&&U.outreachMemberUsername||(e.outreachMemberUsername="Outreach member and username are required");if(U.enterSalesRepManually){if(U.customSalesRepUsername){let s=V(U.customSalesRepUsername);s.isValid||(e.customSalesRepUsername=s.error||"Invalid username format")}else e.customSalesRepUsername="Sales rep username is required"}else if(U.salesperson){if(U.useCustomSalesRepUsername){if(U.customSalesRepUsername){let s=V(U.customSalesRepUsername);s.isValid||(e.customSalesRepUsername=s.error||"Invalid username format")}else e.customSalesRepUsername="Custom sales rep username is required"}}else e.salesperson="Sales representative is required";if(U.customCalendlyLink){let s=function(e){if(!e)return{isValid:!0};try{return F.parse(e),{isValid:!0}}catch(e){if(e instanceof P.z.ZodError){var s;return{isValid:!1,error:null===(s=e.errors[0])||void 0===s?void 0:s.message}}return{isValid:!1,error:"Invalid Calendly link"}}}(U.customCalendlyLink);s.isValid||(e.customCalendlyLink=s.error||"Invalid Calendly link")}return M(s=>({...s,errors:e})),!Object.values(e).some(e=>e)},B=async e=>{if(e.preventDefault(),T()){g(!0),N(null);try{let e;console.log("\uD83D\uDE80 Starting form submission..."),U.enterSalesRepManually?e=U.customCalendlyLink||void 0:w&&(e=w.calendarLink);let s=U.useCustomOutreachUsername?U.customOutreachUsername:U.outreachMemberUsername,a=U.enterSalesRepManually?U.customSalesRepUsername:U.useCustomSalesRepUsername&&w?U.customSalesRepUsername:null==w?void 0:w.username;if(!a){z.toast.error("Sales representative username is required");return}let r=U.projectLeads.map(e=>e.trim()).filter(e=>e.length>0),l="\uD83D\uDC65 Project Lead: ".concat(r.join(", "),"\n\uD83D\uDC68‍\uD83D\uDCBC Partnership Representative: ").concat(a,"\n\uD83D\uDC68‍\uD83D\uDCBB BDR: ").concat(s).concat(U.includeCalendly&&e?"\n\uD83D\uDCC5 Schedule a meeting: ".concat(e):""),t={projectName:U.projectName,projectLeads:r,outreachTeamMember:s,outreachMemberEmail:null==_?void 0:_.email,salesperson:U.enterSalesRepManually?"Custom Sales Rep":U.salesperson,salesRepUsername:a,paUsername:U.enterSalesRepManually?void 0:null==w?void 0:w.paUsername,calendlyLink:U.includeCalendly?e:void 0,includeCalendly:U.includeCalendly,welcomeMessage:l,inviteSalesRep:!0,salesRepEmoji:U.enterSalesRepManually?void 0:null==w?void 0:w.emoji,salesRepPAUsername:U.enterSalesRepManually?void 0:null==w?void 0:w.paUsername,salesRepCalendlyLink:U.enterSalesRepManually?U.customCalendlyLink:U.includeCalendly&&(null==w?void 0:w.calendarLink)?w.calendarLink:void 0,salesRepCategory:U.enterSalesRepManually?void 0:null==w?void 0:w.tier,outreachMemberUsernames:null==_?void 0:_.telegramUsernames,outreachMemberEmoji:null==_?void 0:_.emoji};console.log("\uD83D\uDCE4 Sending request:",t);let n=await O.Z.post("/api/create-group",t);n.data.success?(z.toast.success("\uD83C\uDF89 Telegram group created successfully!"),N({success:!0,failed_invites:n.data.failed_invites}),M({projectName:"",projectLeads:[""],outreachTeamMember:"",outreachMemberName:"",outreachMemberUsername:"",customOutreachUsername:"",useCustomOutreachUsername:!1,salesperson:"",customSalesRepUsername:"",useCustomSalesRepUsername:!1,enterSalesRepManually:!1,customCalendlyLink:"",includeCalendly:!0,errors:{projectName:"",projectLead:"",outreachTeamMember:"",salesperson:"",outreachMemberUsername:"",customOutreachUsername:"",customSalesRepUsername:"",customCalendlyLink:""}})):z.toast.error(n.data.error||"Failed to create group")}catch(r){var s,a;console.error("❌ Error creating group:",r);let e=(null===(a=r.response)||void 0===a?void 0:null===(s=a.data)||void 0===s?void 0:s.error)||r.message||"Failed to create Telegram group. Please try again.";z.toast.error(e),N({success:!1,failed_invites:[]})}finally{g(!1)}}};return console.log("Sales Representatives:",n),(0,r.jsxs)("div",{className:"container mx-auto p-4 max-w-3xl",children:[(0,r.jsx)("div",{children:b?(0,r.jsx)("div",{className:"fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50",children:(0,r.jsx)("div",{className:"bg-white/90 rounded-xl p-8 max-w-md w-full mx-4 text-center shadow-2xl",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center gap-6",children:[(0,r.jsxs)("div",{className:"relative w-24 h-24",children:[(0,r.jsx)("div",{className:"absolute inset-0 rounded-full border-8 border-gray-200"}),(0,r.jsx)("div",{className:"absolute inset-0 rounded-full border-8 border-blue-600 animate-spin border-t-transparent"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-blue-600",children:"Creating Your Telegram Group"}),(0,r.jsx)("p",{className:"text-gray-600 mt-3",children:"Please wait while we set up your group with all required members and settings..."})]})]})})}):null}),(0,r.jsxs)(u,{children:[(0,r.jsx)(p,{children:(0,r.jsx)(h,{className:"text-2xl font-bold text-center",children:"Create Telegram Group"})}),(0,r.jsxs)(x,{children:[(0,r.jsx)("div",{children:v&&v.success&&v.failed_invites&&v.failed_invites.length>0?(0,r.jsxs)("div",{className:"mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:[(0,r.jsx)("h3",{className:"text-amber-700 font-medium",children:"Some members couldn't be invited automatically"}),(0,r.jsx)("p",{className:"text-sm text-amber-600 mt-1",children:"The bot has automatically sent /invite_new_group commands for these usernames in the group chat:"}),(0,r.jsx)("ul",{className:"mt-2 text-sm text-amber-800 list-disc list-inside",children:v.failed_invites.map((e,s)=>(0,r.jsx)("li",{children:e.username},s))})]}):null}),(0,r.jsxs)("form",{onSubmit:B,className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(y,{htmlFor:"projectName",children:"Project Name"}),(0,r.jsx)(j,{id:"projectName",value:U.projectName,onChange:e=>{let s=e.target.value,a=I(s);M({...U,projectName:s,errors:{...U.errors,projectName:a.isValid?"":a.error||"Invalid project name"}})},placeholder:"Enter project name",className:U.errors.projectName?"border-red-500":""}),U.errors.projectName&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:U.errors.projectName}),U.projectName&&!U.errors.projectName&&(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["Group will be created as: ",A(U.projectName,w)]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(y,{htmlFor:"projectLead",children:"Project Lead Telegram Usernames"}),U.projectLeads.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)(j,{value:e,onChange:e=>{let a=e.target.value,r=[...U.projectLeads];r[s]=a;let l=r.filter(e=>e.trim().length>0),t="";if(0===l.length)t="At least one project lead is required";else for(let e of l){let s=V(e);if(!s.isValid){t=s.error||"Invalid username format";break}}M({...U,projectLeads:r,errors:{...U.errors,projectLead:t}})},placeholder:"@username",className:"flex-1 ".concat(U.errors.projectLead?"border-red-500":"")}),(0,r.jsx)(d,{type:"button",variant:"destructive",size:"sm",onClick:()=>{if(1===U.projectLeads.length)return;let e=U.projectLeads.filter((e,a)=>a!==s);M({...U,projectLeads:e,errors:{...U.errors,projectLead:""}})},disabled:1===U.projectLeads.length,children:"–"})]},s)),(0,r.jsx)(d,{type:"button",variant:"outline",size:"sm",onClick:()=>{M({...U,projectLeads:[...U.projectLeads,""],errors:{...U.errors,projectLead:""}})},children:"+ Add Project Lead"}),U.errors.projectLead&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:U.errors.projectLead})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(y,{htmlFor:"outreachMember",children:"Outreach Team Member"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)("input",{type:"checkbox",id:"useCustomOutreachUsername",checked:U.useCustomOutreachUsername,onChange:e=>M({...U,useCustomOutreachUsername:e.target.checked,errors:{...U.errors,outreachMemberUsername:"",customOutreachUsername:""}}),className:"h-4 w-4"}),(0,r.jsx)(y,{htmlFor:"useCustomOutreachUsername",children:"Enter BDR username manually"})]}),U.useCustomOutreachUsername?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(y,{htmlFor:"customOutreachUsername",children:"Custom BDR Telegram Username"}),(0,r.jsx)(j,{id:"customOutreachUsername",value:U.customOutreachUsername,onChange:e=>{let s=e.target.value,a=V(s);M({...U,customOutreachUsername:s,errors:{...U.errors,customOutreachUsername:a.isValid?"":a.error||"Invalid username"}})},placeholder:"@username",className:U.errors.customOutreachUsername?"border-red-500":""}),U.errors.customOutreachUsername&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:U.errors.customOutreachUsername})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(C,{value:U.outreachMemberName,onValueChange:e=>M({...U,outreachMemberName:e,outreachMemberUsername:"",errors:{...U.errors,outreachMemberUsername:""}}),children:[(0,r.jsx)(S,{id:"outreachMember",className:U.errors.outreachMemberUsername?"border-red-500":"",children:(0,r.jsx)(R,{placeholder:"Select an outreach member"})}),(0,r.jsx)(k,{children:o.map((e,s)=>(0,r.jsxs)(D,{value:e.name,children:[e.emoji," ",e.name]},s))})]}),_&&(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsx)(y,{htmlFor:"outreachUsername",children:"Telegram Username"}),(0,r.jsxs)("div",{className:"mt-2 p-4 bg-gray-50 rounded-lg space-y-2",children:[(0,r.jsxs)("p",{className:"text-sm font-medium",children:[_.emoji," ",_.name]}),(0,r.jsxs)(C,{value:U.outreachMemberUsername,onValueChange:e=>M({...U,outreachMemberUsername:e,errors:{...U.errors,outreachMemberUsername:""}}),children:[(0,r.jsx)(S,{id:"outreachUsername",children:(0,r.jsx)(R,{placeholder:"Select username"})}),(0,r.jsx)(k,{children:null==_?void 0:null===(e=_.telegramUsernames)||void 0===e?void 0:e.filter(e=>e).map(e=>(0,r.jsx)(D,{value:e,children:e},e))})]})]})]})]}),U.errors.outreachMemberUsername&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:U.errors.outreachMemberUsername})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(y,{htmlFor:"salesperson",children:"Sales Representative"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)("input",{type:"checkbox",id:"enterSalesRepManually",checked:U.enterSalesRepManually,onChange:e=>M({...U,enterSalesRepManually:e.target.checked,errors:{...U.errors,customSalesRepUsername:""}}),className:"h-4 w-4"}),(0,r.jsx)(y,{htmlFor:"enterSalesRepManually",children:"Enter Partnership Representative manually"})]}),U.enterSalesRepManually?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(y,{htmlFor:"customSalesRepUsername",children:"Partnership Representative Username"}),(0,r.jsx)(j,{id:"customSalesRepUsername",value:U.customSalesRepUsername,onChange:e=>M({...U,customSalesRepUsername:e.target.value,errors:{...U.errors,customSalesRepUsername:""}}),placeholder:"@username",className:U.errors.customSalesRepUsername?"border-red-500":""}),U.errors.customSalesRepUsername&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:U.errors.customSalesRepUsername}),(0,r.jsx)("div",{className:"flex items-center space-x-4 mt-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"checkbox",id:"includeCalendlyManual",checked:U.includeCalendly,onChange:e=>M({...U,includeCalendly:e.target.checked}),className:"h-4 w-4"}),(0,r.jsx)(y,{htmlFor:"includeCalendlyManual",children:"Include Calendly Link"})]})}),U.includeCalendly&&(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsx)(y,{htmlFor:"customCalendlyLink",children:"Calendly Link"}),(0,r.jsx)(j,{id:"customCalendlyLink",value:U.customCalendlyLink,onChange:e=>M({...U,customCalendlyLink:e.target.value,errors:{...U.errors,customCalendlyLink:""}}),placeholder:"https://calendly.com/your-link",className:U.errors.customCalendlyLink?"border-red-500":""}),U.errors.customCalendlyLink&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:U.errors.customCalendlyLink})]})]}):m?(0,r.jsxs)("div",{className:"flex items-center space-x-2 p-3 border rounded-md",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"}),(0,r.jsx)("span",{className:"text-sm text-gray-500",children:"Loading sales representatives..."})]}):n&&n.length>0?(0,r.jsxs)(C,{value:U.salesperson,onValueChange:e=>M({...U,salesperson:e,errors:{...U.errors,salesperson:""}}),children:[(0,r.jsx)(S,{id:"salesperson",className:U.errors.salesperson?"border-red-500":"",children:(0,r.jsx)(R,{placeholder:"Select a sales representative"})}),(0,r.jsx)(k,{children:n.filter(e=>e.username).map((e,s)=>(0,r.jsx)(D,{value:e.username,children:"".concat(e.emoji," ").concat(e.name)},s))})]}):(0,r.jsx)("div",{className:"p-3 border rounded-md bg-yellow-50 border-yellow-200",children:(0,r.jsx)("p",{className:"text-sm text-yellow-800",children:"⚠️ No sales representatives available. Please check your Google Sheets configuration."})}),w&&(0,r.jsxs)("div",{className:"mt-2 p-4 bg-gray-50 rounded-lg space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)("input",{type:"checkbox",id:"useCustomSalesRepUsername",checked:U.useCustomSalesRepUsername,onChange:e=>M({...U,useCustomSalesRepUsername:e.target.checked,errors:{...U.errors,customSalesRepUsername:""}}),className:"h-4 w-4"}),(0,r.jsx)(y,{htmlFor:"useCustomSalesRepUsername",children:"Use custom Telegram username"})]}),U.useCustomSalesRepUsername?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(y,{htmlFor:"customSalesRepUsername",children:"Custom Partnership Representative Username"}),(0,r.jsx)(j,{id:"customSalesRepUsername",value:U.customSalesRepUsername,onChange:e=>M({...U,customSalesRepUsername:e.target.value,errors:{...U.errors,customSalesRepUsername:""}}),placeholder:"@username",className:U.errors.customSalesRepUsername?"border-red-500":""}),U.errors.customSalesRepUsername&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:U.errors.customSalesRepUsername})]}):(0,r.jsxs)("p",{className:"text-sm",children:[(0,r.jsx)("span",{className:"font-semibold",children:"TG Username:"})," ",w.username]}),(0,r.jsxs)("p",{className:"text-sm",children:[(0,r.jsx)("span",{className:"font-semibold",children:"PA Username:"})," ",w.paUsername]}),(0,r.jsx)("div",{className:"flex items-center space-x-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"checkbox",id:"includeCalendlySelected",checked:U.includeCalendly,onChange:e=>M({...U,includeCalendly:e.target.checked}),className:"h-4 w-4"}),(0,r.jsx)(y,{htmlFor:"includeCalendlySelected",children:"Include Calendly Link"})]})}),U.includeCalendly&&w&&(0,r.jsxs)("p",{className:"text-sm",children:[(0,r.jsx)("span",{className:"font-semibold",children:"Meeting Link:"})," ",(0,r.jsx)("a",{href:null==w?void 0:w.calendarLink,target:"_blank",rel:"noopener noreferrer",className:"text-blue-500 hover:underline",children:"Schedule Meeting"})]}),(0,r.jsxs)("p",{className:"text-sm",children:[(0,r.jsx)("span",{className:"font-semibold",children:"Pipeline:"})," ",w.tier]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[(0,r.jsx)("input",{type:"checkbox",id:"includeCalendlyGlobal",checked:U.includeCalendly,onChange:e=>M({...U,includeCalendly:e.target.checked}),className:"h-4 w-4"}),(0,r.jsx)(y,{htmlFor:"includeCalendlyGlobal",children:"Include Calendly Link"})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-500 p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("p",{className:"font-semibold mb-2",children:"Group Creation Process:"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-semibold text-gray-700",children:"1. Initial Members:"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 ml-4",children:[(0,r.jsx)("li",{children:"@IbcAdmin_Bot (Bot Admin)"}),w&&"None"!==w.paUsername&&(0,r.jsxs)("li",{children:[w.paUsername," (PA - Will be made admin)"]}),_&&U.outreachMemberUsername&&(0,r.jsxs)("li",{children:[U.outreachMemberUsername," (",_.emoji," Outreach Admin)"]}),U.projectLeads.filter(e=>e.trim().length>0).length>0&&(0,r.jsxs)("li",{children:[U.projectLeads.filter(e=>e.trim().length>0).join(", ")," (Project Lead)"]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-semibold text-gray-700",children:"2. Automated Actions:"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 ml-4",children:[(0,r.jsx)("li",{children:"Welcome message will be sent by the IBC Admin account"}),(0,r.jsx)("li",{children:"Bot will execute /optin command"}),!U.enterSalesRepManually&&w&&"None"!==w.paUsername&&(0,r.jsx)("li",{children:"PA will be made admin via /setadmin"}),U.enterSalesRepManually&&U.customSalesRepUsername||w?(0,r.jsxs)("li",{children:["Sales rep will be invited via their username: ",U.enterSalesRepManually?U.customSalesRepUsername:U.useCustomSalesRepUsername&&U.customSalesRepUsername?U.customSalesRepUsername:null==w?void 0:w.username]}):null,U.enterSalesRepManually&&U.customSalesRepUsername||w?(0,r.jsx)("li",{children:"Bot will execute /invite_new_group for the sales rep"}):null]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-semibold text-gray-700",children:"3. Group Settings:"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 ml-4",children:[(0,r.jsxs)("li",{children:["Group Name: ",U.projectName?A(U.projectName,w):"Will be set based on project name"]}),(0,r.jsx)("li",{children:"Type: Supergroup (Megagroup)"}),(0,r.jsx)("li",{children:"About: IBC Group Discussion"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-semibold text-gray-700",children:"4. Welcome Message:"}),(w||U.enterSalesRepManually&&U.customSalesRepUsername)&&U.projectLeads.filter(e=>e.trim().length>0).length>0&&(U.outreachMemberUsername||U.useCustomOutreachUsername&&U.customOutreachUsername)?(0,r.jsxs)("div",{className:"bg-white p-3 rounded border mt-2 text-gray-800 font-mono text-sm",children:[(0,r.jsxs)("div",{children:["\uD83D\uDC65 Project Lead: ",U.projectLeads.filter(e=>e.trim().length>0).join(", ")]}),(0,r.jsxs)("div",{children:["\uD83D\uDC68‍\uD83D\uDCBC Partnership Representative: ",U.enterSalesRepManually?U.customSalesRepUsername:U.useCustomSalesRepUsername&&U.customSalesRepUsername?U.customSalesRepUsername:null==w?void 0:w.username]}),(0,r.jsxs)("div",{children:["\uD83D\uDC68‍\uD83D\uDCBB BDR: ",U.useCustomOutreachUsername?U.customOutreachUsername:U.outreachMemberUsername]}),U.includeCalendly&&U.enterSalesRepManually&&U.customCalendlyLink&&(0,r.jsxs)("div",{children:["\uD83D\uDCC5 Schedule a meeting: ",U.customCalendlyLink]}),U.includeCalendly&&!U.enterSalesRepManually&&w&&(0,r.jsxs)("div",{children:["\uD83D\uDCC5 Schedule a meeting: ",null==w?void 0:w.calendarLink]})]}):(0,r.jsxs)("div",{className:"bg-white p-3 rounded border mt-2 text-gray-800 font-mono text-sm opacity-50",children:[(0,r.jsx)("div",{children:"\uD83D\uDC65 Project Lead: (Enter project lead)"}),(0,r.jsx)("div",{children:"\uD83D\uDC68‍\uD83D\uDCBC Partnership Representative: (Select representative)"}),(0,r.jsx)("div",{children:"\uD83D\uDC68‍\uD83D\uDCBB BDR: (Select BDR)"}),(0,r.jsx)("div",{children:"\uD83D\uDCC5 Schedule a meeting: (Will show if enabled)"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{className:"font-semibold text-gray-700",children:[(w||U.enterSalesRepManually&&U.customSalesRepUsername)&&U.projectLeads.filter(e=>e.trim().length>0).length>0&&(U.outreachMemberUsername||U.useCustomOutreachUsername&&U.customOutreachUsername)?"5":"4",". Sales Rep Details:"]}),w||U.enterSalesRepManually&&U.customSalesRepUsername?(0,r.jsxs)("ul",{className:"list-none space-y-1 ml-4",children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("span",{className:"font-medium",children:"TG Username:"})," ",(0,r.jsx)("span",{className:"text-blue-600",children:U.enterSalesRepManually?U.customSalesRepUsername:U.useCustomSalesRepUsername&&U.customSalesRepUsername?U.customSalesRepUsername:null==w?void 0:w.username}),(U.enterSalesRepManually||U.useCustomSalesRepUsername&&U.customSalesRepUsername)&&(0,r.jsx)("span",{className:"ml-2 text-sm text-orange-600",children:"(Custom)"})]}),U.enterSalesRepManually&&U.includeCalendly&&U.customCalendlyLink&&(0,r.jsxs)("li",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Meeting Link:"})," ",(0,r.jsx)("a",{href:U.customCalendlyLink,target:"_blank",rel:"noopener noreferrer",className:"text-blue-500 hover:underline",children:"Schedule Meeting"})]}),!U.enterSalesRepManually&&w&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("span",{className:"font-medium",children:"PA Username:"})," ",(0,r.jsx)("span",{className:"text-blue-600",children:w.paUsername})]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Pipeline:"})," ",(0,r.jsx)("span",{className:"text-blue-600",children:w.tier})]}),U.includeCalendly&&(0,r.jsxs)("li",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Meeting Link:"})," ",(0,r.jsx)("a",{href:null==w?void 0:w.calendarLink,target:"_blank",rel:"noopener noreferrer",className:"text-blue-500 hover:underline",children:"Schedule Meeting"})]})]})]}):(0,r.jsxs)("ul",{className:"list-none space-y-1 ml-4 opacity-50",children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("span",{className:"font-medium",children:"TG Username:"})," (Select or enter sales rep)"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Details:"})," Additional details will appear here"]})]})]})]})]}),(0,r.jsx)(d,{type:"submit",className:"w-full bg-blue-600 hover:bg-blue-700 text-white",disabled:Object.values(U.errors).some(e=>""!==e),children:"Create Telegram Group"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 text-center",children:"This will create a new Telegram group with all required members and settings. The process takes a few seconds to complete."})]})]})]})]})}P.z.object({projectName:T,projectLeads:P.z.array(_).min(1,"At least one project lead is required").max(5,"Maximum 5 project leads allowed"),outreachMemberName:P.z.string().min(1,"Outreach member is required").optional(),outreachMemberUsername:_.optional(),customOutreachUsername:_.optional(),useCustomOutreachUsername:P.z.boolean(),salesperson:P.z.string().optional(),customSalesRepUsername:_.optional(),useCustomSalesRepUsername:P.z.boolean(),enterSalesRepManually:P.z.boolean(),customCalendlyLink:F,includeCalendly:P.z.boolean()}).refine(e=>(!e.useCustomOutreachUsername||!!e.customOutreachUsername)&&(!!e.useCustomOutreachUsername||!!e.outreachMemberName&&!!e.outreachMemberUsername)&&(!e.enterSalesRepManually||!!e.customSalesRepUsername)&&(!!e.enterSalesRepManually||!!e.salesperson)&&(!e.useCustomSalesRepUsername||!!e.customSalesRepUsername),{message:"Please fill in all required fields correctly"}),P.z.object({projectName:T,projectLeads:P.z.array(_).min(1).max(5),outreachTeamMember:_,outreachMemberEmail:B,salesperson:P.z.string().min(1,"Salesperson is required"),salesRepUsername:_,paUsername:_.optional(),calendlyLink:F,includeCalendly:P.z.boolean(),welcomeMessage:P.z.string().min(1,"Welcome message is required"),inviteSalesRep:P.z.boolean(),salesRepEmoji:P.z.string().optional(),salesRepPAUsername:_.optional(),salesRepCalendlyLink:F,salesRepCategory:P.z.string().optional(),outreachMemberUsernames:P.z.array(_).optional(),outreachMemberEmoji:P.z.string().optional()}),P.z.object({clientEmail:P.z.string().email("Invalid client email"),privateKey:P.z.string().min(1,"Private key is required"),spreadsheetId:P.z.string().min(1,"Spreadsheet ID is required")})}},function(e){e.O(0,[335,597,971,69,744],function(){return e(e.s=372)}),_N_E=e.O()}]);