/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'img.clerk.com',
      },
    ],
  },
  transpilePackages: ["@clerk/nextjs"],
  // Removed env section - sensitive credentials should not be exposed to client
  // Only NEXT_PUBLIC_ environment variables are automatically available on the client
}

module.exports = nextConfig 