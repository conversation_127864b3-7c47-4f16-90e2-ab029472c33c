{"name": "@bundled-es-modules/statuses", "version": "1.0.1", "description": "mirror of statuses, bundled and exposed as ES module", "author": "<PERSON> <<EMAIL>>", "main": "index-esm.js", "type": "module", "exports": {".": {"import": "./index-esm.js", "require": "./index-cjs.cjs"}}, "scripts": {"dev": "node dev.js", "build": "esbuild source.js --bundle --format=esm --outfile=index-esm.js"}, "keywords": [], "files": ["index-esm.js", "index-cjs.cjs"], "license": "ISC", "dependencies": {"statuses": "^2.0.1"}, "devDependencies": {"esbuild": "^0.17.17"}}