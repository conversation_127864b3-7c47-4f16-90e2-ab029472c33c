import { H as HttpRequestEventMap } from '../../glossary-7152281e.js';
import { I as Interceptor } from '../../Interceptor-af98b768.js';
import '@open-draft/deferred-promise';
import '@open-draft/logger';
import 'strict-event-emitter';

declare class FetchInterceptor extends Interceptor<HttpRequestEventMap> {
    static symbol: symbol;
    constructor();
    protected checkEnvironment(): boolean;
    protected setup(): Promise<void>;
}

export { FetchInterceptor };
