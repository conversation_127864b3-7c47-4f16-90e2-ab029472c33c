import {
  XMLHttpRequestInterceptor
} from "../chunk-QKSBFQDK.mjs";
import "../chunk-6HYIRFX2.mjs";
import {
  FetchInterceptor
} from "../chunk-ARPHZXGT.mjs";
import "../chunk-L37TY7LC.mjs";
import "../chunk-3RXCRGL2.mjs";
import "../chunk-TX5GBTFY.mjs";
import "../chunk-QED3Q6Z2.mjs";

// src/presets/browser.ts
var browser_default = [
  new FetchInterceptor(),
  new XMLHttpRequestInterceptor()
];
export {
  browser_default as default
};
//# sourceMappingURL=browser.mjs.map