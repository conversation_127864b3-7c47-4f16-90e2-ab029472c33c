{"version": 3, "sources": ["../../src/interceptors/XMLHttpRequest/index.ts", "../../src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts", "../../src/interceptors/XMLHttpRequest/utils/concatArrayBuffer.ts", "../../src/interceptors/XMLHttpRequest/polyfills/EventPolyfill.ts", "../../src/interceptors/XMLHttpRequest/polyfills/ProgressEventPolyfill.ts", "../../src/interceptors/XMLHttpRequest/utils/createEvent.ts", "../../src/utils/findPropertySource.ts", "../../src/utils/createProxy.ts", "../../src/interceptors/XMLHttpRequest/utils/isDomParserSupportedType.ts", "../../src/utils/parseJson.ts", "../../src/interceptors/XMLHttpRequest/utils/createResponse.ts", "../../src/interceptors/XMLHttpRequest/utils/getBodyByteLength.ts", "../../src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts"], "sourcesContent": ["import { invariant } from 'outvariant'\nimport { Emitter } from 'strict-event-emitter'\nimport { HttpRequestEventMap, IS_PATCHED_MODULE } from '../../glossary'\nimport { Interceptor } from '../../Interceptor'\nimport { createXMLHttpRequestProxy } from './XMLHttpRequestProxy'\nimport { hasConfigurableGlobal } from '../../utils/hasConfigurableGlobal'\n\nexport type XMLHttpRequestEmitter = Emitter<HttpRequestEventMap>\n\nexport class XMLHttpRequestInterceptor extends Interceptor<HttpRequestEventMap> {\n  static interceptorSymbol = Symbol('xhr')\n\n  constructor() {\n    super(XMLHttpRequestInterceptor.interceptorSymbol)\n  }\n\n  protected checkEnvironment() {\n    return hasConfigurableGlobal('XMLHttpRequest')\n  }\n\n  protected setup() {\n    const logger = this.logger.extend('setup')\n\n    logger.info('patching \"XMLHttpRequest\" module...')\n\n    const PureXMLHttpRequest = globalThis.XMLHttpRequest\n\n    invariant(\n      !(PureXMLHttpRequest as any)[IS_PATCHED_MODULE],\n      'Failed to patch the \"XMLHttpRequest\" module: already patched.'\n    )\n\n    globalThis.XMLHttpRequest = createXMLHttpRequestProxy({\n      emitter: this.emitter,\n      logger: this.logger,\n    })\n\n    logger.info(\n      'native \"XMLHttpRequest\" module patched!',\n      globalThis.XMLHttpRequest.name\n    )\n\n    Object.defineProperty(globalThis.XMLHttpRequest, IS_PATCHED_MODULE, {\n      enumerable: true,\n      configurable: true,\n      value: true,\n    })\n\n    this.subscriptions.push(() => {\n      Object.defineProperty(globalThis.XMLHttpRequest, IS_PATCHED_MODULE, {\n        value: undefined,\n      })\n\n      globalThis.XMLHttpRequest = PureXMLHttpRequest\n      logger.info(\n        'native \"XMLHttpRequest\" module restored!',\n        globalThis.XMLHttpRequest.name\n      )\n    })\n  }\n}\n", "import { invariant } from 'outvariant'\nimport { isNodeProcess } from 'is-node-process'\nimport type { Logger } from '@open-draft/logger'\nimport { concatArrayBuffer } from './utils/concatArrayBuffer'\nimport { createEvent } from './utils/createEvent'\nimport {\n  decodeBuffer,\n  encodeBuffer,\n  toArrayBuffer,\n} from '../../utils/bufferUtils'\nimport { createProxy } from '../../utils/createProxy'\nimport { isDomParserSupportedType } from './utils/isDomParserSupportedType'\nimport { parseJson } from '../../utils/parseJson'\nimport { createResponse } from './utils/createResponse'\nimport { INTERNAL_REQUEST_ID_HEADER_NAME } from '../../Interceptor'\nimport { createRequestId } from '../../createRequestId'\nimport { getBodyByteLength } from './utils/getBodyByteLength'\nimport { setRawRequest } from '../../getRawRequest'\n\nconst kIsRequestHandled = Symbol('kIsRequestHandled')\nconst IS_NODE = isNodeProcess()\nconst kFetchRequest = Symbol('kFetchRequest')\n\n/**\n * An `XMLHttpRequest` instance controller that allows us\n * to handle any given request instance (e.g. responding to it).\n */\nexport class XMLHttpRequestController {\n  public request: XMLHttpRequest\n  public requestId: string\n  public onRequest?: (\n    this: XMLHttpRequestController,\n    args: {\n      request: Request\n      requestId: string\n    }\n  ) => Promise<void>\n  public onResponse?: (\n    this: XMLHttpRequestController,\n    args: {\n      response: Response\n      isMockedResponse: boolean\n      request: Request\n      requestId: string\n    }\n  ) => void;\n\n  [kIsRequestHandled]: boolean;\n  [kFetchRequest]?: Request\n  private method: string = 'GET'\n  private url: URL = null as any\n  private requestHeaders: Headers\n  private responseBuffer: Uint8Array\n  private events: Map<keyof XMLHttpRequestEventTargetEventMap, Array<Function>>\n  private uploadEvents: Map<\n    keyof XMLHttpRequestEventTargetEventMap,\n    Array<Function>\n  >\n\n  constructor(readonly initialRequest: XMLHttpRequest, public logger: Logger) {\n    this[kIsRequestHandled] = false\n\n    this.events = new Map()\n    this.uploadEvents = new Map()\n    this.requestId = createRequestId()\n    this.requestHeaders = new Headers()\n    this.responseBuffer = new Uint8Array()\n\n    this.request = createProxy(initialRequest, {\n      setProperty: ([propertyName, nextValue], invoke) => {\n        switch (propertyName) {\n          case 'ontimeout': {\n            const eventName = propertyName.slice(\n              2\n            ) as keyof XMLHttpRequestEventTargetEventMap\n\n            /**\n             * @note Proxy callbacks to event listeners because JSDOM has trouble\n             * translating these properties to callbacks. It seemed to be operating\n             * on events exclusively.\n             */\n            this.request.addEventListener(eventName, nextValue as any)\n\n            return invoke()\n          }\n\n          default: {\n            return invoke()\n          }\n        }\n      },\n      methodCall: ([methodName, args], invoke) => {\n        switch (methodName) {\n          case 'open': {\n            const [method, url] = args as [string, string | undefined]\n\n            if (typeof url === 'undefined') {\n              this.method = 'GET'\n              this.url = toAbsoluteUrl(method)\n            } else {\n              this.method = method\n              this.url = toAbsoluteUrl(url)\n            }\n\n            this.logger = this.logger.extend(`${this.method} ${this.url.href}`)\n            this.logger.info('open', this.method, this.url.href)\n\n            return invoke()\n          }\n\n          case 'addEventListener': {\n            const [eventName, listener] = args as [\n              keyof XMLHttpRequestEventTargetEventMap,\n              Function\n            ]\n\n            this.registerEvent(eventName, listener)\n            this.logger.info('addEventListener', eventName, listener)\n\n            return invoke()\n          }\n\n          case 'setRequestHeader': {\n            const [name, value] = args as [string, string]\n            this.requestHeaders.set(name, value)\n\n            this.logger.info('setRequestHeader', name, value)\n\n            return invoke()\n          }\n\n          case 'send': {\n            const [body] = args as [\n              body?: XMLHttpRequestBodyInit | Document | null\n            ]\n\n            this.request.addEventListener('load', () => {\n              if (typeof this.onResponse !== 'undefined') {\n                // Create a Fetch API Response representation of whichever\n                // response this XMLHttpRequest received. Note those may\n                // be either a mocked and the original response.\n                const fetchResponse = createResponse(\n                  this.request,\n                  /**\n                   * The `response` property is the right way to read\n                   * the ambiguous response body, as the request's \"responseType\" may differ.\n                   * @see https://xhr.spec.whatwg.org/#the-response-attribute\n                   */\n                  this.request.response\n                )\n\n                // Notify the consumer about the response.\n                this.onResponse.call(this, {\n                  response: fetchResponse,\n                  isMockedResponse: this[kIsRequestHandled],\n                  request: fetchRequest,\n                  requestId: this.requestId!,\n                })\n              }\n            })\n\n            const requestBody =\n              typeof body === 'string' ? encodeBuffer(body) : body\n\n            // Delegate request handling to the consumer.\n            const fetchRequest = this.toFetchApiRequest(requestBody)\n            this[kFetchRequest] = fetchRequest.clone()\n\n            const onceRequestSettled =\n              this.onRequest?.call(this, {\n                request: fetchRequest,\n                requestId: this.requestId!,\n              }) || Promise.resolve()\n\n            onceRequestSettled.finally(() => {\n              // If the consumer didn't handle the request (called `.respondWith()`) perform it as-is.\n              if (!this[kIsRequestHandled]) {\n                this.logger.info(\n                  'request callback settled but request has not been handled (readystate %d), performing as-is...',\n                  this.request.readyState\n                )\n\n                /**\n                 * @note Set the intercepted request ID on the original request in Node.js\n                 * so that if it triggers any other interceptors, they don't attempt\n                 * to process it once again.\n                 *\n                 * For instance, XMLHttpRequest is often implemented via \"http.ClientRequest\"\n                 * and we don't want for both XHR and ClientRequest interceptors to\n                 * handle the same request at the same time (e.g. emit the \"response\" event twice).\n                 */\n                if (IS_NODE) {\n                  this.request.setRequestHeader(\n                    INTERNAL_REQUEST_ID_HEADER_NAME,\n                    this.requestId!\n                  )\n                }\n\n                return invoke()\n              }\n            })\n\n            break\n          }\n\n          default: {\n            return invoke()\n          }\n        }\n      },\n    })\n\n    /**\n     * Proxy the `.upload` property to gather the event listeners/callbacks.\n     */\n    define(\n      this.request,\n      'upload',\n      createProxy(this.request.upload, {\n        setProperty: ([propertyName, nextValue], invoke) => {\n          switch (propertyName) {\n            case 'onloadstart':\n            case 'onprogress':\n            case 'onaboart':\n            case 'onerror':\n            case 'onload':\n            case 'ontimeout':\n            case 'onloadend': {\n              const eventName = propertyName.slice(\n                2\n              ) as keyof XMLHttpRequestEventTargetEventMap\n\n              this.registerUploadEvent(eventName, nextValue as Function)\n            }\n          }\n\n          return invoke()\n        },\n        methodCall: ([methodName, args], invoke) => {\n          switch (methodName) {\n            case 'addEventListener': {\n              const [eventName, listener] = args as [\n                keyof XMLHttpRequestEventTargetEventMap,\n                Function\n              ]\n              this.registerUploadEvent(eventName, listener)\n              this.logger.info('upload.addEventListener', eventName, listener)\n\n              return invoke()\n            }\n          }\n        },\n      })\n    )\n  }\n\n  private registerEvent(\n    eventName: keyof XMLHttpRequestEventTargetEventMap,\n    listener: Function\n  ): void {\n    const prevEvents = this.events.get(eventName) || []\n    const nextEvents = prevEvents.concat(listener)\n    this.events.set(eventName, nextEvents)\n\n    this.logger.info('registered event \"%s\"', eventName, listener)\n  }\n\n  private registerUploadEvent(\n    eventName: keyof XMLHttpRequestEventTargetEventMap,\n    listener: Function\n  ): void {\n    const prevEvents = this.uploadEvents.get(eventName) || []\n    const nextEvents = prevEvents.concat(listener)\n    this.uploadEvents.set(eventName, nextEvents)\n\n    this.logger.info('registered upload event \"%s\"', eventName, listener)\n  }\n\n  /**\n   * Responds to the current request with the given\n   * Fetch API `Response` instance.\n   */\n  public async respondWith(response: Response): Promise<void> {\n    /**\n     * @note Since `XMLHttpRequestController` delegates the handling of the responses\n     * to the \"load\" event listener that doesn't distinguish between the mocked and original\n     * responses, mark the request that had a mocked response with a corresponding symbol.\n     *\n     * Mark this request as having a mocked response immediately since\n     * calculating request/response total body length is asynchronous.\n     */\n    this[kIsRequestHandled] = true\n\n    /**\n     * Dispatch request upload events for requests with a body.\n     * @see https://github.com/mswjs/interceptors/issues/573\n     */\n    if (this[kFetchRequest]) {\n      const totalRequestBodyLength = await getBodyByteLength(\n        this[kFetchRequest]\n      )\n\n      this.trigger('loadstart', this.request.upload, {\n        loaded: 0,\n        total: totalRequestBodyLength,\n      })\n      this.trigger('progress', this.request.upload, {\n        loaded: totalRequestBodyLength,\n        total: totalRequestBodyLength,\n      })\n      this.trigger('load', this.request.upload, {\n        loaded: totalRequestBodyLength,\n        total: totalRequestBodyLength,\n      })\n      this.trigger('loadend', this.request.upload, {\n        loaded: totalRequestBodyLength,\n        total: totalRequestBodyLength,\n      })\n    }\n\n    this.logger.info(\n      'responding with a mocked response: %d %s',\n      response.status,\n      response.statusText\n    )\n\n    define(this.request, 'status', response.status)\n    define(this.request, 'statusText', response.statusText)\n    define(this.request, 'responseURL', this.url.href)\n\n    this.request.getResponseHeader = new Proxy(this.request.getResponseHeader, {\n      apply: (_, __, args: [name: string]) => {\n        this.logger.info('getResponseHeader', args[0])\n\n        if (this.request.readyState < this.request.HEADERS_RECEIVED) {\n          this.logger.info('headers not received yet, returning null')\n\n          // Headers not received yet, nothing to return.\n          return null\n        }\n\n        const headerValue = response.headers.get(args[0])\n        this.logger.info(\n          'resolved response header \"%s\" to',\n          args[0],\n          headerValue\n        )\n\n        return headerValue\n      },\n    })\n\n    this.request.getAllResponseHeaders = new Proxy(\n      this.request.getAllResponseHeaders,\n      {\n        apply: () => {\n          this.logger.info('getAllResponseHeaders')\n\n          if (this.request.readyState < this.request.HEADERS_RECEIVED) {\n            this.logger.info('headers not received yet, returning empty string')\n\n            // Headers not received yet, nothing to return.\n            return ''\n          }\n\n          const headersList = Array.from(response.headers.entries())\n          const allHeaders = headersList\n            .map(([headerName, headerValue]) => {\n              return `${headerName}: ${headerValue}`\n            })\n            .join('\\r\\n')\n\n          this.logger.info('resolved all response headers to', allHeaders)\n\n          return allHeaders\n        },\n      }\n    )\n\n    // Update the response getters to resolve against the mocked response.\n    Object.defineProperties(this.request, {\n      response: {\n        enumerable: true,\n        configurable: false,\n        get: () => this.response,\n      },\n      responseText: {\n        enumerable: true,\n        configurable: false,\n        get: () => this.responseText,\n      },\n      responseXML: {\n        enumerable: true,\n        configurable: false,\n        get: () => this.responseXML,\n      },\n    })\n\n    const totalResponseBodyLength = await getBodyByteLength(response.clone())\n\n    this.logger.info('calculated response body length', totalResponseBodyLength)\n\n    this.trigger('loadstart', this.request, {\n      loaded: 0,\n      total: totalResponseBodyLength,\n    })\n\n    this.setReadyState(this.request.HEADERS_RECEIVED)\n    this.setReadyState(this.request.LOADING)\n\n    const finalizeResponse = () => {\n      this.logger.info('finalizing the mocked response...')\n\n      this.setReadyState(this.request.DONE)\n\n      this.trigger('load', this.request, {\n        loaded: this.responseBuffer.byteLength,\n        total: totalResponseBodyLength,\n      })\n\n      this.trigger('loadend', this.request, {\n        loaded: this.responseBuffer.byteLength,\n        total: totalResponseBodyLength,\n      })\n    }\n\n    if (response.body) {\n      this.logger.info('mocked response has body, streaming...')\n\n      const reader = response.body.getReader()\n\n      const readNextResponseBodyChunk = async () => {\n        const { value, done } = await reader.read()\n\n        if (done) {\n          this.logger.info('response body stream done!')\n          finalizeResponse()\n          return\n        }\n\n        if (value) {\n          this.logger.info('read response body chunk:', value)\n          this.responseBuffer = concatArrayBuffer(this.responseBuffer, value)\n\n          this.trigger('progress', this.request, {\n            loaded: this.responseBuffer.byteLength,\n            total: totalResponseBodyLength,\n          })\n        }\n\n        readNextResponseBodyChunk()\n      }\n\n      readNextResponseBodyChunk()\n    } else {\n      finalizeResponse()\n    }\n  }\n\n  private responseBufferToText(): string {\n    return decodeBuffer(this.responseBuffer)\n  }\n\n  get response(): unknown {\n    this.logger.info(\n      'getResponse (responseType: %s)',\n      this.request.responseType\n    )\n\n    if (this.request.readyState !== this.request.DONE) {\n      return null\n    }\n\n    switch (this.request.responseType) {\n      case 'json': {\n        const responseJson = parseJson(this.responseBufferToText())\n        this.logger.info('resolved response JSON', responseJson)\n\n        return responseJson\n      }\n\n      case 'arraybuffer': {\n        const arrayBuffer = toArrayBuffer(this.responseBuffer)\n        this.logger.info('resolved response ArrayBuffer', arrayBuffer)\n\n        return arrayBuffer\n      }\n\n      case 'blob': {\n        const mimeType =\n          this.request.getResponseHeader('Content-Type') || 'text/plain'\n        const responseBlob = new Blob([this.responseBufferToText()], {\n          type: mimeType,\n        })\n\n        this.logger.info(\n          'resolved response Blob (mime type: %s)',\n          responseBlob,\n          mimeType\n        )\n\n        return responseBlob\n      }\n\n      default: {\n        const responseText = this.responseBufferToText()\n        this.logger.info(\n          'resolving \"%s\" response type as text',\n          this.request.responseType,\n          responseText\n        )\n\n        return responseText\n      }\n    }\n  }\n\n  get responseText(): string {\n    /**\n     * Throw when trying to read the response body as text when the\n     * \"responseType\" doesn't expect text. This just respects the spec better.\n     * @see https://xhr.spec.whatwg.org/#the-responsetext-attribute\n     */\n    invariant(\n      this.request.responseType === '' || this.request.responseType === 'text',\n      'InvalidStateError: The object is in invalid state.'\n    )\n\n    if (\n      this.request.readyState !== this.request.LOADING &&\n      this.request.readyState !== this.request.DONE\n    ) {\n      return ''\n    }\n\n    const responseText = this.responseBufferToText()\n    this.logger.info('getResponseText: \"%s\"', responseText)\n\n    return responseText\n  }\n\n  get responseXML(): Document | null {\n    invariant(\n      this.request.responseType === '' ||\n        this.request.responseType === 'document',\n      'InvalidStateError: The object is in invalid state.'\n    )\n\n    if (this.request.readyState !== this.request.DONE) {\n      return null\n    }\n\n    const contentType = this.request.getResponseHeader('Content-Type') || ''\n\n    if (typeof DOMParser === 'undefined') {\n      console.warn(\n        'Cannot retrieve XMLHttpRequest response body as XML: DOMParser is not defined. You are likely using an environment that is not browser or does not polyfill browser globals correctly.'\n      )\n      return null\n    }\n\n    if (isDomParserSupportedType(contentType)) {\n      return new DOMParser().parseFromString(\n        this.responseBufferToText(),\n        contentType\n      )\n    }\n\n    return null\n  }\n\n  public errorWith(error?: Error): void {\n    /**\n     * @note Mark this request as handled even if it received a mock error.\n     * This prevents the controller from trying to perform this request as-is.\n     */\n    this[kIsRequestHandled] = true\n    this.logger.info('responding with an error')\n\n    this.setReadyState(this.request.DONE)\n    this.trigger('error', this.request)\n    this.trigger('loadend', this.request)\n  }\n\n  /**\n   * Transitions this request's `readyState` to the given one.\n   */\n  private setReadyState(nextReadyState: number): void {\n    this.logger.info(\n      'setReadyState: %d -> %d',\n      this.request.readyState,\n      nextReadyState\n    )\n\n    if (this.request.readyState === nextReadyState) {\n      this.logger.info('ready state identical, skipping transition...')\n      return\n    }\n\n    define(this.request, 'readyState', nextReadyState)\n\n    this.logger.info('set readyState to: %d', nextReadyState)\n\n    if (nextReadyState !== this.request.UNSENT) {\n      this.logger.info('triggerring \"readystatechange\" event...')\n\n      this.trigger('readystatechange', this.request)\n    }\n  }\n\n  /**\n   * Triggers given event on the `XMLHttpRequest` instance.\n   */\n  private trigger<\n    EventName extends keyof (XMLHttpRequestEventTargetEventMap & {\n      readystatechange: ProgressEvent<XMLHttpRequestEventTarget>\n    })\n  >(\n    eventName: EventName,\n    target: XMLHttpRequest | XMLHttpRequestUpload,\n    options?: ProgressEventInit\n  ): void {\n    const callback = (target as XMLHttpRequest)[`on${eventName}`]\n    const event = createEvent(target, eventName, options)\n\n    this.logger.info('trigger \"%s\"', eventName, options || '')\n\n    // Invoke direct callbacks.\n    if (typeof callback === 'function') {\n      this.logger.info('found a direct \"%s\" callback, calling...', eventName)\n      callback.call(target as XMLHttpRequest, event)\n    }\n\n    // Invoke event listeners.\n    const events =\n      target instanceof XMLHttpRequestUpload ? this.uploadEvents : this.events\n\n    for (const [registeredEventName, listeners] of events) {\n      if (registeredEventName === eventName) {\n        this.logger.info(\n          'found %d listener(s) for \"%s\" event, calling...',\n          listeners.length,\n          eventName\n        )\n\n        listeners.forEach((listener) => listener.call(target, event))\n      }\n    }\n  }\n\n  /**\n   * Converts this `XMLHttpRequest` instance into a Fetch API `Request` instance.\n   */\n  private toFetchApiRequest(\n    body: XMLHttpRequestBodyInit | Document | null | undefined\n  ): Request {\n    this.logger.info('converting request to a Fetch API Request...')\n\n    // If the `Document` is used as the body of this XMLHttpRequest,\n    // set its inner text as the Fetch API Request body.\n    const resolvedBody =\n      body instanceof Document ? body.documentElement.innerText : body\n\n    const fetchRequest = new Request(this.url.href, {\n      method: this.method,\n      headers: this.requestHeaders,\n      /**\n       * @see https://xhr.spec.whatwg.org/#cross-origin-credentials\n       */\n      credentials: this.request.withCredentials ? 'include' : 'same-origin',\n      body: ['GET', 'HEAD'].includes(this.method.toUpperCase())\n        ? null\n        : resolvedBody,\n    })\n\n    const proxyHeaders = createProxy(fetchRequest.headers, {\n      methodCall: ([methodName, args], invoke) => {\n        // Forward the latest state of the internal request headers\n        // because the interceptor might have modified them\n        // without responding to the request.\n        switch (methodName) {\n          case 'append':\n          case 'set': {\n            const [headerName, headerValue] = args as [string, string]\n            this.request.setRequestHeader(headerName, headerValue)\n            break\n          }\n\n          case 'delete': {\n            const [headerName] = args as [string]\n            console.warn(\n              `XMLHttpRequest: Cannot remove a \"${headerName}\" header from the Fetch API representation of the \"${fetchRequest.method} ${fetchRequest.url}\" request. XMLHttpRequest headers cannot be removed.`\n            )\n            break\n          }\n        }\n\n        return invoke()\n      },\n    })\n    define(fetchRequest, 'headers', proxyHeaders)\n    setRawRequest(fetchRequest, this.request)\n\n    this.logger.info('converted request to a Fetch API Request!', fetchRequest)\n\n    return fetchRequest\n  }\n}\n\nfunction toAbsoluteUrl(url: string | URL): URL {\n  /**\n   * @note XMLHttpRequest interceptor may run in environments\n   * that implement XMLHttpRequest but don't implement \"location\"\n   * (for example, React Native). If that's the case, return the\n   * input URL as-is (nothing to be relative to).\n   * @see https://github.com/mswjs/msw/issues/1777\n   */\n  if (typeof location === 'undefined') {\n    return new URL(url)\n  }\n\n  return new URL(url.toString(), location.href)\n}\n\nfunction define(\n  target: object,\n  property: string | symbol,\n  value: unknown\n): void {\n  Reflect.defineProperty(target, property, {\n    // Ensure writable properties to allow redefining readonly properties.\n    writable: true,\n    enumerable: true,\n    value,\n  })\n}\n", "/**\n * Concatenate two `Uint8Array` buffers.\n */\nexport function concatArrayBuffer(\n  left: Uint8Array,\n  right: Uint8Array\n): Uint8Array {\n  const result = new Uint8Array(left.byteLength + right.byteLength)\n  result.set(left, 0)\n  result.set(right, left.byteLength)\n  return result\n}\n", "export class EventPolyfill implements Event {\n  readonly NONE = 0\n  readonly CAPTURING_PHASE = 1\n  readonly AT_TARGET = 2\n  readonly BUBBLING_PHASE = 3\n\n  public type: string = ''\n  public srcElement: EventTarget | null = null\n  public target: EventTarget | null\n  public currentTarget: EventTarget | null = null\n  public eventPhase: number = 0\n  public timeStamp: number\n  public isTrusted: boolean = true\n  public composed: boolean = false\n  public cancelable: boolean = true\n  public defaultPrevented: boolean = false\n  public bubbles: boolean = true\n  public lengthComputable: boolean = true\n  public loaded: number = 0\n  public total: number = 0\n\n  cancelBubble: boolean = false\n  returnValue: boolean = true\n\n  constructor(\n    type: string,\n    options?: { target: EventTarget; currentTarget: EventTarget }\n  ) {\n    this.type = type\n    this.target = options?.target || null\n    this.currentTarget = options?.currentTarget || null\n    this.timeStamp = Date.now()\n  }\n\n  public composedPath(): EventTarget[] {\n    return []\n  }\n\n  public initEvent(type: string, bubbles?: boolean, cancelable?: boolean) {\n    this.type = type\n    this.bubbles = !!bubbles\n    this.cancelable = !!cancelable\n  }\n\n  public preventDefault() {\n    this.defaultPrevented = true\n  }\n\n  public stopPropagation() {}\n  public stopImmediatePropagation() {}\n}\n", "import { EventPolyfill } from './EventPolyfill'\n\nexport class ProgressEventPolyfill extends EventPolyfill {\n  readonly lengthComputable: boolean\n  readonly composed: boolean\n  readonly loaded: number\n  readonly total: number\n\n  constructor(type: string, init?: ProgressEventInit) {\n    super(type)\n\n    this.lengthComputable = init?.lengthComputable || false\n    this.composed = init?.composed || false\n    this.loaded = init?.loaded || 0\n    this.total = init?.total || 0\n  }\n}\n", "import { EventPolyfill } from '../polyfills/EventPolyfill'\nimport { ProgressEventPolyfill } from '../polyfills/ProgressEventPolyfill'\n\nconst SUPPORTS_PROGRESS_EVENT = typeof ProgressEvent !== 'undefined'\n\nexport function createEvent(\n  target: XMLHttpRequest | XMLHttpRequestUpload,\n  type: string,\n  init?: ProgressEventInit\n): EventPolyfill | ProgressEvent {\n  const progressEvents = [\n    'error',\n    'progress',\n    'loadstart',\n    'loadend',\n    'load',\n    'timeout',\n    'abort',\n  ]\n\n  /**\n   * `ProgressEvent` is not supported in React Native.\n   * @see https://github.com/mswjs/interceptors/issues/40\n   */\n  const ProgressEventClass = SUPPORTS_PROGRESS_EVENT\n    ? ProgressEvent\n    : ProgressEventPolyfill\n\n  const event = progressEvents.includes(type)\n    ? new ProgressEventClass(type, {\n        lengthComputable: true,\n        loaded: init?.loaded || 0,\n        total: init?.total || 0,\n      })\n    : new EventPolyfill(type, {\n        target,\n        currentTarget: target,\n      })\n\n  return event\n}\n", "/**\n * Returns the source object of the given property on the target object\n * (the target itself, any parent in its prototype, or null).\n */\nexport function findPropertySource(\n  target: object,\n  propertyName: string | symbol\n): object | null {\n  if (!(propertyName in target)) {\n    return null\n  }\n\n  const hasProperty = Object.prototype.hasOwnProperty.call(target, propertyName)\n  if (hasProperty) {\n    return target\n  }\n\n  const prototype = Reflect.getPrototypeOf(target)\n  return prototype ? findPropertySource(prototype, propertyName) : null\n}\n", "import { findPropertySource } from './findPropertySource'\n\nexport interface ProxyOptions<Target extends Record<string, any>> {\n  constructorCall?(args: Array<unknown>, next: NextFunction<Target>): Target\n\n  methodCall?<F extends keyof Target>(\n    this: Target,\n    data: [methodName: F, args: Array<unknown>],\n    next: NextFunction<void>\n  ): void\n\n  setProperty?(\n    data: [propertyName: string | symbol, nextValue: unknown],\n    next: NextFunction<boolean>\n  ): boolean\n\n  getProperty?(\n    data: [propertyName: string | symbol, receiver: Target],\n    next: NextFunction<void>\n  ): void\n}\n\nexport type NextFunction<ReturnType> = () => ReturnType\n\nexport function createProxy<Target extends object>(\n  target: Target,\n  options: ProxyOptions<Target>\n): Target {\n  const proxy = new Proxy(target, optionsToProxyHandler(options))\n\n  return proxy\n}\n\nfunction optionsToProxyHandler<T extends Record<string, any>>(\n  options: ProxyOptions<T>\n): ProxyHandler<T> {\n  const { constructorCall, methodCall, getProperty, setProperty } = options\n  const handler: Proxy<PERSON>andler<T> = {}\n\n  if (typeof constructorCall !== 'undefined') {\n    handler.construct = function (target, args, newTarget) {\n      const next = Reflect.construct.bind(null, target as any, args, newTarget)\n      return constructorCall.call(newTarget, args, next)\n    }\n  }\n\n  handler.set = function (target, propertyName, nextValue) {\n    const next = () => {\n      const propertySource = findPropertySource(target, propertyName) || target\n      const ownDescriptors = Reflect.getOwnPropertyDescriptor(\n        propertySource,\n        propertyName\n      )\n\n      // Respect any custom setters present for this property.\n      if (typeof ownDescriptors?.set !== 'undefined') {\n        ownDescriptors.set.apply(target, [nextValue])\n        return true\n      }\n\n      // Otherwise, set the property on the source.\n      return Reflect.defineProperty(propertySource, propertyName, {\n        writable: true,\n        enumerable: true,\n        configurable: true,\n        value: nextValue,\n      })\n    }\n\n    if (typeof setProperty !== 'undefined') {\n      return setProperty.call(target, [propertyName, nextValue], next)\n    }\n\n    return next()\n  }\n\n  handler.get = function (target, propertyName, receiver) {\n    /**\n     * @note Using `Reflect.get()` here causes \"TypeError: Illegal invocation\".\n     */\n    const next = () => target[propertyName as any]\n\n    const value =\n      typeof getProperty !== 'undefined'\n        ? getProperty.call(target, [propertyName, receiver], next)\n        : next()\n\n    if (typeof value === 'function') {\n      return (...args: Array<any>) => {\n        const next = value.bind(target, ...args)\n\n        if (typeof methodCall !== 'undefined') {\n          return methodCall.call(target, [propertyName as any, args], next)\n        }\n\n        return next()\n      }\n    }\n\n    return value\n  }\n\n  return handler\n}\n", "export function isDomParserSupportedType(\n  type: string\n): type is DOMParserSupportedType {\n  const supportedTypes: Array<DOMParserSupportedType> = [\n    'application/xhtml+xml',\n    'application/xml',\n    'image/svg+xml',\n    'text/html',\n    'text/xml',\n  ]\n  return supportedTypes.some((supportedType) => {\n    return type.startsWith(supportedType)\n  })\n}\n", "/**\n * Parses a given string into JSON.\n * Gracefully handles invalid JSON by returning `null`.\n */\nexport function parseJson(data: string): Record<string, unknown> | null {\n  try {\n    const json = JSON.parse(data)\n    return json\n  } catch (_) {\n    return null\n  }\n}\n", "import { FetchResponse } from '../../../utils/fetchUtils'\n\n/**\n * Creates a Fetch API `Response` instance from the given\n * `XMLHttpRequest` instance and a response body.\n */\nexport function createResponse(\n  request: XMLHttpRequest,\n  body: BodyInit | null\n): Response {\n  /**\n   * Handle XMLHttpRequest responses that must have null as the\n   * response body when represented using Fetch API Response.\n   * XMLHttpRequest response will always have an empty string\n   * as the \"request.response\" in those cases, resulting in an error\n   * when constructing a Response instance.\n   * @see https://github.com/mswjs/interceptors/issues/379\n   */\n  const responseBodyOrNull = FetchResponse.isResponseWithBody(request.status)\n    ? body\n    : null\n\n  return new FetchResponse(responseBodyOrNull, {\n    url: request.responseURL,\n    status: request.status,\n    statusText: request.statusText,\n    headers: createHeadersFromXMLHttpReqestHeaders(\n      request.getAllResponseHeaders()\n    ),\n  })\n}\n\nfunction createHeadersFromXMLHttpReqestHeaders(headersString: string): Headers {\n  const headers = new Headers()\n\n  const lines = headersString.split(/[\\r\\n]+/)\n  for (const line of lines) {\n    if (line.trim() === '') {\n      continue\n    }\n\n    const [name, ...parts] = line.split(': ')\n    const value = parts.join(': ')\n\n    headers.append(name, value)\n  }\n\n  return headers\n}\n", "/**\n * Return a total byte length of the given request/response body.\n * If the `Content-Length` header is present, it will be used as the byte length.\n */\nexport async function getBodyByteLength(\n  input: Request | Response\n): Promise<number> {\n  const explicitContentLength = input.headers.get('content-length')\n\n  if (explicitContentLength != null && explicitContentLength !== '') {\n    return Number(explicitContentLength)\n  }\n\n  const buffer = await input.arrayBuffer()\n  return buffer.byteLength\n}\n", "import type { Logger } from '@open-draft/logger'\nimport { XMLHttpRequestEmitter } from '.'\nimport { RequestController } from '../../RequestController'\nimport { XMLHttpRequestController } from './XMLHttpRequestController'\nimport { handleRequest } from '../../utils/handleRequest'\n\nexport interface XMLHttpRequestProxyOptions {\n  emitter: XMLHttpRequestEmitter\n  logger: Logger\n}\n\n/**\n * Create a proxied `XMLHttpRequest` class.\n * The proxied class establishes spies on certain methods,\n * allowing us to intercept requests and respond to them.\n */\nexport function createXMLHttpRequestProxy({\n  emitter,\n  logger,\n}: XMLHttpRequestProxyOptions) {\n  const XMLHttpRequestProxy = new Proxy(globalThis.XMLHttpRequest, {\n    construct(target, args, newTarget) {\n      logger.info('constructed new XMLHttpRequest')\n\n      const originalRequest = Reflect.construct(\n        target,\n        args,\n        newTarget\n      ) as XMLHttpRequest\n\n      /**\n       * @note Forward prototype descriptors onto the proxied object.\n       * XMLHttpRequest is implemented in JSDOM in a way that assigns\n       * a bunch of descriptors, like \"set responseType()\" on the prototype.\n       * With this propagation, we make sure that those descriptors trigger\n       * when the user operates with the proxied request instance.\n       */\n      const prototypeDescriptors = Object.getOwnPropertyDescriptors(\n        target.prototype\n      )\n      for (const propertyName in prototypeDescriptors) {\n        Reflect.defineProperty(\n          originalRequest,\n          propertyName,\n          prototypeDescriptors[propertyName]\n        )\n      }\n\n      const xhrRequestController = new XMLHttpRequestController(\n        originalRequest,\n        logger\n      )\n\n      xhrRequestController.onRequest = async function ({ request, requestId }) {\n        const controller = new RequestController(request)\n\n        this.logger.info('awaiting mocked response...')\n\n        this.logger.info(\n          'emitting the \"request\" event for %s listener(s)...',\n          emitter.listenerCount('request')\n        )\n\n        const isRequestHandled = await handleRequest({\n          request,\n          requestId,\n          controller,\n          emitter,\n          onResponse: async (response) => {\n            await this.respondWith(response)\n          },\n          onRequestError: () => {\n            this.errorWith(new TypeError('Network error'))\n          },\n          onError: (error) => {\n            this.logger.info('request errored!', { error })\n\n            if (error instanceof Error) {\n              this.errorWith(error)\n            }\n          },\n        })\n\n        if (!isRequestHandled) {\n          this.logger.info(\n            'no mocked response received, performing request as-is...'\n          )\n        }\n      }\n\n      xhrRequestController.onResponse = async function ({\n        response,\n        isMockedResponse,\n        request,\n        requestId,\n      }) {\n        this.logger.info(\n          'emitting the \"response\" event for %s listener(s)...',\n          emitter.listenerCount('response')\n        )\n\n        emitter.emit('response', {\n          response,\n          isMockedResponse,\n          request,\n          requestId,\n        })\n      }\n\n      // Return the proxied request from the controller\n      // so that the controller can react to the consumer's interactions\n      // with this request (opening/sending/etc).\n      return xhrRequestController.request\n    },\n  })\n\n  return XMLHttpRequestProxy\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAAS,aAAAA,kBAAiB;;;ACA1B,SAAS,iBAAiB;AAC1B,SAAS,qBAAqB;;;ACEvB,SAAS,kBACd,MACA,OACY;AACZ,QAAM,SAAS,IAAI,WAAW,KAAK,aAAa,MAAM,UAAU;AAChE,SAAO,IAAI,MAAM,CAAC;AAClB,SAAO,IAAI,OAAO,KAAK,UAAU;AACjC,SAAO;AACT;;;ACXO,IAAM,gBAAN,MAAqC;AAAA,EAwB1C,YACE,MACA,SACA;AA1BF,SAAS,OAAO;AAChB,SAAS,kBAAkB;AAC3B,SAAS,YAAY;AACrB,SAAS,iBAAiB;AAE1B,SAAO,OAAe;AACtB,SAAO,aAAiC;AAExC,SAAO,gBAAoC;AAC3C,SAAO,aAAqB;AAE5B,SAAO,YAAqB;AAC5B,SAAO,WAAoB;AAC3B,SAAO,aAAsB;AAC7B,SAAO,mBAA4B;AACnC,SAAO,UAAmB;AAC1B,SAAO,mBAA4B;AACnC,SAAO,SAAiB;AACxB,SAAO,QAAgB;AAEvB,wBAAwB;AACxB,uBAAuB;AAMrB,SAAK,OAAO;AACZ,SAAK,UAAS,mCAAS,WAAU;AACjC,SAAK,iBAAgB,mCAAS,kBAAiB;AAC/C,SAAK,YAAY,KAAK,IAAI;AAAA,EAC5B;AAAA,EAEO,eAA8B;AACnC,WAAO,CAAC;AAAA,EACV;AAAA,EAEO,UAAU,MAAc,SAAmB,YAAsB;AACtE,SAAK,OAAO;AACZ,SAAK,UAAU,CAAC,CAAC;AACjB,SAAK,aAAa,CAAC,CAAC;AAAA,EACtB;AAAA,EAEO,iBAAiB;AACtB,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EAEO,kBAAkB;AAAA,EAAC;AAAA,EACnB,2BAA2B;AAAA,EAAC;AACrC;;;AChDO,IAAM,wBAAN,cAAoC,cAAc;AAAA,EAMvD,YAAY,MAAc,MAA0B;AAClD,UAAM,IAAI;AAEV,SAAK,oBAAmB,6BAAM,qBAAoB;AAClD,SAAK,YAAW,6BAAM,aAAY;AAClC,SAAK,UAAS,6BAAM,WAAU;AAC9B,SAAK,SAAQ,6BAAM,UAAS;AAAA,EAC9B;AACF;;;ACbA,IAAM,0BAA0B,OAAO,kBAAkB;AAElD,SAAS,YACd,QACA,MACA,MAC+B;AAC/B,QAAM,iBAAiB;AAAA,IACrB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAMA,QAAM,qBAAqB,0BACvB,gBACA;AAEJ,QAAM,QAAQ,eAAe,SAAS,IAAI,IACtC,IAAI,mBAAmB,MAAM;AAAA,IAC3B,kBAAkB;AAAA,IAClB,SAAQ,6BAAM,WAAU;AAAA,IACxB,QAAO,6BAAM,UAAS;AAAA,EACxB,CAAC,IACD,IAAI,cAAc,MAAM;AAAA,IACtB;AAAA,IACA,eAAe;AAAA,EACjB,CAAC;AAEL,SAAO;AACT;;;ACpCO,SAAS,mBACd,QACA,cACe;AACf,MAAI,EAAE,gBAAgB,SAAS;AAC7B,WAAO;AAAA,EACT;AAEA,QAAM,cAAc,OAAO,UAAU,eAAe,KAAK,QAAQ,YAAY;AAC7E,MAAI,aAAa;AACf,WAAO;AAAA,EACT;AAEA,QAAM,YAAY,QAAQ,eAAe,MAAM;AAC/C,SAAO,YAAY,mBAAmB,WAAW,YAAY,IAAI;AACnE;;;ACKO,SAAS,YACd,QACA,SACQ;AACR,QAAM,QAAQ,IAAI,MAAM,QAAQ,sBAAsB,OAAO,CAAC;AAE9D,SAAO;AACT;AAEA,SAAS,sBACP,SACiB;AACjB,QAAM,EAAE,iBAAiB,YAAY,aAAa,YAAY,IAAI;AAClE,QAAM,UAA2B,CAAC;AAElC,MAAI,OAAO,oBAAoB,aAAa;AAC1C,YAAQ,YAAY,SAAU,QAAQ,MAAM,WAAW;AACrD,YAAM,OAAO,QAAQ,UAAU,KAAK,MAAM,QAAe,MAAM,SAAS;AACxE,aAAO,gBAAgB,KAAK,WAAW,MAAM,IAAI;AAAA,IACnD;AAAA,EACF;AAEA,UAAQ,MAAM,SAAU,QAAQ,cAAc,WAAW;AACvD,UAAM,OAAO,MAAM;AACjB,YAAM,iBAAiB,mBAAmB,QAAQ,YAAY,KAAK;AACnE,YAAM,iBAAiB,QAAQ;AAAA,QAC7B;AAAA,QACA;AAAA,MACF;AAGA,UAAI,QAAO,iDAAgB,SAAQ,aAAa;AAC9C,uBAAe,IAAI,MAAM,QAAQ,CAAC,SAAS,CAAC;AAC5C,eAAO;AAAA,MACT;AAGA,aAAO,QAAQ,eAAe,gBAAgB,cAAc;AAAA,QAC1D,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AAEA,QAAI,OAAO,gBAAgB,aAAa;AACtC,aAAO,YAAY,KAAK,QAAQ,CAAC,cAAc,SAAS,GAAG,IAAI;AAAA,IACjE;AAEA,WAAO,KAAK;AAAA,EACd;AAEA,UAAQ,MAAM,SAAU,QAAQ,cAAc,UAAU;AAItD,UAAM,OAAO,MAAM,OAAO,YAAmB;AAE7C,UAAM,QACJ,OAAO,gBAAgB,cACnB,YAAY,KAAK,QAAQ,CAAC,cAAc,QAAQ,GAAG,IAAI,IACvD,KAAK;AAEX,QAAI,OAAO,UAAU,YAAY;AAC/B,aAAO,IAAI,SAAqB;AAC9B,cAAMC,QAAO,MAAM,KAAK,QAAQ,GAAG,IAAI;AAEvC,YAAI,OAAO,eAAe,aAAa;AACrC,iBAAO,WAAW,KAAK,QAAQ,CAAC,cAAqB,IAAI,GAAGA,KAAI;AAAA,QAClE;AAEA,eAAOA,MAAK;AAAA,MACd;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;ACvGO,SAAS,yBACd,MACgC;AAChC,QAAM,iBAAgD;AAAA,IACpD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,SAAO,eAAe,KAAK,CAAC,kBAAkB;AAC5C,WAAO,KAAK,WAAW,aAAa;AAAA,EACtC,CAAC;AACH;;;ACTO,SAAS,UAAU,MAA8C;AACtE,MAAI;AACF,UAAM,OAAO,KAAK,MAAM,IAAI;AAC5B,WAAO;AAAA,EACT,SAAS,GAAP;AACA,WAAO;AAAA,EACT;AACF;;;ACLO,SAAS,eACd,SACA,MACU;AASV,QAAM,qBAAqB,cAAc,mBAAmB,QAAQ,MAAM,IACtE,OACA;AAEJ,SAAO,IAAI,cAAc,oBAAoB;AAAA,IAC3C,KAAK,QAAQ;AAAA,IACb,QAAQ,QAAQ;AAAA,IAChB,YAAY,QAAQ;AAAA,IACpB,SAAS;AAAA,MACP,QAAQ,sBAAsB;AAAA,IAChC;AAAA,EACF,CAAC;AACH;AAEA,SAAS,sCAAsC,eAAgC;AAC7E,QAAM,UAAU,IAAI,QAAQ;AAE5B,QAAM,QAAQ,cAAc,MAAM,SAAS;AAC3C,aAAW,QAAQ,OAAO;AACxB,QAAI,KAAK,KAAK,MAAM,IAAI;AACtB;AAAA,IACF;AAEA,UAAM,CAAC,MAAM,GAAG,KAAK,IAAI,KAAK,MAAM,IAAI;AACxC,UAAM,QAAQ,MAAM,KAAK,IAAI;AAE7B,YAAQ,OAAO,MAAM,KAAK;AAAA,EAC5B;AAEA,SAAO;AACT;;;AC5CA,eAAsB,kBACpB,OACiB;AACjB,QAAM,wBAAwB,MAAM,QAAQ,IAAI,gBAAgB;AAEhE,MAAI,yBAAyB,QAAQ,0BAA0B,IAAI;AACjE,WAAO,OAAO,qBAAqB;AAAA,EACrC;AAEA,QAAM,SAAS,MAAM,MAAM,YAAY;AACvC,SAAO,OAAO;AAChB;;;AVIA,IAAM,oBAAoB,OAAO,mBAAmB;AACpD,IAAM,UAAU,cAAc;AAC9B,IAAM,gBAAgB,OAAO,eAAe;AAMrC,IAAM,2BAAN,MAA+B;AAAA,EAgCpC,YAAqB,gBAAuC,QAAgB;AAAvD;AAAuC;AAV5D,SAAQ,SAAiB;AACzB,SAAQ,MAAW;AAUjB,SAAK,iBAAiB,IAAI;AAE1B,SAAK,SAAS,oBAAI,IAAI;AACtB,SAAK,eAAe,oBAAI,IAAI;AAC5B,SAAK,YAAY,gBAAgB;AACjC,SAAK,iBAAiB,IAAI,QAAQ;AAClC,SAAK,iBAAiB,IAAI,WAAW;AAErC,SAAK,UAAU,YAAY,gBAAgB;AAAA,MACzC,aAAa,CAAC,CAAC,cAAc,SAAS,GAAG,WAAW;AAClD,gBAAQ,cAAc;AAAA,UACpB,KAAK,aAAa;AAChB,kBAAM,YAAY,aAAa;AAAA,cAC7B;AAAA,YACF;AAOA,iBAAK,QAAQ,iBAAiB,WAAW,SAAgB;AAEzD,mBAAO,OAAO;AAAA,UAChB;AAAA,UAEA,SAAS;AACP,mBAAO,OAAO;AAAA,UAChB;AAAA,QACF;AAAA,MACF;AAAA,MACA,YAAY,CAAC,CAAC,YAAY,IAAI,GAAG,WAAW;AA3FlD;AA4FQ,gBAAQ,YAAY;AAAA,UAClB,KAAK,QAAQ;AACX,kBAAM,CAAC,QAAQ,GAAG,IAAI;AAEtB,gBAAI,OAAO,QAAQ,aAAa;AAC9B,mBAAK,SAAS;AACd,mBAAK,MAAM,cAAc,MAAM;AAAA,YACjC,OAAO;AACL,mBAAK,SAAS;AACd,mBAAK,MAAM,cAAc,GAAG;AAAA,YAC9B;AAEA,iBAAK,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,UAAU,KAAK,IAAI,MAAM;AAClE,iBAAK,OAAO,KAAK,QAAQ,KAAK,QAAQ,KAAK,IAAI,IAAI;AAEnD,mBAAO,OAAO;AAAA,UAChB;AAAA,UAEA,KAAK,oBAAoB;AACvB,kBAAM,CAAC,WAAW,QAAQ,IAAI;AAK9B,iBAAK,cAAc,WAAW,QAAQ;AACtC,iBAAK,OAAO,KAAK,oBAAoB,WAAW,QAAQ;AAExD,mBAAO,OAAO;AAAA,UAChB;AAAA,UAEA,KAAK,oBAAoB;AACvB,kBAAM,CAAC,MAAM,KAAK,IAAI;AACtB,iBAAK,eAAe,IAAI,MAAM,KAAK;AAEnC,iBAAK,OAAO,KAAK,oBAAoB,MAAM,KAAK;AAEhD,mBAAO,OAAO;AAAA,UAChB;AAAA,UAEA,KAAK,QAAQ;AACX,kBAAM,CAAC,IAAI,IAAI;AAIf,iBAAK,QAAQ,iBAAiB,QAAQ,MAAM;AAC1C,kBAAI,OAAO,KAAK,eAAe,aAAa;AAI1C,sBAAM,gBAAgB;AAAA,kBACpB,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAML,KAAK,QAAQ;AAAA,gBACf;AAGA,qBAAK,WAAW,KAAK,MAAM;AAAA,kBACzB,UAAU;AAAA,kBACV,kBAAkB,KAAK,iBAAiB;AAAA,kBACxC,SAAS;AAAA,kBACT,WAAW,KAAK;AAAA,gBAClB,CAAC;AAAA,cACH;AAAA,YACF,CAAC;AAED,kBAAM,cACJ,OAAO,SAAS,WAAW,aAAa,IAAI,IAAI;AAGlD,kBAAM,eAAe,KAAK,kBAAkB,WAAW;AACvD,iBAAK,aAAa,IAAI,aAAa,MAAM;AAEzC,kBAAM,uBACJ,UAAK,cAAL,mBAAgB,KAAK,MAAM;AAAA,cACzB,SAAS;AAAA,cACT,WAAW,KAAK;AAAA,YAClB,OAAM,QAAQ,QAAQ;AAExB,+BAAmB,QAAQ,MAAM;AAE/B,kBAAI,CAAC,KAAK,iBAAiB,GAAG;AAC5B,qBAAK,OAAO;AAAA,kBACV;AAAA,kBACA,KAAK,QAAQ;AAAA,gBACf;AAWA,oBAAI,SAAS;AACX,uBAAK,QAAQ;AAAA,oBACX;AAAA,oBACA,KAAK;AAAA,kBACP;AAAA,gBACF;AAEA,uBAAO,OAAO;AAAA,cAChB;AAAA,YACF,CAAC;AAED;AAAA,UACF;AAAA,UAEA,SAAS;AACP,mBAAO,OAAO;AAAA,UAChB;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAKD;AAAA,MACE,KAAK;AAAA,MACL;AAAA,MACA,YAAY,KAAK,QAAQ,QAAQ;AAAA,QAC/B,aAAa,CAAC,CAAC,cAAc,SAAS,GAAG,WAAW;AAClD,kBAAQ,cAAc;AAAA,YACpB,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK,aAAa;AAChB,oBAAM,YAAY,aAAa;AAAA,gBAC7B;AAAA,cACF;AAEA,mBAAK,oBAAoB,WAAW,SAAqB;AAAA,YAC3D;AAAA,UACF;AAEA,iBAAO,OAAO;AAAA,QAChB;AAAA,QACA,YAAY,CAAC,CAAC,YAAY,IAAI,GAAG,WAAW;AAC1C,kBAAQ,YAAY;AAAA,YAClB,KAAK,oBAAoB;AACvB,oBAAM,CAAC,WAAW,QAAQ,IAAI;AAI9B,mBAAK,oBAAoB,WAAW,QAAQ;AAC5C,mBAAK,OAAO,KAAK,2BAA2B,WAAW,QAAQ;AAE/D,qBAAO,OAAO;AAAA,YAChB;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEQ,cACN,WACA,UACM;AACN,UAAM,aAAa,KAAK,OAAO,IAAI,SAAS,KAAK,CAAC;AAClD,UAAM,aAAa,WAAW,OAAO,QAAQ;AAC7C,SAAK,OAAO,IAAI,WAAW,UAAU;AAErC,SAAK,OAAO,KAAK,yBAAyB,WAAW,QAAQ;AAAA,EAC/D;AAAA,EAEQ,oBACN,WACA,UACM;AACN,UAAM,aAAa,KAAK,aAAa,IAAI,SAAS,KAAK,CAAC;AACxD,UAAM,aAAa,WAAW,OAAO,QAAQ;AAC7C,SAAK,aAAa,IAAI,WAAW,UAAU;AAE3C,SAAK,OAAO,KAAK,gCAAgC,WAAW,QAAQ;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAa,YAAY,UAAmC;AAS1D,SAAK,iBAAiB,IAAI;AAM1B,QAAI,KAAK,aAAa,GAAG;AACvB,YAAM,yBAAyB,MAAM;AAAA,QACnC,KAAK,aAAa;AAAA,MACpB;AAEA,WAAK,QAAQ,aAAa,KAAK,QAAQ,QAAQ;AAAA,QAC7C,QAAQ;AAAA,QACR,OAAO;AAAA,MACT,CAAC;AACD,WAAK,QAAQ,YAAY,KAAK,QAAQ,QAAQ;AAAA,QAC5C,QAAQ;AAAA,QACR,OAAO;AAAA,MACT,CAAC;AACD,WAAK,QAAQ,QAAQ,KAAK,QAAQ,QAAQ;AAAA,QACxC,QAAQ;AAAA,QACR,OAAO;AAAA,MACT,CAAC;AACD,WAAK,QAAQ,WAAW,KAAK,QAAQ,QAAQ;AAAA,QAC3C,QAAQ;AAAA,QACR,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AAEA,SAAK,OAAO;AAAA,MACV;AAAA,MACA,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAEA,WAAO,KAAK,SAAS,UAAU,SAAS,MAAM;AAC9C,WAAO,KAAK,SAAS,cAAc,SAAS,UAAU;AACtD,WAAO,KAAK,SAAS,eAAe,KAAK,IAAI,IAAI;AAEjD,SAAK,QAAQ,oBAAoB,IAAI,MAAM,KAAK,QAAQ,mBAAmB;AAAA,MACzE,OAAO,CAAC,GAAG,IAAI,SAAyB;AACtC,aAAK,OAAO,KAAK,qBAAqB,KAAK,CAAC,CAAC;AAE7C,YAAI,KAAK,QAAQ,aAAa,KAAK,QAAQ,kBAAkB;AAC3D,eAAK,OAAO,KAAK,0CAA0C;AAG3D,iBAAO;AAAA,QACT;AAEA,cAAM,cAAc,SAAS,QAAQ,IAAI,KAAK,CAAC,CAAC;AAChD,aAAK,OAAO;AAAA,UACV;AAAA,UACA,KAAK,CAAC;AAAA,UACN;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AAED,SAAK,QAAQ,wBAAwB,IAAI;AAAA,MACvC,KAAK,QAAQ;AAAA,MACb;AAAA,QACE,OAAO,MAAM;AACX,eAAK,OAAO,KAAK,uBAAuB;AAExC,cAAI,KAAK,QAAQ,aAAa,KAAK,QAAQ,kBAAkB;AAC3D,iBAAK,OAAO,KAAK,kDAAkD;AAGnE,mBAAO;AAAA,UACT;AAEA,gBAAM,cAAc,MAAM,KAAK,SAAS,QAAQ,QAAQ,CAAC;AACzD,gBAAM,aAAa,YAChB,IAAI,CAAC,CAAC,YAAY,WAAW,MAAM;AAClC,mBAAO,GAAG,eAAe;AAAA,UAC3B,CAAC,EACA,KAAK,MAAM;AAEd,eAAK,OAAO,KAAK,oCAAoC,UAAU;AAE/D,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAGA,WAAO,iBAAiB,KAAK,SAAS;AAAA,MACpC,UAAU;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,KAAK,MAAM,KAAK;AAAA,MAClB;AAAA,MACA,cAAc;AAAA,QACZ,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,KAAK,MAAM,KAAK;AAAA,MAClB;AAAA,MACA,aAAa;AAAA,QACX,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,KAAK,MAAM,KAAK;AAAA,MAClB;AAAA,IACF,CAAC;AAED,UAAM,0BAA0B,MAAM,kBAAkB,SAAS,MAAM,CAAC;AAExE,SAAK,OAAO,KAAK,mCAAmC,uBAAuB;AAE3E,SAAK,QAAQ,aAAa,KAAK,SAAS;AAAA,MACtC,QAAQ;AAAA,MACR,OAAO;AAAA,IACT,CAAC;AAED,SAAK,cAAc,KAAK,QAAQ,gBAAgB;AAChD,SAAK,cAAc,KAAK,QAAQ,OAAO;AAEvC,UAAM,mBAAmB,MAAM;AAC7B,WAAK,OAAO,KAAK,mCAAmC;AAEpD,WAAK,cAAc,KAAK,QAAQ,IAAI;AAEpC,WAAK,QAAQ,QAAQ,KAAK,SAAS;AAAA,QACjC,QAAQ,KAAK,eAAe;AAAA,QAC5B,OAAO;AAAA,MACT,CAAC;AAED,WAAK,QAAQ,WAAW,KAAK,SAAS;AAAA,QACpC,QAAQ,KAAK,eAAe;AAAA,QAC5B,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AAEA,QAAI,SAAS,MAAM;AACjB,WAAK,OAAO,KAAK,wCAAwC;AAEzD,YAAM,SAAS,SAAS,KAAK,UAAU;AAEvC,YAAM,4BAA4B,YAAY;AAC5C,cAAM,EAAE,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK;AAE1C,YAAI,MAAM;AACR,eAAK,OAAO,KAAK,4BAA4B;AAC7C,2BAAiB;AACjB;AAAA,QACF;AAEA,YAAI,OAAO;AACT,eAAK,OAAO,KAAK,6BAA6B,KAAK;AACnD,eAAK,iBAAiB,kBAAkB,KAAK,gBAAgB,KAAK;AAElE,eAAK,QAAQ,YAAY,KAAK,SAAS;AAAA,YACrC,QAAQ,KAAK,eAAe;AAAA,YAC5B,OAAO;AAAA,UACT,CAAC;AAAA,QACH;AAEA,kCAA0B;AAAA,MAC5B;AAEA,gCAA0B;AAAA,IAC5B,OAAO;AACL,uBAAiB;AAAA,IACnB;AAAA,EACF;AAAA,EAEQ,uBAA+B;AACrC,WAAO,aAAa,KAAK,cAAc;AAAA,EACzC;AAAA,EAEA,IAAI,WAAoB;AACtB,SAAK,OAAO;AAAA,MACV;AAAA,MACA,KAAK,QAAQ;AAAA,IACf;AAEA,QAAI,KAAK,QAAQ,eAAe,KAAK,QAAQ,MAAM;AACjD,aAAO;AAAA,IACT;AAEA,YAAQ,KAAK,QAAQ,cAAc;AAAA,MACjC,KAAK,QAAQ;AACX,cAAM,eAAe,UAAU,KAAK,qBAAqB,CAAC;AAC1D,aAAK,OAAO,KAAK,0BAA0B,YAAY;AAEvD,eAAO;AAAA,MACT;AAAA,MAEA,KAAK,eAAe;AAClB,cAAM,cAAc,cAAc,KAAK,cAAc;AACrD,aAAK,OAAO,KAAK,iCAAiC,WAAW;AAE7D,eAAO;AAAA,MACT;AAAA,MAEA,KAAK,QAAQ;AACX,cAAM,WACJ,KAAK,QAAQ,kBAAkB,cAAc,KAAK;AACpD,cAAM,eAAe,IAAI,KAAK,CAAC,KAAK,qBAAqB,CAAC,GAAG;AAAA,UAC3D,MAAM;AAAA,QACR,CAAC;AAED,aAAK,OAAO;AAAA,UACV;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAAA,MAEA,SAAS;AACP,cAAM,eAAe,KAAK,qBAAqB;AAC/C,aAAK,OAAO;AAAA,UACV;AAAA,UACA,KAAK,QAAQ;AAAA,UACb;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA,EAEA,IAAI,eAAuB;AAMzB;AAAA,MACE,KAAK,QAAQ,iBAAiB,MAAM,KAAK,QAAQ,iBAAiB;AAAA,MAClE;AAAA,IACF;AAEA,QACE,KAAK,QAAQ,eAAe,KAAK,QAAQ,WACzC,KAAK,QAAQ,eAAe,KAAK,QAAQ,MACzC;AACA,aAAO;AAAA,IACT;AAEA,UAAM,eAAe,KAAK,qBAAqB;AAC/C,SAAK,OAAO,KAAK,yBAAyB,YAAY;AAEtD,WAAO;AAAA,EACT;AAAA,EAEA,IAAI,cAA+B;AACjC;AAAA,MACE,KAAK,QAAQ,iBAAiB,MAC5B,KAAK,QAAQ,iBAAiB;AAAA,MAChC;AAAA,IACF;AAEA,QAAI,KAAK,QAAQ,eAAe,KAAK,QAAQ,MAAM;AACjD,aAAO;AAAA,IACT;AAEA,UAAM,cAAc,KAAK,QAAQ,kBAAkB,cAAc,KAAK;AAEtE,QAAI,OAAO,cAAc,aAAa;AACpC,cAAQ;AAAA,QACN;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,QAAI,yBAAyB,WAAW,GAAG;AACzC,aAAO,IAAI,UAAU,EAAE;AAAA,QACrB,KAAK,qBAAqB;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA,EAEO,UAAU,OAAqB;AAKpC,SAAK,iBAAiB,IAAI;AAC1B,SAAK,OAAO,KAAK,0BAA0B;AAE3C,SAAK,cAAc,KAAK,QAAQ,IAAI;AACpC,SAAK,QAAQ,SAAS,KAAK,OAAO;AAClC,SAAK,QAAQ,WAAW,KAAK,OAAO;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA,EAKQ,cAAc,gBAA8B;AAClD,SAAK,OAAO;AAAA,MACV;AAAA,MACA,KAAK,QAAQ;AAAA,MACb;AAAA,IACF;AAEA,QAAI,KAAK,QAAQ,eAAe,gBAAgB;AAC9C,WAAK,OAAO,KAAK,+CAA+C;AAChE;AAAA,IACF;AAEA,WAAO,KAAK,SAAS,cAAc,cAAc;AAEjD,SAAK,OAAO,KAAK,yBAAyB,cAAc;AAExD,QAAI,mBAAmB,KAAK,QAAQ,QAAQ;AAC1C,WAAK,OAAO,KAAK,yCAAyC;AAE1D,WAAK,QAAQ,oBAAoB,KAAK,OAAO;AAAA,IAC/C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKQ,QAKN,WACA,QACA,SACM;AACN,UAAM,WAAY,OAA0B,KAAK,WAAW;AAC5D,UAAM,QAAQ,YAAY,QAAQ,WAAW,OAAO;AAEpD,SAAK,OAAO,KAAK,gBAAgB,WAAW,WAAW,EAAE;AAGzD,QAAI,OAAO,aAAa,YAAY;AAClC,WAAK,OAAO,KAAK,4CAA4C,SAAS;AACtE,eAAS,KAAK,QAA0B,KAAK;AAAA,IAC/C;AAGA,UAAM,SACJ,kBAAkB,uBAAuB,KAAK,eAAe,KAAK;AAEpE,eAAW,CAAC,qBAAqB,SAAS,KAAK,QAAQ;AACrD,UAAI,wBAAwB,WAAW;AACrC,aAAK,OAAO;AAAA,UACV;AAAA,UACA,UAAU;AAAA,UACV;AAAA,QACF;AAEA,kBAAU,QAAQ,CAAC,aAAa,SAAS,KAAK,QAAQ,KAAK,CAAC;AAAA,MAC9D;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKQ,kBACN,MACS;AACT,SAAK,OAAO,KAAK,8CAA8C;AAI/D,UAAM,eACJ,gBAAgB,WAAW,KAAK,gBAAgB,YAAY;AAE9D,UAAM,eAAe,IAAI,QAAQ,KAAK,IAAI,MAAM;AAAA,MAC9C,QAAQ,KAAK;AAAA,MACb,SAAS,KAAK;AAAA;AAAA;AAAA;AAAA,MAId,aAAa,KAAK,QAAQ,kBAAkB,YAAY;AAAA,MACxD,MAAM,CAAC,OAAO,MAAM,EAAE,SAAS,KAAK,OAAO,YAAY,CAAC,IACpD,OACA;AAAA,IACN,CAAC;AAED,UAAM,eAAe,YAAY,aAAa,SAAS;AAAA,MACrD,YAAY,CAAC,CAAC,YAAY,IAAI,GAAG,WAAW;AAI1C,gBAAQ,YAAY;AAAA,UAClB,KAAK;AAAA,UACL,KAAK,OAAO;AACV,kBAAM,CAAC,YAAY,WAAW,IAAI;AAClC,iBAAK,QAAQ,iBAAiB,YAAY,WAAW;AACrD;AAAA,UACF;AAAA,UAEA,KAAK,UAAU;AACb,kBAAM,CAAC,UAAU,IAAI;AACrB,oBAAQ;AAAA,cACN,oCAAoC,gEAAgE,aAAa,UAAU,aAAa;AAAA,YAC1I;AACA;AAAA,UACF;AAAA,QACF;AAEA,eAAO,OAAO;AAAA,MAChB;AAAA,IACF,CAAC;AACD,WAAO,cAAc,WAAW,YAAY;AAC5C,kBAAc,cAAc,KAAK,OAAO;AAExC,SAAK,OAAO,KAAK,6CAA6C,YAAY;AAE1E,WAAO;AAAA,EACT;AACF;AAppBG,mBACA;AAqpBH,SAAS,cAAc,KAAwB;AAQ7C,MAAI,OAAO,aAAa,aAAa;AACnC,WAAO,IAAI,IAAI,GAAG;AAAA,EACpB;AAEA,SAAO,IAAI,IAAI,IAAI,SAAS,GAAG,SAAS,IAAI;AAC9C;AAEA,SAAS,OACP,QACA,UACA,OACM;AACN,UAAQ,eAAe,QAAQ,UAAU;AAAA;AAAA,IAEvC,UAAU;AAAA,IACV,YAAY;AAAA,IACZ;AAAA,EACF,CAAC;AACH;;;AW/sBO,SAAS,0BAA0B;AAAA,EACxC;AAAA,EACA;AACF,GAA+B;AAC7B,QAAM,sBAAsB,IAAI,MAAM,WAAW,gBAAgB;AAAA,IAC/D,UAAU,QAAQ,MAAM,WAAW;AACjC,aAAO,KAAK,gCAAgC;AAE5C,YAAM,kBAAkB,QAAQ;AAAA,QAC9B;AAAA,QACA;AAAA,QACA;AAAA,MACF;AASA,YAAM,uBAAuB,OAAO;AAAA,QAClC,OAAO;AAAA,MACT;AACA,iBAAW,gBAAgB,sBAAsB;AAC/C,gBAAQ;AAAA,UACN;AAAA,UACA;AAAA,UACA,qBAAqB,YAAY;AAAA,QACnC;AAAA,MACF;AAEA,YAAM,uBAAuB,IAAI;AAAA,QAC/B;AAAA,QACA;AAAA,MACF;AAEA,2BAAqB,YAAY,eAAgB,EAAE,SAAS,UAAU,GAAG;AACvE,cAAM,aAAa,IAAI,kBAAkB,OAAO;AAEhD,aAAK,OAAO,KAAK,6BAA6B;AAE9C,aAAK,OAAO;AAAA,UACV;AAAA,UACA,QAAQ,cAAc,SAAS;AAAA,QACjC;AAEA,cAAM,mBAAmB,MAAM,cAAc;AAAA,UAC3C;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,YAAY,OAAO,aAAa;AAC9B,kBAAM,KAAK,YAAY,QAAQ;AAAA,UACjC;AAAA,UACA,gBAAgB,MAAM;AACpB,iBAAK,UAAU,IAAI,UAAU,eAAe,CAAC;AAAA,UAC/C;AAAA,UACA,SAAS,CAAC,UAAU;AAClB,iBAAK,OAAO,KAAK,oBAAoB,EAAE,MAAM,CAAC;AAE9C,gBAAI,iBAAiB,OAAO;AAC1B,mBAAK,UAAU,KAAK;AAAA,YACtB;AAAA,UACF;AAAA,QACF,CAAC;AAED,YAAI,CAAC,kBAAkB;AACrB,eAAK,OAAO;AAAA,YACV;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,2BAAqB,aAAa,eAAgB;AAAA,QAChD;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,GAAG;AACD,aAAK,OAAO;AAAA,UACV;AAAA,UACA,QAAQ,cAAc,UAAU;AAAA,QAClC;AAEA,gBAAQ,KAAK,YAAY;AAAA,UACvB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AAKA,aAAO,qBAAqB;AAAA,IAC9B;AAAA,EACF,CAAC;AAED,SAAO;AACT;;;AZ5GO,IAAM,6BAAN,cAAwC,YAAiC;AAAA,EAG9E,cAAc;AACZ,UAAM,2BAA0B,iBAAiB;AAAA,EACnD;AAAA,EAEU,mBAAmB;AAC3B,WAAO,sBAAsB,gBAAgB;AAAA,EAC/C;AAAA,EAEU,QAAQ;AAChB,UAAM,SAAS,KAAK,OAAO,OAAO,OAAO;AAEzC,WAAO,KAAK,qCAAqC;AAEjD,UAAM,qBAAqB,WAAW;AAEtC,IAAAC;AAAA,MACE,CAAE,mBAA2B,iBAAiB;AAAA,MAC9C;AAAA,IACF;AAEA,eAAW,iBAAiB,0BAA0B;AAAA,MACpD,SAAS,KAAK;AAAA,MACd,QAAQ,KAAK;AAAA,IACf,CAAC;AAED,WAAO;AAAA,MACL;AAAA,MACA,WAAW,eAAe;AAAA,IAC5B;AAEA,WAAO,eAAe,WAAW,gBAAgB,mBAAmB;AAAA,MAClE,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,OAAO;AAAA,IACT,CAAC;AAED,SAAK,cAAc,KAAK,MAAM;AAC5B,aAAO,eAAe,WAAW,gBAAgB,mBAAmB;AAAA,QAClE,OAAO;AAAA,MACT,CAAC;AAED,iBAAW,iBAAiB;AAC5B,aAAO;AAAA,QACL;AAAA,QACA,WAAW,eAAe;AAAA,MAC5B;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAnDO,IAAM,4BAAN;AAAM,0BACJ,oBAAoB,OAAO,KAAK;", "names": ["invariant", "next", "invariant"]}