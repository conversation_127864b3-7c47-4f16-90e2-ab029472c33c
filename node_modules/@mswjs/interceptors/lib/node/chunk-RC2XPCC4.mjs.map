{"version": 3, "sources": ["../../src/BatchInterceptor.ts"], "sourcesContent": ["import { EventMap, Listener } from 'strict-event-emitter'\nimport { Interceptor, ExtractEventNames } from './Interceptor'\n\nexport interface BatchInterceptorOptions<\n  InterceptorList extends ReadonlyArray<Interceptor<any>>\n> {\n  name: string\n  interceptors: InterceptorList\n}\n\nexport type ExtractEventMapType<\n  InterceptorList extends ReadonlyArray<Interceptor<any>>\n> = InterceptorList extends ReadonlyArray<infer InterceptorType>\n  ? InterceptorType extends Interceptor<infer EventMap>\n    ? EventMap\n    : never\n  : never\n\n/**\n * A batch interceptor that exposes a single interface\n * to apply and operate with multiple interceptors at once.\n */\nexport class BatchInterceptor<\n  InterceptorList extends ReadonlyArray<Interceptor<any>>,\n  Events extends EventMap = ExtractEventMapType<InterceptorList>\n> extends Interceptor<Events> {\n  static symbol: symbol\n\n  private interceptors: InterceptorList\n\n  constructor(options: BatchInterceptorOptions<InterceptorList>) {\n    BatchInterceptor.symbol = Symbol(options.name)\n    super(BatchInterceptor.symbol)\n    this.interceptors = options.interceptors\n  }\n\n  protected setup() {\n    const logger = this.logger.extend('setup')\n\n    logger.info('applying all %d interceptors...', this.interceptors.length)\n\n    for (const interceptor of this.interceptors) {\n      logger.info('applying \"%s\" interceptor...', interceptor.constructor.name)\n      interceptor.apply()\n\n      logger.info('adding interceptor dispose subscription')\n      this.subscriptions.push(() => interceptor.dispose())\n    }\n  }\n\n  public on<EventName extends ExtractEventNames<Events>>(\n    event: EventName,\n    listener: Listener<Events[EventName]>\n  ): this {\n    // Instead of adding a listener to the batch interceptor,\n    // propagate the listener to each of the individual interceptors.\n    for (const interceptor of this.interceptors) {\n      interceptor.on(event, listener)\n    }\n\n    return this\n  }\n\n  public once<EventName extends ExtractEventNames<Events>>(\n    event: EventName,\n    listener: Listener<Events[EventName]>\n  ): this {\n    for (const interceptor of this.interceptors) {\n      interceptor.once(event, listener)\n    }\n\n    return this\n  }\n\n  public off<EventName extends ExtractEventNames<Events>>(\n    event: EventName,\n    listener: Listener<Events[EventName]>\n  ): this {\n    for (const interceptor of this.interceptors) {\n      interceptor.off(event, listener)\n    }\n\n    return this\n  }\n\n  public removeAllListeners<EventName extends ExtractEventNames<Events>>(\n    event?: EventName | undefined\n  ): this {\n    for (const interceptors of this.interceptors) {\n      interceptors.removeAllListeners(event)\n    }\n\n    return this\n  }\n}\n"], "mappings": ";;;;;AAsBO,IAAM,mBAAN,cAGG,YAAoB;AAAA,EAK5B,YAAY,SAAmD;AAC7D,qBAAiB,SAAS,OAAO,QAAQ,IAAI;AAC7C,UAAM,iBAAiB,MAAM;AAC7B,SAAK,eAAe,QAAQ;AAAA,EAC9B;AAAA,EAEU,QAAQ;AAChB,UAAM,SAAS,KAAK,OAAO,OAAO,OAAO;AAEzC,WAAO,KAAK,mCAAmC,KAAK,aAAa,MAAM;AAEvE,eAAW,eAAe,KAAK,cAAc;AAC3C,aAAO,KAAK,gCAAgC,YAAY,YAAY,IAAI;AACxE,kBAAY,MAAM;AAElB,aAAO,KAAK,yCAAyC;AACrD,WAAK,cAAc,KAAK,MAAM,YAAY,QAAQ,CAAC;AAAA,IACrD;AAAA,EACF;AAAA,EAEO,GACL,OACA,UACM;AAGN,eAAW,eAAe,KAAK,cAAc;AAC3C,kBAAY,GAAG,OAAO,QAAQ;AAAA,IAChC;AAEA,WAAO;AAAA,EACT;AAAA,EAEO,KACL,OACA,UACM;AACN,eAAW,eAAe,KAAK,cAAc;AAC3C,kBAAY,KAAK,OAAO,QAAQ;AAAA,IAClC;AAEA,WAAO;AAAA,EACT;AAAA,EAEO,IACL,OACA,UACM;AACN,eAAW,eAAe,KAAK,cAAc;AAC3C,kBAAY,IAAI,OAAO,QAAQ;AAAA,IACjC;AAEA,WAAO;AAAA,EACT;AAAA,EAEO,mBACL,OACM;AACN,eAAW,gBAAgB,KAAK,cAAc;AAC5C,mBAAa,mBAAmB,KAAK;AAAA,IACvC;AAEA,WAAO;AAAA,EACT;AACF;", "names": []}