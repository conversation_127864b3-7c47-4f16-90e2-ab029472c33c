{"version": 3, "sources": ["../../../src/presets/node.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAQA,IAAO,eAAQ;AAAA,EACb,IAAI,yBAAyB;AAAA,EAC7B,IAAI,0BAA0B;AAAA,EAC9B,IAAI,iBAAiB;AACvB", "sourcesContent": ["import { ClientRequestInterceptor } from '../interceptors/ClientRequest'\nimport { XMLHttpRequestInterceptor } from '../interceptors/XMLHttpRequest'\nimport { FetchInterceptor } from '../interceptors/fetch'\n\n/**\n * The default preset provisions the interception of requests\n * regardless of their type (http/https/XMLHttpRequest).\n */\nexport default [\n  new ClientRequestInterceptor(),\n  new XMLHttpRequestInterceptor(),\n  new FetchInterceptor(),\n] as const\n"]}