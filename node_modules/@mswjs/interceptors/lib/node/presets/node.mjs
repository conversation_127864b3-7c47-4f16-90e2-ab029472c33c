import {
  ClientRequestInterceptor
} from "../chunk-GLGFOTGJ.mjs";
import "../chunk-TJDMZZXE.mjs";
import {
  XMLHttpRequestInterceptor
} from "../chunk-EADPZWWI.mjs";
import "../chunk-6HYIRFX2.mjs";
import {
  FetchInterceptor
} from "../chunk-GL6JCI7E.mjs";
import "../chunk-TX5GBTFY.mjs";
import "../chunk-6YM4PLBI.mjs";
import "../chunk-LGXJ3UUF.mjs";
import "../chunk-IHJSPMYM.mjs";
import "../chunk-3GJB4JDF.mjs";

// src/presets/node.ts
var node_default = [
  new ClientRequestInterceptor(),
  new XMLHttpRequestInterceptor(),
  new FetchInterceptor()
];
export {
  node_default as default
};
//# sourceMappingURL=node.mjs.map