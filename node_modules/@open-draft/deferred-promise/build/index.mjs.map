{"version": 3, "sources": ["../src/createDeferredExecutor.ts", "../src/DeferredPromise.ts"], "sourcesContent": ["export type PromiseState = 'pending' | 'fulfilled' | 'rejected'\n\nexport type Executor<Value> = ConstructorParameters<typeof Promise<Value>>[0]\nexport type ResolveFunction<Value> = Parameters<Executor<Value>>[0]\nexport type RejectFunction<Reason> = Parameters<Executor<Reason>>[1]\n\nexport type DeferredPromiseExecutor<Input = never, Output = Input> = {\n  (resolve?: ResolveFunction<Input>, reject?: RejectFunction<any>): void\n\n  resolve: ResolveFunction<Input>\n  reject: RejectFunction<any>\n  result?: Output\n  state: PromiseState\n  rejectionReason?: unknown\n}\nexport function createDeferredExecutor<\n  Input = never,\n  Output = Input\n>(): DeferredPromiseExecutor<Input, Output> {\n  const executor = <DeferredPromiseExecutor<Input, Output>>((\n    resolve,\n    reject\n  ) => {\n    executor.state = 'pending'\n\n    executor.resolve = (data) => {\n      if (executor.state !== 'pending') {\n        return\n      }\n\n      executor.result = data as Output\n\n      const onFulfilled = <Value>(value: Value) => {\n        executor.state = 'fulfilled'\n        return value\n      }\n\n      return resolve(\n        data instanceof Promise ? data : Promise.resolve(data).then(onFulfilled)\n      )\n    }\n\n    executor.reject = (reason) => {\n      if (executor.state !== 'pending') {\n        return\n      }\n\n      queueMicrotask(() => {\n        executor.state = 'rejected'\n      })\n\n      return reject((executor.rejectionReason = reason))\n    }\n  })\n\n  return executor\n}\n", "import {\n  type Executor,\n  type RejectFunction,\n  type ResolveFunction,\n  type DeferredPromiseExecutor,\n  createDeferredExecutor,\n} from './createDeferredExecutor'\n\nexport class DeferredPromise<Input, Output = Input> extends Promise<Input> {\n  #executor: DeferredPromiseExecutor\n\n  public resolve: ResolveFunction<Output>\n  public reject: RejectFunction<Output>\n\n  constructor(executor: Executor<Input> | null = null) {\n    const deferredExecutor = createDeferredExecutor()\n    super((originalResolve, originalReject) => {\n      deferredExecutor(originalResolve, originalReject)\n      executor?.(deferredExecutor.resolve, deferredExecutor.reject)\n    })\n\n    this.#executor = deferredExecutor\n    this.resolve = this.#executor.resolve\n    this.reject = this.#executor.reject\n  }\n\n  public get state() {\n    return this.#executor.state\n  }\n\n  public get rejectionReason() {\n    return this.#executor.rejectionReason\n  }\n\n  public then<ThenResult = Input, CatchResult = never>(\n    onFulfilled?: (value: Input) => ThenResult | PromiseLike<ThenResult>,\n    onRejected?: (reason: any) => CatchResult | PromiseLike<CatchResult>\n  ) {\n    return this.#decorate(super.then(onFulfilled, onRejected))\n  }\n\n  public catch<CatchResult = never>(\n    onRejected?: (reason: any) => CatchResult | PromiseLike<CatchResult>\n  ) {\n    return this.#decorate(super.catch(onRejected))\n  }\n\n  public finally(onfinally?: () => void | Promise<any>) {\n    return this.#decorate(super.finally(onfinally))\n  }\n\n  #decorate<ChildInput>(\n    promise: Promise<ChildInput>\n  ): DeferredPromise<ChildInput, Output> {\n    return Object.defineProperties(promise, {\n      resolve: { configurable: true, value: this.resolve },\n      reject: { configurable: true, value: this.reject },\n    }) as DeferredPromise<ChildInput, Output>\n  }\n}\n"], "mappings": ";AAeO,SAAS,yBAG4B;AAC1C,QAAM,WAAoD,CACxD,SACA,WACG;AACH,aAAS,QAAQ;AAEjB,aAAS,UAAU,CAAC,SAAS;AAC3B,UAAI,SAAS,UAAU,WAAW;AAChC;AAAA,MACF;AAEA,eAAS,SAAS;AAElB,YAAM,cAAc,CAAQ,UAAiB;AAC3C,iBAAS,QAAQ;AACjB,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,QACL,gBAAgB,UAAU,OAAO,QAAQ,QAAQ,IAAI,EAAE,KAAK,WAAW;AAAA,MACzE;AAAA,IACF;AAEA,aAAS,SAAS,CAAC,WAAW;AAC5B,UAAI,SAAS,UAAU,WAAW;AAChC;AAAA,MACF;AAEA,qBAAe,MAAM;AACnB,iBAAS,QAAQ;AAAA,MACnB,CAAC;AAED,aAAO,OAAQ,SAAS,kBAAkB,MAAO;AAAA,IACnD;AAAA,EACF;AAEA,SAAO;AACT;;;AChDO,IAAM,kBAAN,cAAqD,QAAe;AAAA,EACzE;AAAA,EAEO;AAAA,EACA;AAAA,EAEP,YAAY,WAAmC,MAAM;AACnD,UAAM,mBAAmB,uBAAuB;AAChD,UAAM,CAAC,iBAAiB,mBAAmB;AACzC,uBAAiB,iBAAiB,cAAc;AAChD,iBAAW,iBAAiB,SAAS,iBAAiB,MAAM;AAAA,IAC9D,CAAC;AAED,SAAK,YAAY;AACjB,SAAK,UAAU,KAAK,UAAU;AAC9B,SAAK,SAAS,KAAK,UAAU;AAAA,EAC/B;AAAA,EAEA,IAAW,QAAQ;AACjB,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EAEA,IAAW,kBAAkB;AAC3B,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EAEO,KACL,aACA,YACA;AACA,WAAO,KAAK,UAAU,MAAM,KAAK,aAAa,UAAU,CAAC;AAAA,EAC3D;AAAA,EAEO,MACL,YACA;AACA,WAAO,KAAK,UAAU,MAAM,MAAM,UAAU,CAAC;AAAA,EAC/C;AAAA,EAEO,QAAQ,WAAuC;AACpD,WAAO,KAAK,UAAU,MAAM,QAAQ,SAAS,CAAC;AAAA,EAChD;AAAA,EAEA,UACE,SACqC;AACrC,WAAO,OAAO,iBAAiB,SAAS;AAAA,MACtC,SAAS,EAAE,cAAc,MAAM,OAAO,KAAK,QAAQ;AAAA,MACnD,QAAQ,EAAE,cAAc,MAAM,OAAO,KAAK,OAAO;AAAA,IACnD,CAAC;AAAA,EACH;AACF;", "names": []}