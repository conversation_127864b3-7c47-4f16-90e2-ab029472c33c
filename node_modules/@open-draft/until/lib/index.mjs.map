{"version": 3, "sources": ["../src/until.ts"], "sourcesContent": ["export type AsyncTuple<\n  ErrorType extends any = Error,\n  DataType extends any = unknown,\n> =\n  | {\n      error: ErrorType\n      data: null\n    }\n  | { error: null; data: DataType }\n\n/**\n * Gracefully handles a given Promise factory.\n * @example\n * const { error, data } = await until(() => asyncAction())\n */\nexport const until = async <\n  ErrorType extends any = Error,\n  DataType extends any = unknown,\n>(\n  promise: () => Promise<DataType>,\n): Promise<AsyncTuple<ErrorType, DataType>> => {\n  try {\n    const data = await promise().catch((error) => {\n      throw error\n    })\n    return { error: null, data }\n  } catch (error) {\n    return { error, data: null }\n  }\n}\n"], "mappings": ";AAeO,IAAM,QAAQ,OAInB,YAC6C;AAC7C,MAAI;AACF,UAAM,OAAO,MAAM,QAAQ,EAAE,MAAM,CAAC,UAAU;AAC5C,YAAM;AAAA,IACR,CAAC;AACD,WAAO,EAAE,OAAO,MAAM,KAAK;AAAA,EAC7B,SAAS,OAAP;AACA,WAAO,EAAE,OAAO,MAAM,KAAK;AAAA,EAC7B;AACF;", "names": []}