{"author": {"email": "<EMAIL>", "name": "<PERSON><PERSON>"}, "contributors": [{"email": "<EMAIL>", "name": "<PERSON>"}], "engines": {"node": ">=12.0.0"}, "devDependencies": {"@types/mocha": "^9.1.1", "@types/node": "^17.0.32", "@typescript-eslint/eslint-plugin": "^5.23.0", "@typescript-eslint/parser": "^5.23.0", "asn1-test-suite": "^1.0.2", "eslint": "^8.15.0", "eslint-plugin-deprecation": "^1.3.2", "mocha": "^10.0.0", "nyc": "^15.1.0", "rollup": "^2.72.1", "rollup-plugin-dts": "^4.2.1", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-typescript2": "^0.31.2", "ts-node": "^10.7.0", "typescript": "^4.6.4"}, "repository": {"type": "git", "url": "git://github.com/PeculiarVentures/asn1.js.git"}, "dependencies": {"pvtsutils": "^1.3.2", "pvutils": "^1.1.3", "tslib": "^2.4.0"}, "description": "asn1js is a pure JavaScript library implementing this standard. ASN.1 is the basis of all X.509 related data structures and numerous other protocols used on the web", "keywords": ["asn1", "parser", "asn.1", "ber", "der", "sequence", "set", "bitstring", "octetstring", "utctime", "utf8string", "bmpstring", "universalstring", "generalizedtime"], "main": "build/index.js", "module": "build/index.es.js", "types": "build/index.d.ts", "name": "asn1js", "files": ["build", "LICENSE", "README.md"], "scripts": {"build": "rollup -c", "test": "mocha", "prepublishOnly": "npm run build", "lint": "eslint . --ext .ts", "lint:fix": "eslint --fix . --ext .ts", "coverage": "nyc npm test"}, "version": "3.0.5", "license": "BSD-3-<PERSON><PERSON>"}