{"name": "bignumber.js", "description": "A library for arbitrary-precision decimal and non-decimal arithmetic", "version": "9.2.1", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "repository": {"type": "git", "url": "https://github.com/MikeMcl/bignumber.js.git"}, "main": "bignumber", "module": "bignumber.mjs", "browser": "bignumber.js", "types": "bignumber.d.cts", "exports": {".": {"import": {"types": "./bignumber.d.mts", "default": "./bignumber.mjs"}, "require": {"types": "./bignumber.d.cts", "default": "./bignumber.js"}, "browser": {"types": "./bignumber.d.cts", "default": "./bignumber.js"}}, "./bignumber.mjs": "./bignumber.mjs", "./bignumber.js": "./bignumber.js", "./package.json": "./package.json"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": "*"}, "license": "MIT", "scripts": {"test": "node test/test"}, "dependencies": {}}