{"version": 3, "sources": ["../node_modules/set-cookie-parser/lib/set-cookie.js", "../src/index.ts", "../src/Headers.ts", "../src/utils/normalizeHeaderName.ts", "../src/utils/normalizeHeaderValue.ts", "../src/utils/isValidHeaderName.ts", "../src/utils/isValidHeaderValue.ts", "../src/getRawHeaders.ts", "../src/transformers/headersToList.ts", "../src/transformers/headersToString.ts", "../src/transformers/headersToObject.ts", "../src/transformers/stringToHeaders.ts", "../src/transformers/listToHeaders.ts", "../src/transformers/reduceHeadersObject.ts", "../src/transformers/objectToHeaders.ts", "../src/transformers/flattenHeadersList.ts", "../src/transformers/flattenHeadersObject.ts"], "names": ["module", "value", "cookies", "splitCookiesString"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA,8DAAAA,SAAA;AAAA;AAEA,QAAI,sBAAsB;AAAA,MACxB,cAAc;AAAA,MACd,KAAK;AAAA,MACL,QAAQ;AAAA,IACV;AAEA,aAAS,iBAAiB,KAAK;AAC7B,aAAO,OAAO,QAAQ,YAAY,CAAC,CAAC,IAAI,KAAK;AAAA,IAC/C;AAEA,aAAS,YAAY,gBAAgB,SAAS;AAC5C,UAAI,QAAQ,eAAe,MAAM,GAAG,EAAE,OAAO,gBAAgB;AAE7D,UAAI,mBAAmB,MAAM,MAAM;AACnC,UAAI,SAAS,mBAAmB,gBAAgB;AAChD,UAAI,OAAO,OAAO;AAClB,UAAI,QAAQ,OAAO;AAEnB,gBAAU,UACN,OAAO,OAAO,CAAC,GAAG,qBAAqB,OAAO,IAC9C;AAEJ,UAAI;AACF,gBAAQ,QAAQ,eAAe,mBAAmB,KAAK,IAAI;AAAA,MAC7D,SAAS,GAAG;AACV,gBAAQ;AAAA,UACN,gFACE,QACA;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,UAAI,SAAS;AAAA,QACX;AAAA,QACA;AAAA,MACF;AAEA,YAAM,QAAQ,SAAU,MAAM;AAC5B,YAAI,QAAQ,KAAK,MAAM,GAAG;AAC1B,YAAI,MAAM,MAAM,MAAM,EAAE,SAAS,EAAE,YAAY;AAC/C,YAAIC,SAAQ,MAAM,KAAK,GAAG;AAC1B,YAAI,QAAQ,WAAW;AACrB,iBAAO,UAAU,IAAI,KAAKA,MAAK;AAAA,QACjC,WAAW,QAAQ,WAAW;AAC5B,iBAAO,SAAS,SAASA,QAAO,EAAE;AAAA,QACpC,WAAW,QAAQ,UAAU;AAC3B,iBAAO,SAAS;AAAA,QAClB,WAAW,QAAQ,YAAY;AAC7B,iBAAO,WAAW;AAAA,QACpB,WAAW,QAAQ,YAAY;AAC7B,iBAAO,WAAWA;AAAA,QACpB,OAAO;AACL,iBAAO,GAAG,IAAIA;AAAA,QAChB;AAAA,MACF,CAAC;AAED,aAAO;AAAA,IACT;AAEA,aAAS,mBAAmB,kBAAkB;AAG5C,UAAI,OAAO;AACX,UAAI,QAAQ;AACZ,UAAI,eAAe,iBAAiB,MAAM,GAAG;AAC7C,UAAI,aAAa,SAAS,GAAG;AAC3B,eAAO,aAAa,MAAM;AAC1B,gBAAQ,aAAa,KAAK,GAAG;AAAA,MAC/B,OAAO;AACL,gBAAQ;AAAA,MACV;AAEA,aAAO,EAAE,MAAY,MAAa;AAAA,IACpC;AAEA,aAAS,MAAM,OAAO,SAAS;AAC7B,gBAAU,UACN,OAAO,OAAO,CAAC,GAAG,qBAAqB,OAAO,IAC9C;AAEJ,UAAI,CAAC,OAAO;AACV,YAAI,CAAC,QAAQ,KAAK;AAChB,iBAAO,CAAC;AAAA,QACV,OAAO;AACL,iBAAO,CAAC;AAAA,QACV;AAAA,MACF;AAEA,UAAI,MAAM,SAAS;AACjB,YAAI,OAAO,MAAM,QAAQ,iBAAiB,YAAY;AAGpD,kBAAQ,MAAM,QAAQ,aAAa;AAAA,QACrC,WAAW,MAAM,QAAQ,YAAY,GAAG;AAEtC,kBAAQ,MAAM,QAAQ,YAAY;AAAA,QACpC,OAAO;AAEL,cAAI,MACF,MAAM,QACJ,OAAO,KAAK,MAAM,OAAO,EAAE,KAAK,SAAU,KAAK;AAC7C,mBAAO,IAAI,YAAY,MAAM;AAAA,UAC/B,CAAC,CACH;AAEF,cAAI,CAAC,OAAO,MAAM,QAAQ,UAAU,CAAC,QAAQ,QAAQ;AACnD,oBAAQ;AAAA,cACN;AAAA,YACF;AAAA,UACF;AACA,kBAAQ;AAAA,QACV;AAAA,MACF;AACA,UAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,gBAAQ,CAAC,KAAK;AAAA,MAChB;AAEA,gBAAU,UACN,OAAO,OAAO,CAAC,GAAG,qBAAqB,OAAO,IAC9C;AAEJ,UAAI,CAAC,QAAQ,KAAK;AAChB,eAAO,MAAM,OAAO,gBAAgB,EAAE,IAAI,SAAU,KAAK;AACvD,iBAAO,YAAY,KAAK,OAAO;AAAA,QACjC,CAAC;AAAA,MACH,OAAO;AACL,YAAI,UAAU,CAAC;AACf,eAAO,MAAM,OAAO,gBAAgB,EAAE,OAAO,SAAUC,UAAS,KAAK;AACnE,cAAI,SAAS,YAAY,KAAK,OAAO;AACrC,UAAAA,SAAQ,OAAO,IAAI,IAAI;AACvB,iBAAOA;AAAA,QACT,GAAG,OAAO;AAAA,MACZ;AAAA,IACF;AAaA,aAASC,oBAAmB,eAAe;AACzC,UAAI,MAAM,QAAQ,aAAa,GAAG;AAChC,eAAO;AAAA,MACT;AACA,UAAI,OAAO,kBAAkB,UAAU;AACrC,eAAO,CAAC;AAAA,MACV;AAEA,UAAI,iBAAiB,CAAC;AACtB,UAAI,MAAM;AACV,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,eAAS,iBAAiB;AACxB,eAAO,MAAM,cAAc,UAAU,KAAK,KAAK,cAAc,OAAO,GAAG,CAAC,GAAG;AACzE,iBAAO;AAAA,QACT;AACA,eAAO,MAAM,cAAc;AAAA,MAC7B;AAEA,eAAS,iBAAiB;AACxB,aAAK,cAAc,OAAO,GAAG;AAE7B,eAAO,OAAO,OAAO,OAAO,OAAO,OAAO;AAAA,MAC5C;AAEA,aAAO,MAAM,cAAc,QAAQ;AACjC,gBAAQ;AACR,gCAAwB;AAExB,eAAO,eAAe,GAAG;AACvB,eAAK,cAAc,OAAO,GAAG;AAC7B,cAAI,OAAO,KAAK;AAEd,wBAAY;AACZ,mBAAO;AAEP,2BAAe;AACf,wBAAY;AAEZ,mBAAO,MAAM,cAAc,UAAU,eAAe,GAAG;AACrD,qBAAO;AAAA,YACT;AAGA,gBAAI,MAAM,cAAc,UAAU,cAAc,OAAO,GAAG,MAAM,KAAK;AAEnE,sCAAwB;AAExB,oBAAM;AACN,6BAAe,KAAK,cAAc,UAAU,OAAO,SAAS,CAAC;AAC7D,sBAAQ;AAAA,YACV,OAAO;AAGL,oBAAM,YAAY;AAAA,YACpB;AAAA,UACF,OAAO;AACL,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,YAAI,CAAC,yBAAyB,OAAO,cAAc,QAAQ;AACzD,yBAAe,KAAK,cAAc,UAAU,OAAO,cAAc,MAAM,CAAC;AAAA,QAC1E;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,IAAAH,QAAO,UAAU;AACjB,IAAAA,QAAO,QAAQ,QAAQ;AACvB,IAAAA,QAAO,QAAQ,cAAc;AAC7B,IAAAA,QAAO,QAAQ,qBAAqBG;AAAA;AAAA;;;ACjOpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,+BAAmC;;;ACAnC,IAAM,6BAA6B;AAE5B,SAAS,oBAAoB,MAAsB;AACxD,MAAI,2BAA2B,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,IAAI;AAC/D,UAAM,IAAI,UAAU,wCAAwC;AAAA,EAC9D;AAEA,SAAO,KAAK,KAAK,EAAE,YAAY;AACjC;;;ACRA,IAAM,oBAAoB;AAAA,EACxB,OAAO,aAAa,EAAI;AAAA,EACxB,OAAO,aAAa,EAAI;AAAA,EACxB,OAAO,aAAa,CAAI;AAAA,EACxB,OAAO,aAAa,EAAI;AAC1B;AAEA,IAAM,6BAA6B,IAAI;AAAA,EACrC,MAAM,kBAAkB,KAAK,EAAE,CAAC,OAAO,kBAAkB,KAAK,EAAE,CAAC;AAAA,EACjE;AACF;AAMO,SAAS,qBAAqB,OAAuB;AAC1D,QAAM,YAAY,MAAM,QAAQ,4BAA4B,EAAE;AAC9D,SAAO;AACT;;;ACfO,SAAS,kBAAkB,OAAgB;AAChD,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,WAAW,GAAG;AACtB,WAAO;AAAA,EACT;AAEA,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAM,YAAY,MAAM,WAAW,CAAC;AAEpC,QAAI,YAAY,OAAQ,CAAC,QAAQ,SAAS,GAAG;AAC3C,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,QAAQ,OAAiC;AAChD,SAAO,CAAC;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,EAAE,SAAS,KAAK;AAClB;;;AC1CO,SAAS,mBAAmB,OAAyB;AAC1D,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,KAAK,MAAM,OAAO;AAC1B,WAAO;AAAA,EACT;AAEA,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAM,YAAY,MAAM,WAAW,CAAC;AAEpC;AAAA;AAAA,MAEE,cAAc;AAAA,MAEd,cAAc,MACd,cAAc;AAAA,MACd;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;;;AJrBO,IAAM,qBAAoC,OAAO,mBAAmB;AAEpE,IAAM,mBAAkC,OAAO,gBAAgB;AAEtE,IAAM,yBAAyB;AAX/B;AAaO,IAAM,UAAN,MAAM,SAAQ;AAAA,EAQnB,YAAY,MAAkD;AAN9D;AAAA,SAAS,MAA8C,CAAC;AAIxD;AAAA;AAAA,SAAS,MAAyC,oBAAI,IAAI;AAmC1D,SAAC,MAAsB;AA5BrB,QACE,CAAC,WAAW,iBAAiB,EAAE,SAAS,MAAM,YAAY,IAAI,KAC9D,gBAAgB,YACf,OAAO,WAAW,YAAY,eAC7B,gBAAgB,WAAW,SAC7B;AACA,YAAM,iBAAiB;AACvB,qBAAe,QAAQ,CAAC,OAAO,SAAS;AACtC,aAAK,OAAO,MAAM,KAAK;AAAA,MACzB,GAAG,IAAI;AAAA,IACT,WAAW,MAAM,QAAQ,IAAI,GAAG;AAC9B,WAAK,QAAQ,CAAC,CAAC,MAAM,KAAK,MAAM;AAC9B,aAAK;AAAA,UACH;AAAA,UACA,MAAM,QAAQ,KAAK,IAAI,MAAM,KAAK,sBAAsB,IAAI;AAAA,QAC9D;AAAA,MACF,CAAC;AAAA,IACH,WAAW,MAAM;AACf,aAAO,oBAAoB,IAAI,EAAE,QAAQ,CAAC,SAAS;AACjD,cAAM,QAAQ,KAAK,IAAI;AACvB,aAAK;AAAA,UACH;AAAA,UACA,MAAM,QAAQ,KAAK,IAAI,MAAM,KAAK,sBAAsB,IAAI;AAAA,QAC9D;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAIA,EAzCS,yBAIA,uBAmCR,YAAO,aAEP,OAAO,SAAQ,IAAI;AAClB,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EAEA,CAAC,OAAiC;AAChC,eAAW,CAAC,IAAI,KAAK,KAAK,QAAQ,GAAG;AACnC,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,CAAC,SAAmC;AAClC,eAAW,CAAC,EAAE,KAAK,KAAK,KAAK,QAAQ,GAAG;AACtC,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,CAAC,UAA8C;AAE7C,QAAI,aAAa,OAAO,KAAK,KAAK,kBAAkB,CAAC,EAAE;AAAA,MAAK,CAAC,GAAG,MAC9D,EAAE,cAAc,CAAC;AAAA,IACnB;AACA,eAAW,QAAQ,YAAY;AAC7B,UAAI,SAAS,cAAc;AACzB,mBAAW,SAAS,KAAK,aAAa,GAAG;AACvC,gBAAM,CAAC,MAAM,KAAK;AAAA,QACpB;AAAA,MACF,OAAO;AACL,cAAM,CAAC,MAAM,KAAK,IAAI,IAAI,CAAC;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,MAAuB;AACzB,QAAI,CAAC,kBAAkB,IAAI,GAAG;AAC5B,YAAM,IAAI,UAAU,wBAAwB,IAAI,GAAG;AAAA,IACrD;AAEA,WAAO,KAAK,kBAAkB,EAAE,eAAe,oBAAoB,IAAI,CAAC;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,MAA6B;AAC/B,QAAI,CAAC,kBAAkB,IAAI,GAAG;AAC5B,YAAM,UAAU,wBAAwB,IAAI,GAAG;AAAA,IACjD;AAEA,WAAO,KAAK,kBAAkB,EAAE,oBAAoB,IAAI,CAAC,KAAK;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,MAAc,OAAqB;AACrC,QAAI,CAAC,kBAAkB,IAAI,KAAK,CAAC,mBAAmB,KAAK,GAAG;AAC1D;AAAA,IACF;AAEA,UAAM,iBAAiB,oBAAoB,IAAI;AAC/C,UAAM,kBAAkB,qBAAqB,KAAK;AAElD,SAAK,kBAAkB,EAAE,cAAc,IACrC,qBAAqB,eAAe;AACtC,SAAK,gBAAgB,EAAE,IAAI,gBAAgB,IAAI;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,MAAc,OAAqB;AACxC,QAAI,CAAC,kBAAkB,IAAI,KAAK,CAAC,mBAAmB,KAAK,GAAG;AAC1D;AAAA,IACF;AAEA,UAAM,iBAAiB,oBAAoB,IAAI;AAC/C,UAAM,kBAAkB,qBAAqB,KAAK;AAElD,QAAI,gBAAgB,KAAK,IAAI,cAAc,IACvC,GAAG,KAAK,IAAI,cAAc,CAAC,KAAK,eAAe,KAC/C;AAEJ,SAAK,IAAI,MAAM,aAAa;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,MAAoB;AACzB,QAAI,CAAC,kBAAkB,IAAI,GAAG;AAC5B;AAAA,IACF;AAEA,QAAI,CAAC,KAAK,IAAI,IAAI,GAAG;AACnB;AAAA,IACF;AAEA,UAAM,iBAAiB,oBAAoB,IAAI;AAC/C,WAAO,KAAK,kBAAkB,EAAE,cAAc;AAC9C,SAAK,gBAAgB,EAAE,OAAO,cAAc;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QACE,UAMA,SACA;AACA,eAAW,CAAC,MAAM,KAAK,KAAK,KAAK,QAAQ,GAAG;AAC1C,eAAS,KAAK,SAAS,OAAO,MAAM,IAAI;AAAA,IAC1C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAyB;AACvB,UAAM,kBAAkB,KAAK,IAAI,YAAY;AAE7C,QAAI,oBAAoB,MAAM;AAC5B,aAAO,CAAC;AAAA,IACV;AAEA,QAAI,oBAAoB,IAAI;AAC1B,aAAO,CAAC,EAAE;AAAA,IACZ;AAEA,eAAO,6CAAmB,eAAe;AAAA,EAC3C;AACF;;;AKhMO,SAAS,cAAc,SAAkB;AAC9C,QAAM,aAAqC,CAAC;AAE5C,aAAW,CAAC,MAAM,KAAK,KAAK,QAAQ,QAAQ,GAAG;AAC7C,eAAW,QAAQ,gBAAgB,EAAE,IAAI,IAAI,CAAC,IAAI;AAAA,EACpD;AAEA,SAAO;AACT;;;ACXO,SAAS,cAAc,SAA+B;AAC3D,QAAM,cAA2B,CAAC;AAElC,UAAQ,QAAQ,CAAC,OAAO,SAAS;AAC/B,UAAM,gBAAgB,MAAM,SAAS,GAAG,IACpC,MAAM,MAAM,GAAG,EAAE,IAAI,CAACF,WAAUA,OAAM,KAAK,CAAC,IAC5C;AAEJ,gBAAY,KAAK,CAAC,MAAM,aAAa,CAAC;AAAA,EACxC,CAAC;AAED,SAAO;AACT;;;ACTO,SAAS,gBAAgB,SAA0B;AACxD,QAAM,OAAO,cAAc,OAAO;AAClC,QAAM,QAAQ,KAAK,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM;AACxC,UAAM,SAAU,CAAC,EAAe,OAAO,KAAK;AAC5C,WAAO,GAAG,IAAI,KAAK,OAAO,KAAK,IAAI,CAAC;AAAA,EACtC,CAAC;AAED,SAAO,MAAM,KAAK,MAAM;AAC1B;;;ACTA,IAAM,qBAAqB,CAAC,YAAY;AAMjC,SAAS,gBAAgB,SAAiC;AAC/D,QAAM,gBAA+B,CAAC;AAEtC,UAAQ,QAAQ,CAAC,OAAO,SAAS;AAC/B,UAAM,eACJ,CAAC,mBAAmB,SAAS,KAAK,YAAY,CAAC,KAAK,MAAM,SAAS,GAAG;AACxE,kBAAc,IAAI,IAAI,eAClB,MAAM,MAAM,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,IACpC;AAAA,EACN,CAAC;AAED,SAAO;AACT;;;AChBO,SAAS,gBAAgB,KAAsB;AACpD,QAAM,QAAQ,IAAI,KAAK,EAAE,MAAM,SAAS;AAExC,SAAO,MAAM,OAAO,CAAC,SAAS,SAAS;AACrC,QAAI,KAAK,KAAK,MAAM,IAAI;AACtB,aAAO;AAAA,IACT;AAEA,UAAM,QAAQ,KAAK,MAAM,IAAI;AAC7B,UAAM,OAAO,MAAM,MAAM;AACzB,UAAM,QAAQ,MAAM,KAAK,IAAI;AAC7B,YAAQ,OAAO,MAAM,KAAK;AAE1B,WAAO;AAAA,EACT,GAAG,IAAI,QAAQ,CAAC;AAClB;;;AClBO,SAAS,cAAc,MAA4B;AACxD,QAAM,UAAU,IAAI,QAAQ;AAE5B,OAAK,QAAQ,CAAC,CAAC,MAAM,KAAK,MAAM;AAC9B,UAAM,SAAU,CAAC,EAAe,OAAO,KAAK;AAE5C,WAAO,QAAQ,CAACA,WAAU;AACxB,cAAQ,OAAO,MAAMA,MAAK;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC;AAED,SAAO;AACT;;;ACVO,SAAS,oBACd,SACA,SACA,cACG;AACH,SAAO,OAAO,KAAK,OAAO,EAAE,OAAU,CAAC,aAAa,SAAS;AAC3D,WAAO,QAAQ,aAAa,MAAM,QAAQ,IAAI,CAAC;AAAA,EACjD,GAAG,YAAY;AACjB;;;ACPO,SAAS,gBACd,eACS;AACT,SAAO;AAAA,IACL;AAAA,IACA,CAAC,SAAS,MAAM,UAAU;AACxB,YAAM,SAAU,CAAC,EAAe,OAAO,KAAK,EAAE,OAAO,OAAO;AAE5D,aAAO,QAAQ,CAACA,WAAU;AACxB,gBAAQ,OAAO,MAAMA,MAAK;AAAA,MAC5B,CAAC;AAED,aAAO;AAAA,IACT;AAAA,IACA,IAAI,QAAQ;AAAA,EACd;AACF;;;ACpBO,SAAS,mBAAmB,MAAoC;AACrE,SAAO,KAAK,IAAI,CAAC,CAAC,MAAM,MAAM,MAAM;AAClC,WAAO,CAAC,MAAO,CAAC,EAAe,OAAO,MAAM,EAAE,KAAK,IAAI,CAAC;AAAA,EAC1D,CAAC;AACH;;;ACHO,SAAS,qBACd,eACmB;AACnB,SAAO;AAAA,IACL;AAAA,IACA,CAAC,SAAS,MAAM,UAAU;AACxB,cAAQ,IAAI,IAAK,CAAC,EAAe,OAAO,KAAK,EAAE,KAAK,IAAI;AACxD,aAAO;AAAA,IACT;AAAA,IACA,CAAC;AAAA,EACH;AACF", "sourcesContent": ["\"use strict\";\n\nvar defaultParseOptions = {\n  decodeValues: true,\n  map: false,\n  silent: false,\n};\n\nfunction isNonEmptyString(str) {\n  return typeof str === \"string\" && !!str.trim();\n}\n\nfunction parseString(setCookieValue, options) {\n  var parts = setCookieValue.split(\";\").filter(isNonEmptyString);\n\n  var nameValuePairStr = parts.shift();\n  var parsed = parseNameValuePair(nameValuePairStr);\n  var name = parsed.name;\n  var value = parsed.value;\n\n  options = options\n    ? Object.assign({}, defaultParseOptions, options)\n    : defaultParseOptions;\n\n  try {\n    value = options.decodeValues ? decodeURIComponent(value) : value; // decode cookie value\n  } catch (e) {\n    console.error(\n      \"set-cookie-parser encountered an error while decoding a cookie with value '\" +\n        value +\n        \"'. Set options.decodeValues to false to disable this feature.\",\n      e\n    );\n  }\n\n  var cookie = {\n    name: name,\n    value: value,\n  };\n\n  parts.forEach(function (part) {\n    var sides = part.split(\"=\");\n    var key = sides.shift().trimLeft().toLowerCase();\n    var value = sides.join(\"=\");\n    if (key === \"expires\") {\n      cookie.expires = new Date(value);\n    } else if (key === \"max-age\") {\n      cookie.maxAge = parseInt(value, 10);\n    } else if (key === \"secure\") {\n      cookie.secure = true;\n    } else if (key === \"httponly\") {\n      cookie.httpOnly = true;\n    } else if (key === \"samesite\") {\n      cookie.sameSite = value;\n    } else {\n      cookie[key] = value;\n    }\n  });\n\n  return cookie;\n}\n\nfunction parseNameValuePair(nameValuePairStr) {\n  // Parses name-value-pair according to rfc6265bis draft\n\n  var name = \"\";\n  var value = \"\";\n  var nameValueArr = nameValuePairStr.split(\"=\");\n  if (nameValueArr.length > 1) {\n    name = nameValueArr.shift();\n    value = nameValueArr.join(\"=\"); // everything after the first =, joined by a \"=\" if there was more than one part\n  } else {\n    value = nameValuePairStr;\n  }\n\n  return { name: name, value: value };\n}\n\nfunction parse(input, options) {\n  options = options\n    ? Object.assign({}, defaultParseOptions, options)\n    : defaultParseOptions;\n\n  if (!input) {\n    if (!options.map) {\n      return [];\n    } else {\n      return {};\n    }\n  }\n\n  if (input.headers) {\n    if (typeof input.headers.getSetCookie === \"function\") {\n      // for fetch responses - they combine headers of the same type in the headers array,\n      // but getSetCookie returns an uncombined array\n      input = input.headers.getSetCookie();\n    } else if (input.headers[\"set-cookie\"]) {\n      // fast-path for node.js (which automatically normalizes header names to lower-case\n      input = input.headers[\"set-cookie\"];\n    } else {\n      // slow-path for other environments - see #25\n      var sch =\n        input.headers[\n          Object.keys(input.headers).find(function (key) {\n            return key.toLowerCase() === \"set-cookie\";\n          })\n        ];\n      // warn if called on a request-like object with a cookie header rather than a set-cookie header - see #34, 36\n      if (!sch && input.headers.cookie && !options.silent) {\n        console.warn(\n          \"Warning: set-cookie-parser appears to have been called on a request object. It is designed to parse Set-Cookie headers from responses, not Cookie headers from requests. Set the option {silent: true} to suppress this warning.\"\n        );\n      }\n      input = sch;\n    }\n  }\n  if (!Array.isArray(input)) {\n    input = [input];\n  }\n\n  options = options\n    ? Object.assign({}, defaultParseOptions, options)\n    : defaultParseOptions;\n\n  if (!options.map) {\n    return input.filter(isNonEmptyString).map(function (str) {\n      return parseString(str, options);\n    });\n  } else {\n    var cookies = {};\n    return input.filter(isNonEmptyString).reduce(function (cookies, str) {\n      var cookie = parseString(str, options);\n      cookies[cookie.name] = cookie;\n      return cookies;\n    }, cookies);\n  }\n}\n\n/*\n  Set-Cookie header field-values are sometimes comma joined in one string. This splits them without choking on commas\n  that are within a single set-cookie field-value, such as in the Expires portion.\n\n  This is uncommon, but explicitly allowed - see https://tools.ietf.org/html/rfc2616#section-4.2\n  Node.js does this for every header *except* set-cookie - see https://github.com/nodejs/node/blob/d5e363b77ebaf1caf67cd7528224b651c86815c1/lib/_http_incoming.js#L128\n  React Native's fetch does this for *every* header, including set-cookie.\n\n  Based on: https://github.com/google/j2objc/commit/16820fdbc8f76ca0c33472810ce0cb03d20efe25\n  Credits to: https://github.com/tomball for original and https://github.com/chrusart for JavaScript implementation\n*/\nfunction splitCookiesString(cookiesString) {\n  if (Array.isArray(cookiesString)) {\n    return cookiesString;\n  }\n  if (typeof cookiesString !== \"string\") {\n    return [];\n  }\n\n  var cookiesStrings = [];\n  var pos = 0;\n  var start;\n  var ch;\n  var lastComma;\n  var nextStart;\n  var cookiesSeparatorFound;\n\n  function skipWhitespace() {\n    while (pos < cookiesString.length && /\\s/.test(cookiesString.charAt(pos))) {\n      pos += 1;\n    }\n    return pos < cookiesString.length;\n  }\n\n  function notSpecialChar() {\n    ch = cookiesString.charAt(pos);\n\n    return ch !== \"=\" && ch !== \";\" && ch !== \",\";\n  }\n\n  while (pos < cookiesString.length) {\n    start = pos;\n    cookiesSeparatorFound = false;\n\n    while (skipWhitespace()) {\n      ch = cookiesString.charAt(pos);\n      if (ch === \",\") {\n        // ',' is a cookie separator if we have later first '=', not ';' or ','\n        lastComma = pos;\n        pos += 1;\n\n        skipWhitespace();\n        nextStart = pos;\n\n        while (pos < cookiesString.length && notSpecialChar()) {\n          pos += 1;\n        }\n\n        // currently special character\n        if (pos < cookiesString.length && cookiesString.charAt(pos) === \"=\") {\n          // we found cookies separator\n          cookiesSeparatorFound = true;\n          // pos is inside the next cookie, so back up and return it.\n          pos = nextStart;\n          cookiesStrings.push(cookiesString.substring(start, lastComma));\n          start = pos;\n        } else {\n          // in param ',' or param separator ';',\n          // we continue from that comma\n          pos = lastComma + 1;\n        }\n      } else {\n        pos += 1;\n      }\n    }\n\n    if (!cookiesSeparatorFound || pos >= cookiesString.length) {\n      cookiesStrings.push(cookiesString.substring(start, cookiesString.length));\n    }\n  }\n\n  return cookiesStrings;\n}\n\nmodule.exports = parse;\nmodule.exports.parse = parse;\nmodule.exports.parseString = parseString;\nmodule.exports.splitCookiesString = splitCookiesString;\n", "export { Headers } from './Headers'\n\nexport { getRawHeaders } from './getRawHeaders'\nexport { headersToString } from './transformers/headersToString'\nexport { headersToList } from './transformers/headersToList'\nexport { headersToObject } from './transformers/headersToObject'\nexport { stringToHeaders } from './transformers/stringToHeaders'\nexport { listToHeaders } from './transformers/listToHeaders'\nexport { objectToHeaders } from './transformers/objectToHeaders'\n\nexport { reduceHeadersObject } from './transformers/reduceHeadersObject'\nexport { flattenHeadersList } from './transformers/flattenHeadersList'\nexport { flattenHeadersObject } from './transformers/flattenHeadersObject'\n\n/* Typings */\nexport {\n  HeadersList,\n  FlatHeadersList,\n  HeadersObject,\n  FlatHeadersObject,\n} from './glossary'\n", "import { splitCookiesString } from 'set-cookie-parser'\nimport { HeadersList, HeadersObject } from './glossary'\nimport { normalizeHeaderName } from './utils/normalizeHeaderName'\nimport { normalizeHeaderValue } from './utils/normalizeHeaderValue'\nimport { isValidHeaderName } from './utils/isValidHeaderName'\nimport { isValidHeaderValue } from './utils/isValidHeaderValue'\n\nexport const NORMALIZED_HEADERS: unique symbol = Symbol('normalizedHeaders')\n\nexport const RAW_HEADER_NAMES: unique symbol = Symbol('rawHeaderNames')\n\nconst HEADER_VALUE_DELIMITER = ', ' as const\n\nexport class Headers {\n  // Normalized header {\"name\":\"a, b\"} storage.\n  private [NORMALIZED_HEADERS]: Record<string, string> = {}\n\n  // Keeps the mapping between the raw header name\n  // and the normalized header name to ease the lookup.\n  private [RAW_HEADER_NAMES]: Map<string, string> = new Map()\n\n  constructor(init?: HeadersInit | HeadersObject | HeadersList) {\n    /**\n     * @note Cannot necessarily check if the `init` is an instance of the\n     * `Headers` because that class may not be defined in Node or jsdom.\n     */\n    if (\n      ['Headers', 'HeadersPolyfill'].includes(init?.constructor.name) ||\n      init instanceof Headers ||\n      (typeof globalThis.Headers !== 'undefined' &&\n        init instanceof globalThis.Headers)\n    ) {\n      const initialHeaders = init as Headers\n      initialHeaders.forEach((value, name) => {\n        this.append(name, value)\n      }, this)\n    } else if (Array.isArray(init)) {\n      init.forEach(([name, value]) => {\n        this.append(\n          name,\n          Array.isArray(value) ? value.join(HEADER_VALUE_DELIMITER) : value\n        )\n      })\n    } else if (init) {\n      Object.getOwnPropertyNames(init).forEach((name) => {\n        const value = init[name]\n        this.append(\n          name,\n          Array.isArray(value) ? value.join(HEADER_VALUE_DELIMITER) : value\n        )\n      })\n    }\n  }\n\n  [Symbol.toStringTag] = 'Headers';\n\n  [Symbol.iterator]() {\n    return this.entries()\n  }\n\n  *keys(): IterableIterator<string> {\n    for (const [name] of this.entries()) {\n      yield name\n    }\n  }\n\n  *values(): IterableIterator<string> {\n    for (const [, value] of this.entries()) {\n      yield value\n    }\n  }\n\n  *entries(): IterableIterator<[string, string]> {\n    // https://fetch.spec.whatwg.org/#concept-header-list-sort-and-combine\n    let sortedKeys = Object.keys(this[NORMALIZED_HEADERS]).sort((a, b) =>\n      a.localeCompare(b)\n    )\n    for (const name of sortedKeys) {\n      if (name === 'set-cookie') {\n        for (const value of this.getSetCookie()) {\n          yield [name, value]\n        }\n      } else {\n        yield [name, this.get(name)]\n      }\n    }\n  }\n\n  /**\n   * Returns a boolean stating whether a `Headers` object contains a certain header.\n   */\n  has(name: string): boolean {\n    if (!isValidHeaderName(name)) {\n      throw new TypeError(`Invalid header name \"${name}\"`)\n    }\n\n    return this[NORMALIZED_HEADERS].hasOwnProperty(normalizeHeaderName(name))\n  }\n\n  /**\n   * Returns a `ByteString` sequence of all the values of a header with a given name.\n   */\n  get(name: string): string | null {\n    if (!isValidHeaderName(name)) {\n      throw TypeError(`Invalid header name \"${name}\"`)\n    }\n\n    return this[NORMALIZED_HEADERS][normalizeHeaderName(name)] ?? null\n  }\n\n  /**\n   * Sets a new value for an existing header inside a `Headers` object, or adds the header if it does not already exist.\n   */\n  set(name: string, value: string): void {\n    if (!isValidHeaderName(name) || !isValidHeaderValue(value)) {\n      return\n    }\n\n    const normalizedName = normalizeHeaderName(name)\n    const normalizedValue = normalizeHeaderValue(value)\n\n    this[NORMALIZED_HEADERS][normalizedName] =\n      normalizeHeaderValue(normalizedValue)\n    this[RAW_HEADER_NAMES].set(normalizedName, name)\n  }\n\n  /**\n   * Appends a new value onto an existing header inside a `Headers` object, or adds the header if it does not already exist.\n   */\n  append(name: string, value: string): void {\n    if (!isValidHeaderName(name) || !isValidHeaderValue(value)) {\n      return\n    }\n\n    const normalizedName = normalizeHeaderName(name)\n    const normalizedValue = normalizeHeaderValue(value)\n\n    let resolvedValue = this.has(normalizedName)\n      ? `${this.get(normalizedName)}, ${normalizedValue}`\n      : normalizedValue\n\n    this.set(name, resolvedValue)\n  }\n\n  /**\n   * Deletes a header from the `Headers` object.\n   */\n  delete(name: string): void {\n    if (!isValidHeaderName(name)) {\n      return\n    }\n\n    if (!this.has(name)) {\n      return\n    }\n\n    const normalizedName = normalizeHeaderName(name)\n    delete this[NORMALIZED_HEADERS][normalizedName]\n    this[RAW_HEADER_NAMES].delete(normalizedName)\n  }\n\n  /**\n   * Traverses the `Headers` object,\n   * calling the given callback for each header.\n   */\n  forEach<ThisArg = this>(\n    callback: (\n      this: ThisArg,\n      value: string,\n      name: string,\n      parent: this\n    ) => void,\n    thisArg?: ThisArg\n  ) {\n    for (const [name, value] of this.entries()) {\n      callback.call(thisArg, value, name, this)\n    }\n  }\n\n  /**\n   * Returns an array containing the values\n   * of all Set-Cookie headers associated\n   * with a response\n   */\n  getSetCookie(): string[] {\n    const setCookieHeader = this.get('set-cookie')\n\n    if (setCookieHeader === null) {\n      return []\n    }\n\n    if (setCookieHeader === '') {\n      return ['']\n    }\n\n    return splitCookiesString(setCookieHeader)\n  }\n}\n", "const HEADERS_INVALID_CHARACTERS = /[^a-z0-9\\-#$%&'*+.^_`|~]/i\n\nexport function normalizeHeaderName(name: string): string {\n  if (HEADERS_INVALID_CHARACTERS.test(name) || name.trim() === '') {\n    throw new TypeError('Invalid character in header field name')\n  }\n\n  return name.trim().toLowerCase()\n}\n", "const charCodesToRemove = [\n  String.fromCharCode(0x0a),\n  String.fromCharCode(0x0d),\n  String.fromCharCode(0x09),\n  String.fromCharCode(0x20),\n]\n\nconst HEADER_VALUE_REMOVE_REGEXP = new RegExp(\n  `(^[${charCodesToRemove.join('')}]|$[${charCodesToRemove.join('')}])`,\n  'g'\n)\n\n/**\n * Normalize the given header value.\n * @see https://fetch.spec.whatwg.org/#concept-header-value-normalize\n */\nexport function normalizeHeaderValue(value: string): string {\n  const nextValue = value.replace(HEADER_VALUE_REMOVE_REGEXP, '')\n  return nextValue\n}\n", "/**\n * Validate the given header name.\n * @see https://fetch.spec.whatwg.org/#header-name\n */\nexport function isValidHeaderName(value: unknown) {\n  if (typeof value !== 'string') {\n    return false\n  }\n\n  if (value.length === 0) {\n    return false\n  }\n\n  for (let i = 0; i < value.length; i++) {\n    const character = value.charCodeAt(i)\n\n    if (character > 0x7f || !isToken(character)) {\n      return false\n    }\n  }\n\n  return true\n}\n\nfunction isToken(value: string | number): boolean {\n  return ![\n    0x7f,\n    0x20,\n    '(',\n    ')',\n    '<',\n    '>',\n    '@',\n    ',',\n    ';',\n    ':',\n    '\\\\',\n    '\"',\n    '/',\n    '[',\n    ']',\n    '?',\n    '=',\n    '{',\n    '}',\n  ].includes(value)\n}\n", "/**\n * Validate the given header value.\n * @see https://fetch.spec.whatwg.org/#header-value\n */\nexport function isValidHeaderValue(value: unknown): boolean {\n  if (typeof value !== 'string') {\n    return false\n  }\n\n  if (value.trim() !== value) {\n    return false\n  }\n\n  for (let i = 0; i < value.length; i++) {\n    const character = value.charCodeAt(i)\n\n    if (\n      // NUL.\n      character === 0x00 ||\n      // HTTP newline bytes.\n      character === 0x0a ||\n      character === 0x0d\n    ) {\n      return false\n    }\n  }\n\n  return true\n}\n", "import { RAW_HEADER_NAMES } from './Headers'\n\n/**\n * Returns the object of all raw headers.\n */\nexport function getRawHeaders(headers: Headers) {\n  const rawHeaders: Record<string, string> = {}\n\n  for (const [name, value] of headers.entries()) {\n    rawHeaders[headers[RAW_HEADER_NAMES].get(name)] = value\n  }\n\n  return rawHeaders\n}\n", "import { HeadersList } from '../glossary'\n\nexport function headersToList(headers: Headers): HeadersList {\n  const headersList: HeadersList = []\n\n  headers.forEach((value, name) => {\n    const resolvedValue = value.includes(',')\n      ? value.split(',').map((value) => value.trim())\n      : value\n\n    headersList.push([name, resolvedValue])\n  })\n\n  return headersList\n}\n", "import { headersToList } from './headersToList'\n\n/**\n * Converts a given `Headers` instance to its string representation.\n */\nexport function headersToString(headers: Headers): string {\n  const list = headersToList(headers)\n  const lines = list.map(([name, value]) => {\n    const values = ([] as string[]).concat(value)\n    return `${name}: ${values.join(', ')}`\n  })\n\n  return lines.join('\\r\\n')\n}\n", "import { HeadersObject } from '../glossary'\n\n// List of headers that cannot have multiple values,\n// while potentially having a comma in their single value.\nconst singleValueHeaders = ['user-agent']\n\n/**\n * Converts a given `Headers` instance into a plain object.\n * Respects headers with multiple values.\n */\nexport function headersToObject(headers: Headers): HeadersObject {\n  const headersObject: HeadersObject = {}\n\n  headers.forEach((value, name) => {\n    const isMultiValue =\n      !singleValueHeaders.includes(name.toLowerCase()) && value.includes(',')\n    headersObject[name] = isMultiValue\n      ? value.split(',').map((s) => s.trim())\n      : value\n  })\n\n  return headersObject\n}\n", "import { Headers } from '../Headers'\n\n/**\n * Converts a string representation of headers (i.e. from XMLHttpRequest)\n * to a new `Headers` instance.\n */\nexport function stringToHeaders(str: string): Headers {\n  const lines = str.trim().split(/[\\r\\n]+/)\n\n  return lines.reduce((headers, line) => {\n    if (line.trim() === '') {\n      return headers\n    }\n\n    const parts = line.split(': ')\n    const name = parts.shift()\n    const value = parts.join(': ')\n    headers.append(name, value)\n\n    return headers\n  }, new Headers())\n}\n", "import { Headers } from '../Headers'\nimport { HeadersList } from '../glossary'\n\nexport function listToHeaders(list: HeadersList): Headers {\n  const headers = new Headers()\n\n  list.forEach(([name, value]) => {\n    const values = ([] as string[]).concat(value)\n\n    values.forEach((value) => {\n      headers.append(name, value)\n    })\n  })\n\n  return headers\n}\n", "import { HeadersObject } from '../glossary'\n\n/**\n * Reduces given headers object instnace.\n */\nexport function reduceHeadersObject<R>(\n  headers: HeadersObject,\n  reducer: (headers: R, name: string, value: string | string[]) => R,\n  initialState: R\n): R {\n  return Object.keys(headers).reduce<R>((nextHeaders, name) => {\n    return reducer(nextHeaders, name, headers[name])\n  }, initialState)\n}\n", "import { Headers } from '../Headers'\nimport { reduceHeadersObject } from './reduceHeadersObject'\n\n/**\n * Converts a given headers object to a new `Headers` instance.\n */\nexport function objectToHeaders(\n  headersObject: Record<string, string | string[] | undefined>\n): Headers {\n  return reduceHeadersObject(\n    headersObject,\n    (headers, name, value) => {\n      const values = ([] as string[]).concat(value).filter(Boolean)\n\n      values.forEach((value) => {\n        headers.append(name, value)\n      })\n\n      return headers\n    },\n    new Headers()\n  )\n}\n", "import { HeadersList, FlatHeadersList } from '../glossary'\n\nexport function flattenHeadersList(list: HeadersList): FlatHeadersList {\n  return list.map(([name, values]) => {\n    return [name, ([] as string[]).concat(values).join(', ')]\n  })\n}\n", "import { HeadersObject, FlatHeadersObject } from '../glossary'\nimport { reduceHeadersObject } from './reduceHeadersObject'\n\nexport function flattenHeadersObject(\n  headersObject: HeadersObject\n): FlatHeadersObject {\n  return reduceHeadersObject<FlatHeadersObject>(\n    headersObject,\n    (headers, name, value) => {\n      headers[name] = ([] as string[]).concat(value).join(', ')\n      return headers\n    },\n    {}\n  )\n}\n"]}