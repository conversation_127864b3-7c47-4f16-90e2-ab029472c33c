{"name": "headers-polyfill", "version": "4.0.3", "description": "A native \"Headers\" class polyfill.", "main": "./lib/index.js", "module": "./lib/index.mjs", "types": "./lib/index.d.ts", "exports": {".": {"types": "./lib/index.d.ts", "require": "./lib/index.js", "default": "./lib/index.mjs"}}, "repository": "https://github.com/mswjs/headers-polyfill", "author": "<PERSON><PERSON>", "license": "MIT", "scripts": {"start": "tsup --watch", "clean": "<PERSON><PERSON><PERSON> lib", "build": "yarn clean && tsup", "test": "jest", "release": "release publish", "prepublishOnly": "yarn test && yarn build"}, "files": ["lib", "README.md"], "devDependencies": {"@ossjs/release": "^0.7.3", "@swc/core": "^1.3.84", "@swc/jest": "^0.2.29", "@types/jest": "^28.1.4", "@types/set-cookie-parser": "^2.4.3", "jest": "^28.1.2", "jest-environment-jsdom": "^28.1.2", "rimraf": "^3.0.2", "set-cookie-parser": "^2.6.0", "tsup": "^7.2.0", "typescript": "^5.2.2"}}