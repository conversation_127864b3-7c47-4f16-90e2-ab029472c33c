{"version": 3, "sources": ["../src/index.ts"], "sourcesContent": ["/**\n * Determines if the current process is a Node.js process.\n */\nexport function isNodeProcess(): boolean {\n  if (typeof navigator !== 'undefined' && navigator.product === 'ReactNative') {\n    return true\n  }\n\n  if (typeof process !== 'undefined') {\n    // Electron (https://www.electronjs.org/docs/latest/api/process#processtype-readonly)\n    const type = (process as any).type\n    if (type === 'renderer' || type === 'worker') {\n      return false\n    }\n\n\n    return !!(\n      process.versions &&\n      process.versions.node\n    )\n  }\n\n  return false\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAGO,SAAS,gBAAyB;AACvC,MAAI,OAAO,cAAc,eAAe,UAAU,YAAY,eAAe;AAC3E,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,YAAY,aAAa;AAElC,UAAM,OAAQ,QAAgB;AAC9B,QAAI,SAAS,cAAc,SAAS,UAAU;AAC5C,aAAO;AAAA,IACT;AAGA,WAAO,CAAC,EACN,QAAQ,YACR,QAAQ,SAAS;AAAA,EAErB;AAEA,SAAO;AACT;", "names": []}