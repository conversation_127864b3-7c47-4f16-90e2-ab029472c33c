{"version": 3, "sources": ["../src/format.ts", "../src/invariant.ts"], "sourcesContent": ["const POSITIONALS_EXP = /(%?)(%([sdijo]))/g\n\nfunction serializePositional(positional: any, flag: string): any {\n  switch (flag) {\n    // Strings.\n    case 's':\n      return positional\n\n    // Digits.\n    case 'd':\n    case 'i':\n      return Number(positional)\n\n    // JSON.\n    case 'j':\n      return JSON.stringify(positional)\n\n    // Objects.\n    case 'o': {\n      // Preserve stings to prevent extra quotes around them.\n      if (typeof positional === 'string') {\n        return positional\n      }\n\n      const json = JSON.stringify(positional)\n\n      // If the positional isn't serializable, return it as-is.\n      if (json === '{}' || json === '[]' || /^\\[object .+?\\]$/.test(json)) {\n        return positional\n      }\n\n      return json\n    }\n  }\n}\n\nexport function format(message: string, ...positionals: any[]): string {\n  if (positionals.length === 0) {\n    return message\n  }\n\n  let positionalIndex = 0\n  let formattedMessage = message.replace(\n    POSITIONALS_EXP,\n    (match, isEscaped, _, flag) => {\n      const positional = positionals[positionalIndex]\n      const value = serializePositional(positional, flag)\n\n      if (!isEscaped) {\n        positionalIndex++\n        return value\n      }\n\n      return match\n    }\n  )\n\n  // Append unresolved positionals to string as-is.\n  if (positionalIndex < positionals.length) {\n    formattedMessage += ` ${positionals.slice(positionalIndex).join(' ')}`\n  }\n\n  formattedMessage = formattedMessage.replace(/%{2,2}/g, '%')\n\n  return formattedMessage\n}\n", "import { format } from './format'\n\nconst STACK_FRAMES_TO_IGNORE = 2\n\n/**\n * Remove the \"outvariant\" package trace from the given error.\n * This scopes down the error stack to the relevant parts\n * when used in other applications.\n */\nfunction cleanErrorStack(error: Error): void {\n  if (!error.stack) {\n    return\n  }\n\n  const nextStack = error.stack.split('\\n')\n  nextStack.splice(1, STACK_FRAMES_TO_IGNORE)\n  error.stack = nextStack.join('\\n')\n}\n\nexport class InvariantError extends Error {\n  name = 'Invariant Violation'\n\n  constructor(public readonly message: string, ...positionals: any[]) {\n    super(message)\n    this.message = format(message, ...positionals)\n    cleanErrorStack(this)\n  }\n}\n\nexport interface CustomErrorConstructor {\n  new (message: string): Error\n}\n\nexport interface CustomErrorFactory {\n  (message: string): Error\n}\n\nexport type CustomError = CustomErrorConstructor | CustomErrorFactory\n\ntype Invariant = {\n  (\n    predicate: unknown,\n    message: string,\n    ...positionals: any[]\n  ): asserts predicate\n\n  as(\n    ErrorConstructor: CustomError,\n    predicate: unknown,\n    message: string,\n    ...positionals: unknown[]\n  ): asserts predicate\n}\n\nexport const invariant: Invariant = (\n  predicate,\n  message,\n  ...positionals\n): asserts predicate => {\n  if (!predicate) {\n    throw new InvariantError(message, ...positionals)\n  }\n}\n\ninvariant.as = (ErrorConstructor, predicate, message, ...positionals) => {\n  if (!predicate) {\n    const formatMessage =\n      positionals.length === 0 ? message : format(message, ...positionals)\n    let error: Error\n\n    try {\n      error = Reflect.construct(ErrorConstructor as CustomErrorConstructor, [\n        formatMessage,\n      ])\n    } catch (err) {\n      error = (ErrorConstructor as CustomErrorFactory)(formatMessage)\n    }\n\n    throw error\n  }\n}\n"], "mappings": ";AAAA,IAAM,kBAAkB;AAExB,SAAS,oBAAoB,YAAiB,MAAmB;AAC/D,UAAQ,MAAM;AAAA,IAEZ,KAAK;AACH,aAAO;AAAA,IAGT,KAAK;AAAA,IACL,KAAK;AACH,aAAO,OAAO,UAAU;AAAA,IAG1B,KAAK;AACH,aAAO,KAAK,UAAU,UAAU;AAAA,IAGlC,KAAK,KAAK;AAER,UAAI,OAAO,eAAe,UAAU;AAClC,eAAO;AAAA,MACT;AAEA,YAAM,OAAO,KAAK,UAAU,UAAU;AAGtC,UAAI,SAAS,QAAQ,SAAS,QAAQ,mBAAmB,KAAK,IAAI,GAAG;AACnE,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAEO,SAAS,OAAO,YAAoB,aAA4B;AACrE,MAAI,YAAY,WAAW,GAAG;AAC5B,WAAO;AAAA,EACT;AAEA,MAAI,kBAAkB;AACtB,MAAI,mBAAmB,QAAQ;AAAA,IAC7B;AAAA,IACA,CAAC,OAAO,WAAW,GAAG,SAAS;AAC7B,YAAM,aAAa,YAAY;AAC/B,YAAM,QAAQ,oBAAoB,YAAY,IAAI;AAElD,UAAI,CAAC,WAAW;AACd;AACA,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT;AAAA,EACF;AAGA,MAAI,kBAAkB,YAAY,QAAQ;AACxC,wBAAoB,IAAI,YAAY,MAAM,eAAe,EAAE,KAAK,GAAG;AAAA,EACrE;AAEA,qBAAmB,iBAAiB,QAAQ,WAAW,GAAG;AAE1D,SAAO;AACT;;;AC/DA,IAAM,yBAAyB;AAO/B,SAAS,gBAAgB,OAAoB;AAC3C,MAAI,CAAC,MAAM,OAAO;AAChB;AAAA,EACF;AAEA,QAAM,YAAY,MAAM,MAAM,MAAM,IAAI;AACxC,YAAU,OAAO,GAAG,sBAAsB;AAC1C,QAAM,QAAQ,UAAU,KAAK,IAAI;AACnC;AAEO,IAAM,iBAAN,cAA6B,MAAM;AAAA,EAGxC,YAA4B,YAAoB,aAAoB;AAClE,UAAM,OAAO;AADa;AAF5B,gBAAO;AAIL,SAAK,UAAU,OAAO,SAAS,GAAG,WAAW;AAC7C,oBAAgB,IAAI;AAAA,EACtB;AACF;AA2BO,IAAM,YAAuB,CAClC,WACA,YACG,gBACmB;AACtB,MAAI,CAAC,WAAW;AACd,UAAM,IAAI,eAAe,SAAS,GAAG,WAAW;AAAA,EAClD;AACF;AAEA,UAAU,KAAK,CAAC,kBAAkB,WAAW,YAAY,gBAAgB;AACvE,MAAI,CAAC,WAAW;AACd,UAAM,gBACJ,YAAY,WAAW,IAAI,UAAU,OAAO,SAAS,GAAG,WAAW;AACrE,QAAI;AAEJ,QAAI;AACF,cAAQ,QAAQ,UAAU,kBAA4C;AAAA,QACpE;AAAA,MACF,CAAC;AAAA,IACH,SAAS,KAAP;AACA,cAAS,iBAAwC,aAAa;AAAA,IAChE;AAEA,UAAM;AAAA,EACR;AACF;", "names": []}