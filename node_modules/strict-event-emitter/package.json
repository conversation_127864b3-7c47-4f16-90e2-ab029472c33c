{"name": "strict-event-emitter", "version": "0.5.1", "description": "Type-safe implementation of EventEmitter for browser and Node.js", "main": "lib/index.js", "module": "lib/index.mjs", "typings": "lib/index.d.ts", "exports": {".": {"types": "./lib/index.d.ts", "require": "./lib/index.js", "default": "./lib/index.mjs"}}, "repository": "**************:open-draft/strict-event-emitter.git", "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "files": ["lib"], "devDependencies": {"@ossjs/release": "^0.8.0", "@types/events": "^3.0.0", "@types/jest": "^29.2.4", "jest": "^29.3.1", "jest-extended": "^3.2.0", "rimraf": "^3.0.2", "ts-jest": "^29.0.3", "ts-node": "^9.1.1", "tsup": "^6.2.3", "typescript": "4.8"}, "scripts": {"dev": "tsc -w", "test": "jest", "test:ts": "tsc -p test/typings.tsconfig.json", "clean": "rimraf ./lib", "build": "pnpm clean && tsup", "release": "release publish"}}