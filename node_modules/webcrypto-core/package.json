{"name": "webcrypto-core", "version": "1.8.1", "description": "Common layer to be used by crypto libraries based on WebCrypto API for input validation.", "main": "build/webcrypto-core.js", "module": "build/webcrypto-core.es.js", "types": "build/index.d.ts", "files": ["build", "README.md", "LICENSE"], "scripts": {"test": "mocha", "build": "rollup -c", "clear": "rimraf build/*", "rebuild": "npm run clear && npm run build", "lint": "eslint . --ext .ts", "lint:fix": "eslint --fix . --ext .ts", "coverage": "nyc npm test", "precoveragehtml": "npm run coverage", "coveragehtml": "nyc report -r html", "predev": "if [ ! -f coverage/index.html ]; then mkdir coverage; cp .waiting.html coverage/index.html; fi", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/PeculiarVentures/webcrypto-core.git"}, "keywords": ["webcrypto", "crypto", "polyfill", "aes", "rsa", "sha", "ec", "shake"], "dependencies": {"@peculiar/asn1-schema": "^2.3.13", "@peculiar/json-schema": "^1.1.12", "asn1js": "^3.0.5", "pvtsutils": "^1.3.5", "tslib": "^2.7.0"}, "devDependencies": {"@types/mocha": "^10.0.9", "@types/node": "^22.7.5", "@typescript-eslint/eslint-plugin": "^7.11.0", "@typescript-eslint/parser": "^7.11.0", "eslint": "^8.57.0", "mocha": "^10.7.3", "nyc": "^17.1.0", "rimraf": "^6.0.1", "rollup": "^4.24.0", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-typescript2": "^0.36.0", "ts-node": "^10.9.2", "typescript": "^5.6.2"}, "author": "PeculiarVentures", "license": "MIT", "bugs": {"url": "https://github.com/PeculiarVentures/webcrypto-core/issues"}, "homepage": "https://github.com/PeculiarVentures/webcrypto-core#readme", "nyc": {"extension": [".ts", ".tsx"], "include": ["src/**/*.ts"], "exclude": ["**/*.d.ts"], "reporter": ["lcov", "text-summary"]}, "mocha": {"require": "ts-node/register", "extension": ["ts"], "spec": ["test/**/*.ts"]}}