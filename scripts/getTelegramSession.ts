import { TelegramClient } from "telegram";
import { StringSession } from "telegram/sessions";
import * as readline from "readline";
import * as dotenv from 'dotenv';
import * as path from 'path';

// Load environment variables
const envPath = path.resolve(__dirname, '../.env');
dotenv.config({ path: envPath });

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

function question(query: string): Promise<string> {
  return new Promise((resolve) => {
    rl.question(query, resolve);
  });
}

async function generateTelegramSession() {
  try {
    console.log('🚀 Telegram Session Generator');
    console.log('============================\n');

    // Get API credentials from environment
    const apiId = Number(process.env.TELEGRAM_API_ID);
    const apiHash = process.env.TELEGRAM_API_HASH;

    if (!apiId || !apiHash) {
      console.error('❌ Error: TELEGRAM_API_ID and TELEGRAM_API_HASH must be set in .env file');
      console.log('\nTo get these credentials:');
      console.log('1. Go to https://my.telegram.org/apps');
      console.log('2. Create a new application');
      console.log('3. Add the API ID and API Hash to your .env file');
      process.exit(1);
    }

    console.log('✅ API credentials found');
    console.log(`📱 API ID: ${apiId}`);
    console.log(`🔑 API Hash: ${apiHash.substring(0, 8)}...`);
    console.log('');

    // Create client with empty session
    const stringSession = new StringSession("");
    const client = new TelegramClient(stringSession, apiId, apiHash, {
      connectionRetries: 5,
    });

    console.log('🔗 Connecting to Telegram...');
    await client.connect();

    if (!client.connected) {
      throw new Error("Failed to connect to Telegram");
    }

    console.log('✅ Connected to Telegram successfully');

    // Get phone number
    const phoneNumber = await question('📞 Enter your phone number (with country code, e.g., +1234567890): ');
    
    if (!phoneNumber.startsWith('+')) {
      console.error('❌ Phone number must start with + and include country code');
      process.exit(1);
    }

    console.log('📨 Sending verification code...');

    // Start authentication process
    await client.start({
      phoneNumber: async () => phoneNumber,
      password: async () => {
        const password = await question('🔑 Enter your 2FA password (if enabled): ');
        return password;
      },
      phoneCode: async () => {
        const code = await question('🔢 Enter the verification code you received: ');
        return code;
      },
      onError: (err) => {
        console.error('Authentication error:', err);
      },
    });

    console.log('🔐 Authentication completed!');

    // Get session string
    const sessionString = client.session.save() as unknown as string;

    console.log('\n🎉 Successfully authenticated!');
    console.log('📋 Your session string:');
    console.log('=' .repeat(50));
    console.log(sessionString);
    console.log('=' .repeat(50));
    console.log('\n📝 Add this to your .env file as:');
    console.log(`TELEGRAM_SESSION_STRING="${sessionString}"`);
    console.log('\n⚠️  Keep this session string secure and never share it!');

    // Get user info
    const me = await client.getMe();
    console.log(`\n👤 Logged in as: ${me.firstName} ${me.lastName || ''} (@${me.username || 'no username'})`);

    await client.disconnect();
    console.log('\n✅ Session generation completed successfully!');

  } catch (error: any) {
    console.error('\n❌ Error generating session:', error.message);
    
    if (error.message.includes('PHONE_CODE_INVALID')) {
      console.log('💡 The verification code you entered is invalid. Please try again.');
    } else if (error.message.includes('PHONE_NUMBER_INVALID')) {
      console.log('💡 The phone number format is invalid. Make sure to include the country code.');
    } else if (error.message.includes('PASSWORD_HASH_INVALID')) {
      console.log('💡 The 2FA password you entered is incorrect.');
    } else if (error.message.includes('FLOOD_WAIT')) {
      console.log('💡 Too many attempts. Please wait before trying again.');
    }
    
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Run the script
if (require.main === module) {
  generateTelegramSession().catch((error) => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

export { generateTelegramSession };
