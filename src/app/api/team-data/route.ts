export const dynamic = 'force-dynamic';

import { NextResponse } from 'next/server';
import { google } from 'googleapis';
import { getGoogleSheetsConfig, isGoogleSheetsConfigured } from '@/lib/env';
import { GoogleSheetsError, createErrorResponse, withErrorHandling, logError } from '@/lib/errors';
import type { TeamDataResponse } from '@/types/team';

async function getTeamDataHandler(): Promise<NextResponse> {
  // Check if Google Sheets is configured
  if (!isGoogleSheetsConfigured) {
    console.warn('⚠️ Google Sheets not configured, returning empty data');
    return NextResponse.json<TeamDataResponse>({
      salesReps: [],
      outreachMembers: [],
      warning: 'Google Sheets integration not configured'
    });
  }

  try {
    console.log('📊 Fetching team data from Google Sheets...');

    const { clientEmail, privateKey, spreadsheetId } = getGoogleSheetsConfig();

    // Clean up the private key (handle escaped newlines)
    const cleanPrivateKey = privateKey.replace(/\\n/g, '\n');
    const cleanClientEmail = clientEmail.replace(/\\n/g, '\n');

    const jwtClient = new google.auth.JWT({
      email: cleanClientEmail.replace(/"/g, '').trim(),
      key: cleanPrivateKey.replace(/"/g, '').trim(),
      scopes: ['https://www.googleapis.com/auth/spreadsheets.readonly'],
    });

    const sheets = google.sheets({ version: 'v4', auth: jwtClient });
    const cleanSpreadsheetId = spreadsheetId.replace(/"/g, '').trim();

    const firstSheetName = "Partnership Representatives".trim();
    const secondSheetName = "Outreach Consultants".trim();

    // Read first sheet (sales reps)
    let salesRepsResponse;
    try {
      console.log(`📋 Fetching ${firstSheetName} sheet...`);
      salesRepsResponse = await sheets.spreadsheets.values.get({
        spreadsheetId: cleanSpreadsheetId,
        range: firstSheetName,
      });
    } catch (error: any) {
      console.error(`❌ Error fetching ${firstSheetName} sheet:`, error.message);
      throw GoogleSheetsError.fromGoogleApiError(error, firstSheetName);
    }
    
    console.log('Raw Partnership Representatives data:', salesRepsResponse.data);

    const salesRepsRows = salesRepsResponse.data.values || [];
    const salesRepsData = salesRepsRows.slice(1).map((row) => ({
      name: row[0] || '',
      emoji: row[1] || '',
      username: row[2] || '',
      paUsername: row[3] || '',
      calendarLink: row[4] || '',
      tier: row[5] || '', // Changed from 'category' to 'tier' to match SalesRep interface
    }));

    // Read second sheet (outreach members)
    let outreachResponse;
    try {
      console.log(`📋 Fetching ${secondSheetName} sheet...`);
      outreachResponse = await sheets.spreadsheets.values.get({
        spreadsheetId: cleanSpreadsheetId,
        range: secondSheetName,
      });
    } catch (error: any) {
      console.error(`❌ Error fetching ${secondSheetName} sheet:`, error.message);
      throw GoogleSheetsError.fromGoogleApiError(error, secondSheetName);
    }
    
    console.log('Raw Outreach Consultants data:', outreachResponse.data);

    const outreachRows = outreachResponse.data.values || [];
    const outreachHeaders = outreachRows[0] || [];
    const outreachData = outreachRows.slice(1).map((row) => {
      const obj: any = {};
      outreachHeaders.forEach((header: string, idx: number) => {
        obj[header.trim().toLowerCase()] = row[idx] || '';
      });

      // Parse telegram usernames from columns 4, 5, and 6, filtering out empty or whitespace-only strings
      const telegramUsernames = [row[4], row[5], row[6]]
        .map((s) => (s || '').trim())
        .filter((s) => s);

      return {
        name: obj['name'] || '',
        emoji: obj['emoji'] || '',
        telegramUsernames,
      };
    });

    console.log('✅ Successfully processed team data');
    console.log(`📊 Found ${salesRepsData.length} sales reps and ${outreachData.length} outreach members`);

    return NextResponse.json<TeamDataResponse>({
      salesReps: salesRepsData,
      outreachMembers: outreachData,
    });

  } catch (error: any) {
    console.error('❌ Error fetching team data:', error);

    const errorResponse = createErrorResponse(error, {
      operation: 'fetch_team_data',
      spreadsheetId: 'unknown'
    });

    return NextResponse.json(errorResponse, {
      status: error.statusCode || 500
    });
  }
}

export const GET = withErrorHandling(getTeamDataHandler);