"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { getGroupName, findSalesRepByUsername, findOutreachMemberByName } from "@/types/team"
import type { FormData, SalesRep, OutreachMember, GroupCreationResult } from "@/types/team"

interface GroupPreviewProps {
  formData: FormData;
  setFormData: (data: FormData) => void;
  salesReps: SalesRep[];
  outreachMembers: OutreachMember[];
  creationResult: GroupCreationResult | null;
}

export function GroupPreview({ 
  formData, 
  setFormData, 
  salesReps, 
  outreachMembers, 
  creationResult 
}: GroupPreviewProps) {
  const selectedSalesRep = findSalesRepByUsername(salesReps, formData.salesperson);
  const selectedOutreachMember = findOutreachMemberByName(outreachMembers, formData.outreachMemberName);

  // Get the appropriate outreach member username
  const outreachUsername = formData.useCustomOutreachUsername 
    ? formData.customOutreachUsername 
    : formData.outreachMemberUsername;

  // Get the appropriate sales rep username
  const salesRepUsername = formData.enterSalesRepManually
    ? formData.customSalesRepUsername
    : (formData.useCustomSalesRepUsername && selectedSalesRep
      ? formData.customSalesRepUsername
      : selectedSalesRep?.username);

  // Get the appropriate calendly link
  let calendlyLink: string | undefined;
  if (formData.enterSalesRepManually) {
    calendlyLink = formData.customCalendlyLink || undefined;
  } else if (selectedSalesRep) {
    calendlyLink = selectedSalesRep.calendarLink;
  }

  const projectLeadsArray = formData.projectLeads
    .map(s => s.trim())
    .filter(s => s.length > 0);

  const groupName = getGroupName(formData.projectName, selectedSalesRep);

  const welcomeMessage = `👥 Project Lead: ${projectLeadsArray.join(", ")}
👨‍💼 Partnership Representative: ${salesRepUsername}
👨‍💻 BDR: ${outreachUsername}${formData.includeCalendly && calendlyLink ? `
📅 Schedule a meeting: ${calendlyLink}` : ''}`;

  return (
    <div className="space-y-6">
      {/* Calendly Settings */}
      <div className="space-y-2">
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="includeCalendly"
            checked={formData.includeCalendly}
            onChange={(e) =>
              setFormData({
                ...formData,
                includeCalendly: e.target.checked
              })
            }
            className="rounded"
          />
          <Label htmlFor="includeCalendly" className="text-sm">
            Include Calendly scheduling message
          </Label>
        </div>
        <p className="text-sm text-gray-500">
          When enabled, a Calendly link will be included in the group for easy meeting scheduling.
        </p>
      </div>

      {/* Group Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Group Preview</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {formData.projectName && (
            <div>
              <p className="font-semibold text-sm">Group Name:</p>
              <p className="text-sm bg-gray-100 p-2 rounded">{groupName}</p>
            </div>
          )}

          <div>
            <p className="font-semibold text-sm">Welcome Message:</p>
            <div className="text-sm bg-gray-100 p-3 rounded whitespace-pre-line">
              {welcomeMessage}
            </div>
          </div>

          <div>
            <p className="font-semibold text-sm">Members to be added:</p>
            <div className="text-sm space-y-1">
              <p><strong>Admins:</strong></p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                {projectLeadsArray.map((lead, idx) => (
                  <li key={idx}>{lead} (Project Lead)</li>
                ))}
                {outreachUsername && <li>{outreachUsername} (BDR)</li>}
                <li>@IbcAdmin_Bot (Bot Admin)</li>
              </ul>
              
              <p className="mt-2"><strong>Regular Members:</strong></p>
              <ul className="list-disc list-inside ml-4">
                {salesRepUsername && <li>{salesRepUsername} (Partnership Representative)</li>}
                {selectedSalesRep?.paUsername && (
                  <li>{selectedSalesRep.paUsername} (PA)</li>
                )}
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Creation Result */}
      {creationResult && (
        <Card className={creationResult.success ? "border-green-500" : "border-red-500"}>
          <CardHeader>
            <CardTitle className={`text-lg ${creationResult.success ? "text-green-700" : "text-red-700"}`}>
              {creationResult.success ? "✅ Group Created Successfully!" : "❌ Group Creation Failed"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {creationResult.success ? (
              <div className="space-y-2">
                <p className="text-sm text-green-700">
                  Your Telegram group has been created successfully!
                </p>
                {creationResult.failed_invites && creationResult.failed_invites.length > 0 && (
                  <div className="mt-4">
                    <p className="font-semibold text-sm text-orange-700">
                      ⚠️ Some members couldn't be added automatically:
                    </p>
                    <ul className="list-disc list-inside ml-4 text-sm text-orange-600">
                      {creationResult.failed_invites.map((invite, idx) => (
                        <li key={idx}>
                          {invite.username}: {invite.reason}
                        </li>
                      ))}
                    </ul>
                    <p className="text-sm text-gray-600 mt-2">
                      You can manually invite these members to the group.
                    </p>
                  </div>
                )}
              </div>
            ) : (
              <p className="text-sm text-red-700">
                There was an error creating the group. Please try again or contact support.
              </p>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
