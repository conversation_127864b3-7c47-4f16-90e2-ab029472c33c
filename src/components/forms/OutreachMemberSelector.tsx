"use client"

import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { validateUsername } from "@/lib/validation"
import { sanitizeUsername } from "@/lib/sanitization"
import type { FormData, OutreachMember } from "@/types/team"

interface OutreachMemberSelectorProps {
  formData: FormData;
  setFormData: (data: FormData) => void;
  outreachMembers: OutreachMember[];
  isLoadingTeamData: boolean;
}

export function OutreachMemberSelector({ 
  formData, 
  setFormData, 
  outreachMembers, 
  isLoadingTeamData 
}: OutreachMemberSelectorProps) {
  const selectedOutreachMember = outreachMembers.find(
    member => member.name === formData.outreachMemberName
  );

  return (
    <div className="space-y-4">
      <Label><PERSON><PERSON> (Business Development Representative) *</Label>
      
      {/* Toggle for custom username */}
      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          id="useCustomOutreachUsername"
          checked={formData.useCustomOutreachUsername}
          onChange={(e) =>
            setFormData({
              ...formData,
              useCustomOutreachUsername: e.target.checked,
              errors: { ...formData.errors, outreachTeamMember: "", customOutreachUsername: "" }
            })
          }
          className="rounded"
        />
        <Label htmlFor="useCustomOutreachUsername" className="text-sm">
          Enter BDR username manually
        </Label>
      </div>

      {formData.useCustomOutreachUsername ? (
        /* Custom Username Input */
        <div className="space-y-2">
          <Label htmlFor="customOutreachUsername">BDR Username *</Label>
          <Input
            id="customOutreachUsername"
            value={formData.customOutreachUsername}
            onChange={(e) => {
              const rawValue = e.target.value;
              const sanitizedValue = sanitizeUsername(rawValue);
              const validation = validateUsername(sanitizedValue);
              setFormData({ 
                ...formData, 
                customOutreachUsername: sanitizedValue,
                errors: { 
                  ...formData.errors, 
                  customOutreachUsername: validation.isValid ? "" : (validation.error || "Invalid username")
                }
              });
            }}
            placeholder="@username"
            className={formData.errors.customOutreachUsername ? "border-red-500" : ""}
          />
          {formData.errors.customOutreachUsername && (
            <p className="text-sm text-red-500">{formData.errors.customOutreachUsername}</p>
          )}
        </div>
      ) : (
        /* Outreach Member Dropdown */
        <div className="space-y-2">
          {isLoadingTeamData ? (
            <div className="flex items-center space-x-2 p-3 border rounded-md">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <span className="text-sm text-gray-500">Loading outreach members...</span>
            </div>
          ) : outreachMembers && outreachMembers.length > 0 ? (
            <Select
              value={formData.outreachMemberName}
              onValueChange={(value) => {
                const member = outreachMembers.find(m => m.name === value);
                setFormData({
                  ...formData,
                  outreachMemberName: value,
                  outreachMemberUsername: member?.telegramUsernames?.[0] || "",
                  errors: { ...formData.errors, outreachTeamMember: "" }
                });
              }}
            >
              <SelectTrigger
                className={formData.errors.outreachTeamMember ? "border-red-500" : ""}
              >
                <SelectValue placeholder="Select an outreach member" />
              </SelectTrigger>
              <SelectContent>
                {outreachMembers.map((member: OutreachMember, idx: number) => (
                  <SelectItem key={idx} value={member.name}>
                    {member.emoji} {member.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          ) : (
            <div className="p-3 border rounded-md bg-yellow-50 border-yellow-200">
              <p className="text-sm text-yellow-800">
                ⚠️ No outreach members available. Please check your Google Sheets configuration.
              </p>
            </div>
          )}
          
          {formData.errors.outreachTeamMember && (
            <p className="text-sm text-red-500">{formData.errors.outreachTeamMember}</p>
          )}
        </div>
      )}

      {/* Display selected member info */}
      {!formData.useCustomOutreachUsername && selectedOutreachMember && (
        <div className="mt-4 p-4 bg-gray-50 rounded-lg">
          <h4 className="font-semibold text-sm mb-2">Selected BDR:</h4>
          <p className="text-sm">
            <span className="font-semibold">Name:</span> {selectedOutreachMember.emoji} {selectedOutreachMember.name}
          </p>
          <p className="text-sm">
            <span className="font-semibold">Username:</span> {selectedOutreachMember.telegramUsernames?.[0] || 'N/A'}
          </p>
          {selectedOutreachMember.email && (
            <p className="text-sm">
              <span className="font-semibold">Email:</span> {selectedOutreachMember.email}
            </p>
          )}
          {selectedOutreachMember.telegramUsernames && selectedOutreachMember.telegramUsernames.length > 1 && (
            <p className="text-sm">
              <span className="font-semibold">Additional usernames:</span> {selectedOutreachMember.telegramUsernames.slice(1).join(', ')}
            </p>
          )}
        </div>
      )}
    </div>
  );
}
