"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { validateProjectName, validateUsername } from "@/lib/validation"
import { sanitizeProjectName, sanitizeUsername } from "@/lib/sanitization"
import type { FormData } from "@/types/team"

interface ProjectDetailsFormProps {
  formData: FormData;
  setFormData: (data: FormData) => void;
}

export function ProjectDetailsForm({ formData, setFormData }: ProjectDetailsFormProps) {
  const addProjectLead = () => {
    setFormData({
      ...formData,
      projectLeads: [...formData.projectLeads, ""]
    });
  };

  const removeProjectLead = (index: number) => {
    const newLeads = formData.projectLeads.filter((_, i) => i !== index);
    setFormData({
      ...formData,
      projectLeads: newLeads.length > 0 ? newLeads : [""]
    });
  };

  return (
    <div className="space-y-6">
      {/* Project Name */}
      <div className="space-y-2">
        <Label htmlFor="projectName">Project Name *</Label>
        <Input
          id="projectName"
          value={formData.projectName}
          onChange={(e) => {
            const rawValue = e.target.value;
            const sanitizedValue = sanitizeProjectName(rawValue);
            const validation = validateProjectName(sanitizedValue);
            setFormData({ 
              ...formData, 
              projectName: sanitizedValue,
              errors: { 
                ...formData.errors, 
                projectName: validation.isValid ? "" : (validation.error || "Invalid project name")
              }
            });
          }}
          placeholder="Enter project name"
          className={formData.errors.projectName ? "border-red-500" : ""}
        />
        {formData.errors.projectName && (
          <p className="text-sm text-red-500">{formData.errors.projectName}</p>
        )}
      </div>

      {/* Project Leads */}
      <div className="space-y-2">
        <Label>Project Lead Username(s) *</Label>
        <div className="space-y-2">
          {formData.projectLeads.map((lead, index) => (
            <div key={index} className="flex gap-2">
              <Input
                value={lead}
                onChange={(e) => {
                  const rawValue = e.target.value;
                  const sanitizedValue = sanitizeUsername(rawValue);
                  const newLeads = [...formData.projectLeads];
                  newLeads[index] = sanitizedValue;
                  
                  // Validate all project leads
                  const validLeads = newLeads.filter(l => l.trim().length > 0);
                  let error = "";
                  if (validLeads.length === 0) {
                    error = "At least one project lead is required";
                  } else {
                    for (const username of validLeads) {
                      const validation = validateUsername(username);
                      if (!validation.isValid) {
                        error = validation.error || "Invalid username format";
                        break;
                      }
                    }
                  }
                  
                  setFormData({
                    ...formData,
                    projectLeads: newLeads,
                    errors: { ...formData.errors, projectLead: error }
                  });
                }}
                placeholder="@username"
                className={`flex-1 ${formData.errors.projectLead ? "border-red-500" : ""}`}
              />
              {formData.projectLeads.length > 1 && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => removeProjectLead(index)}
                  className="px-3"
                >
                  ✕
                </Button>
              )}
            </div>
          ))}
        </div>
        
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={addProjectLead}
          className="mt-2"
        >
          + Add Another Project Lead
        </Button>
        
        {formData.errors.projectLead && (
          <p className="text-sm text-red-500">{formData.errors.projectLead}</p>
        )}
        
        <p className="text-sm text-gray-500">
          Enter Telegram usernames starting with @. You can add multiple project leads.
        </p>
      </div>
    </div>
  );
}
