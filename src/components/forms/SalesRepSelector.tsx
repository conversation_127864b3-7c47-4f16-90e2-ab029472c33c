"use client"

import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { validateUsername, validateCalendlyLink } from "@/lib/validation"
import { sanitizeUsername, sanitizeUrl } from "@/lib/sanitization"
import { findSalesRepByUsername } from "@/types/team"
import type { FormData, SalesRep } from "@/types/team"

interface SalesRepSelectorProps {
  formData: FormData;
  setFormData: (data: FormData) => void;
  salesReps: SalesRep[];
  isLoadingTeamData: boolean;
}

export function SalesRepSelector({ 
  formData, 
  setFormData, 
  salesReps, 
  isLoadingTeamData 
}: SalesRepSelectorProps) {
  const selectedSalesRep = findSalesRepByUsername(salesReps, formData.salesperson);

  return (
    <div className="space-y-4">
      <Label>Partnership Representative *</Label>
      
      {/* Toggle for manual entry */}
      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          id="enterSalesRepManually"
          checked={formData.enterSalesRepManually}
          onChange={(e) =>
            setFormData({
              ...formData,
              enterSalesRepManually: e.target.checked,
              errors: { ...formData.errors, salesperson: "", customSalesRepUsername: "" }
            })
          }
          className="rounded"
        />
        <Label htmlFor="enterSalesRepManually" className="text-sm">
          Enter sales representative manually
        </Label>
      </div>

      {formData.enterSalesRepManually ? (
        /* Manual Entry */
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="customSalesRepUsername">Sales Rep Username *</Label>
            <Input
              id="customSalesRepUsername"
              value={formData.customSalesRepUsername}
              onChange={(e) => {
                const rawValue = e.target.value;
                const sanitizedValue = sanitizeUsername(rawValue);
                const validation = validateUsername(sanitizedValue);
                setFormData({
                  ...formData,
                  customSalesRepUsername: sanitizedValue,
                  errors: { 
                    ...formData.errors, 
                    customSalesRepUsername: validation.isValid ? "" : (validation.error || "Invalid username")
                  }
                });
              }}
              placeholder="@username"
              className={formData.errors.customSalesRepUsername ? "border-red-500" : ""}
            />
            {formData.errors.customSalesRepUsername && (
              <p className="text-sm text-red-500">{formData.errors.customSalesRepUsername}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="customCalendlyLink">Custom Calendly Link (Optional)</Label>
            <Input
              id="customCalendlyLink"
              value={formData.customCalendlyLink}
              onChange={(e) => {
                const rawValue = e.target.value;
                const sanitizedValue = sanitizeUrl(rawValue);
                const validation = validateCalendlyLink(sanitizedValue);
                setFormData({
                  ...formData,
                  customCalendlyLink: sanitizedValue,
                  errors: { 
                    ...formData.errors, 
                    customCalendlyLink: validation.isValid ? "" : (validation.error || "Invalid Calendly link")
                  }
                });
              }}
              placeholder="https://calendly.com/..."
              className={formData.errors.customCalendlyLink ? "border-red-500" : ""}
            />
            {formData.errors.customCalendlyLink && (
              <p className="text-sm text-red-500">{formData.errors.customCalendlyLink}</p>
            )}
          </div>
        </div>
      ) : (
        /* Sales Rep Dropdown */
        <div className="space-y-4">
          {isLoadingTeamData ? (
            <div className="flex items-center space-x-2 p-3 border rounded-md">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <span className="text-sm text-gray-500">Loading sales representatives...</span>
            </div>
          ) : salesReps && salesReps.length > 0 ? (
            <Select
              value={formData.salesperson}
              onValueChange={(value) =>
                setFormData({
                  ...formData,
                  salesperson: value,
                  errors: { ...formData.errors, salesperson: "" }
                })
              }
            >
              <SelectTrigger
                id="salesperson"
                className={formData.errors.salesperson ? "border-red-500" : ""}
              >
                <SelectValue placeholder="Select a sales representative" />
              </SelectTrigger>
              <SelectContent>
                {salesReps
                  .filter((rep: SalesRep) => rep.username)
                  .map((rep: SalesRep, idx: number) => (
                    <SelectItem key={idx} value={rep.username}>
                      {`${rep.emoji} ${rep.name}`}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
          ) : (
            <div className="p-3 border rounded-md bg-yellow-50 border-yellow-200">
              <p className="text-sm text-yellow-800">
                ⚠️ No sales representatives available. Please check your Google Sheets configuration.
              </p>
            </div>
          )}

          {formData.errors.salesperson && (
            <p className="text-sm text-red-500">{formData.errors.salesperson}</p>
          )}

          {/* Custom username override for selected sales rep */}
          {!formData.enterSalesRepManually && selectedSalesRep && (
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="useCustomSalesRepUsername"
                  checked={formData.useCustomSalesRepUsername}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      useCustomSalesRepUsername: e.target.checked,
                      errors: { ...formData.errors, customSalesRepUsername: "" }
                    })
                  }
                  className="rounded"
                />
                <Label htmlFor="useCustomSalesRepUsername" className="text-sm">
                  Use different username for this sales rep
                </Label>
              </div>

              {formData.useCustomSalesRepUsername && (
                <div className="space-y-2">
                  <Label htmlFor="customSalesRepUsernameOverride">Custom Username *</Label>
                  <Input
                    id="customSalesRepUsernameOverride"
                    value={formData.customSalesRepUsername}
                    onChange={(e) => {
                      const rawValue = e.target.value;
                      const sanitizedValue = sanitizeUsername(rawValue);
                      const validation = validateUsername(sanitizedValue);
                      setFormData({
                        ...formData,
                        customSalesRepUsername: sanitizedValue,
                        errors: { 
                          ...formData.errors, 
                          customSalesRepUsername: validation.isValid ? "" : (validation.error || "Invalid username")
                        }
                      });
                    }}
                    placeholder="@username"
                    className={formData.errors.customSalesRepUsername ? "border-red-500" : ""}
                  />
                  {formData.errors.customSalesRepUsername && (
                    <p className="text-sm text-red-500">{formData.errors.customSalesRepUsername}</p>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Display selected sales rep info */}
      {!formData.enterSalesRepManually && selectedSalesRep && (
        <div className="mt-4 p-4 bg-gray-50 rounded-lg">
          <h4 className="font-semibold text-sm mb-2">Selected Partnership Representative:</h4>
          <p className="text-sm">
            <span className="font-semibold">Name:</span> {selectedSalesRep.emoji} {selectedSalesRep.name}
          </p>
          <p className="text-sm">
            <span className="font-semibold">Username:</span> {selectedSalesRep.username}
          </p>
          <p className="text-sm">
            <span className="font-semibold">PA Username:</span> {selectedSalesRep.paUsername || 'N/A'}
          </p>
          {selectedSalesRep.calendarLink && (
            <p className="text-sm">
              <span className="font-semibold">Calendar:</span>{" "}
              <a 
                href={selectedSalesRep.calendarLink} 
                target="_blank" 
                rel="noopener noreferrer" 
                className="text-blue-500 hover:underline"
              >
                Schedule Meeting
              </a>
            </p>
          )}
          {/* Meme team links removed - not available in current data structure */}
          <p className="text-sm">
            <span className="font-semibold">Pipeline:</span> {selectedSalesRep.tier}
          </p>
        </div>
      )}
    </div>
  );
}
