"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useUser } from "@clerk/nextjs"
import { useState, useEffect } from "react"
import axios from "axios"
import { toast } from "sonner"
import type { 
  SalesRep, 
  OutreachMember, 
  FormData, 
  TeamDataResponse,
  CreateGroupRequest,
  CreateGroupResponse,
  GroupCreationResult
} from "@/types/team"
import { validateAndSanitizeFormData } from "@/lib/validation"
import { findSalesRepByUsername, findOutreachMemberByName } from "@/types/team"
import { ProjectDetailsForm } from "./ProjectDetailsForm"
import { OutreachMemberSelector } from "./OutreachMemberSelector"
import { SalesRepSelector } from "./SalesRepSelector"
import { GroupPreview } from "./GroupPreview"

export function TelegramGroupFormRefactored() {
  const { user } = useUser()

  const [salesReps, setSalesReps] = useState<SalesRep[]>([]);
  const [outreachMembers, setOutreachMembers] = useState<OutreachMember[]>([]);
  const [isLoadingTeamData, setIsLoadingTeamData] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [creationResult, setCreationResult] = useState<GroupCreationResult | null>(null);

  const [formData, setFormData] = useState<FormData>({
    projectName: "",
    projectLeads: [""],
    outreachTeamMember: "",
    outreachMemberName: "",
    outreachMemberUsername: "",
    customOutreachUsername: "",
    useCustomOutreachUsername: false,
    salesperson: "",
    customSalesRepUsername: "",
    useCustomSalesRepUsername: false,
    enterSalesRepManually: false,
    customCalendlyLink: "",
    includeCalendly: true,
    errors: {
      projectName: "",
      projectLead: "",
      outreachTeamMember: "",
      salesperson: "",
      outreachMemberUsername: "",
      customOutreachUsername: "",
      customSalesRepUsername: "",
      customCalendlyLink: ""
    }
  });

  // Load team data
  useEffect(() => {
    const fetchTeamData = async () => {
      try {
        setIsLoadingTeamData(true);
        const response = await axios.get<TeamDataResponse>('/api/team-data');
        console.log('📊 Team data response:', response.data);

        // Filter out sales reps without usernames
        const filteredSalesReps = (response.data.salesReps || []).filter(
          (rep: SalesRep) => rep.username && rep.username.trim() !== ""
        );
        console.log('✅ Filtered salesReps:', filteredSalesReps);

        setSalesReps(filteredSalesReps);
        setOutreachMembers(response.data.outreachMembers || []);
        
        if (response.data.warning) {
          toast.warning(response.data.warning);
        }
      } catch (error) {
        console.error('❌ Failed to fetch team data:', error);
        toast.error('Failed to load team data. Some features may not work properly.');
      } finally {
        setIsLoadingTeamData(false);
      }
    };
    fetchTeamData();
  }, []);

  const selectedSalesRep = findSalesRepByUsername(salesReps, formData.salesperson);
  const selectedOutreachMember = findOutreachMemberByName(outreachMembers, formData.outreachMemberName);

  const validateForm = () => {
    try {
      validateAndSanitizeFormData(formData, user?.id);
      return true;
    } catch (error: any) {
      console.error('❌ Form validation failed:', error);
      toast.error('Please fix the form errors before submitting.');
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setCreationResult(null);

    try {
      console.log('🚀 Starting form submission...');
      
      // Get the appropriate calendly link
      let calendlyLink: string | undefined;
      if (formData.enterSalesRepManually) {
        calendlyLink = formData.customCalendlyLink || undefined;
      } else if (selectedSalesRep) {
        calendlyLink = selectedSalesRep.calendarLink;
      }
      
      // Get the appropriate outreach member username
      const outreachUsername = formData.useCustomOutreachUsername 
        ? formData.customOutreachUsername 
        : formData.outreachMemberUsername;
      
      // Get the appropriate sales rep username
      const salesRepUsername = formData.enterSalesRepManually
        ? formData.customSalesRepUsername
        : (formData.useCustomSalesRepUsername && selectedSalesRep
          ? formData.customSalesRepUsername
          : selectedSalesRep?.username);
      
      if (!salesRepUsername) {
        toast.error('Sales representative username is required');
        return;
      }

      // Create welcome message with the exact format provided
      const projectLeadsArray = formData.projectLeads
        .map(s => s.trim())
        .filter(s => s.length > 0);
      
      const welcomeMessage = `👥 Project Lead: ${projectLeadsArray.join(", ")}
👨‍💼 Partnership Representative: ${salesRepUsername}
👨‍💻 BDR: ${outreachUsername}${formData.includeCalendly && calendlyLink ? `
📅 Schedule a meeting: ${calendlyLink}` : ''}`;

      const requestBody: CreateGroupRequest = {
        projectName: formData.projectName,
        projectLeads: projectLeadsArray,
        outreachTeamMember: outreachUsername,
        outreachMemberEmail: selectedOutreachMember?.email,
        salesperson: formData.enterSalesRepManually ? "Custom Sales Rep" : formData.salesperson,
        salesRepUsername: salesRepUsername,
        paUsername: formData.enterSalesRepManually ? undefined : selectedSalesRep?.paUsername,
        calendlyLink: formData.includeCalendly ? calendlyLink : undefined,
        includeCalendly: formData.includeCalendly,
        welcomeMessage: welcomeMessage,
        inviteSalesRep: true,

        // Additional fields for Sales Representative
        salesRepEmoji: formData.enterSalesRepManually ? undefined : selectedSalesRep?.emoji,
        salesRepPAUsername: formData.enterSalesRepManually ? undefined : selectedSalesRep?.paUsername,
        salesRepCalendlyLink: formData.enterSalesRepManually
          ? formData.customCalendlyLink
          : (formData.includeCalendly && selectedSalesRep?.calendarLink ? selectedSalesRep.calendarLink : undefined),
        salesRepCategory: formData.enterSalesRepManually ? undefined : selectedSalesRep?.tier,

        // Additional fields for Outreach Member
        outreachMemberUsernames: selectedOutreachMember?.telegramUsernames,
        outreachMemberEmoji: selectedOutreachMember?.emoji
      };

      console.log('📤 Sending request:', requestBody);
      const response = await axios.post<CreateGroupResponse>("/api/create-group", requestBody);

      if (response.data.success) {
        toast.success("🎉 Telegram group created successfully!");
        setCreationResult({
          success: true,
          failed_invites: response.data.failed_invites
        });
        
        // Reset form
        setFormData({
          projectName: "",
          projectLeads: [""],
          outreachTeamMember: "",
          outreachMemberName: "",
          outreachMemberUsername: "",
          customOutreachUsername: "",
          useCustomOutreachUsername: false,
          salesperson: "",
          customSalesRepUsername: "",
          useCustomSalesRepUsername: false,
          enterSalesRepManually: false,
          customCalendlyLink: "",
          includeCalendly: true,
          errors: {
            projectName: "",
            projectLead: "",
            outreachTeamMember: "",
            salesperson: "",
            outreachMemberUsername: "",
            customOutreachUsername: "",
            customSalesRepUsername: "",
            customCalendlyLink: ""
          }
        });
      } else {
        toast.error(response.data.error || "Failed to create group");
      }

    } catch (error: any) {
      console.error("❌ Error creating group:", error);
      
      const errorMessage = error.response?.data?.error || 
        error.message || 
        "Failed to create Telegram group. Please try again.";
      
      toast.error(errorMessage);
      
      setCreationResult({
        success: false,
        failed_invites: []
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!user) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardContent className="p-6">
          <p className="text-center text-gray-500">Please sign in to create Telegram groups.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="text-2xl font-bold text-center">
          Create Telegram Group
        </CardTitle>
        <p className="text-center text-gray-600">
          Automatically create and configure a Telegram group for your IBC project
        </p>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Project Details */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Project Details</h3>
            <ProjectDetailsForm formData={formData} setFormData={setFormData} />
          </div>

          {/* Outreach Member Selection */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Outreach Team</h3>
            <OutreachMemberSelector 
              formData={formData} 
              setFormData={setFormData}
              outreachMembers={outreachMembers}
              isLoadingTeamData={isLoadingTeamData}
            />
          </div>

          {/* Sales Rep Selection */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Partnership Representative</h3>
            <SalesRepSelector 
              formData={formData} 
              setFormData={setFormData}
              salesReps={salesReps}
              isLoadingTeamData={isLoadingTeamData}
            />
          </div>

          {/* Group Preview */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Preview & Settings</h3>
            <GroupPreview 
              formData={formData} 
              setFormData={setFormData}
              salesReps={salesReps}
              outreachMembers={outreachMembers}
              creationResult={creationResult}
            />
          </div>

          {/* Submit Button */}
          <div className="flex justify-center pt-6">
            <Button 
              type="submit" 
              disabled={isLoading}
              className="px-8 py-2"
            >
              {isLoading ? "Creating Group..." : "Create Telegram Group"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
