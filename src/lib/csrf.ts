import { NextRequest, NextResponse } from 'next/server';
import { createHash, randomBytes } from 'crypto';

/**
 * CSRF Protection Utility
 * Implements Double Submit Cookie pattern for CSRF protection
 */

const CSRF_TOKEN_HEADER = 'x-csrf-token';
const CSRF_COOKIE_NAME = 'csrf-token';
const TOKEN_LENGTH = 32;

/**
 * Generate a cryptographically secure random token
 */
export function generateCSRFToken(): string {
  return randomBytes(TOKEN_LENGTH).toString('hex');
}

/**
 * Create a hash of the token for comparison
 */
function hashToken(token: string): string {
  return createHash('sha256').update(token).digest('hex');
}

/**
 * Set CSRF token in cookie
 */
export function setCSRFCookie(response: NextResponse, token: string): void {
  response.cookies.set(CSRF_COOKIE_NAME, hashToken(token), {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 60 * 60 * 24, // 24 hours
    path: '/',
  });
}

/**
 * Get CSRF token from cookie
 */
export function getCSRFTokenFromCookie(req: NextRequest): string | null {
  return req.cookies.get(CSRF_COOKIE_NAME)?.value || null;
}

/**
 * Get CSRF token from header
 */
export function getCSRFTokenFromHeader(req: NextRequest): string | null {
  return req.headers.get(CSRF_TOKEN_HEADER);
}

/**
 * Validate CSRF token
 */
export function validateCSRFToken(req: NextRequest): boolean {
  const cookieToken = getCSRFTokenFromCookie(req);
  const headerToken = getCSRFTokenFromHeader(req);
  
  if (!cookieToken || !headerToken) {
    return false;
  }
  
  // Compare hashed header token with cookie token
  const hashedHeaderToken = hashToken(headerToken);
  return hashedHeaderToken === cookieToken;
}

/**
 * CSRF protection middleware
 */
export function createCSRFProtection(options: {
  ignoreMethods?: string[];
  ignorePaths?: string[];
} = {}) {
  const { 
    ignoreMethods = ['GET', 'HEAD', 'OPTIONS'],
    ignorePaths = []
  } = options;
  
  return async (req: NextRequest): Promise<NextResponse | null> => {
    const method = req.method;
    const pathname = req.nextUrl.pathname;

    // Skip CSRF protection for safe methods
    if (ignoreMethods.includes(method)) {
      return null;
    }

    // Skip CSRF protection for ignored paths
    if (ignorePaths.some(path => pathname.startsWith(path))) {
      return null;
    }
    
    // Validate CSRF token for unsafe methods
    if (!validateCSRFToken(req)) {
      console.warn(`🚫 CSRF validation failed for ${method} ${pathname}`);
      
      return NextResponse.json(
        {
          success: false,
          error: 'CSRF token validation failed',
          code: 'CSRF_INVALID'
        },
        { status: 403 }
      );
    }
    
    return null; // Continue to next middleware/handler
  };
}

/**
 * API endpoint to get CSRF token
 */
export function createCSRFTokenEndpoint() {
  return async (req: NextRequest): Promise<NextResponse> => {
    const token = generateCSRFToken();
    const response = NextResponse.json({
      success: true,
      token,
    });
    
    setCSRFCookie(response, token);
    
    return response;
  };
}

/**
 * Utility to add CSRF token to API responses
 */
export function withCSRFToken(response: NextResponse): NextResponse {
  const token = generateCSRFToken();
  setCSRFCookie(response, token);
  
  // Also include token in response headers for client-side access
  response.headers.set('X-CSRF-Token', token);
  
  return response;
}

/**
 * Client-side utility to get CSRF token from meta tag or API
 */
export const clientCSRF = {
  /**
   * Get CSRF token from meta tag
   */
  getTokenFromMeta(): string | null {
    if (typeof document === 'undefined') return null;
    
    const metaTag = document.querySelector('meta[name="csrf-token"]');
    return metaTag?.getAttribute('content') || null;
  },
  
  /**
   * Fetch CSRF token from API
   */
  async fetchToken(): Promise<string | null> {
    try {
      const response = await fetch('/api/csrf-token', {
        method: 'GET',
        credentials: 'same-origin',
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch CSRF token');
      }
      
      const data = await response.json();
      return data.token || null;
    } catch (error) {
      console.error('Error fetching CSRF token:', error);
      return null;
    }
  },
  
  /**
   * Add CSRF token to request headers
   */
  addTokenToHeaders(headers: HeadersInit = {}): HeadersInit {
    const token = this.getTokenFromMeta();
    if (token) {
      return {
        ...headers,
        [CSRF_TOKEN_HEADER]: token,
      };
    }
    return headers;
  },
  
  /**
   * Create fetch wrapper with CSRF protection
   */
  createProtectedFetch() {
    return async (url: string, options: RequestInit = {}): Promise<Response> => {
      const token = this.getTokenFromMeta() || await this.fetchToken();
      
      if (!token) {
        throw new Error('Unable to obtain CSRF token');
      }
      
      const headers = {
        ...options.headers,
        [CSRF_TOKEN_HEADER]: token,
      };
      
      return fetch(url, {
        ...options,
        headers,
        credentials: 'same-origin',
      });
    };
  }
};

// Note: React hook for CSRF protection can be created in a separate file if needed
// This would require React imports: useState, useEffect
