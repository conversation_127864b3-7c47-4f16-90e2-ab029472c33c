import { z } from 'zod';

/**
 * Environment variable validation schema
 * This ensures all required environment variables are present and valid
 */
const envSchema = z.object({
  // Clerk Authentication
  NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: z.string().min(1, "Clerk publishable key is required"),
  CLERK_SECRET_KEY: z.string().min(1, "Clerk secret key is required"),
  
  // Telegram API
  TELEGRAM_API_ID: z.string().min(1, "Telegram API ID is required").transform(Number),
  TELEGRAM_API_HASH: z.string().min(1, "Telegram API Hash is required"),
  TELEGRAM_SESSION_STRING: z.string().min(1, "Telegram session string is required"),
  
  // Google Sheets API (optional - for team data)
  GOOGLE_SHEETS_CLIENT_EMAIL: z.string().email("Invalid Google Sheets client email").optional(),
  GOOGLE_SHEETS_PRIVATE_KEY: z.string().optional(),
  GOOGLE_SHEETS_SPREADSHEET_ID: z.string().optional(),
  
  // Node Environment
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
});

/**
 * Client-side environment variables (only NEXT_PUBLIC_ vars)
 */
const clientEnvSchema = z.object({
  NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: z.string().min(1, "Clerk publishable key is required"),
});

/**
 * Validate and parse environment variables
 * This function should be called at application startup
 */
function validateEnv() {
  try {
    const parsed = envSchema.parse(process.env);
    console.log('✅ Environment variables validated successfully');
    return parsed;
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error('❌ Environment validation failed:');
      error.errors.forEach((err) => {
        console.error(`  - ${err.path.join('.')}: ${err.message}`);
      });
      
      console.error('\n📝 Required environment variables:');
      console.error('  - NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY');
      console.error('  - CLERK_SECRET_KEY');
      console.error('  - TELEGRAM_API_ID');
      console.error('  - TELEGRAM_API_HASH');
      console.error('  - TELEGRAM_SESSION_STRING');
      console.error('\n📝 Optional environment variables:');
      console.error('  - GOOGLE_SHEETS_CLIENT_EMAIL');
      console.error('  - GOOGLE_SHEETS_PRIVATE_KEY');
      console.error('  - GOOGLE_SHEETS_SPREADSHEET_ID');
      
      process.exit(1);
    }
    throw error;
  }
}

/**
 * Validate client-side environment variables
 */
function validateClientEnv() {
  try {
    return clientEnvSchema.parse({
      NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error('❌ Client environment validation failed:');
      error.errors.forEach((err) => {
        console.error(`  - ${err.path.join('.')}: ${err.message}`);
      });
    }
    throw error;
  }
}

/**
 * Get validated environment variables
 * Only call this on the server side
 */
export const env = validateEnv();

/**
 * Get validated client environment variables
 * Safe to call on both client and server
 */
export const clientEnv = validateClientEnv();

/**
 * Type definitions for environment variables
 */
export type Env = z.infer<typeof envSchema>;
export type ClientEnv = z.infer<typeof clientEnvSchema>;

/**
 * Utility function to check if we're in development
 */
export const isDevelopment = env.NODE_ENV === 'development';

/**
 * Utility function to check if we're in production
 */
export const isProduction = env.NODE_ENV === 'production';

/**
 * Utility function to check if Google Sheets is configured
 */
export const isGoogleSheetsConfigured = Boolean(
  env.GOOGLE_SHEETS_CLIENT_EMAIL && 
  env.GOOGLE_SHEETS_PRIVATE_KEY && 
  env.GOOGLE_SHEETS_SPREADSHEET_ID
);

/**
 * Get Telegram configuration
 */
export const getTelegramConfig = () => ({
  apiId: env.TELEGRAM_API_ID,
  apiHash: env.TELEGRAM_API_HASH,
  sessionString: env.TELEGRAM_SESSION_STRING,
});

/**
 * Get Clerk configuration
 */
export const getClerkConfig = () => ({
  publishableKey: env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY,
  secretKey: env.CLERK_SECRET_KEY,
});

/**
 * Get Google Sheets configuration
 */
export const getGoogleSheetsConfig = () => {
  if (!isGoogleSheetsConfigured) {
    throw new Error('Google Sheets is not configured. Please set the required environment variables.');
  }
  
  return {
    clientEmail: env.GOOGLE_SHEETS_CLIENT_EMAIL!,
    privateKey: env.GOOGLE_SHEETS_PRIVATE_KEY!,
    spreadsheetId: env.GOOGLE_SHEETS_SPREADSHEET_ID!,
  };
};
