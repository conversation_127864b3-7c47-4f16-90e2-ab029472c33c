/**
 * Custom error classes for better error handling and debugging
 */

export class TelegramError extends Error {
  public readonly code?: string;
  public readonly statusCode: number;

  constructor(message: string, code?: string, statusCode: number = 500) {
    super(message);
    this.name = 'TelegramError';
    this.code = code;
    this.statusCode = statusCode;
  }

  static fromTelegramApiError(error: any): TelegramError {
    const message = error.message || 'Unknown Telegram API error';
    
    if (message.toLowerCase().includes('flood')) {
      return new TelegramError(
        'Too many requests. Please wait before trying again.',
        'FLOOD_WAIT',
        429
      );
    }
    
    if (message.toLowerCase().includes('not enough rights')) {
      return new TelegramError(
        'Insufficient permissions. Please check bot admin rights.',
        'INSUFFICIENT_RIGHTS',
        403
      );
    }
    
    if (message.toLowerCase().includes('session')) {
      return new TelegramError(
        'Telegram session expired. Please regenerate session string.',
        'SESSION_EXPIRED',
        401
      );
    }
    
    if (message.toLowerCase().includes('user not found')) {
      return new TelegramError(
        'User not found. Please check the username.',
        'USER_NOT_FOUND',
        404
      );
    }
    
    if (message.toLowerCase().includes('chat not found')) {
      return new TelegramError(
        'Chat not found or bot was removed from chat.',
        'CHAT_NOT_FOUND',
        404
      );
    }
    
    return new TelegramError(message, 'UNKNOWN_ERROR', 500);
  }
}

export class GoogleSheetsError extends Error {
  public readonly sheetName?: string;
  public readonly statusCode: number;

  constructor(message: string, sheetName?: string, statusCode: number = 500) {
    super(message);
    this.name = 'GoogleSheetsError';
    this.sheetName = sheetName;
    this.statusCode = statusCode;
  }

  static fromGoogleApiError(error: any, sheetName?: string): GoogleSheetsError {
    const message = error.message || 'Unknown Google Sheets API error';
    
    if (error.code === 403) {
      return new GoogleSheetsError(
        'Access denied to Google Sheets. Please check service account permissions.',
        sheetName,
        403
      );
    }
    
    if (error.code === 404) {
      return new GoogleSheetsError(
        `Spreadsheet or sheet "${sheetName}" not found. Please check the spreadsheet ID and sheet name.`,
        sheetName,
        404
      );
    }
    
    if (error.code === 400) {
      return new GoogleSheetsError(
        `Invalid request to Google Sheets API: ${message}`,
        sheetName,
        400
      );
    }
    
    return new GoogleSheetsError(
      `Google Sheets API error: ${message}`,
      sheetName,
      500
    );
  }
}

export class ValidationError extends Error {
  public readonly field?: string;
  public readonly statusCode: number = 400;

  constructor(message: string, field?: string) {
    super(message);
    this.name = 'ValidationError';
    this.field = field;
  }
}

export class AuthenticationError extends Error {
  public readonly statusCode: number = 401;

  constructor(message: string = 'Authentication required') {
    super(message);
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends Error {
  public readonly statusCode: number = 403;

  constructor(message: string = 'Insufficient permissions') {
    super(message);
    this.name = 'AuthorizationError';
  }
}

export class RateLimitError extends Error {
  public readonly statusCode: number = 429;
  public readonly retryAfter?: number;

  constructor(message: string = 'Rate limit exceeded', retryAfter?: number) {
    super(message);
    this.name = 'RateLimitError';
    this.retryAfter = retryAfter;
  }
}

/**
 * Error handler utility functions
 */
export function isOperationalError(error: Error): boolean {
  return (
    error instanceof TelegramError ||
    error instanceof GoogleSheetsError ||
    error instanceof ValidationError ||
    error instanceof AuthenticationError ||
    error instanceof AuthorizationError ||
    error instanceof RateLimitError
  );
}

export function getErrorStatusCode(error: Error): number {
  if ('statusCode' in error && typeof error.statusCode === 'number') {
    return error.statusCode;
  }
  return 500;
}

export function getErrorMessage(error: Error): string {
  if (isOperationalError(error)) {
    return error.message;
  }
  
  // Don't expose internal error details in production
  if (process.env.NODE_ENV === 'production') {
    return 'An unexpected error occurred. Please try again later.';
  }
  
  return error.message || 'Unknown error occurred';
}

/**
 * Log error with appropriate level and context
 */
export function logError(error: Error, context?: Record<string, any>): void {
  const errorInfo = {
    name: error.name,
    message: error.message,
    stack: error.stack,
    context,
    timestamp: new Date().toISOString(),
  };

  if (isOperationalError(error)) {
    console.warn('⚠️ Operational error:', errorInfo);
  } else {
    console.error('❌ Unexpected error:', errorInfo);
  }
}

/**
 * Create error response for API routes
 */
export function createErrorResponse(error: Error, context?: Record<string, any>) {
  logError(error, context);
  
  return {
    success: false,
    error: getErrorMessage(error),
    code: 'code' in error ? error.code : undefined,
    timestamp: new Date().toISOString(),
  };
}

/**
 * Async error wrapper for API routes
 */
export function withErrorHandling<T extends any[], R>(
  fn: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R> => {
    try {
      return await fn(...args);
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(String(error));
    }
  };
}

/**
 * Retry wrapper with exponential backoff
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      // Don't retry on certain errors
      if (
        lastError instanceof ValidationError ||
        lastError instanceof AuthenticationError ||
        lastError instanceof AuthorizationError
      ) {
        throw lastError;
      }
      
      // Don't retry on last attempt
      if (attempt === maxRetries) {
        break;
      }
      
      // Exponential backoff
      const delay = baseDelay * Math.pow(2, attempt);
      console.log(`⏳ Retrying in ${delay}ms (attempt ${attempt + 1}/${maxRetries + 1})`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError!;
}
