import { NextRequest, NextResponse } from 'next/server';
import { RateLimitError } from './errors';

/**
 * Simple in-memory rate limiter
 * In production, consider using Redis or a dedicated rate limiting service
 */

interface RateLimitEntry {
  count: number;
  resetTime: number;
}

// In-memory store for rate limiting
const rateLimitStore = new Map<string, RateLimitEntry>();

// Cleanup old entries every 5 minutes
setInterval(() => {
  const now = Date.now();
  for (const [key, entry] of rateLimitStore.entries()) {
    if (now > entry.resetTime) {
      rateLimitStore.delete(key);
    }
  }
}, 5 * 60 * 1000);

/**
 * Rate limiting configuration
 */
export interface RateLimitConfig {
  /** Maximum number of requests allowed */
  limit: number;
  /** Time window in milliseconds */
  windowMs: number;
  /** Custom key generator function */
  keyGenerator?: (req: NextRequest) => string;
  /** Skip rate limiting for certain requests */
  skip?: (req: NextRequest) => boolean;
  /** Custom error message */
  message?: string;
}

/**
 * Default rate limit configurations for different endpoints
 */
export const rateLimitConfigs = {
  // Group creation is resource intensive
  createGroup: {
    limit: 5, // 5 requests
    windowMs: 15 * 60 * 1000, // per 15 minutes
    message: 'Too many group creation requests. Please wait before trying again.',
  },
  
  // Team data can be called more frequently
  teamData: {
    limit: 30, // 30 requests
    windowMs: 60 * 1000, // per minute
    message: 'Too many requests for team data. Please wait a moment.',
  },
  
  // Health checks can be frequent
  health: {
    limit: 100, // 100 requests
    windowMs: 60 * 1000, // per minute
    message: 'Too many health check requests.',
  },
  
  // General API rate limit
  general: {
    limit: 100, // 100 requests
    windowMs: 15 * 60 * 1000, // per 15 minutes
    message: 'Too many requests. Please try again later.',
  },
} as const;

/**
 * Generate a rate limit key based on IP address and user ID
 */
function generateKey(req: NextRequest, prefix: string = 'general'): string {
  // Try to get user ID from headers (set by Clerk middleware)
  const userId = req.headers.get('x-user-id') || 'anonymous';
  
  // Get IP address
  const forwarded = req.headers.get('x-forwarded-for');
  const ip = forwarded ? forwarded.split(',')[0] : 
             req.headers.get('x-real-ip') || 
             'unknown';
  
  return `${prefix}:${userId}:${ip}`;
}

/**
 * Check if request should be rate limited
 */
export function checkRateLimit(
  key: string, 
  config: RateLimitConfig
): { allowed: boolean; remaining: number; resetTime: number; retryAfter?: number } {
  const now = Date.now();
  const entry = rateLimitStore.get(key);
  
  // If no entry exists or window has expired, create new entry
  if (!entry || now > entry.resetTime) {
    const newEntry: RateLimitEntry = {
      count: 1,
      resetTime: now + config.windowMs,
    };
    rateLimitStore.set(key, newEntry);
    
    return {
      allowed: true,
      remaining: config.limit - 1,
      resetTime: newEntry.resetTime,
    };
  }
  
  // Check if limit exceeded
  if (entry.count >= config.limit) {
    const retryAfter = Math.ceil((entry.resetTime - now) / 1000);
    return {
      allowed: false,
      remaining: 0,
      resetTime: entry.resetTime,
      retryAfter,
    };
  }
  
  // Increment count
  entry.count++;
  rateLimitStore.set(key, entry);
  
  return {
    allowed: true,
    remaining: config.limit - entry.count,
    resetTime: entry.resetTime,
  };
}

/**
 * Rate limiting middleware for API routes
 */
export function createRateLimiter(config: RateLimitConfig) {
  return async (req: NextRequest): Promise<NextResponse | null> => {
    // Skip if skip function returns true
    if (config.skip && config.skip(req)) {
      return null;
    }
    
    // Generate key
    const key = config.keyGenerator ? config.keyGenerator(req) : generateKey(req);
    
    // Check rate limit
    const result = checkRateLimit(key, config);
    
    if (!result.allowed) {
      console.warn(`🚫 Rate limit exceeded for key: ${key}`);
      
      const response = NextResponse.json(
        {
          success: false,
          error: config.message || 'Too many requests',
          retryAfter: result.retryAfter,
        },
        { status: 429 }
      );
      
      // Add rate limit headers
      response.headers.set('X-RateLimit-Limit', config.limit.toString());
      response.headers.set('X-RateLimit-Remaining', result.remaining.toString());
      response.headers.set('X-RateLimit-Reset', result.resetTime.toString());
      if (result.retryAfter) {
        response.headers.set('Retry-After', result.retryAfter.toString());
      }
      
      return response;
    }
    
    // Add rate limit headers to successful responses
    const response = NextResponse.next();
    response.headers.set('X-RateLimit-Limit', config.limit.toString());
    response.headers.set('X-RateLimit-Remaining', result.remaining.toString());
    response.headers.set('X-RateLimit-Reset', result.resetTime.toString());
    
    return null; // Continue to next middleware/handler
  };
}

/**
 * Rate limiter for group creation endpoint
 */
export const groupCreationRateLimit = createRateLimiter({
  ...rateLimitConfigs.createGroup,
  keyGenerator: (req) => generateKey(req, 'create-group'),
});

/**
 * Rate limiter for team data endpoint
 */
export const teamDataRateLimit = createRateLimiter({
  ...rateLimitConfigs.teamData,
  keyGenerator: (req) => generateKey(req, 'team-data'),
});

/**
 * Rate limiter for health endpoint
 */
export const healthRateLimit = createRateLimiter({
  ...rateLimitConfigs.health,
  keyGenerator: (req) => generateKey(req, 'health'),
});

/**
 * General rate limiter
 */
export const generalRateLimit = createRateLimiter({
  ...rateLimitConfigs.general,
  keyGenerator: (req) => generateKey(req, 'general'),
});

/**
 * Utility function to apply rate limiting to API handlers
 */
export function withRateLimit<T extends any[], R>(
  rateLimiter: (req: NextRequest) => Promise<NextResponse | null>,
  handler: (req: NextRequest, ...args: T) => Promise<R>
) {
  return async (req: NextRequest, ...args: T): Promise<R | NextResponse> => {
    const rateLimitResponse = await rateLimiter(req);
    if (rateLimitResponse) {
      return rateLimitResponse;
    }
    return handler(req, ...args);
  };
}

/**
 * Get current rate limit status for a key
 */
export function getRateLimitStatus(key: string, config: RateLimitConfig) {
  const entry = rateLimitStore.get(key);
  const now = Date.now();
  
  if (!entry || now > entry.resetTime) {
    return {
      count: 0,
      remaining: config.limit,
      resetTime: now + config.windowMs,
    };
  }
  
  return {
    count: entry.count,
    remaining: Math.max(0, config.limit - entry.count),
    resetTime: entry.resetTime,
  };
}

/**
 * Clear rate limit for a specific key (useful for testing)
 */
export function clearRateLimit(key: string): void {
  rateLimitStore.delete(key);
}

/**
 * Clear all rate limits (useful for testing)
 */
export function clearAllRateLimits(): void {
  rateLimitStore.clear();
}
