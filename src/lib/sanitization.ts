/**
 * Input sanitization utilities to prevent XSS, injection attacks, and data corruption
 */

/**
 * HTML entities to escape
 */
const HTML_ENTITIES: Record<string, string> = {
  '&': '&amp;',
  '<': '&lt;',
  '>': '&gt;',
  '"': '&quot;',
  "'": '&#x27;',
  '/': '&#x2F;',
  '`': '&#x60;',
  '=': '&#x3D;',
};

/**
 * Dangerous protocols that should be removed
 */
const DANGEROUS_PROTOCOLS = [
  'javascript:',
  'data:',
  'vbscript:',
  'file:',
  'about:',
];

/**
 * Dangerous HTML tags that should be removed
 */
const DANGEROUS_TAGS = [
  'script',
  'iframe',
  'object',
  'embed',
  'form',
  'input',
  'textarea',
  'select',
  'button',
  'link',
  'meta',
  'style',
];

/**
 * Event handlers that should be removed
 */
const EVENT_HANDLERS = [
  'onload',
  'onerror',
  'onclick',
  'onmouseover',
  'onmouseout',
  'onkeydown',
  'onkeyup',
  'onkeypress',
  'onfocus',
  'onblur',
  'onchange',
  'onsubmit',
  'onreset',
];

/**
 * Escape HTML entities to prevent XSS
 */
export function escapeHtml(input: string): string {
  if (typeof input !== 'string') {
    return String(input);
  }
  
  return input.replace(/[&<>"'`=\/]/g, (match) => HTML_ENTITIES[match] || match);
}

/**
 * Remove HTML tags from input
 */
export function stripHtml(input: string): string {
  if (typeof input !== 'string') {
    return String(input);
  }
  
  return input.replace(/<[^>]*>/g, '');
}

/**
 * Sanitize text input by removing dangerous content
 */
export function sanitizeText(input: string): string {
  if (typeof input !== 'string') {
    return String(input);
  }
  
  let sanitized = input;
  
  // Remove null bytes
  sanitized = sanitized.replace(/\0/g, '');
  
  // Remove dangerous protocols
  DANGEROUS_PROTOCOLS.forEach(protocol => {
    const regex = new RegExp(protocol, 'gi');
    sanitized = sanitized.replace(regex, '');
  });
  
  // Remove event handlers
  EVENT_HANDLERS.forEach(handler => {
    const regex = new RegExp(`${handler}\\s*=`, 'gi');
    sanitized = sanitized.replace(regex, '');
  });
  
  // Remove dangerous HTML tags
  DANGEROUS_TAGS.forEach(tag => {
    const regex = new RegExp(`<\\s*\\/?\\s*${tag}[^>]*>`, 'gi');
    sanitized = sanitized.replace(regex, '');
  });
  
  // Trim whitespace
  sanitized = sanitized.trim();
  
  return sanitized;
}

/**
 * Sanitize username input (Telegram usernames)
 */
export function sanitizeUsername(input: string): string {
  if (typeof input !== 'string') {
    return '';
  }
  
  let sanitized = input.trim();
  
  // Remove any HTML
  sanitized = stripHtml(sanitized);
  
  // Ensure it starts with @
  if (sanitized && !sanitized.startsWith('@')) {
    sanitized = '@' + sanitized;
  }
  
  // Remove any characters that aren't allowed in Telegram usernames
  // Telegram usernames can only contain letters, numbers, and underscores
  sanitized = sanitized.replace(/[^@a-zA-Z0-9_]/g, '');
  
  // Limit length (Telegram usernames are max 32 characters including @)
  if (sanitized.length > 32) {
    sanitized = sanitized.substring(0, 32);
  }
  
  return sanitized;
}

/**
 * Sanitize project name input
 */
export function sanitizeProjectName(input: string): string {
  if (typeof input !== 'string') {
    return '';
  }
  
  let sanitized = input.trim();
  
  // Remove HTML tags
  sanitized = stripHtml(sanitized);
  
  // Remove dangerous content
  sanitized = sanitizeText(sanitized);
  
  // Remove excessive whitespace
  sanitized = sanitized.replace(/\s+/g, ' ');
  
  // Limit length
  if (sanitized.length > 100) {
    sanitized = sanitized.substring(0, 100).trim();
  }
  
  return sanitized;
}

/**
 * Sanitize URL input
 */
export function sanitizeUrl(input: string): string {
  if (typeof input !== 'string') {
    return '';
  }
  
  let sanitized = input.trim();
  
  // Remove HTML
  sanitized = stripHtml(sanitized);
  
  // Check for dangerous protocols
  const lowerUrl = sanitized.toLowerCase();
  for (const protocol of DANGEROUS_PROTOCOLS) {
    if (lowerUrl.startsWith(protocol)) {
      return '';
    }
  }
  
  // Only allow http and https URLs
  if (sanitized && !sanitized.match(/^https?:\/\//i)) {
    return '';
  }
  
  return sanitized;
}

/**
 * Sanitize email input
 */
export function sanitizeEmail(input: string): string {
  if (typeof input !== 'string') {
    return '';
  }
  
  let sanitized = input.trim().toLowerCase();
  
  // Remove HTML
  sanitized = stripHtml(sanitized);
  
  // Basic email format check and sanitization
  sanitized = sanitized.replace(/[^a-z0-9@._-]/g, '');
  
  return sanitized;
}

/**
 * Sanitize form data object
 */
export function sanitizeFormData(data: Record<string, any>): Record<string, any> {
  const sanitized: Record<string, any> = {};
  
  for (const [key, value] of Object.entries(data)) {
    if (typeof value === 'string') {
      // Apply appropriate sanitization based on field name
      if (key.toLowerCase().includes('username')) {
        sanitized[key] = sanitizeUsername(value);
      } else if (key.toLowerCase().includes('email')) {
        sanitized[key] = sanitizeEmail(value);
      } else if (key.toLowerCase().includes('url') || key.toLowerCase().includes('link')) {
        sanitized[key] = sanitizeUrl(value);
      } else if (key.toLowerCase().includes('project') && key.toLowerCase().includes('name')) {
        sanitized[key] = sanitizeProjectName(value);
      } else {
        sanitized[key] = sanitizeText(value);
      }
    } else if (Array.isArray(value)) {
      // Sanitize array elements
      sanitized[key] = value.map(item => 
        typeof item === 'string' ? sanitizeText(item) : item
      );
    } else {
      // Keep non-string values as-is
      sanitized[key] = value;
    }
  }
  
  return sanitized;
}

/**
 * Validate and sanitize Telegram group message content
 */
export function sanitizeGroupMessage(input: string): string {
  if (typeof input !== 'string') {
    return '';
  }
  
  let sanitized = input.trim();
  
  // Remove HTML tags but preserve line breaks
  sanitized = stripHtml(sanitized);
  
  // Remove dangerous content
  sanitized = sanitizeText(sanitized);
  
  // Preserve line breaks but limit consecutive ones
  sanitized = sanitized.replace(/\n{3,}/g, '\n\n');
  
  // Limit total length (Telegram messages have limits)
  if (sanitized.length > 4096) {
    sanitized = sanitized.substring(0, 4093) + '...';
  }
  
  return sanitized;
}

/**
 * Content Security Policy (CSP) helper
 */
export function generateCSPHeader(): string {
  const directives = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://clerk.com https://*.clerk.com",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "font-src 'self' https://fonts.gstatic.com",
    "img-src 'self' data: https: blob:",
    "connect-src 'self' https://api.clerk.com https://*.clerk.com https://sheets.googleapis.com",
    "frame-src 'self' https://clerk.com https://*.clerk.com",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
    "frame-ancestors 'none'",
    "upgrade-insecure-requests",
  ];
  
  return directives.join('; ');
}

/**
 * Sanitization middleware for API routes
 */
export function createSanitizationMiddleware() {
  return (data: any): any => {
    if (typeof data === 'string') {
      return sanitizeText(data);
    }
    
    if (Array.isArray(data)) {
      return data.map(item => createSanitizationMiddleware()(item));
    }
    
    if (data && typeof data === 'object') {
      return sanitizeFormData(data);
    }
    
    return data;
  };
}

/**
 * Log sanitization actions for security monitoring
 */
export function logSanitization(
  originalValue: string, 
  sanitizedValue: string, 
  field: string,
  userId?: string
): void {
  if (originalValue !== sanitizedValue) {
    console.warn('🧹 Input sanitized:', {
      field,
      userId: userId || 'anonymous',
      originalLength: originalValue.length,
      sanitizedLength: sanitizedValue.length,
      timestamp: new Date().toISOString(),
      // Don't log actual content for privacy
    });
  }
}
