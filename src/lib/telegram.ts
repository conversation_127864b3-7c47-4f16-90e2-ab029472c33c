import { TelegramClient } from "telegram";
import { StringSession } from "telegram/sessions";
import { getTelegramConfig } from "./env";

let client: TelegramClient | null = null;
let connectionPromise: Promise<TelegramClient> | null = null;
let lastActivity = Date.now();
let cleanupTimer: NodeJS.Timeout | null = null;

// Auto-disconnect after 5 minutes of inactivity
const INACTIVITY_TIMEOUT = 5 * 60 * 1000;

/**
 * Schedule automatic cleanup of inactive client
 */
function scheduleCleanup(): void {
  if (cleanupTimer) {
    clearTimeout(cleanupTimer);
  }

  cleanupTimer = setTimeout(async () => {
    const timeSinceLastActivity = Date.now() - lastActivity;
    if (timeSinceLastActivity >= INACTIVITY_TIMEOUT && client) {
      console.log("🧹 Auto-disconnecting inactive Telegram client");
      await disconnectTelegramClient();
    }
  }, INACTIVITY_TIMEOUT);
}

/**
 * Update last activity timestamp and schedule cleanup
 */
function updateActivity(): void {
  lastActivity = Date.now();
  scheduleCleanup();
}

/**
 * Initialize and return a Telegram client instance
 * Uses validated environment variables and implements proper error handling
 */
export async function initTelegramClient(): Promise<TelegramClient> {
  // If we already have a connection promise, wait for it
  if (connectionPromise) {
    console.log("⏳ Waiting for existing connection...");
    return await connectionPromise;
  }

  // If client exists and is connected, update activity and return it
  if (client && client.connected) {
    updateActivity();
    return client;
  }

  // Create new connection promise
  connectionPromise = createNewClient();

  try {
    const newClient = await connectionPromise;
    updateActivity();
    return newClient;
  } finally {
    connectionPromise = null;
  }
}

/**
 * Create a new Telegram client instance
 */
async function createNewClient(): Promise<TelegramClient> {
  try {
    const { apiId, apiHash, sessionString } = getTelegramConfig();
    const stringSession = new StringSession(sessionString);

    console.log("🔗 Initializing Telegram client...");

    const newClient = new TelegramClient(stringSession, apiId, apiHash, {
      connectionRetries: 5,
      timeout: 30000, // 30 second timeout
      autoReconnect: true,
    });

    await newClient.connect();

    if (!newClient.connected) {
      throw new Error("Failed to connect to Telegram - client not connected");
    }

    // Verify authentication by getting user info
    const me = await newClient.getMe();
    console.log(`✅ Connected to Telegram as: ${me.firstName} ${me.lastName || ''} (@${me.username || 'no username'})`);

    // Set up event handlers for connection management
    newClient.addEventHandler((event: any) => {
      if (event.className === 'UpdateConnectionState') {
        if (event.state === -1) { // Disconnected
          console.log("🔌 Telegram client disconnected");
          client = null;
        }
      }
    });

    client = newClient;
    return newClient;

  } catch (error: any) {
    console.error("❌ Failed to initialize Telegram client:", error.message);

    // Clean up failed client
    if (client) {
      try {
        await client.disconnect();
      } catch (disconnectError) {
        console.error("Error disconnecting failed client:", disconnectError);
      }
      client = null;
    }

    throw new Error(`Telegram client initialization failed: ${error.message}`);
  }
}

/**
 * Disconnect the Telegram client and clean up resources
 */
export async function disconnectTelegramClient(): Promise<void> {
  // Clear cleanup timer
  if (cleanupTimer) {
    clearTimeout(cleanupTimer);
    cleanupTimer = null;
  }

  // Clear connection promise
  connectionPromise = null;

  if (client) {
    try {
      if (client.connected) {
        await client.disconnect();
        console.log("🔌 Telegram client disconnected");
      }
    } catch (error: any) {
      console.error("❌ Error disconnecting Telegram client:", error.message);
    } finally {
      client = null;
    }
  }
}

/**
 * Check if the Telegram client is connected
 */
export function isTelegramClientConnected(): boolean {
  return client !== null && (client.connected ?? false);
}

/**
 * Get client connection status and info
 */
export function getTelegramClientStatus(): {
  connected: boolean;
  lastActivity: Date | null;
  timeSinceLastActivity: number | null;
} {
  return {
    connected: isTelegramClientConnected(),
    lastActivity: lastActivity ? new Date(lastActivity) : null,
    timeSinceLastActivity: lastActivity ? Date.now() - lastActivity : null,
  };
}

/**
 * Force reconnection of the Telegram client
 */
export async function reconnectTelegramClient(): Promise<TelegramClient> {
  console.log("🔄 Forcing Telegram client reconnection...");
  await disconnectTelegramClient();
  return await initTelegramClient();
}

/**
 * Graceful shutdown - disconnect client and clean up
 */
export async function shutdownTelegramClient(): Promise<void> {
  console.log("🛑 Shutting down Telegram client...");
  await disconnectTelegramClient();
}

// Handle process termination gracefully
if (typeof process !== 'undefined') {
  process.on('SIGINT', shutdownTelegramClient);
  process.on('SIGTERM', shutdownTelegramClient);
  process.on('beforeExit', shutdownTelegramClient);
}