import { z } from 'zod';

/**
 * Validation schemas for form inputs and API requests
 */

// Username validation - must start with @ and contain valid characters
const usernameSchema = z.string()
  .min(2, "Username must be at least 2 characters")
  .max(32, "Username must be at most 32 characters")
  .regex(/^@[a-zA-Z0-9_]+$/, "Username must start with @ and contain only letters, numbers, and underscores");

// Project name validation
const projectNameSchema = z.string()
  .min(1, "Project name is required")
  .max(100, "Project name must be at most 100 characters")
  .trim();

// Calendly link validation
const calendlyLinkSchema = z.string()
  .url("Must be a valid URL")
  .regex(/^https:\/\/(calendly\.com|cal\.com)/, "Must be a Calendly or Cal.com link")
  .optional()
  .or(z.literal(""));

// Email validation
const emailSchema = z.string().email("Must be a valid email address").optional();

/**
 * Form validation schema
 */
export const formValidationSchema = z.object({
  projectName: projectNameSchema,
  projectLeads: z.array(usernameSchema)
    .min(1, "At least one project lead is required")
    .max(5, "Maximum 5 project leads allowed"),
  outreachMemberName: z.string().min(1, "Outreach member is required").optional(),
  outreachMemberUsername: usernameSchema.optional(),
  customOutreachUsername: usernameSchema.optional(),
  useCustomOutreachUsername: z.boolean(),
  salesperson: z.string().optional(),
  customSalesRepUsername: usernameSchema.optional(),
  useCustomSalesRepUsername: z.boolean(),
  enterSalesRepManually: z.boolean(),
  customCalendlyLink: calendlyLinkSchema,
  includeCalendly: z.boolean(),
}).refine((data) => {
  // If using custom outreach username, it must be provided
  if (data.useCustomOutreachUsername && !data.customOutreachUsername) {
    return false;
  }
  
  // If not using custom outreach username, member name and username must be provided
  if (!data.useCustomOutreachUsername && (!data.outreachMemberName || !data.outreachMemberUsername)) {
    return false;
  }
  
  // If entering sales rep manually, username must be provided
  if (data.enterSalesRepManually && !data.customSalesRepUsername) {
    return false;
  }
  
  // If not entering sales rep manually, salesperson must be selected
  if (!data.enterSalesRepManually && !data.salesperson) {
    return false;
  }
  
  // If using custom sales rep username, it must be provided
  if (data.useCustomSalesRepUsername && !data.customSalesRepUsername) {
    return false;
  }
  
  return true;
}, {
  message: "Please fill in all required fields correctly"
});

/**
 * API request validation schema
 */
export const createGroupRequestSchema = z.object({
  projectName: projectNameSchema,
  projectLeads: z.array(usernameSchema).min(1).max(5),
  outreachTeamMember: usernameSchema,
  outreachMemberEmail: emailSchema,
  salesperson: z.string().min(1, "Salesperson is required"),
  salesRepUsername: usernameSchema,
  paUsername: usernameSchema.optional(),
  calendlyLink: calendlyLinkSchema,
  includeCalendly: z.boolean(),
  welcomeMessage: z.string().min(1, "Welcome message is required"),
  inviteSalesRep: z.boolean(),
  
  // Additional fields for Sales Representative
  salesRepEmoji: z.string().optional(),
  salesRepPAUsername: usernameSchema.optional(),
  salesRepCalendlyLink: calendlyLinkSchema,
  salesRepCategory: z.string().optional(),
  
  // Additional fields for Outreach Member
  outreachMemberUsernames: z.array(usernameSchema).optional(),
  outreachMemberEmoji: z.string().optional(),
});

/**
 * Environment variables validation (for Google Sheets)
 */
export const googleSheetsConfigSchema = z.object({
  clientEmail: z.string().email("Invalid client email"),
  privateKey: z.string().min(1, "Private key is required"),
  spreadsheetId: z.string().min(1, "Spreadsheet ID is required"),
});

/**
 * Validation helper functions
 */
export function validateUsername(username: string): { isValid: boolean; error?: string } {
  try {
    usernameSchema.parse(username);
    return { isValid: true };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { isValid: false, error: error.errors[0]?.message };
    }
    return { isValid: false, error: "Invalid username" };
  }
}

export function validateProjectName(projectName: string): { isValid: boolean; error?: string } {
  try {
    projectNameSchema.parse(projectName);
    return { isValid: true };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { isValid: false, error: error.errors[0]?.message };
    }
    return { isValid: false, error: "Invalid project name" };
  }
}

export function validateCalendlyLink(link: string): { isValid: boolean; error?: string } {
  if (!link) return { isValid: true }; // Optional field
  
  try {
    calendlyLinkSchema.parse(link);
    return { isValid: true };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { isValid: false, error: error.errors[0]?.message };
    }
    return { isValid: false, error: "Invalid Calendly link" };
  }
}

import {
  sanitizeText,
  sanitizeUsername,
  sanitizeProjectName,
  sanitizeUrl,
  sanitizeFormData,
  logSanitization
} from './sanitization';

/**
 * Sanitize user input to prevent XSS and other attacks
 * @deprecated Use specific sanitization functions from ./sanitization instead
 */
export function sanitizeInput(input: string): string {
  return sanitizeText(input);
}

/**
 * Validate and sanitize form data
 */
export function validateAndSanitizeFormData(data: any, userId?: string) {
  // Sanitize string inputs with appropriate functions
  const sanitized = {
    ...data,
    projectName: sanitizeProjectName(data.projectName || ''),
    projectLeads: Array.isArray(data.projectLeads)
      ? data.projectLeads.map((lead: string) => {
          const sanitizedLead = sanitizeUsername(lead || '');
          logSanitization(lead || '', sanitizedLead, 'projectLead', userId);
          return sanitizedLead;
        })
      : [],
    customOutreachUsername: sanitizeUsername(data.customOutreachUsername || ''),
    customSalesRepUsername: sanitizeUsername(data.customSalesRepUsername || ''),
    customCalendlyLink: sanitizeUrl(data.customCalendlyLink || ''),
  };

  // Log sanitization for security monitoring
  logSanitization(data.projectName || '', sanitized.projectName, 'projectName', userId);
  logSanitization(data.customOutreachUsername || '', sanitized.customOutreachUsername, 'customOutreachUsername', userId);
  logSanitization(data.customSalesRepUsername || '', sanitized.customSalesRepUsername, 'customSalesRepUsername', userId);
  logSanitization(data.customCalendlyLink || '', sanitized.customCalendlyLink, 'customCalendlyLink', userId);

  // Validate with schema
  return formValidationSchema.parse(sanitized);
}

/**
 * Type definitions for validation results
 */
export type FormValidationResult = z.infer<typeof formValidationSchema>;
export type CreateGroupRequestValidation = z.infer<typeof createGroupRequestSchema>;
export type GoogleSheetsConfigValidation = z.infer<typeof googleSheetsConfigSchema>;
