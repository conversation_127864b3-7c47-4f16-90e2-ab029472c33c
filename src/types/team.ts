/**
 * Type definitions for team members and related data structures
 */

export interface SalesRep {
  name: string;
  emoji: string;
  username: string;
  paUsername: string;
  calendarLink: string;
  tier: string;
}

export interface OutreachMember {
  name: string;
  emoji: string;
  telegramUsernames: string[];
  email?: string;
}

export interface MemeTeamLinks {
  oliver: string;
  tim: string;
  kyle: string;
}

export interface CalendlyLinks {
  intro?: string;
  followUp?: string;
  partnerships?: string;
  otc?: string;
  outreach?: string;
  default?: string;
  memeTeam?: MemeTeamLinks;
}

export interface TeamDataResponse {
  salesReps: SalesRep[];
  outreachMembers: OutreachMember[];
  warning?: string;
}

export interface FormData {
  projectName: string;
  projectLeads: string[];
  outreachTeamMember: string;
  outreachMemberName: string;
  outreachMemberUsername: string;
  customOutreachUsername: string;
  useCustomOutreachUsername: boolean;
  salesperson: string;
  customSalesRepUsername: string;
  useCustomSalesRepUsername: boolean;
  enterSalesRepManually: boolean;
  customCalendlyLink: string;
  includeCalendly: boolean;
  errors: FormErrors;
}

export interface FormErrors {
  projectName: string;
  projectLead: string;
  outreachTeamMember: string;
  salesperson: string;
  outreachMemberUsername: string;
  customOutreachUsername: string;
  customSalesRepUsername: string;
  customCalendlyLink: string;
}

export interface CreateGroupRequest {
  projectName: string;
  projectLeads: string[];
  outreachTeamMember: string;
  outreachMemberEmail?: string;
  salesperson: string;
  salesRepUsername: string;
  paUsername?: string;
  calendlyLink?: string;
  includeCalendly: boolean;
  welcomeMessage: string;
  inviteSalesRep: boolean;
  
  // Additional fields for Sales Representative
  salesRepEmoji?: string;
  salesRepPAUsername?: string;
  salesRepCalendlyLink?: string;
  salesRepCategory?: string;
  
  // Additional fields for Outreach Member
  outreachMemberUsernames?: string[];
  outreachMemberEmoji?: string;
}

export interface CreateGroupResponse {
  success: boolean;
  group_id?: string;
  group_name?: string;
  message?: string;
  added_members?: {
    admins: string[];
    regular: string[];
  };
  failed_invites?: FailedInvite[];
  error?: string;
}

export interface FailedInvite {
  username: string;
  reason: string;
}

export interface GroupCreationResult {
  success: boolean;
  failed_invites?: FailedInvite[];
}

/**
 * Type guards for runtime type checking
 */
export function isSalesRep(obj: any): obj is SalesRep {
  return (
    typeof obj === 'object' &&
    typeof obj.name === 'string' &&
    typeof obj.emoji === 'string' &&
    typeof obj.username === 'string' &&
    typeof obj.paUsername === 'string' &&
    typeof obj.calendarLink === 'string' &&
    typeof obj.tier === 'string'
  );
}

export function isOutreachMember(obj: any): obj is OutreachMember {
  return (
    typeof obj === 'object' &&
    typeof obj.name === 'string' &&
    typeof obj.emoji === 'string' &&
    Array.isArray(obj.telegramUsernames) &&
    obj.telegramUsernames.every((username: any) => typeof username === 'string')
  );
}

export function isTeamDataResponse(obj: any): obj is TeamDataResponse {
  return (
    typeof obj === 'object' &&
    Array.isArray(obj.salesReps) &&
    Array.isArray(obj.outreachMembers) &&
    obj.salesReps.every(isSalesRep) &&
    obj.outreachMembers.every(isOutreachMember)
  );
}

/**
 * Utility functions for working with team data
 */
export function findSalesRepByUsername(salesReps: SalesRep[], username: string): SalesRep | undefined {
  return salesReps.find(rep => rep.username === username);
}

export function findOutreachMemberByName(outreachMembers: OutreachMember[], name: string): OutreachMember | undefined {
  return outreachMembers.find(member => member.name === name);
}

export function formatUsername(username: string): string | null {
  if (!username) return null;
  const trimmed = username.trim();
  if (trimmed.toLowerCase() === "none") return null;
  if (!trimmed.startsWith("@")) return "@" + trimmed;
  return trimmed;
}

export function validateUsername(username: string): boolean {
  if (!username) return false;
  const formatted = formatUsername(username);
  return formatted !== null && formatted.length > 1;
}

export function getGroupName(projectName: string, salesRep?: SalesRep): string {
  if (!projectName) return "";
  const emoji = salesRep?.emoji || "";
  return `${emoji} ${projectName} <> IBC Group`;
}
