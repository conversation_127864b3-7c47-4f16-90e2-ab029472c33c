import { describe, it, expect, vi, beforeEach } from 'vitest';
import { POST } from '@/app/api/create-group/route';
import { NextRequest } from 'next/server';
import type { CreateGroupRequest } from '@/types/team';

// Mock dependencies
vi.mock('@/lib/telegram', () => ({
  initTelegramClient: vi.fn(),
}));

vi.mock('@/lib/audit-logger', () => ({
  auditLogger: {
    logGroupCreation: vi.fn(),
    log: vi.fn(),
  },
}));

describe('/api/create-group', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  const validRequest: CreateGroupRequest = {
    projectName: 'Test Project',
    salesRepUsername: '@johndoe',
    outreachMemberUsernames: ['@alice', '@bob'],
  };

  it('should create a group successfully', async () => {
    const { initTelegramClient } = await import('@/lib/telegram');
    
    // Mock successful telegram client
    const mockChannel = {
      id: 'test-group-123',
      title: 'Test Project - IBC Group',
    };
    
    const mockClient = {
      invoke: vi.fn().mockResolvedValue(mockChannel),
      getEntity: vi.fn()
        .mockResolvedValueOnce({ id: 'sales-rep-id' })
        .mockResolvedValueOnce({ id: 'alice-id' })
        .mockResolvedValueOnce({ id: 'bob-id' }),
      sendMessage: vi.fn().mockResolvedValue({}),
    };
    
    vi.mocked(initTelegramClient).mockResolvedValue(mockClient as any);

    const request = new NextRequest('http://localhost:3000/api/create-group', {
      method: 'POST',
      body: JSON.stringify(validRequest),
      headers: {
        'content-type': 'application/json',
        'x-user-id': 'test-user-id',
        'x-user-email': '<EMAIL>',
      },
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data).toMatchObject({
      success: true,
      group_id: 'test-group-123',
      group_name: 'Test Project - IBC Group',
      message: 'Group created successfully',
    });

    // Verify audit logging
    const { auditLogger } = await import('@/lib/audit-logger');
    expect(auditLogger.logGroupCreation).toHaveBeenCalledWith({
      userId: 'test-user-id',
      userEmail: '<EMAIL>',
      projectName: 'Test Project',
      groupId: 'test-group-123',
      salesRep: '@johndoe',
      outreachMembers: ['@alice', '@bob'],
      success: true,
      req: request,
    });
  });

  it('should handle validation errors', async () => {
    const invalidRequest = {
      projectName: '', // Empty project name
      salesRepUsername: '@johndoe',
      outreachMemberUsernames: ['@alice'],
    };

    const request = new NextRequest('http://localhost:3000/api/create-group', {
      method: 'POST',
      body: JSON.stringify(invalidRequest),
      headers: {
        'content-type': 'application/json',
        'x-user-id': 'test-user-id',
      },
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(400);
    expect(data).toHaveProperty('error');
    expect(data.error).toContain('validation');

    // Verify audit logging for failed creation
    const { auditLogger } = await import('@/lib/audit-logger');
    expect(auditLogger.logGroupCreation).toHaveBeenCalledWith({
      userId: 'test-user-id',
      userEmail: undefined,
      projectName: '',
      salesRep: '@johndoe',
      outreachMembers: ['@alice'],
      success: false,
      errorMessage: expect.stringContaining('validation'),
      req: request,
    });
  });

  it('should handle telegram client initialization errors', async () => {
    const { initTelegramClient } = await import('@/lib/telegram');
    
    // Mock failed telegram client initialization
    vi.mocked(initTelegramClient).mockRejectedValue(new Error('Failed to connect to Telegram'));

    const request = new NextRequest('http://localhost:3000/api/create-group', {
      method: 'POST',
      body: JSON.stringify(validRequest),
      headers: {
        'content-type': 'application/json',
        'x-user-id': 'test-user-id',
      },
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(500);
    expect(data).toHaveProperty('error');
    expect(data.error).toContain('Failed to connect to Telegram');

    // Verify audit logging for failed creation
    const { auditLogger } = await import('@/lib/audit-logger');
    expect(auditLogger.logGroupCreation).toHaveBeenCalledWith({
      userId: 'test-user-id',
      userEmail: undefined,
      projectName: 'Test Project',
      salesRep: '@johndoe',
      outreachMembers: ['@alice', '@bob'],
      success: false,
      errorMessage: 'Failed to connect to Telegram',
      req: request,
    });
  });

  it('should handle group creation failures', async () => {
    const { initTelegramClient } = await import('@/lib/telegram');
    
    // Mock telegram client that fails during group creation
    const mockClient = {
      invoke: vi.fn().mockRejectedValue(new Error('Failed to create group')),
    };
    
    vi.mocked(initTelegramClient).mockResolvedValue(mockClient as any);

    const request = new NextRequest('http://localhost:3000/api/create-group', {
      method: 'POST',
      body: JSON.stringify(validRequest),
      headers: {
        'content-type': 'application/json',
        'x-user-id': 'test-user-id',
      },
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(500);
    expect(data).toHaveProperty('error');

    // Verify audit logging for failed creation
    const { auditLogger } = await import('@/lib/audit-logger');
    expect(auditLogger.logGroupCreation).toHaveBeenCalledWith({
      userId: 'test-user-id',
      userEmail: undefined,
      projectName: 'Test Project',
      salesRep: '@johndoe',
      outreachMembers: ['@alice', '@bob'],
      success: false,
      errorMessage: expect.stringContaining('Failed to create group'),
      req: request,
    });
  });

  it('should handle malformed JSON requests', async () => {
    const request = new NextRequest('http://localhost:3000/api/create-group', {
      method: 'POST',
      body: 'invalid json',
      headers: {
        'content-type': 'application/json',
        'x-user-id': 'test-user-id',
      },
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(500);
    expect(data).toHaveProperty('error');

    // Verify audit logging for parse error
    const { auditLogger } = await import('@/lib/audit-logger');
    expect(auditLogger.log).toHaveBeenCalledWith({
      userId: 'test-user-id',
      userEmail: undefined,
      action: 'create_telegram_group',
      resource: 'telegram_group',
      details: { parseError: 'Failed to parse request body' },
      success: false,
      errorMessage: expect.any(String),
      severity: 'medium',
      category: 'group_creation',
    });
  });

  it('should sanitize input data', async () => {
    const { initTelegramClient } = await import('@/lib/telegram');
    
    // Mock successful telegram client
    const mockChannel = {
      id: 'test-group-123',
      title: 'Test Project - IBC Group',
    };
    
    const mockClient = {
      invoke: vi.fn().mockResolvedValue(mockChannel),
      getEntity: vi.fn().mockResolvedValue({ id: 'user-id' }),
      sendMessage: vi.fn().mockResolvedValue({}),
    };
    
    vi.mocked(initTelegramClient).mockResolvedValue(mockClient as any);

    const requestWithXSS = {
      projectName: '<script>alert("xss")</script>Test Project',
      salesRepUsername: '@johndoe<script>',
      outreachMemberUsernames: ['@alice<img src=x onerror=alert(1)>'],
    };

    const request = new NextRequest('http://localhost:3000/api/create-group', {
      method: 'POST',
      body: JSON.stringify(requestWithXSS),
      headers: {
        'content-type': 'application/json',
        'x-user-id': 'test-user-id',
      },
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.success).toBe(true);

    // Verify that the audit log contains sanitized data
    const { auditLogger } = await import('@/lib/audit-logger');
    const logCall = vi.mocked(auditLogger.logGroupCreation).mock.calls[0][0];
    
    // The project name should be sanitized (script tags removed)
    expect(logCall.projectName).not.toContain('<script>');
    expect(logCall.salesRep).not.toContain('<script>');
    expect(logCall.outreachMembers[0]).not.toContain('<img');
  });

  it('should handle partial member addition failures', async () => {
    const { initTelegramClient } = await import('@/lib/telegram');
    
    // Mock successful telegram client with some member addition failures
    const mockChannel = {
      id: 'test-group-123',
      title: 'Test Project - IBC Group',
    };
    
    const mockClient = {
      invoke: vi.fn().mockResolvedValue(mockChannel),
      getEntity: vi.fn()
        .mockResolvedValueOnce({ id: 'sales-rep-id' })
        .mockResolvedValueOnce({ id: 'alice-id' })
        .mockRejectedValueOnce(new Error('User not found')), // Bob fails
      sendMessage: vi.fn().mockResolvedValue({}),
    };
    
    vi.mocked(initTelegramClient).mockResolvedValue(mockClient as any);

    const request = new NextRequest('http://localhost:3000/api/create-group', {
      method: 'POST',
      body: JSON.stringify(validRequest),
      headers: {
        'content-type': 'application/json',
        'x-user-id': 'test-user-id',
      },
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.success).toBe(true);
    expect(data.failed_invites).toHaveLength(1);
    expect(data.failed_invites[0]).toMatchObject({
      username: '@bob',
      reason: expect.stringContaining('User not found'),
    });

    // Verify audit logging still shows success (group was created)
    const { auditLogger } = await import('@/lib/audit-logger');
    expect(auditLogger.logGroupCreation).toHaveBeenCalledWith({
      userId: 'test-user-id',
      userEmail: undefined,
      projectName: 'Test Project',
      groupId: 'test-group-123',
      salesRep: '@johndoe',
      outreachMembers: ['@alice', '@bob'],
      success: true,
      req: request,
    });
  });
});
