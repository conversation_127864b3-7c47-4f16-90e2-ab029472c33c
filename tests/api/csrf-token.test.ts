import { describe, it, expect, vi, beforeEach } from 'vitest';
import { GET } from '@/app/api/csrf-token/route';
import { NextRequest } from 'next/server';

describe('/api/csrf-token', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should return a CSRF token', async () => {
    const request = new NextRequest('http://localhost:3000/api/csrf-token');
    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data).toMatchObject({
      success: true,
      token: expect.any(String),
    });
    expect(data.token).toHaveLength(64); // 32 bytes * 2 (hex encoding)
  });

  it('should set CSRF cookie in response', async () => {
    const request = new NextRequest('http://localhost:3000/api/csrf-token');
    const response = await GET(request);

    const setCookieHeader = response.headers.get('set-cookie');
    expect(setCookieHeader).toBeTruthy();
    expect(setCookieHeader).toContain('csrf-token=');
    expect(setCookieHeader).toContain('HttpOnly');
    expect(setCookieHeader).toContain('Secure');
    expect(setCookieHeader).toContain('SameSite=Strict');
  });

  it('should generate different tokens on each request', async () => {
    const request1 = new NextRequest('http://localhost:3000/api/csrf-token');
    const request2 = new NextRequest('http://localhost:3000/api/csrf-token');

    const response1 = await GET(request1);
    const response2 = await GET(request2);

    const data1 = await response1.json();
    const data2 = await response2.json();

    expect(data1.token).not.toBe(data2.token);
  });

  it('should handle multiple concurrent requests', async () => {
    const requests = Array.from({ length: 5 }, () => 
      new NextRequest('http://localhost:3000/api/csrf-token')
    );

    const responses = await Promise.all(requests.map(req => GET(req)));
    const dataArray = await Promise.all(responses.map(res => res.json()));

    // All should succeed
    responses.forEach(response => {
      expect(response.status).toBe(200);
    });

    // All tokens should be unique
    const tokens = dataArray.map(data => data.token);
    const uniqueTokens = new Set(tokens);
    expect(uniqueTokens.size).toBe(tokens.length);
  });
});
