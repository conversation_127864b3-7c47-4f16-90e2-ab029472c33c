import { describe, it, expect, vi, beforeEach } from 'vitest';
import { GET } from '@/app/api/health/route';
import { NextRequest } from 'next/server';

// Mock the telegram client
vi.mock('@/lib/telegram', () => ({
  initTelegramClient: vi.fn(),
}));

describe('/api/health', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should return healthy status when telegram client is working', async () => {
    const { initTelegramClient } = await import('@/lib/telegram');
    
    // Mock successful telegram client
    const mockClient = {
      getMe: vi.fn().mockResolvedValue({
        id: 'test-bot-id',
        username: 'test_bot',
        firstName: 'Test Bot',
      }),
    };
    
    vi.mocked(initTelegramClient).mockResolvedValue(mockClient as any);

    const request = new NextRequest('http://localhost:3000/api/health');
    const response = await GET();
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data).toEqual({
      status: 'healthy',
      user_info: JSON.stringify({
        id: 'test-bot-id',
        username: 'test_bot',
        firstName: 'Test Bot',
      }),
    });
    expect(initTelegramClient).toHaveBeenCalledOnce();
    expect(mockClient.getMe).toHaveBeenCalledOnce();
  });

  it('should return error status when telegram client fails', async () => {
    const { initTelegramClient } = await import('@/lib/telegram');
    
    // Mock failed telegram client
    vi.mocked(initTelegramClient).mockRejectedValue(new Error('Connection failed'));

    const response = await GET();
    const data = await response.json();

    expect(response.status).toBe(500);
    expect(data).toEqual({
      error: 'Connection failed',
    });
    expect(initTelegramClient).toHaveBeenCalledOnce();
  });

  it('should handle telegram client initialization error', async () => {
    const { initTelegramClient } = await import('@/lib/telegram');
    
    // Mock telegram client that throws during getMe
    const mockClient = {
      getMe: vi.fn().mockRejectedValue(new Error('Authentication failed')),
    };
    
    vi.mocked(initTelegramClient).mockResolvedValue(mockClient as any);

    const response = await GET();
    const data = await response.json();

    expect(response.status).toBe(500);
    expect(data).toEqual({
      error: 'Authentication failed',
    });
    expect(initTelegramClient).toHaveBeenCalledOnce();
    expect(mockClient.getMe).toHaveBeenCalledOnce();
  });

  it('should handle unexpected errors gracefully', async () => {
    const { initTelegramClient } = await import('@/lib/telegram');
    
    // Mock telegram client that throws a non-Error object
    vi.mocked(initTelegramClient).mockRejectedValue('Unexpected error');

    const response = await GET();
    const data = await response.json();

    expect(response.status).toBe(500);
    expect(data).toHaveProperty('error');
    expect(initTelegramClient).toHaveBeenCalledOnce();
  });
});
