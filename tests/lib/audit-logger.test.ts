import { describe, it, expect, vi, beforeEach } from 'vitest';
import { auditLogger } from '@/lib/audit-logger';
import { NextRequest } from 'next/server';

describe('AuditLogger', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Clear logs between tests
    auditLogger['logs'] = [];
  });

  describe('log', () => {
    it('should log an audit entry', () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      auditLogger.log({
        userId: 'test-user',
        action: 'test_action',
        resource: 'test_resource',
        details: { test: 'data' },
        success: true,
        severity: 'low',
        category: 'system',
      });

      const logs = auditLogger.getLogs();
      expect(logs).toHaveLength(1);
      expect(logs[0]).toMatchObject({
        userId: 'test-user',
        action: 'test_action',
        resource: 'test_resource',
        details: { test: 'data' },
        success: true,
        severity: 'low',
        category: 'system',
      });
      expect(logs[0].id).toBeTruthy();
      expect(logs[0].timestamp).toBeTruthy();

      expect(consoleSpy).toHaveBeenCalledWith('🔍 AUDIT LOG:', expect.any(Object));
      consoleSpy.mockRestore();
    });

    it('should use appropriate log level based on severity', () => {
      const logSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      const warnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      const errorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      // Low severity -> console.log
      auditLogger.log({
        action: 'test',
        resource: 'test',
        details: {},
        success: true,
        severity: 'low',
        category: 'system',
      });

      // Medium severity -> console.warn
      auditLogger.log({
        action: 'test',
        resource: 'test',
        details: {},
        success: false,
        severity: 'medium',
        category: 'system',
      });

      // High severity -> console.error
      auditLogger.log({
        action: 'test',
        resource: 'test',
        details: {},
        success: false,
        severity: 'high',
        category: 'security',
      });

      expect(logSpy).toHaveBeenCalledTimes(1);
      expect(warnSpy).toHaveBeenCalledTimes(1);
      expect(errorSpy).toHaveBeenCalledTimes(1);

      logSpy.mockRestore();
      warnSpy.mockRestore();
      errorSpy.mockRestore();
    });
  });

  describe('logGroupCreation', () => {
    it('should log successful group creation', () => {
      const request = new NextRequest('http://localhost:3000/api/create-group');

      auditLogger.logGroupCreation({
        userId: 'test-user',
        userEmail: '<EMAIL>',
        projectName: 'Test Project',
        groupId: 'group-123',
        salesRep: '@johndoe',
        outreachMembers: ['@alice', '@bob'],
        success: true,
        req: request,
      });

      const logs = auditLogger.getLogs();
      expect(logs).toHaveLength(1);
      expect(logs[0]).toMatchObject({
        userId: 'test-user',
        userEmail: '<EMAIL>',
        action: 'create_telegram_group',
        resource: 'telegram_group',
        resourceId: 'group-123',
        success: true,
        severity: 'low',
        category: 'group_creation',
        details: {
          projectName: 'Test Project',
          salesRep: '@johndoe',
          outreachMembersCount: 2,
          outreachMembers: ['@alice', '@bob'],
        },
      });
    });

    it('should log failed group creation', () => {
      auditLogger.logGroupCreation({
        userId: 'test-user',
        projectName: 'Test Project',
        salesRep: '@johndoe',
        outreachMembers: ['@alice'],
        success: false,
        errorMessage: 'Connection failed',
      });

      const logs = auditLogger.getLogs();
      expect(logs).toHaveLength(1);
      expect(logs[0]).toMatchObject({
        success: false,
        errorMessage: 'Connection failed',
        severity: 'medium',
        category: 'group_creation',
      });
    });
  });

  describe('logSecurityEvent', () => {
    it('should log security events', () => {
      const request = new NextRequest('http://localhost:3000/api/test');

      auditLogger.logSecurityEvent({
        userId: 'test-user',
        event: {
          type: 'rate_limit_exceeded',
          details: { endpoint: '/api/test' },
          severity: 'medium',
        },
        req: request,
      });

      const logs = auditLogger.getLogs();
      expect(logs).toHaveLength(1);
      expect(logs[0]).toMatchObject({
        action: 'security_event',
        resource: 'security',
        success: false,
        severity: 'medium',
        category: 'security',
        details: {
          eventType: 'rate_limit_exceeded',
          endpoint: '/api/test',
        },
      });
    });
  });

  describe('getLogs', () => {
    beforeEach(() => {
      // Add some test logs
      auditLogger.log({
        userId: 'user1',
        action: 'action1',
        resource: 'resource1',
        details: {},
        success: true,
        severity: 'low',
        category: 'system',
      });

      auditLogger.log({
        userId: 'user2',
        action: 'action2',
        resource: 'resource2',
        details: {},
        success: false,
        severity: 'high',
        category: 'security',
      });

      auditLogger.log({
        userId: 'user1',
        action: 'action3',
        resource: 'resource1',
        details: {},
        success: true,
        severity: 'medium',
        category: 'group_creation',
      });
    });

    it('should return all logs when no filters applied', () => {
      const logs = auditLogger.getLogs();
      expect(logs).toHaveLength(3);
    });

    it('should filter logs by userId', () => {
      const logs = auditLogger.getLogs({ userId: 'user1' });
      expect(logs).toHaveLength(2);
      expect(logs.every(log => log.userId === 'user1')).toBe(true);
    });

    it('should filter logs by action', () => {
      const logs = auditLogger.getLogs({ action: 'action1' });
      expect(logs).toHaveLength(1);
      expect(logs[0].action).toBe('action1');
    });

    it('should filter logs by category', () => {
      const logs = auditLogger.getLogs({ category: 'security' });
      expect(logs).toHaveLength(1);
      expect(logs[0].category).toBe('security');
    });

    it('should filter logs by severity', () => {
      const logs = auditLogger.getLogs({ severity: 'high' });
      expect(logs).toHaveLength(1);
      expect(logs[0].severity).toBe('high');
    });

    it('should limit number of results', () => {
      const logs = auditLogger.getLogs({ limit: 2 });
      expect(logs).toHaveLength(2);
    });

    it('should return logs in descending order by timestamp', () => {
      const logs = auditLogger.getLogs();
      expect(logs).toHaveLength(3);
      
      // Check that timestamps are in descending order
      for (let i = 1; i < logs.length; i++) {
        expect(new Date(logs[i-1].timestamp).getTime())
          .toBeGreaterThanOrEqual(new Date(logs[i].timestamp).getTime());
      }
    });
  });

  describe('getSecuritySummary', () => {
    beforeEach(() => {
      // Add test logs with different categories and severities
      const now = new Date();
      
      // Recent security event
      auditLogger.log({
        action: 'security_event',
        resource: 'security',
        details: {},
        success: false,
        severity: 'high',
        category: 'security',
      });

      // Recent failed action
      auditLogger.log({
        action: 'create_group',
        resource: 'telegram_group',
        details: {},
        success: false,
        severity: 'medium',
        category: 'group_creation',
      });

      // Recent critical event
      auditLogger.log({
        action: 'system_failure',
        resource: 'system',
        details: {},
        success: false,
        severity: 'critical',
        category: 'system',
      });

      // Successful action
      auditLogger.log({
        userId: 'user1',
        action: 'fetch_data',
        resource: 'data',
        details: {},
        success: true,
        severity: 'low',
        category: 'data_access',
      });
    });

    it('should return security summary', () => {
      const summary = auditLogger.getSecuritySummary('24h');
      
      expect(summary).toMatchObject({
        totalEvents: 4,
        securityEvents: 1,
        failedActions: 3,
        criticalEvents: 1,
      });
      
      expect(summary.topActions).toEqual(
        expect.arrayContaining([
          { action: 'security_event', count: 1 },
          { action: 'create_group', count: 1 },
          { action: 'system_failure', count: 1 },
          { action: 'fetch_data', count: 1 },
        ])
      );
      
      expect(summary.topUsers).toEqual(
        expect.arrayContaining([
          { userId: 'user1', count: 1 },
        ])
      );
    });

    it('should handle empty logs', () => {
      auditLogger['logs'] = [];
      const summary = auditLogger.getSecuritySummary('24h');
      
      expect(summary).toMatchObject({
        totalEvents: 0,
        securityEvents: 0,
        failedActions: 0,
        criticalEvents: 0,
        topActions: [],
        topUsers: [],
      });
    });
  });

  describe('memory management', () => {
    it('should limit the number of logs stored', () => {
      const originalMaxLogs = auditLogger['maxLogs'];
      auditLogger['maxLogs'] = 3; // Set low limit for testing

      // Add more logs than the limit
      for (let i = 0; i < 5; i++) {
        auditLogger.log({
          action: `action${i}`,
          resource: 'test',
          details: {},
          success: true,
          severity: 'low',
          category: 'system',
        });
      }

      const logs = auditLogger.getLogs();
      expect(logs).toHaveLength(3);
      
      // Should keep the most recent logs
      expect(logs[0].action).toBe('action4');
      expect(logs[1].action).toBe('action3');
      expect(logs[2].action).toBe('action2');

      // Restore original limit
      auditLogger['maxLogs'] = originalMaxLogs;
    });
  });
});
