import { describe, it, expect, vi, beforeEach } from 'vitest';
import { 
  sanitizeFormData, 
  sanitizeGroupMessage, 
  logSanitization,
  generateCSPHeader 
} from '@/lib/sanitization';

describe('Sanitization Functions', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('sanitizeFormData', () => {
    it('should sanitize basic XSS attempts', () => {
      const maliciousData = {
        projectName: '<script>alert("xss")</script>Test Project',
        salesRepUsername: '@john<script>alert(1)</script>doe',
        outreachMemberUsernames: [
          '@alice<img src=x onerror=alert(1)>',
          '@bob<iframe src="javascript:alert(1)"></iframe>'
        ],
      };

      const sanitized = sanitizeFormData(maliciousData);

      expect(sanitized.projectName).toBe('Test Project');
      expect(sanitized.salesRepUsername).toBe('@johndoe');
      expect(sanitized.outreachMemberUsernames).toEqual(['@alice', '@bob']);
    });

    it('should remove HTML tags while preserving text content', () => {
      const dataWithHTML = {
        projectName: '<b>Important</b> Project <em>Name</em>',
        salesRepUsername: '@<strong>john</strong>doe',
        outreachMemberUsernames: ['@<span>alice</span>', '@<div>bob</div>'],
      };

      const sanitized = sanitizeFormData(dataWithHTML);

      expect(sanitized.projectName).toBe('Important Project Name');
      expect(sanitized.salesRepUsername).toBe('@johndoe');
      expect(sanitized.outreachMemberUsernames).toEqual(['@alice', '@bob']);
    });

    it('should handle nested HTML tags', () => {
      const dataWithNestedHTML = {
        projectName: '<div><script>alert(1)</script><p>Test <b>Project</b></p></div>',
        salesRepUsername: '@<div><span>john</span>doe</div>',
        outreachMemberUsernames: ['@<p><em>alice</em></p>'],
      };

      const sanitized = sanitizeFormData(dataWithNestedHTML);

      expect(sanitized.projectName).toBe('Test Project');
      expect(sanitized.salesRepUsername).toBe('@johndoe');
      expect(sanitized.outreachMemberUsernames).toEqual(['@alice']);
    });

    it('should handle malformed HTML', () => {
      const dataWithMalformedHTML = {
        projectName: '<script>alert(1)<p>Test Project</p>',
        salesRepUsername: '@john<b>doe',
        outreachMemberUsernames: ['@alice<span>', '@bob</div>'],
      };

      const sanitized = sanitizeFormData(dataWithMalformedHTML);

      expect(sanitized.projectName).toBe('Test Project');
      expect(sanitized.salesRepUsername).toBe('@johndoe');
      expect(sanitized.outreachMemberUsernames).toEqual(['@alice', '@bob']);
    });

    it('should preserve valid characters and symbols', () => {
      const validData = {
        projectName: 'Project Alpha-Beta (v2.0) - Phase 1',
        salesRepUsername: '@john_doe_123',
        outreachMemberUsernames: ['@alice_smith', '@bob_jones_99'],
      };

      const sanitized = sanitizeFormData(validData);

      expect(sanitized.projectName).toBe('Project Alpha-Beta (v2.0) - Phase 1');
      expect(sanitized.salesRepUsername).toBe('@john_doe_123');
      expect(sanitized.outreachMemberUsernames).toEqual(['@alice_smith', '@bob_jones_99']);
    });

    it('should handle empty and null values', () => {
      const dataWithEmpties = {
        projectName: '',
        salesRepUsername: '@johndoe',
        outreachMemberUsernames: ['', '@alice', null as any, '@bob'],
      };

      const sanitized = sanitizeFormData(dataWithEmpties);

      expect(sanitized.projectName).toBe('');
      expect(sanitized.salesRepUsername).toBe('@johndoe');
      expect(sanitized.outreachMemberUsernames).toEqual(['', '@alice', '', '@bob']);
    });

    it('should handle unicode characters', () => {
      const dataWithUnicode = {
        projectName: 'Проект 测试 🚀',
        salesRepUsername: '@john_doe',
        outreachMemberUsernames: ['@alice_测试', '@bob_проект'],
      };

      const sanitized = sanitizeFormData(dataWithUnicode);

      expect(sanitized.projectName).toBe('Проект 测试 🚀');
      expect(sanitized.salesRepUsername).toBe('@john_doe');
      expect(sanitized.outreachMemberUsernames).toEqual(['@alice_测试', '@bob_проект']);
    });

    it('should log sanitization when changes are made', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      const maliciousData = {
        projectName: '<script>alert("xss")</script>Test',
        salesRepUsername: '@johndoe',
        outreachMemberUsernames: ['@alice'],
      };

      sanitizeFormData(maliciousData);

      expect(consoleSpy).toHaveBeenCalledWith(
        '🧹 Sanitization applied to field:',
        'projectName',
        expect.objectContaining({
          original: '<script>alert("xss")</script>Test',
          sanitized: 'Test'
        })
      );

      consoleSpy.mockRestore();
    });
  });

  describe('sanitizeGroupMessage', () => {
    it('should sanitize HTML in group message', () => {
      const maliciousMessage = `
        Welcome to <script>alert('xss')</script>our group!
        <img src=x onerror=alert(1)>
        Visit our <a href="javascript:alert(1)">website</a>
      `;

      const sanitized = sanitizeGroupMessage(maliciousMessage);

      expect(sanitized).not.toContain('<script>');
      expect(sanitized).not.toContain('<img');
      expect(sanitized).not.toContain('javascript:');
      expect(sanitized).toContain('Welcome to');
      expect(sanitized).toContain('our group!');
      expect(sanitized).toContain('website');
    });

    it('should preserve line breaks and formatting', () => {
      const messageWithFormatting = `
        Welcome to our group!
        
        Here are the rules:
        1. Be respectful
        2. No spam
        
        Thank you!
      `;

      const sanitized = sanitizeGroupMessage(messageWithFormatting);

      expect(sanitized).toContain('Welcome to our group!');
      expect(sanitized).toContain('Here are the rules:');
      expect(sanitized).toContain('1. Be respectful');
      expect(sanitized).toContain('2. No spam');
      expect(sanitized).toContain('Thank you!');
    });

    it('should handle empty message', () => {
      const sanitized = sanitizeGroupMessage('');
      expect(sanitized).toBe('');
    });

    it('should handle message with only HTML tags', () => {
      const htmlOnlyMessage = '<script></script><div></div><span></span>';
      const sanitized = sanitizeGroupMessage(htmlOnlyMessage);
      expect(sanitized.trim()).toBe('');
    });
  });

  describe('logSanitization', () => {
    it('should log when sanitization occurs', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      logSanitization('testField', 'original<script>', 'original');

      expect(consoleSpy).toHaveBeenCalledWith(
        '🧹 Sanitization applied to field:',
        'testField',
        {
          original: 'original<script>',
          sanitized: 'original'
        }
      );

      consoleSpy.mockRestore();
    });

    it('should not log when no sanitization occurs', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      logSanitization('testField', 'clean text', 'clean text');

      expect(consoleSpy).not.toHaveBeenCalled();

      consoleSpy.mockRestore();
    });
  });

  describe('generateCSPHeader', () => {
    it('should generate a valid CSP header', () => {
      const csp = generateCSPHeader();

      expect(csp).toContain("default-src 'self'");
      expect(csp).toContain("script-src 'self'");
      expect(csp).toContain("style-src 'self'");
      expect(csp).toContain("img-src 'self'");
      expect(csp).toContain("connect-src 'self'");
      expect(csp).toContain("font-src 'self'");
      expect(csp).toContain("object-src 'none'");
      expect(csp).toContain("base-uri 'self'");
      expect(csp).toContain("form-action 'self'");
      expect(csp).toContain("frame-ancestors 'none'");
    });

    it('should include necessary domains for the application', () => {
      const csp = generateCSPHeader();

      // Should include Clerk domains for authentication
      expect(csp).toContain('clerk.dev');
      expect(csp).toContain('clerk.com');

      // Should include Google domains for sheets integration
      expect(csp).toContain('googleapis.com');

      // Should include data: for images (common for icons/avatars)
      expect(csp).toContain('data:');
    });

    it('should be a single line without line breaks', () => {
      const csp = generateCSPHeader();

      expect(csp).not.toContain('\n');
      expect(csp).not.toContain('\r');
    });

    it('should not contain dangerous directives', () => {
      const csp = generateCSPHeader();

      expect(csp).not.toContain("'unsafe-eval'");
      expect(csp).not.toContain("'unsafe-inline'");
      expect(csp).not.toContain('*');
    });
  });
});
