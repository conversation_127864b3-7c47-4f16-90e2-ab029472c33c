import { describe, it, expect } from 'vitest';
import { createGroupRequestSchema } from '@/lib/validation';

describe('Validation Schemas', () => {
  describe('createGroupRequestSchema', () => {
    it('should validate a correct request', () => {
      const validRequest = {
        projectName: 'Test Project',
        salesRepUsername: '@johndoe',
        outreachMemberUsernames: ['@alice', '@bob'],
      };

      const result = createGroupRequestSchema.safeParse(validRequest);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data).toEqual(validRequest);
      }
    });

    it('should reject empty project name', () => {
      const invalidRequest = {
        projectName: '',
        salesRepUsername: '@johndoe',
        outreachMemberUsernames: ['@alice'],
      };

      const result = createGroupRequestSchema.safeParse(invalidRequest);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toContain('projectName');
        expect(result.error.issues[0].message).toContain('at least 1 character');
      }
    });

    it('should reject project name that is too long', () => {
      const invalidRequest = {
        projectName: 'a'.repeat(101), // 101 characters
        salesRepUsername: '@johndoe',
        outreachMemberUsernames: ['@alice'],
      };

      const result = createGroupRequestSchema.safeParse(invalidRequest);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toContain('projectName');
        expect(result.error.issues[0].message).toContain('at most 100 characters');
      }
    });

    it('should reject invalid sales rep username format', () => {
      const invalidRequest = {
        projectName: 'Test Project',
        salesRepUsername: 'johndoe', // Missing @
        outreachMemberUsernames: ['@alice'],
      };

      const result = createGroupRequestSchema.safeParse(invalidRequest);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toContain('salesRepUsername');
        expect(result.error.issues[0].message).toContain('must start with @');
      }
    });

    it('should reject sales rep username that is too short', () => {
      const invalidRequest = {
        projectName: 'Test Project',
        salesRepUsername: '@a', // Too short
        outreachMemberUsernames: ['@alice'],
      };

      const result = createGroupRequestSchema.safeParse(invalidRequest);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toContain('salesRepUsername');
        expect(result.error.issues[0].message).toContain('at least 2 characters');
      }
    });

    it('should reject sales rep username that is too long', () => {
      const invalidRequest = {
        projectName: 'Test Project',
        salesRepUsername: '@' + 'a'.repeat(33), // 34 characters total
        outreachMemberUsernames: ['@alice'],
      };

      const result = createGroupRequestSchema.safeParse(invalidRequest);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toContain('salesRepUsername');
        expect(result.error.issues[0].message).toContain('at most 33 characters');
      }
    });

    it('should reject empty outreach members array', () => {
      const invalidRequest = {
        projectName: 'Test Project',
        salesRepUsername: '@johndoe',
        outreachMemberUsernames: [],
      };

      const result = createGroupRequestSchema.safeParse(invalidRequest);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toContain('outreachMemberUsernames');
        expect(result.error.issues[0].message).toContain('at least 1 element');
      }
    });

    it('should reject too many outreach members', () => {
      const invalidRequest = {
        projectName: 'Test Project',
        salesRepUsername: '@johndoe',
        outreachMemberUsernames: Array.from({ length: 21 }, (_, i) => `@user${i}`),
      };

      const result = createGroupRequestSchema.safeParse(invalidRequest);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toContain('outreachMemberUsernames');
        expect(result.error.issues[0].message).toContain('at most 20 elements');
      }
    });

    it('should reject invalid outreach member username format', () => {
      const invalidRequest = {
        projectName: 'Test Project',
        salesRepUsername: '@johndoe',
        outreachMemberUsernames: ['@alice', 'bob'], // 'bob' missing @
      };

      const result = createGroupRequestSchema.safeParse(invalidRequest);
      expect(result.success).toBe(false);
      if (!result.success) {
        const issue = result.error.issues.find(issue => 
          issue.path.includes('outreachMemberUsernames') && issue.path.includes(1)
        );
        expect(issue).toBeTruthy();
        expect(issue?.message).toContain('must start with @');
      }
    });

    it('should reject duplicate outreach members', () => {
      const invalidRequest = {
        projectName: 'Test Project',
        salesRepUsername: '@johndoe',
        outreachMemberUsernames: ['@alice', '@bob', '@alice'], // Duplicate @alice
      };

      const result = createGroupRequestSchema.safeParse(invalidRequest);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toContain('outreachMemberUsernames');
        expect(result.error.issues[0].message).toContain('duplicate');
      }
    });

    it('should reject when sales rep is also in outreach members', () => {
      const invalidRequest = {
        projectName: 'Test Project',
        salesRepUsername: '@johndoe',
        outreachMemberUsernames: ['@alice', '@johndoe'], // Sales rep in outreach
      };

      const result = createGroupRequestSchema.safeParse(invalidRequest);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('cannot be in outreach members');
      }
    });

    it('should handle missing fields', () => {
      const invalidRequest = {
        projectName: 'Test Project',
        // Missing salesRepUsername and outreachMemberUsernames
      };

      const result = createGroupRequestSchema.safeParse(invalidRequest);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues).toHaveLength(2);
        expect(result.error.issues.some(issue => 
          issue.path.includes('salesRepUsername') && issue.code === 'invalid_type'
        )).toBe(true);
        expect(result.error.issues.some(issue => 
          issue.path.includes('outreachMemberUsernames') && issue.code === 'invalid_type'
        )).toBe(true);
      }
    });

    it('should handle null values', () => {
      const invalidRequest = {
        projectName: null,
        salesRepUsername: null,
        outreachMemberUsernames: null,
      };

      const result = createGroupRequestSchema.safeParse(invalidRequest);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues).toHaveLength(3);
        expect(result.error.issues.every(issue => issue.code === 'invalid_type')).toBe(true);
      }
    });

    it('should handle wrong types', () => {
      const invalidRequest = {
        projectName: 123,
        salesRepUsername: true,
        outreachMemberUsernames: 'not an array',
      };

      const result = createGroupRequestSchema.safeParse(invalidRequest);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues).toHaveLength(3);
        expect(result.error.issues.every(issue => issue.code === 'invalid_type')).toBe(true);
      }
    });

    it('should trim whitespace from strings', () => {
      const requestWithWhitespace = {
        projectName: '  Test Project  ',
        salesRepUsername: '  @johndoe  ',
        outreachMemberUsernames: ['  @alice  ', '  @bob  '],
      };

      const result = createGroupRequestSchema.safeParse(requestWithWhitespace);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.projectName).toBe('Test Project');
        expect(result.data.salesRepUsername).toBe('@johndoe');
        expect(result.data.outreachMemberUsernames).toEqual(['@alice', '@bob']);
      }
    });

    it('should handle special characters in usernames', () => {
      const validRequest = {
        projectName: 'Test Project',
        salesRepUsername: '@john_doe123',
        outreachMemberUsernames: ['@alice_smith', '@bob_jones_99'],
      };

      const result = createGroupRequestSchema.safeParse(validRequest);
      expect(result.success).toBe(true);
    });

    it('should reject usernames with invalid characters', () => {
      const invalidRequest = {
        projectName: 'Test Project',
        salesRepUsername: '@john-doe!', // Invalid characters
        outreachMemberUsernames: ['@alice'],
      };

      const result = createGroupRequestSchema.safeParse(invalidRequest);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toContain('salesRepUsername');
        expect(result.error.issues[0].message).toContain('alphanumeric characters and underscores');
      }
    });
  });
});
