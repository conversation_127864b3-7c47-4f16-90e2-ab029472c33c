import { http, HttpResponse } from 'msw';
import type { TeamDataResponse, CreateGroupResponse } from '@/types/team';

export const handlers = [
  // Health check endpoint
  http.get('/api/health', () => {
    return HttpResponse.json({
      status: 'healthy',
      user_info: JSON.stringify({
        id: 'test-bot-id',
        username: 'test_bot',
        firstName: 'Test Bot',
      }),
    });
  }),

  // Team data endpoint
  http.get('/api/team-data', () => {
    const response: TeamDataResponse = {
      salesReps: [
        {
          name: '<PERSON>',
          username: '@johndoe',
          personalAssistant: '@johndoe_pa',
        },
        {
          name: '<PERSON>',
          username: '@janesmith',
          personalAssistant: '@janesmith_pa',
        },
      ],
      outreachMembers: [
        {
          name: '<PERSON>',
          username: '@alice<PERSON><PERSON><PERSON>',
        },
        {
          name: '<PERSON>',
          username: '@bob<PERSON><PERSON>',
        },
      ],
    };
    return HttpResponse.json(response);
  }),

  // Create group endpoint - success case
  http.post('/api/create-group', async ({ request }) => {
    const body = await request.json() as any;
    
    // Simulate validation error
    if (!body.projectName) {
      return HttpResponse.json(
        { error: 'Project name is required' },
        { status: 400 }
      );
    }

    // Simulate successful group creation
    const response: CreateGroupResponse = {
      success: true,
      group_id: 'test-group-123',
      group_name: `${body.projectName} - IBC Group`,
      message: 'Group created successfully',
      added_members: {
        admins: [body.salesRepUsername],
        regular: body.outreachMemberUsernames || [],
      },
      failed_invites: [],
    };

    return HttpResponse.json(response);
  }),

  // CSRF token endpoint
  http.get('/api/csrf-token', () => {
    return HttpResponse.json({
      success: true,
      token: 'mock-csrf-token-123',
    });
  }),

  // Rate limit error simulation
  http.post('/api/create-group-rate-limited', () => {
    return HttpResponse.json(
      { error: 'Rate limit exceeded' },
      { status: 429 }
    );
  }),

  // Server error simulation
  http.get('/api/team-data-error', () => {
    return HttpResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }),

  // Unauthorized access simulation
  http.get('/api/unauthorized', () => {
    return HttpResponse.json(
      { error: 'Unauthorized' },
      { status: 401 }
    );
  }),
];
