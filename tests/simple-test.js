// Simple test to verify our validation logic works
const { createGroupRequestSchema } = require('../src/lib/validation.ts');

console.log('🧪 Running simple validation tests...');

// Test 1: Valid request
try {
  const validRequest = {
    projectName: 'Test Project',
    salesRepUsername: '@johndoe',
    outreachMemberUsernames: ['@alice', '@bob'],
  };
  
  const result = createGroupRequestSchema.safeParse(validRequest);
  if (result.success) {
    console.log('✅ Test 1 passed: Valid request accepted');
  } else {
    console.log('❌ Test 1 failed: Valid request rejected');
    console.log(result.error.issues);
  }
} catch (error) {
  console.log('❌ Test 1 error:', error.message);
}

// Test 2: Invalid request (empty project name)
try {
  const invalidRequest = {
    projectName: '',
    salesRepUsername: '@johndoe',
    outreachMemberUsernames: ['@alice'],
  };
  
  const result = createGroupRequestSchema.safeParse(invalidRequest);
  if (!result.success) {
    console.log('✅ Test 2 passed: Invalid request rejected');
  } else {
    console.log('❌ Test 2 failed: Invalid request accepted');
  }
} catch (error) {
  console.log('❌ Test 2 error:', error.message);
}

console.log('🏁 Simple tests completed');
